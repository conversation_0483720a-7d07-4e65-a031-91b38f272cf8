package com.engine.zyydx2.gyl.workflow.po.action;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @FileName WriteField2XinJuAction.java
 * @Description 更新业务字段数据，
 * action都要挂在节点后
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/5
 */
@Getter
@Setter
public class WriteField2XinJuAction extends BaseBean implements Action {
    //---Action参数----START
    /**
     * 外部数据源名称
     */
    private String extSourceName;
    /**
     * 主表字段名-备注
     */
    private String fieldNotes;
    /**
     * 主表字段名-项目类型
     */
    private String fieldProjectType;
    /**
     * 主表字段名-采购方式
     */
    private String fieldProcureMethod;
    /**
     * 主表字段名-招标代理id
     */
    private String fieldAgencyId;
    //---Action参数----END

    /**
     * 外部表名 审批流程节点信息表
     */
    private final String EXT_TABLE_NAME = "sync_approval_process";

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        log.info(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg;
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //执行自身逻辑
            errorMsg = execuetMy(actionInfo);
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        if (!errorMsg.isEmpty()) {
            log.error("errorMsg：" + errorMsg);
        }
        log.info(this.getClass().getName() + "---END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    /**
     * 执行自身逻辑
     *
     * @param actionInfo
     * @return
     */
    private String execuetMy(ActionInfo actionInfo) {
        String errorMsg;
        RecordSet rs = new RecordSet();
        String requestid = actionInfo.getRequestId();
        //这边取nodeId，节点后执行的action，取当前节点id
        int nodeId = actionInfo.getCurrentNodeId();
        //获取已经存在的数据
        String existId = getExistId(rs, requestid, String.valueOf(nodeId));
        //获取工号，系统管理员为默认为1
        String workCode = getWorkCode(rs, actionInfo.getUser().getUID());
        log.info("existId:" + existId);
        log.info("userId:" + actionInfo.getUser().getUID());
        log.info("workCode:" + workCode);
        //校验是否有数据，这个时候首次流转是当前节点无数据的，需要插入一条数据，如果退回重新走的时候需要更新
        if (existId.isEmpty()) {
            //无数据插入
            errorMsg = insertData(rs, actionInfo, workCode, nodeId);
        } else {
            //有数据，则更新
            errorMsg = updateData(rs, actionInfo, workCode, existId);
        }
        return errorMsg;
    }


    /**
     * 校验是否已经有这个节点的数据了
     *
     * @param rs
     * @param requestid
     * @param nodeid
     * @return
     */
    private String getExistId(RecordSet rs, String requestid, String nodeid) {
        String id = "";
        String sql = "select id from " + EXT_TABLE_NAME + " where require_id = ? and node_id = ?";
        Object[] params = {requestid, nodeid};
        log.info("checkHasData sql :" + sql);
        log.info("checkHasData params :" + Arrays.toString(params));
        if (rs.executeQueryWithDatasource(sql, extSourceName, params) && rs.next()) {
            id = Util.null2String(rs.getString("id"));
        }
        return id;
    }

    /**
     * 插入数据
     *
     * @param rs
     * @param actionInfo
     * @param workCode
     * @return
     */
    private String insertData(RecordSet rs, ActionInfo actionInfo, String workCode, int nodeId) {
        String errorMsg = "";
        //流程id,节点id,节点名称,操作人
        //备注，项目类型，采购方式，招标代理id
        //创建时间,更新时间,
        String[] insertExtFields = {"require_id", "node_id", "node_name", "operator",
                "notes", "project_type", "procure_method", "agency_id",
                "created_at", "updated_at"};
        try {
            log.info("insertData---START");
            Map<String, String> mainData = actionInfo.getMainData();
            String nodeName = getNodeName(rs, nodeId);
            String currentDateTime = TimeUtil.getCurrentTimeString();
            StringBuilder sbFields = new StringBuilder();
            StringBuilder sbValues = new StringBuilder();
            List<Object> listValue = new ArrayList<>();

            //拼接insert sql 占位符
            for (int i = 0; i < insertExtFields.length; i++) {
                sbFields.append(insertExtFields[i]);
                sbValues.append("?");
                if (i < insertExtFields.length - 1) {
                    sbFields.append(",");
                    sbValues.append(",");
                }
            }
            //拼接value
            listValue.add(actionInfo.getRequestId());
            listValue.add(nodeId);
            listValue.add(nodeName);
            listValue.add(workCode);

            listValue.add(mainData.get(fieldNotes));
            listValue.add(mainData.get(fieldProjectType));
            listValue.add(mainData.get(fieldProcureMethod));
            listValue.add(mainData.get(fieldAgencyId));

            listValue.add(currentDateTime);
            listValue.add(currentDateTime);

            String sql = "insert into " + EXT_TABLE_NAME + "(" + sbFields + ") values (" + sbValues + ") ";
            log.info("insertData sql:" + sql);
            log.info("insertData listValue:" + listValue);
            if (!rs.executeUpdateWithDatasource(sql, extSourceName, listValue)) {
                errorMsg = "插入中间表数据出错：" + rs.getExceptionMsg();
            }
            log.info("insertData---rs msg:" + rs.getMsg());
            log.info("insertData---rs exMsg:" + rs.getExceptionMsg());
        } catch (Exception e) {
            errorMsg = "插入中间表数据异常：" + SDUtil.getExceptionDetail(e);
        }
        log.info("insertData---END");
        return errorMsg;

    }


    /**
     * 更新数据
     *
     * @param rs
     * @param actionInfo
     * @param workCode
     * @param id
     * @return
     */
    private String updateData(RecordSet rs, ActionInfo actionInfo, String workCode, String id) {
        String errorMsg = "";
        // 操作人
        // 备注，项目类型，采购方式，招标代理id
        // 更新时间
        String[] updateExtFields = {"operator", "notes", "project_type", "procure_method", "agency_id", "updated_at"};
        try {
            log.info("updateData---START");
            Map<String, String> mainData = actionInfo.getMainData();
            String currentDateTime = TimeUtil.getCurrentTimeString();
            StringBuilder sbUpdate = new StringBuilder();
            List<Object> listValue = new ArrayList<>();
            //拼接update sql 占位符
            for (int i = 0; i < updateExtFields.length; i++) {
                sbUpdate.append(updateExtFields[i]).append("=").append("?");
                if (i < updateExtFields.length - 1) {
                    sbUpdate.append(",");
                }
            }
            //拼接value
            listValue.add(workCode);
            listValue.add(mainData.get(fieldNotes));
            listValue.add(mainData.get(fieldProjectType));
            listValue.add(mainData.get(fieldProcureMethod));
            listValue.add(mainData.get(fieldAgencyId));
            listValue.add(currentDateTime);
            listValue.add(id);

            String sql = "update " + EXT_TABLE_NAME + " set " + sbUpdate + " where id= ? ";
            log.info("updateData sql:" + sql);
            log.info("updateData listValue:" + listValue);
            if (!rs.executeUpdateWithDatasource(sql, extSourceName, listValue)) {
                errorMsg = "更新中间表数据出错：" + rs.getExceptionMsg();
            }
            log.info("updateData---rs msg:" + rs.getMsg());
            log.info("updateData---rs exMsg:" + rs.getExceptionMsg());
        } catch (Exception e) {
            errorMsg = "更新中间表数据异常：" + SDUtil.getExceptionDetail(e);
        }
        log.info("updateData---END");
        return errorMsg;
    }

    /**
     * 获取节点名称
     *
     * @param rs
     * @param nodeid
     * @return
     */
    private String getNodeName(RecordSet rs, int nodeid) {
        String sql = "select nodename from workflow_nodebase where id = ?";
        if (rs.executeQuery(sql, nodeid) && rs.next()) {
            return Util.null2String(rs.getString("nodename"));
        }
        return "";

    }

    /**
     * 获取工号
     *
     * @param rs
     * @param userId
     * @return
     */
    private String getWorkCode(RecordSet rs, int userId) {
        if (userId == 1) {
            return "1";
        }
        String sql = "select workcode from hrmresource where id = ?";
        if (rs.executeQuery(sql, userId) && rs.next()) {
            return Util.null2String(rs.getString("workcode"));
        }
        return "";
    }

}
