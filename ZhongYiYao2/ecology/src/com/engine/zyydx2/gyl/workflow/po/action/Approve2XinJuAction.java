package com.engine.zyydx2.gyl.workflow.po.action;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @FileName Approve2XinJuAction.java
 * @Description 流程流转记录审批数据到心矩中间表
 * action都要挂在节点前
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/5
 */
@Getter
@Setter
public class Approve2XinJuAction extends BaseBean implements Action {
    //---Action参数----START
    /**
     * 外部数据源名称
     */
    private String extSourceName;
    //---Action参数----END
    /**
     * 外部表名 审批流程节点信息表
     */
    private final String EXT_TABLE_NAME = "sync_approval_process";

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        log.info(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg;
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //执行自身逻辑
            errorMsg = execuetMy(actionInfo);

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        if (!errorMsg.isEmpty()) {
            log.error("errorMsg：" + errorMsg);
        }
        log.info(this.getClass().getName() + "---END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    /**
     * 执行自身逻辑
     *
     * @param actionInfo
     * @return
     */
    private String execuetMy(ActionInfo actionInfo) {
        String errorMsg = "";
        //审批状态
        String status = "";

        //流程提交类型
        int submitType = actionInfo.getSubmitType();
        //其他提交类型不做处理，强制收回，流程干预
        //提交 -> 1已通过
        if (submitType == 1) {
            status = "1";
        } else if (submitType == 0) {
            //退回 -> 2拒绝
            status = "2";
        }
        if (!status.isEmpty()) {
            //这边取nodeId，节点前执行的action，这个节点id是指从哪个节点操作过来的
            int nodeId = actionInfo.getCurrentNodeId();
            //下个节点类型，如果是归档，则再插入或更新归档的数据
            String nextNodeType = actionInfo.getNextNodeType();
            //下个节点id
            int nextNodeId = actionInfo.getNextNodeId();
            //写入当前节点中间表状态
            errorMsg = writeStatus(nodeId, status, actionInfo);
            if (errorMsg.isEmpty() && "3".equals(nextNodeType)) {
                //写入归档节点中间表状态
                errorMsg = writeStatus(nextNodeId, status, actionInfo);
            }
        } else {
            log.warn("未获取到审批状态，跳过状态回写动作，当前提交类型为：" + actionInfo.getSubmitType());
        }

        return errorMsg;
    }

    /**
     * 写入状态
     *
     * @param nodeId
     * @param status
     * @param actionInfo
     * @return
     */
    private String writeStatus(int nodeId, String status, ActionInfo actionInfo) {
        String errorMsg = "";
        RecordSet rs = new RecordSet();
        String requestid = actionInfo.getRequestId();
        //获取已经存在的数据
        String existId = getExistId(rs, requestid, String.valueOf(nodeId));
        //获取工号，系统管理员为默认为1
        String workCode = getWorkCode(rs, actionInfo.getUser().getUID());
        log.info("existId:" + existId);
        log.info("userId:" + actionInfo.getUser().getUID());
        log.info("workCode:" + workCode);
        //校验是否有数据
        if (existId.isEmpty()) {
            //无数据插入
            errorMsg = insertData(rs, actionInfo, workCode, status, nodeId);
        } else {
            //有数据，则更新
            errorMsg = updateData(rs, workCode, status, existId);
        }
        return errorMsg;
    }


    /**
     * 校验是否已经有这个节点的数据了
     *
     * @param rs
     * @param requestid
     * @param nodeid
     * @return
     */
    private String getExistId(RecordSet rs, String requestid, String nodeid) {
        String id = "";
        String sql = "select id from " + EXT_TABLE_NAME + " where require_id = ? and node_id = ?";
        Object[] params = {requestid, nodeid};
        log.info("checkHasData sql :" + sql);
        log.info("checkHasData params :" + Arrays.toString(params));
        if (rs.executeQueryWithDatasource(sql, extSourceName, params) && rs.next()) {
            id = Util.null2String(rs.getString("id"));
        }
        return id;
    }

    /**
     * 插入数据
     *
     * @param rs
     * @param actionInfo
     * @param workCode
     * @param status
     * @param nodeId
     * @return
     */
    private String insertData(RecordSet rs, ActionInfo actionInfo, String workCode, String status, int nodeId) {
        String errorMsg = "";
        //流程id,节点id,节点名称,操作人,状态1-已通过，2=拒绝,创建时间,更新时间
        String[] insertExtFields = {"require_id", "node_id", "node_name", "operator", "status", "created_at", "updated_at"};
        try {
            log.info("insertData---START");
            String nodeName = getNodeName(rs, nodeId);
            String currentDateTime = TimeUtil.getCurrentTimeString();
            StringBuilder sbFields = new StringBuilder();
            StringBuilder sbValues = new StringBuilder();
            List<Object> listValue = new ArrayList<>();

            //拼接insert sql 占位符
            for (int i = 0; i < insertExtFields.length; i++) {
                sbFields.append(insertExtFields[i]);
                sbValues.append("?");
                if (i < insertExtFields.length - 1) {
                    sbFields.append(",");
                    sbValues.append(",");
                }
            }
            //拼接value
            listValue.add(actionInfo.getRequestId());
            listValue.add(nodeId);
            listValue.add(nodeName);
            listValue.add(workCode);
            listValue.add(status);
            listValue.add(currentDateTime);
            listValue.add(currentDateTime);

            String sql = "insert into " + EXT_TABLE_NAME + "(" + sbFields + ") values (" + sbValues + ") ";
            log.info("insertData sql:" + sql);
            log.info("insertData listValue:" + listValue);
            if (!rs.executeUpdateWithDatasource(sql, extSourceName, listValue)) {
                errorMsg = "插入中间表数据出错：" + rs.getExceptionMsg();
            }
            log.info("insertData---rs msg:" + rs.getMsg());
            log.info("insertData---rs exMsg:" + rs.getExceptionMsg());
        } catch (Exception e) {
            errorMsg = "插入中间表数据异常：" + SDUtil.getExceptionDetail(e);
        }
        log.info("insertData---END");
        return errorMsg;

    }

    /**
     * 更新数据
     *
     * @param rs
     * @param workCode
     * @param status
     * @param id
     * @return
     */
    private String updateData(RecordSet rs, String workCode, String status, String id) {
        String errorMsg = "";
        String[] updateExtFields = {"operator", "status", "updated_at"};
        try {
            log.info("updateData---START");
            String currentDateTime = TimeUtil.getCurrentTimeString();
            StringBuilder sbUpdate = new StringBuilder();
            List<Object> listValue = new ArrayList<>();
            //拼接update sql 占位符
            for (int i = 0; i < updateExtFields.length; i++) {
                sbUpdate.append(updateExtFields[i]).append("=").append("?");
                if (i < updateExtFields.length - 1) {
                    sbUpdate.append(",");
                }
            }
            //拼接value
            listValue.add(workCode);
            listValue.add(status);
            listValue.add(currentDateTime);
            listValue.add(id);

            String sql = "update " + EXT_TABLE_NAME + " set " + sbUpdate + " where id= ? ";
            log.info("updateData sql:" + sql);
            log.info("updateData listValue:" + listValue);
            if (!rs.executeUpdateWithDatasource(sql, extSourceName, listValue)) {
                errorMsg = "更新中间表数据出错：" + rs.getExceptionMsg();
            }
            log.info("updateData---rs msg:" + rs.getMsg());
            log.info("updateData---rs exMsg:" + rs.getExceptionMsg());
        } catch (Exception e) {
            errorMsg = "更新中间表数据异常：" + SDUtil.getExceptionDetail(e);
        }
        log.info("updateData---end");
        return errorMsg;
    }

    /**
     * 获取节点名称
     *
     * @param rs
     * @param nodeid
     * @return
     */
    private String getNodeName(RecordSet rs, int nodeid) {
        String sql = "select nodename from workflow_nodebase where id = ?";
        if (rs.executeQuery(sql, nodeid) && rs.next()) {
            return Util.null2String(rs.getString("nodename"));
        }
        return "";

    }

    /**
     * 获取工号
     *
     * @param rs
     * @param userId
     * @return
     */
    private String getWorkCode(RecordSet rs, int userId) {
        if (userId == 1) {
            return "1";
        }
        String sql = "select workcode from hrmresource where id = ?";
        if (rs.executeQuery(sql, userId) && rs.next()) {
            return Util.null2String(rs.getString("workcode"));
        }
        return "";
    }
}
