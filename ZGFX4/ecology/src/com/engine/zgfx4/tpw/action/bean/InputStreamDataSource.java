package com.engine.zgfx4.tpw.action.bean;

import javax.activation.DataSource;
import java.io.*;

/**
 * 自定义InputStream数据源实现
 */
public class InputStreamDataSource implements DataSource {
    private final String name;
    private final String contentType;
    private final InputStream inputStream;
    private ByteArrayOutputStream outputStream;

    public InputStreamDataSource(String name, String contentType, InputStream inputStream) {
        this.name = name;
        this.contentType = contentType;
        this.inputStream = inputStream;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        if (outputStream != null) {
            return new ByteArrayInputStream(outputStream.toByteArray());
        }
        return inputStream;
    }

    @Override
    public OutputStream getOutputStream() throws IOException {
        if (outputStream == null) {
            outputStream = new ByteArrayOutputStream();
        }
        return outputStream;
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    @Override
    public String getName() {
        return name;
    }
}