package com.engine.zgfx4.tpw.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zgfx4.tpw.action.bean.PrResponse;
import com.engine.zgfx4.tpw.action.bean.SDFunctionLog;
import com.engine.zgfx4.tpw.action.bean.SDInterfaceCallLog;
import com.engine.zgfx4.tpw.action.bean.SDUpdateDataLog;
import com.engine.zgfx4.tpw.util.DocFileInfo;
import com.engine.zgfx4.tpw.util.DocUtil;
import com.engine.zgfx4.tpw.util.SDLogUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import javax.activation.DataHandler;
import javax.mail.util.ByteArrayDataSource;
import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.soap.*;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;


/**
 * @FileName GeneratePrTicket
 * @Description 调用ariba的PR接口，生成PR单
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/6/9
 */
@Getter
@Setter
public class GeneratePrTicketActionBak extends BaseBean implements Action {

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 二开日志工具类
     */
    private SDLogUtil sdLogUtil;
    /*
     * 接口请求地址
     * */
    private String url;
    /*
     * 接口用户名
     * */
    private String username;
    /*
     * 接口密码
     * */
    private String password;
    /**
     * 请求Request
     */
    private RequestInfo requestInfo;
    /**
     * action信息
     */
    private ActionInfo actionInfo;
    /**
     * action错误信息
     */
    private String actionError;

    private SDFunctionLog sdFunctionLog;

    private SDInterfaceCallLog sdInterfaceCallLog;

    private SDUpdateDataLog sdUpdateDataLog;

    private String docids;

    private static String getElementText(Document doc, String tagName) {
        NodeList nodes = doc.getElementsByTagNameNS("urn:Ariba:Buyer:vsap", tagName);
        return nodes.getLength() > 0 ?
                nodes.item(0).getTextContent() : null;
    }

    public void parseSuccessResponse(String soapResponse, PrResponse result) {
        try {
            // 安全解析配置
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);

            Document doc = factory.newDocumentBuilder()
                    .parse(new ByteArrayInputStream(soapResponse.getBytes(StandardCharsets.UTF_8)));

            // 检查是否成功（非Composing状态）
            String status = getElementText(doc, "StatusString");
            if ("Composing".equalsIgnoreCase(status)) {
                result.setSuccess(false);
                result.setStatus(status);
                result.setPrNumber(getElementText(doc, "UniqueName"));
                return;
            }

            // 提取关键字段
            result.setOriginatingSystem(getElementText(doc, "OriginatingSystem"));
            result.setAmendmentId(getElementText(doc, "OriginatingSystemAmendmentID"));
            result.setReferenceId(getElementText(doc, "OriginatingSystemReferenceID"));
            result.setStatus(status);
            result.setPrNumber(getElementText(doc, "UniqueName"));
            result.setSuccess(true);

        } catch (Exception e) {
            actionError = SDUtil.getExceptionDetail(e);
            throw new RuntimeException("解析Ariba响应失败", e);
        }

    }

    @Override
    public String execute(RequestInfo requestInfoParam) {
        //初始化日志打印工具类
        sdLogUtil = new SDLogUtil();
        appendLog("GeneratePrTicketAction 执行 START");
        try {
            requestInfo = requestInfoParam;
            //初始化数据
            initData();
            if (StringUtils.isBlank(actionError)) {
                appendLog("GeneratePrTicketAction执行主方法executeMy()开始");
                executeMy();
                appendLog("GeneratePrTicketAction执行主方法executeMy()结束");
            }
        } catch (Exception e) {
            actionError = "GeneratePrTicketAction catch exception：" + e.getMessage();
            log.info("GeneratePrTicketAction catch exception：" + e.getMessage());
        }
        finally {
            appendLog(this.getClass().getName() + "---END---requestid:" + actionInfo.getRequestId());
            //后置执行操作
//            afterExecute();
        }
        appendLog("GeneratePrTicketAction 执行 END");
        return ActionUtil.handleResult("", requestInfo);
    }

    private void executeMy() {
        try {

            PrResponse prResponse = sendMtomSoapMessage();
            appendLog("sendMtomSoapMessage()方法的响应对象：PrResponse" + JSONObject.toJSONString(prResponse));
            int status = 0;
            if (prResponse.isSuccess()) {
                status = 0;
            }else {
                status = 1;
            }
            writeBackWFData(prResponse,status);
        } catch (Exception e) {
            actionError = "调用ariba的PR接口，生成PR单Action失败!";
        }
    }

    private PrResponse sendMtomSoapMessage() {
        // 1. 初始化SOAP连接
        PrResponse prResponse = new PrResponse();

        try {
            sdInterfaceCallLog.setApi_req_date(TimeUtil.getCurrentDateString());
            SOAPConnection connection = SOAPConnectionFactory.newInstance().createConnection();
            // 2. 创建SOAP消息
            appendLog("创建SOAP消息");
            SOAPMessage message = createSoapMessage();
            String soapMessageAsString = getSoapMessageAsString(message);
            appendLog("构建完成的SOAP请求消息：" + soapMessageAsString);
            sdInterfaceCallLog.setApi_req_packets(soapMessageAsString);
            // 3. 发送请求并获取响应
            sdInterfaceCallLog.setApi_req_time(TimeUtil.getCurrentTimeString());
            SOAPMessage response = connection.call(message, url);
            appendLog("发送请求并获取响应");
            sdInterfaceCallLog.setApi_res_time(TimeUtil.getCurrentTimeString());
            String responseXml = getFormattedSoapMessage(response);
            sdInterfaceCallLog.setApi_res_packets(responseXml);
            appendLog("完整SOAP响应:" + responseXml);
            prResponse.setErrMsg(responseXml);
            // 4. 处理响应
            parseSuccessResponse(responseXml, prResponse);
            if(prResponse.isSuccess()){
                sdInterfaceCallLog.setApi_result(0);
            }else {
                sdInterfaceCallLog.setApi_result(1);
            }
        } catch (Exception e) {
            actionError = "sendMtomSoapMessage Exception:" + SDUtil.getExceptionDetail(e);
            sdInterfaceCallLog.setApi_result(1);
            sdInterfaceCallLog.setApi_res_packets("sendMtomSoapMessage Exception:" + SDUtil.getExceptionDetail(e));
            appendLog("sendMtomSoapMessage Exception:" + SDUtil.getExceptionDetail(e));
        }
        return prResponse;
    }
    /**
     * 将SOAPMessage转换为格式化字符串
     */
    private String getSoapMessageAsString(SOAPMessage message) {
        try {
            // 创建输出流
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            // 写入输出流
            message.writeTo(out);
            // 格式化XML（可选）
            String rawXml = out.toString(StandardCharsets.UTF_8.name());
            return formatXml(rawXml);
        } catch (Exception e) {
            log.warn("无法转换SOAP消息为字符串", e);
            return "[Error: " + e.getMessage() + "]";
        }
    }

    /**
     * 格式化XML字符串
     */
    private String formatXml(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            Document doc = factory.newDocumentBuilder()
                    .parse(new InputSource(new StringReader(xml)));

            TransformerFactory tf = TransformerFactory.newInstance();
            tf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            return xml; // 返回原始XML如果格式化失败
        }
    }

    /**
     * 创建完整的SOAP消息
     */
    private SOAPMessage createSoapMessage() {
        try {
            //1 强制使用 JDK 内置实现（适用于 Java 8）
            MessageFactory factory = MessageFactory.newInstance();
            SOAPMessage message = factory.createMessage();

            // 启用 MTOM（JDK 内部属性名）
            // 同时设置所有已知的MTOM属性名（覆盖不同实现）
            message.setProperty("javax.xml.soap.mtom.enabled", "true");
            message.setProperty("com.sun.xml.internal.messaging.saaj.soap.mtom.enabled", "true");
            message.setProperty("jakarta.xml.soap.mtom.enabled", "true"); // Jakarta EE兼容
            message.setProperty("mtom.enabled", "true"); // 备用名称
            message.setProperty("javax.xml.soap.mtom.threshold", "1"); // 强制所有附件优化
            // 2. 构建SOAP结构
            buildSoapStructure(message);
            // 3. 添加MTOM附件
//            addMtomAttachments(message);
            // 4. 添加认证头
            addBasicAuthHeader(message);
            // 5. 保存所有更改
            message.saveChanges();
            // 调试：打印最终消息
            logSoapMessage(message);
            return message;
        } catch (Exception e) {
            log.info("createSoapMessage Exception:" + SDUtil.getExceptionDetail(e));
        }

        return null;
    }

    /**
     * 构建SOAP信封结构
     */
    private void buildSoapStructure(SOAPMessage message) throws SOAPException {
        try {
            log.info("buildSoapStructure--start");
            SOAPPart soapPart = message.getSOAPPart();
            SOAPEnvelope envelope = soapPart.getEnvelope();
            log.info("buildSoapStructure--1");
            // 添加命名空间声明
            // 添加所有必要的命名空间声明
            envelope.addNamespaceDeclaration("soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
            envelope.addNamespaceDeclaration("urn", "urn:Ariba:Buyer:vsap");
            log.info("buildSoapStructure--2");
            // 构建Header
            buildHeader(envelope);
            log.info("buildSoapStructure--3");
            // 构建Body
            buildBody(envelope);
            log.info("buildSoapStructure--end");
        } catch (Exception e) {
            log.info("buildSoapStructure Exception:" + SDUtil.getExceptionDetail(e));
        }

    }

    /**
     * 构建SOAP Header
     */
    private void buildHeader(SOAPEnvelope envelope) throws SOAPException {
        SOAPHeader header = envelope.getHeader();
        SOAPHeaderElement headers = header.addHeaderElement(envelope.createName("Headers", "urn", "urn:Ariba:Buyer:vsap"));

        headers.addChildElement("variant", "urn").setTextContent("");
        headers.addChildElement("partition", "urn").setTextContent("");
    }

    /**
     * 构建SOAP Body
     */
    private void buildBody(SOAPEnvelope envelope) throws SOAPException {
        SOAPBody body = envelope.getBody();

        // 创建主请求元素
        SOAPBodyElement requisition = body.addBodyElement(envelope.createName("RequisitionImportPullRequest", "urn", "urn:Ariba:Buyer:vsap"));
        requisition.setAttribute("partition", "");
        requisition.setAttribute("variant", "");

        // 构建Requisition Item
        buildRequisitionItem(envelope, requisition);
    }

    /**
     * 构建Requisition Item结构
     */
    private void buildRequisitionItem(SOAPEnvelope envelope, SOAPElement parent) throws SOAPException {
        SOAPElement item = parent.addChildElement("Requisition_RequisitionImportPull_Item", "urn")
                .addChildElement("item", "urn");
        Map<String, String> mainData = actionInfo.getMainData();

        // 添加基本信息
        item.addChildElement("Name", "urn").setTextContent(Util.null2String(mainData.get("lcbt")));

        // 添加DefaultLineItem
        buildDefaultLineItem(envelope, item, mainData);

        // 添加Requester
        buildRequester(envelope, item, mainData);

        // 添加CompanyCode
        item.addChildElement("CompanyCode", "urn")
                .addChildElement("UniqueName", "urn").setTextContent((Util.null2String(mainData.get("gsdm")).isEmpty() ? "6300" : Util.null2String(mainData.get("gsdm"))));

        // 添加其他字段
        item.addChildElement("UniqueName", "urn").setTextContent(Util.null2String(mainData.get("lcbh")));
        item.addChildElement("ImportedHeaderCommentStaging", "urn")
                .setTextContent(Util.null2String(mainData.get("ttbz")));
        item.addChildElement("ImportedHeaderExternalCommentStaging", "urn")
                .setTextContent(Util.null2String(mainData.get("ttnzgyssfkjyc")).isEmpty() ? "false" : Util.null2String(mainData.get("ttnzgyssfkjyc")));
        item.addChildElement("Operation", "urn").setTextContent(Util.null2String(mainData.get("cz")).isEmpty() ? "New" : Util.null2String(mainData.get("cz")));
        item.addChildElement("OriginatingSystem", "urn").setTextContent(Util.null2String(mainData.get("yxt")).isEmpty() ? "false" : Util.null2String(mainData.get("yxt")));
        item.addChildElement("OriginatingSystemReferenceID", "urn").setTextContent(Util.null2String(mainData.get("lcbh")));

        // 添加Custom字段
        buildCustomFields(envelope, item, mainData);

        // 添加LineItems
        buildLineItems(envelope, item);
    }

    /**
     * 构建DefaultLineItem
     */
    private void buildDefaultLineItem(SOAPEnvelope envelope, SOAPElement parent, Map<String, String> mainData) throws SOAPException {
        SOAPElement defaultLineItem = parent.addChildElement("DefaultLineItem", "urn");
        defaultLineItem.addChildElement("DeliverTo", "urn").setTextContent(Util.null2String(mainData.get("sqrxm")));
        defaultLineItem.addChildElement("NeedBy", "urn").setTextContent(Util.null2String(mainData.get("qwdhrq")).isEmpty() ? "" : Util.null2String(mainData.get("qwdhrq")) + "T20:00:00Z");
    }

    /**
     * 构建Requester
     */
    private void buildRequester(SOAPEnvelope envelope, SOAPElement parent, Map<String, String> mainData) throws SOAPException {
        SOAPElement requester = parent.addChildElement("Requester", "urn");
        requester.addChildElement("UniqueName", "urn").setTextContent(formatEmail(Util.null2String(mainData.get("sqryx"))));
        requester.addChildElement("PasswordAdapter", "urn").setTextContent(Util.null2String(mainData.get("PasswordAdapter")).isEmpty() ? "PasswordAdapter1" : Util.null2String(mainData.get("PasswordAdapter")));
    }

    /**
     * 构建Custom字段
     */
    private void buildCustomFields(SOAPEnvelope envelope, SOAPElement parent, Map<String, String> mainData) throws SOAPException {
        SOAPElement custom = parent.addChildElement("custom", "urn");
        SOAPElement booleanField = custom.addChildElement("CustomBoolean", "urn");
        booleanField.setAttribute("name", "IsContractAttachment");
        booleanField.setTextContent(Util.null2String(mainData.get("sfxygz")).isEmpty() ? "false" : Util.null2String(mainData.get("sfxygz")));

    }

    /**
     * 构建LineItems
     */
    private void buildLineItems(SOAPEnvelope envelope, SOAPElement parent) throws SOAPException {
        SOAPElement lineItems = parent.addChildElement("LineItems", "urn");

        // 添加LineItem
        buildLineItem(envelope, lineItems);
    }

    /**
     * 构建单个LineItem
     */
    private void buildLineItem(SOAPEnvelope envelope, SOAPElement parent) throws SOAPException {
        Map<String, String> mainData = actionInfo.getMainData();
        Map<Integer, List<Map<String, String>>> detailData = actionInfo.getDetailData();
        List<Map<String, String>> details = detailData.get(1); // 假设第一个明细表
        if (details != null && !details.isEmpty()) {

            for (int i = 0; i < details.size(); i++) {
                int rowindex = i + 1;
                Map<String, String> detail = details.get(i);
                SOAPElement item = parent.addChildElement("item", "urn");
                // 添加基础字段
                item.addChildElement("Quantity", "urn").setTextContent(Util.null2String(detail.get("sl")));
                item.addChildElement("NumberInCollection", "urn").setTextContent(rowindex + "");

                // 添加地址信息
                item.addChildElement("BillingAddress", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("zdjsf")).isEmpty() ? "6320-010" : Util.null2String(detail.get("zdjsf")));
                item.addChildElement("ShipTo", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("shxx")).isEmpty() ? "6320-002" : Util.null2String(detail.get("shxx")));
                item.addChildElement("SupplierLocation", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(formatCostCenter(Util.null2String(detail.get("gysbh"))));

                // 添加Description
                buildDescription(envelope, item, detail);
                // 添加其他字段
                item.addChildElement("ImportedItemCategoryStaging", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("xmlb")).isEmpty() ? "M" : Util.null2String(detail.get("xmlb")));
                item.addChildElement("ItemCategory", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("xmlb")).isEmpty() ? "custom" : Util.null2String(detail.get("xmlb")));

                item.addChildElement("PurchaseOrg", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("cgzz")).isEmpty() ? "6300" : Util.null2String(detail.get("cgzz")));
                item.addChildElement("Supplier", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(formatCostCenter(Util.null2String(detail.get("gysbh"))));
                item.addChildElement("TaxCodeCountryStaging", "urn")
                        .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("smdq")));
                // 添加Imported字段
                item.addChildElement("ImportedAccountTypeStaging", "urn").setTextContent(Util.null2String(detail.get("zhlx")).isEmpty() ? "Project" : Util.null2String(detail.get("zhlx")));
                item.addChildElement("ImportedDeliverToStaging", "urn").setTextContent(new User(Integer.parseInt(Util.null2String(mainData.get("sqr")))).getLastname());
                item.addChildElement("ImportedNeedByStaging", "urn").setTextContent(Util.null2String(detail.get("qwdhrq")).isEmpty() ? "" : Util.null2String(detail.get("qwdhrq")) + "T20:00:00Z");
                item.addChildElement("ImportedTaxCodeStaging", "urn").setTextContent(Util.null2String(detail.get("sm")));
                item.addChildElement("Operation", "urn").setTextContent(Util.null2String(detail.get("czlx")).isEmpty() ? "New" : Util.null2String(detail.get("czlx")));
                item.addChildElement("OriginatingSystemLineNumber", "urn").setTextContent(rowindex + "");
                // 添加Accountings
                SOAPElement accountings = item.addChildElement("ImportedAccountingsStaging", "urn");
                SOAPElement type = accountings.addChildElement("Type", "urn");
                type.addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("cffs")).isEmpty() ? "_Percentage" : Util.null2String(detail.get("cffs")));
                SOAPElement splitAccountings = accountings.addChildElement("SplitAccountings", "urn");
                buildAccountingItem(envelope, splitAccountings, rowindex, detail);
            }
        }
    }

    /*
     * 添加MTOM附件
     * */

    /**
     * 构建Description
     */
    private void buildDescription(SOAPEnvelope envelope, SOAPElement parent, Map<String, String> detail) throws SOAPException {
        SOAPElement description = parent.addChildElement("Description", "urn");

        // 添加CommodityCode
        SOAPElement commodityCode = description.addChildElement("CommonCommodityCode", "urn");
        commodityCode.addChildElement("Domain", "urn").setTextContent(Util.null2String(detail.get("y")).isEmpty() ? "custom" : Util.null2String(detail.get("y")));
        commodityCode.addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("spdm")));

        // 添加其他字段
        description.addChildElement("Description", "urn")
                .setTextContent(Util.null2String(detail.get("wzms")));

        // 添加Price
        SOAPElement price = description.addChildElement("Price", "urn");
        price.addChildElement("Amount", "urn").setTextContent(Util.null2String(detail.get("jg")));
        price.addChildElement("Currency", "urn")
                .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("bzwb")));

        description.addChildElement("UnitOfMeasure", "urn")
                .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("jldw")).isEmpty() ? "EA" : Util.null2String(detail.get("jldw")));

        description.addChildElement("SupplierPartNumber", "urn")
                .setTextContent(Util.null2String(detail.get("gysbjh")));
    }


    /**
     * 构建单个Accounting项
     */
    private void buildAccountingItem(SOAPEnvelope envelope, SOAPElement parent, int rowindex, Map<String, String> detail) throws SOAPException {
        SOAPElement item = parent.addChildElement("item", "urn");

        // 添加GeneralLedger
        SOAPElement gl = item.addChildElement("GeneralLedger", "urn");
        gl.addChildElement("CompanyCode", "urn")
                .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("zzzhszgsdm")).isEmpty() ? "6300" : Util.null2String(detail.get("zzzhszgsdm")));
        gl.addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("zzzh")));

        // 添加CostCenter
        SOAPElement costCenter = item.addChildElement("CostCenter", "urn");
        costCenter.addChildElement("CompanyCode", "urn")
                .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("cbzxszgsdm")).isEmpty() ? "6300" : Util.null2String(detail.get("cbzxszgsdm")));
        costCenter.addChildElement("UniqueName", "urn").setTextContent(formatCostCenter(Util.null2String(detail.get("cbzxdm"))));

        // 添加其他字段
        item.addChildElement("InternalOrder", "urn")
                .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("nbdd")).isEmpty() ? "X00000000000" : Util.null2String(detail.get("nbdd")));
        item.addChildElement("WBSElement", "urn")
                .addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("hcpbh")));
        item.addChildElement("NumberInCollection", "urn").setTextContent(rowindex + "");
        item.addChildElement("Percentage", "urn").setTextContent(Util.null2String(detail.get("cfbfb")).isEmpty() ? "100" : Util.null2String(detail.get("cfbfb")));
        item.addChildElement("Quantity", "urn").setTextContent(Util.null2String(detail.get("cfsl")).isEmpty() ? "1" : Util.null2String(detail.get("cfsl")));

        // 添加Custom字段
        buildAccountingCustomFields(envelope, item, detail);
    }

    /**
     * 构建Accounting的Custom字段
     */
    private void buildAccountingCustomFields(SOAPEnvelope envelope, SOAPElement parent, Map<String, String> detail) throws SOAPException {
        SOAPElement custom = parent.addChildElement("custom", "urn");

        SOAPElement budgetProject = custom.addChildElement("CustomBudgetProject", "urn");
        budgetProject.setAttribute("name", "BudgetProject");
        budgetProject.addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("ysxm")).isEmpty() ? "BUDGETTEST" : Util.null2String(detail.get("ysxm")));

        SOAPElement fixedAsset = custom.addChildElement("CustomIsFixedAssetOrChargeUSA", "urn");
        fixedAsset.setAttribute("name", "IsFixedAssetOrChargeUSA");
        fixedAsset.addChildElement("UniqueName", "urn").setTextContent(Util.null2String(detail.get("sfgdzcchargrmgrdxm")).isEmpty() ? "10" : Util.null2String(detail.get("sfgdzcchargrmgrdxm")));
    }

    private void addMtomAttachments(SOAPMessage message) throws Exception {
        if (StringUtils.isNotBlank(docids)) {
            List<DocFileInfo> listDocFileInfo = DocUtil.getDocFileInfoByDocIds(docids);
            int attachmentIndex = 1;
            for (DocFileInfo docFileInfo : listDocFileInfo) {
                int fileId = Util.getIntValue(docFileInfo.getFileid());
                String fileName = docFileInfo.getFileName();
                String contentId = "attachment_" + attachmentIndex++;

                try (InputStream in = DocUtil.getFileInputStream(fileId)) {
                    if (in == null) {
                        throw new IOException("无法获取文件输入流，fileId: " + fileId);
                    }

                    // 创建数据源
                    ByteArrayDataSource dataSource = new ByteArrayDataSource(
                            IOUtils.toByteArray(in),
                            getContentType(fileName)
                    );

                    // 创建附件部分
                    AttachmentPart attachment = message.createAttachmentPart(
                            new DataHandler(dataSource)
                    );
                    attachment.setContentId(contentId);

                    // 设置附件特定的Content-Type
                    attachment.setMimeHeader("Content-Type", getContentType(fileName));
                    attachment.setMimeHeader("Content-Transfer-Encoding", "binary");

                    message.addAttachmentPart(attachment);
                }
            }
        }
    }

    /**
     * 添加单个附件
     */
    private void addAttachment(SOAPMessage message, String filename, String contentType,
                               String contentId, byte[] content) throws Exception {
        ByteArrayDataSource dataSource = new ByteArrayDataSource(content, contentType);
        AttachmentPart attachment = message.createAttachmentPart(new DataHandler(dataSource));
        attachment.setContentId(contentId);
        message.addAttachmentPart(attachment);
    }

    /**
     * 添加Basic认证头
     */
    private void addBasicAuthHeader(SOAPMessage message) {
        String auth = username + ":" + password;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));

        MimeHeaders headers = message.getMimeHeaders();
        headers.addHeader("Authorization", "Basic " + encodedAuth);
    }

    /**
     * 记录SOAP消息（调试用）
     */
    private void logSoapMessage(SOAPMessage message) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        message.writeTo(out);
    }


    // 辅助方法：获取格式化SOAP消息
    private String getFormattedSoapMessage(SOAPMessage message) {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            message.writeTo(out);
            return formatXml(out.toString("UTF-8"));
        } catch (Exception e) {
            appendLog("无法格式化SOAP消息"+SDUtil.getExceptionDetail(e));
            return "[原始消息获取失败: " + e.getMessage() + "]";
        }
    }

    private void initData() {
        try {
            actionError = "";
            actionInfo = ActionUtil.getInfo(requestInfo);
            docids = getAttachmentDocIds();
            appendLog(this.getClass().getName() + "---初始化initData-START" + actionInfo.getRequestId());
            appendLog(this.getClass().getName() + "---此次流程的requestid:" + actionInfo.getRequestId());
            appendLog(this.getClass().getName() + "---接口请求地址:" + url);
            appendLog(this.getClass().getName() + "---接口用户名:" + username);
            appendLog(this.getClass().getName() + "---接口密码:" + password);
            //初始化日志文件
            //二开功能执行记录日志
            sdFunctionLog = SDFunctionLog.initLog(actionInfo.getUser().getUID());
            sdFunctionLog.setFuc_code(this.getClass().getSimpleName());// 输出当前类名: UpdateMainManagerAction
            sdFunctionLog.setFuc_type(0);
            sdFunctionLog.setCode_path(this.getClass().getName());// 输出当前类路径名: com.engine.dfmzht4.gyl.workflow.contractTransfer.UpdateMainManagerAction
            sdFunctionLog.setFuc_desc("调用ariba的PR接口生成PR单");
            sdFunctionLog.setRelate_module("流程");
            sdFunctionLog.setRelate_table(actionInfo.getFormtableName());
            sdFunctionLog.setRelate_dataid("requestId:" + actionInfo.getRequestId());
            //接口调用日志
            sdInterfaceCallLog = SDInterfaceCallLog.initLog();
            //更新数据日志
            sdUpdateDataLog = SDUpdateDataLog.initLog();
            appendLog(this.getClass().getName() + "---初始化END");
        } catch (Exception e) {
            actionError = "action初始化步骤异常:" + SDUtil.getExceptionDetail(e);
            appendLog("action初始化步骤异常" + SDUtil.getExceptionDetail(e));
        }
    }

    private void writeBackWFData(PrResponse prResponse, int apiStatus) {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            //更新前的流程数据
            String queryBeforeSql = "select * from " + actionInfo.getFormtableName() + " where requestid= " + actionInfo.getRequestId();
            recordSet.executeQuery(queryBeforeSql);
            List<Map<String, Object>> dataBeforeList = QueryUtil.getMapList(recordSet);

            String status = prResponse.getStatus();
            String referenceId = prResponse.getReferenceId();
            String prNumber = prResponse.getPrNumber();

            String setUpdate_dml = "UPDATE " + actionInfo.getFormtableName() + " set aribacgsqzt = '" + status + "' ,aribacgsqdh =  '" + prNumber + "' ,apiStatus = '"+apiStatus+"' where requestid = " + actionInfo.getRequestId();
            recordSet.executeUpdate(setUpdate_dml);
            sdUpdateDataLog.setUpdate_begin_time(TimeUtil.getCurrentTimeString());
            if (recordSet.executeUpdate(setUpdate_dml)) {
                sdUpdateDataLog.setUpdate_result(0);
            } else {
                sdUpdateDataLog.setUpdate_result(1);
                sdUpdateDataLog.setError(recordSet.getExceptionMsg());
            }
            sdUpdateDataLog.setUpdate_end_time(TimeUtil.getCurrentTimeString());
            String queryAfterSql = "select * from " + actionInfo.getFormtableName() + " where requestid= " + actionInfo.getRequestId();
            recordSet.executeQuery(queryAfterSql);
            List<Map<String, Object>> dataAfterList = QueryUtil.getMapList(recordSet);
            sdUpdateDataLog.setUpdate_table(actionInfo.getFormtableName());
            sdUpdateDataLog.setUpdate_modid(0);
            sdUpdateDataLog.setUpdate_dataid(actionInfo.getRequestId());
            sdUpdateDataLog.setUpdate_fields("aribacgsqzt,aribacgsqdh,apiStatus");
            sdUpdateDataLog.setBefore_data(JSONObject.toJSONString(dataBeforeList));
            sdUpdateDataLog.setAfter_data(JSONObject.toJSONString(dataAfterList));
            sdUpdateDataLog.setUpdate_dml(setUpdate_dml);
            sdUpdateDataLog.setUpdate_desc("本次更新是执行");
            sdUpdateDataLog.setUpdate_date(TimeUtil.getToday());
            sdUpdateDataLog.saveLog(sdUpdateDataLog, 1);
        } catch (Exception e) {

        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void writeBackWFErrData(int status) {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String setUpdate_dml = "UPDATE " + actionInfo.getFormtableName() + " set apiStatus = "+status+"  where requestid = " + actionInfo.getRequestId();
            recordSet.executeUpdate(setUpdate_dml);

        } catch (Exception e) {

        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    // 获取文档ids
    private String getAttachmentDocIds() {
        // 实现获取附件ID列表的逻辑（同appendAttachmentData中的逻辑）
        String docids = "";
        try {
            // 获取所有的附件字段存储的文档ids
            ArrayList<String> docidlist = new ArrayList<>();
            Map<String, String> mainData = actionInfo.getMainData();
            Map<Integer, List<Map<String, String>>> detailData = actionInfo.getDetailData();
            // 1. 主表附件处理
            String mainAttachment = Util.null2String(mainData.get("fj"));
            if (StringUtils.isNotBlank(mainAttachment)) {
                docidlist.add(mainAttachment);
            }
            // 2. 明细表附件处理
            List<Map<String, String>> details = detailData.get(1); // 假设第一个明细表
            if (details != null && !details.isEmpty()) {
                for (Map<String, String> map : details) {
                    String detailAttachment = Util.null2String(map.get("fjzd"));
                    if (StringUtils.isNotBlank(detailAttachment)) {
                        docidlist.add(detailAttachment);
                    }
                }
            }

            if (!docidlist.isEmpty()) {
                docids = String.join(",", docidlist);
            }
        } catch (Exception e) {
            log.info("获取文档ids异常 " + SDUtil.getExceptionDetail(e));
        }
        return docids;
    }

    /*
     * 根据文件名获取内容类型
     * @param fileName 文件名
     * @return 内容类型
     */
    private String getContentType(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "txt":
                return "text/plain";
            case "pdf":
                return "application/pdf";
            case "doc":
            case "docx":
                return "application/msword";
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "zip":
                return "application/zip";
            default:
                return "application/octet-stream";
        }
    }

    private String getCurrentISODate() {
        return Instant.now().toString();
    }

    private String formatEmail(String email) {
        if (email == null || email.isEmpty()) {
            return email;
        }

        // 分割邮箱为本地部分和域名部分
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email; // 如果不是标准邮箱格式，直接返回原字符串
        }

        // 处理本地部分（firstName.lastName）
        String localPart = parts[0];
        String[] nameParts = localPart.split("\\.");
        if (nameParts.length != 2) {
            return email; // 如果不是标准格式，直接返回
        }

        // 格式化姓名（首字母大写）
        String formattedFirstName = capitalize(nameParts[0]);
        String formattedLastName = capitalize(nameParts[1]);

        // 重新组合邮箱
        return formattedFirstName + "." + formattedLastName + "@" + parts[1];
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    /*
     * 格式化成本中心为10位，保留字母，不足部分用前导0补齐
     * @param costCenter 原始成本中心
     * @return 格式化后的10位成本中心
     */
    private String formatCostCenter(String costCenter) {
        if (costCenter == null || costCenter.isEmpty()) {
            return "0000000000"; // 默认值
        }

        // 移除空格，保留字母和数字
        String cleaned = costCenter.replaceAll("\\s", "");

        // 如果输入为空，返回默认值
        if (cleaned.isEmpty()) {
            return "0000000000";
        }
        // 如果超过10位，截取后10位
        if (cleaned.length() > 10) {
            cleaned = cleaned.substring(cleaned.length() - 10);
        }
        // 用前导0补齐到10位
        return String.format("%10s", cleaned).replace(' ', '0');
    }

    private void afterExecute() {
        try {
            //清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            //插入二开日志
            if (sdFunctionLog != null) {
                String billid = sdFunctionLog.saveLog(sdFunctionLog, sdLogUtil.getFullLog(), actionError);
                if(StringUtils.isNotBlank(billid)){
                    sdInterfaceCallLog.setEkgnzxjl(Integer.parseInt(billid));
                    sdInterfaceCallLog.saveLog(sdInterfaceCallLog,actionInfo.getUser().getUID());
                    sdUpdateDataLog.setEkgnzxjl(Integer.parseInt(billid));
                    SDUpdateDataLog.saveLog(sdUpdateDataLog,actionInfo.getUser().getUID());
                }
            }

        } catch (Exception e) {
            log.error("后置动作执行异常：", e);
        }
    }


    /**
     * 添加日志
     *
     * @param logMsg
     */
    private void appendLog(String logMsg) {
        log.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

}
