package com.engine.zgfx4.tpw.action.bean;


import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.zgfx4.tpw.util.DocUtil;
import lombok.Data;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;

/**
    * @FileName SDFunctionLog
    * @Description 二开功能执行记录
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/6/11
    */
@Data
public class SDFunctionLog {
    /**
     * 建模表单名
     */
    public static final String TABLE_NAME = "uf_sd_function_log";
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 功能标识
     */
    private String fuc_code;
    /**
     * 功能简述
     */
    private String fuc_desc;
    /**
     * 功能类型
     * 0流程action
     * 1定时任务
     * 2自定义接口
     * 3建模页面扩展自定义接口动作
     * 4其他
     */
    private Integer fuc_type;
    /**
     * 执行人
     */
    private Integer execute_person;
    /**
     * 执行日期
     */
    private String execute_date;
    /**
     * 执行开始时间
     */
    private String execute_begin_time;
    /**
     * 执行结束时间
     */
    private String execute_end_time;
    /**
     * 执行耗时(ms)
     */
    private BigDecimal execute_during;
    /**
     * 执行结果
     * 0成功
     * 1失败
     */
    private Integer execute_result;
    /**
     * 失败信息
     */
    private String error;
    /**
     * 日志文本
     */
    private String log_str;
    /**
     * 日志文件
     */
    private String log_file;
    /**
     * 主代码路径
     */
    private String code_path;
    /**
     * 关联模块
     */
    private String relate_module;
    /**
     * 关联表单
     */
    private String relate_table;
    /**
     * 关联数据id
     */
    private String relate_dataid;
    /**
     * 备注
     */
    private String remark;

    /**
     * 保存日志
     *
     * @param mainLog
     * @param
     * @param logStr
     * @param errorMsg
     */
    public static String saveLog(SDFunctionLog mainLog, String logStr, String errorMsg) throws Exception {
        String billid = "";

            mainLog.setExecute_result(errorMsg.isEmpty() ? 0 : 1); //成功失败
            mainLog.setError(errorMsg); //错误信息

            int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            int secCatId = ModuleDataUtil.getDefaultSecCatId(modid); //获取默认的附件目录
            if (secCatId > 0) {
                //使用实际业务操作人，作为日志文件的创建人
                User executeUser = new User(mainLog.getExecute_person());
                int fileDocId = DocUtil.generateStrFile2Doc(logStr, secCatId, "日志信息.txt", executeUser);
                mainLog.setLog_file(String.valueOf(fileDocId));
            }
            //不超过3500字符
            if (logStr.length() > 3500) {
                logStr = logStr.substring(0, 3500) + "---已截断，完整信息请查看文件";
            }
            mainLog.setLog_str(logStr);
            String endTime = TimeUtil.getCurrentTimeString();
            mainLog.setExecute_end_time(endTime);
            //异步保存主表日志
            ModuleResult mr = ModuleDataUtil.insertObj(mainLog, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), mainLog.getExecute_person());
            if (mr.isSuccess()) {
                //插入日志明细
                billid = Util.null2String(mr.getBillid());
            }

        return billid;
    }

    /**
     * 初始化获取bean对象
     *
     * @param execute_person
     * @return
     */
    public static SDFunctionLog initLog(int execute_person) {
        SDFunctionLog log = new SDFunctionLog();
        log.setExecute_date(TimeUtil.getToday());
        log.setExecute_begin_time(TimeUtil.getCurrentTimeString());
        log.setExecute_person(execute_person);
        return log;
    }

}
