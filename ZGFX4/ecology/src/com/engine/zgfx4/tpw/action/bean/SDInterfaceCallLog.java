package com.engine.zgfx4.tpw.action.bean;

import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.zgfx4.tpw.util.DocUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import weaver.general.TimeUtil;
import weaver.hrm.User;

import java.math.BigDecimal;

/**
    * @FileName SDInterfaceCallLog
    * @Description 接口调用日志
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/6/11
    */
@Data
public class SDInterfaceCallLog {

    public static final String TABLE_NAME = "uf_interface_call_log";

    /**
     * 数据id
     */
    private Integer id;
    /**
     * 二开功能执行记录
     */
    private Integer ekgnzxjl;
    /**
     * 接口名称
     */
    private String 	api_name;
    /**
     * 接口地址
     */
    private String 	api_address;
    /**
     * 接口请求报文
     */
    private String 	api_req_packets;
    /**
     * 	接口响应报文
     */
    private String api_res_packets;
    /**
     * 接口请求文件
     */
    private String api_req_file;
    /**
     * 接口响应文件
     */
    private String 	api_res_file;
    /**
     * 接口请求日期
     */
    private String api_req_date;
    /**
     * 	接口请求时间
     */
    private String api_req_time;
    /**
     * 	接口响应时间
     */
    private String 	api_res_time;
    /**
     * 接口请求耗时
     */
    private BigDecimal api_during;
    /**
     * 	备注
     */
    private String 	remark;
    /**
     * 	接口成功失败
     */
    private Integer api_result;

    /**
     * 初始化获取bean对象
     *
     * @param
     * @return
     */
    public static SDInterfaceCallLog initLog() {
        SDInterfaceCallLog log = new SDInterfaceCallLog();
        log.setApi_req_date(TimeUtil.getToday());
        return log;
    }
    /**
     * 保存日志
     *
     * @param mainLog
     * @param
     * @param
     * @param
     */
    public static String saveLog(SDInterfaceCallLog mainLog,int person) {
        String billid = "";
        int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
        int secCatId = ModuleDataUtil.getDefaultSecCatId(modid); //获取默认的附件目录
        if (secCatId > 0) {
            //使用实际业务操作人，作为日志文件的创建人
            User executeUser = new User(person);
            String apiReqPackets = mainLog.getApi_req_packets();
            String apiResPackets = mainLog.getApi_res_packets();
            if(StringUtils.isNotBlank(apiReqPackets)){
                int fileDocId1 = DocUtil.generateStrFile2Doc(apiReqPackets, secCatId, "请求报文.txt", executeUser);
                mainLog.setApi_req_file(String.valueOf(fileDocId1));
                //不超过3500字符
                if (apiReqPackets.length() > 3500) {
                    apiReqPackets = apiReqPackets.substring(0, 3500) + "---已截断，完整信息请查看文件";
                    mainLog.setApi_req_packets(apiReqPackets);
                }
            }
            if(StringUtils.isNotBlank(apiResPackets)){
                int fileDocId2 = DocUtil.generateStrFile2Doc(apiResPackets, secCatId, "响应报文.txt", executeUser);
                mainLog.setApi_res_file(String.valueOf(fileDocId2));
                //不超过3500字符
                if (apiResPackets.length() > 3500) {
                    apiResPackets = apiResPackets.substring(0, 3500) + "---已截断，完整信息请查看文件";
                    mainLog.setApi_res_packets(apiResPackets);
                }
            }
        }
        //异步保存主表日志
        ModuleResult mr = ModuleDataUtil.insertObj(mainLog, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), person);
        return billid;
    }

}
