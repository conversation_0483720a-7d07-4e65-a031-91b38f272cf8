package com.engine.zgfx4.tpw.action;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zgfx4.tpw.action.bean.*;
import com.engine.zgfx4.tpw.util.DocFileInfo;
import com.engine.zgfx4.tpw.util.DocUtil;
import com.engine.zgfx4.tpw.util.SDLogUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;


/**
 * @FileName GeneratePrTicket
 * @Description 调用ariba的PR接口，生成PR单
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/6/9
 */
@Getter
@Setter
public class GeneratePrTicketActionBak2 extends BaseBean implements Action {

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 二开日志工具类
     */
    private SDLogUtil sdLogUtil;
    /*
     * 接口请求地址
     * */
    private String url;
    /*
     * 接口用户名
     * */
    private String username;
    /*
     * 主表附件字段
     * */
    private String mainAttachmentField;
    /*
     * 明细1附件字段
     * */
    private String detailAttachmentField;
    /*
     * 需要可见的明细附件字段
     * */
    private String needLookFields;
    /*
     * 接口密码
     * */
    private String password;
    /**
     * 请求Request
     */
    private RequestInfo requestInfo;
    /**
     * action信息
     */
    private ActionInfo actionInfo;
    /**
     * action错误信息
     */
    private String actionError;

    private SDFunctionLog sdFunctionLog;

    private SDInterfaceCallLog sdInterfaceCallLog;

    private SDUpdateDataLog sdUpdateDataLog;

    private String docids;
    private String needLookDocids;

    // 生成唯一的边界标识符
    private String boundary;

    private StringBuilder bodyAttachmentBuilder;

    private ArrayList<FileInfo> fileInfos;
    private ArrayList<FileInfo> needFileInfos;

    private Map<String, String> mimeTypeMap;

    private static String getElementText(Document doc, String tagName) {
        NodeList nodes = doc.getElementsByTagNameNS("urn:Ariba:Buyer:vsap", tagName);
        return nodes.getLength() > 0 ?
                nodes.item(0).getTextContent() : null;
    }

    private String xml() {
        String xml = "";
        try {
            Map<String, String> mainData = actionInfo.getMainData();
            Map<Integer, List<Map<String, String>>> detailData = actionInfo.getDetailData();
            List<Map<String, String>> details = detailData.get(1); // 假设第一个明细表
            String email = mainData.get("sqryx");
            if (StringUtils.isNotBlank(email)) {
                email = formatEmail(email);
            }
            StringBuilder fileItemXml = new StringBuilder();
            //附件
            for (FileInfo fileInfo : fileInfos) {
                fileItemXml.append("                        <urn:item>\n" +
                        "                            <urn:Attachment>\n" +
                        "                                <urn:ContentType>" + fileInfo.getFileContentType() + "</urn:ContentType>\n" +
                        "                                <urn:Filename>" + fileInfo.getName() + "</urn:Filename>\n" +
                        "                            </urn:Attachment>\n" +
                        "                            <urn:Date>" + TimeUtil.getCurrentDateString() + "T20:00:00Z</urn:Date>\n" +
                        "                            <urn:ExternalAttachment>false</urn:ExternalAttachment>\n" +
                        "                            <urn:MappedFilename><inc:Include href=\"cid:" + fileInfo.getCid() + "\" xmlns:inc=\"http://www.w3.org/2004/08/xop/include\"/></urn:MappedFilename>\n" +
                        "                            <urn:User>\n" +
                        "                              <urn:PasswordAdapter>PasswordAdapter1</urn:PasswordAdapter>\n" +
                        "                             <urn:UniqueName>" + email + "</urn:UniqueName>\n" +
                        "                            </urn:User>\n" +
                        "                        </urn:item>\n");
            }
            for (FileInfo fileInfo : needFileInfos) {
                fileItemXml.append("                        <urn:item>\n" +
                        "                            <urn:Attachment>\n" +
                        "                                <urn:ContentType>" + fileInfo.getFileContentType() + "</urn:ContentType>\n" +
                        "                                <urn:Filename>" + fileInfo.getName() + "</urn:Filename>\n" +
                        "                            </urn:Attachment>\n" +
                        "                            <urn:Date>" + TimeUtil.getCurrentDateString() + "T20:00:00Z</urn:Date>\n" +
                        "                            <urn:ExternalAttachment>true</urn:ExternalAttachment>\n" +
                        "                            <urn:MappedFilename><inc:Include href=\"cid:" + fileInfo.getCid() + "\" xmlns:inc=\"http://www.w3.org/2004/08/xop/include\"/></urn:MappedFilename>\n" +
                        "                            <urn:User>\n" +
                        "                              <urn:PasswordAdapter>PasswordAdapter1</urn:PasswordAdapter>\n" +
                        "                             <urn:UniqueName>" + email + "</urn:UniqueName>\n" +
                        "                            </urn:User>\n" +
                        "                        </urn:item>\n");
            }
            //buildLineItem
            StringBuilder lineItem = new StringBuilder();
            if (details != null && !details.isEmpty()) {
                for (int i = 0; i < details.size(); i++) {
                    int rowindex = i + 1;
                    Map<String, String> detail = details.get(i);
                    String Quantity = Util.null2String(detail.get("sl"));
                    String ShipToUniqueName = Util.null2String(detail.get("shxx")).isEmpty() ? "6320-002" : Util.null2String(detail.get("shxx"));
                    String Domain = Util.null2String(detail.get("y")).isEmpty() ? "custom" : Util.null2String(detail.get("y"));
                    String UnitOfMeasureUniqueName = Util.null2String(detail.get("jldw")).isEmpty() ? "EA" : Util.null2String(detail.get("jldw"));
                    String ImportedItemCategoryStaging = Util.null2String(detail.get("xmlb")).isEmpty() ? "M" : Util.null2String(detail.get("xmlb"));
                    String ItemCategory = Util.null2String(detail.get("xmlb")).isEmpty() ? "custom" : Util.null2String(detail.get("xmlb"));
                    String PurchaseOrg = Util.null2String(detail.get("cgzz")).isEmpty() ? "6300" : Util.null2String(detail.get("cgzz"));
                    String ImportedAccountTypeStaging = Util.null2String(detail.get("zhlx")).isEmpty() ? "Project" : Util.null2String(detail.get("zhlx"));
                    String ImportedNeedByStaging = Util.null2String(detail.get("qwdhrq")).isEmpty() ? "" : Util.null2String(detail.get("qwdhrq")) + "T20:00:00Z";
                    String Operation = Util.null2String(detail.get("czlx")).isEmpty() ? "New" : Util.null2String(detail.get("czlx"));
                    String ImportedAccountingsStaging = Util.null2String(detail.get("cffs")).isEmpty() ? "_Percentage" : Util.null2String(detail.get("cffs"));
                    String CompanyCode = Util.null2String(detail.get("zzzhszgsdm")).isEmpty() ? "6300" : Util.null2String(detail.get("zzzhszgsdm"));
                    String CostCenterCompanyCode = Util.null2String(detail.get("cbzxszgsdm")).isEmpty() ? "6300" : Util.null2String(detail.get("cbzxszgsdm"));
                    String InternalOrder = Util.null2String(detail.get("nbdd")).isEmpty() ? "X00000000000" : Util.null2String(detail.get("nbdd"));
                    String Percentage = Util.null2String(detail.get("cfbfb")).isEmpty() ? "100" : Util.null2String(detail.get("cfbfb"));
                    String Quantity1 = Util.null2String(detail.get("cfsl")).isEmpty() ? "1" : Util.null2String(detail.get("cfsl"));
                    String BudgetProject = Util.null2String(detail.get("ysxm")).isEmpty() ? "BUDGETTEST" : Util.null2String(detail.get("ysxm"));
                    String IsFixedAssetOrChargeUSA = Util.null2String(detail.get("sfgdzcchargrmgrdxm")).isEmpty() ? "10" : Util.null2String(detail.get("sfgdzcchargrmgrdxm"));
                    String item = "   <urn:item>\n" +
                            "              <urn:Quantity>" + Quantity + "</urn:Quantity>\n" +
                            "              <urn:NumberInCollection>" + rowindex + "</urn:NumberInCollection>\n" +
                            "              <urn:BillingAddress>\n" +
                            "                <urn:UniqueName>" + Util.null2String(detail.get("zdjsf")) + "</urn:UniqueName>\n" +
                            "              </urn:BillingAddress>\n" +
                            "              <urn:ShipTo>\n" +
                            "                <urn:UniqueName>" + ShipToUniqueName + "</urn:UniqueName>\n" +
                            "              </urn:ShipTo>\n" +
                            "              <urn:SupplierLocation>\n" +
                            "                <urn:UniqueName>" + formatCostCenter(Util.null2String(detail.get("gysbh"))) + "</urn:UniqueName>\n" +
                            "              </urn:SupplierLocation>\n" +
                            "              <urn:Description>\n" +
                            "                <urn:CommonCommodityCode>\n" +
                            "                  <urn:Domain>" + Domain + "</urn:Domain>\n" +
                            "                  <urn:UniqueName>" + Util.null2String(detail.get("spdm")) + "</urn:UniqueName>\n" +
                            "                </urn:CommonCommodityCode>\n" +
                            "                <urn:Description>" + Util.null2String(detail.get("wzms")) + "</urn:Description>\n" +
                            "                <urn:Price>\n" +
                            "                  <urn:Amount>" + Util.null2String(detail.get("jg")) + "</urn:Amount>\n" +
                            "                  <urn:Currency>\n" +
                            "                    <urn:UniqueName>" + Util.null2String(detail.get("bzwb")) + "</urn:UniqueName>\n" +
                            "                  </urn:Currency>\n" +
                            "                </urn:Price>\n" +
                            "                <urn:UnitOfMeasure>\n" +
                            "                  <urn:UniqueName>" + UnitOfMeasureUniqueName + "</urn:UniqueName>\n" +
                            "                </urn:UnitOfMeasure>\n" +
                            "                <urn:SupplierPartNumber>" + Util.null2String(detail.get("gysbjh")) + "</urn:SupplierPartNumber>\n" +
                            "              </urn:Description>\n" +
                            "              <urn:ImportedItemCategoryStaging>\n" +
                            "                <urn:UniqueName>" + ImportedItemCategoryStaging + "</urn:UniqueName>\n" +
                            "              </urn:ImportedItemCategoryStaging>\n" +
                            "              <urn:ItemCategory>\n" +
                            "                <urn:UniqueName>" + ItemCategory + "</urn:UniqueName>\n" +
                            "              </urn:ItemCategory>\n" +
                            "              <urn:PurchaseOrg>\n" +
                            "                <urn:UniqueName>" + PurchaseOrg + "</urn:UniqueName>\n" +
                            "              </urn:PurchaseOrg>\n" +
                            "              <urn:Supplier>\n" +
                            "                <urn:UniqueName>" + formatCostCenter(Util.null2String(detail.get("gysbh"))) + "</urn:UniqueName>\n" +
                            "              </urn:Supplier>\n" +
                            "              <urn:TaxCodeCountryStaging>\n" +
                            "                <urn:UniqueName>" + Util.null2String(detail.get("smdq")) + "</urn:UniqueName>\n" +
                            "              </urn:TaxCodeCountryStaging>\n" +
                            "              <urn:ImportedAccountTypeStaging>" + ImportedAccountTypeStaging + "</urn:ImportedAccountTypeStaging>\n" +
                            "              <urn:ImportedDeliverToStaging>" + new User(Integer.parseInt(Util.null2String(mainData.get("sqr")))).getLastname() + "</urn:ImportedDeliverToStaging>\n" +
                            "              <urn:ImportedNeedByStaging>" + ImportedNeedByStaging + "</urn:ImportedNeedByStaging>\n" +
                            "              <urn:ImportedTaxCodeStaging>" + Util.null2String(detail.get("sm")) + "</urn:ImportedTaxCodeStaging>\n" +
                            "              <urn:Operation>" + Operation + "</urn:Operation>\n" +
                            "              <urn:OriginatingSystemLineNumber>" + rowindex + "</urn:OriginatingSystemLineNumber>\n" +
                            "              <urn:ImportedAccountingsStaging>\n" +
                            "                <urn:Type>\n" +
                            "                  <urn:UniqueName>" + ImportedAccountingsStaging + "</urn:UniqueName>\n" +
                            "                </urn:Type>\n" +
                            "                <urn:SplitAccountings>\n" +
                            "                  <urn:item>\n" +
                            "                    <urn:GeneralLedger>\n" +
                            "                      <urn:CompanyCode>\n" +
                            "                        <urn:UniqueName>" + CompanyCode + "</urn:UniqueName>\n" +
                            "                      </urn:CompanyCode>\n" +
                            "                      <urn:UniqueName>" + Util.null2String(detail.get("zzzh")) + "</urn:UniqueName>\n" +
                            "                    </urn:GeneralLedger>\n" +
                            "                    <urn:CostCenter>\n" +
                            "                      <urn:CompanyCode>\n" +
                            "                        <urn:UniqueName>" + CostCenterCompanyCode + "</urn:UniqueName>\n" +
                            "                      </urn:CompanyCode>\n" +
                            "                      <urn:UniqueName>" + formatCostCenter(Util.null2String(detail.get("cbzxdm"))) + "</urn:UniqueName>\n" +
                            "                    </urn:CostCenter>\n" +
                            "                    <urn:InternalOrder>\n" +
                            "                      <urn:UniqueName>" + InternalOrder + "</urn:UniqueName>\n" +
                            "                    </urn:InternalOrder>\n" +
                            "                    <urn:WBSElement>\n" +
                            "                      <urn:UniqueName>" + Util.null2String(detail.get("hcpbh")) + "</urn:UniqueName>\n" +
                            "                    </urn:WBSElement>\n" +
                            "                    <urn:NumberInCollection>" + rowindex + "</urn:NumberInCollection>\n" +
                            "                    <urn:Percentage>" + Percentage + "</urn:Percentage>\n" +
                            "                    <urn:Quantity>" + Quantity1 + "</urn:Quantity>\n" +
                            "                    <urn:custom>\n" +
                            "                      <urn:CustomBudgetProject name=\"BudgetProject\">\n" +
                            "                        <urn:UniqueName>" + BudgetProject + "</urn:UniqueName>\n" +
                            "                      </urn:CustomBudgetProject>\n" +
                            "                      <urn:CustomIsFixedAssetOrChargeUSA name=\"IsFixedAssetOrChargeUSA\">\n" +
                            "                        <urn:UniqueName>" + IsFixedAssetOrChargeUSA + "</urn:UniqueName>\n" +
                            "                      </urn:CustomIsFixedAssetOrChargeUSA>\n" +
                            "                    </urn:custom>\n" +
                            "                  </urn:item>\n" +
                            "                </urn:SplitAccountings>\n" +
                            "              </urn:ImportedAccountingsStaging>\n" +
                            "            </urn:item>";
                    lineItem.append(item);
                }
            }
            String qwdhrq = Util.null2String(mainData.get("qwdhrq")).isEmpty() ? "" : Util.null2String(mainData.get("qwdhrq")) + "T20:00:00Z";
            String PasswordAdapter = Util.null2String(mainData.get("PasswordAdapter")).isEmpty() ? "PasswordAdapter1" : Util.null2String(mainData.get("PasswordAdapter"));
            String sqryx = formatEmail(Util.null2String(mainData.get("sqryx")));
            String gsdm = Util.null2String(mainData.get("gsdm")).isEmpty() ? "6300" : Util.null2String(mainData.get("gsdm"));
            String ImportedHeaderExternalCommentStaging = Util.null2String(mainData.get("ttnzgyssfkjyc")).isEmpty() ? "false" : Util.null2String(mainData.get("ttnzgyssfkjyc"));
            String Operation = Util.null2String(mainData.get("cz")).isEmpty() ? "New" : Util.null2String(mainData.get("cz"));
            String OriginatingSystem = Util.null2String(mainData.get("yxt")).isEmpty() ? "false" : Util.null2String(mainData.get("yxt"));
            String IsContractAttachment = mainData.get("sfxygz").isEmpty() ? "false" : Util.null2String(mainData.get("sfxygz"));
            xml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:Ariba:Buyer:vsap\">\n" +
                    "   <soapenv:Body>\n" +
                    "      <urn:RequisitionImportPullRequest partition=\"?\" variant=\"?\">\n" +
                    "         <urn:Requisition_RequisitionImportPull_Item>\n" +
                    "            <urn:item>\n" +
                    "               <urn:Name>" + Util.null2String(mainData.get("lcbt")) + "</urn:Name>\n" +
                    "               <urn:DefaultLineItem>\n" +
                    "                  <urn:DeliverTo>" + Util.null2String(mainData.get("sqrxm")) + "</urn:DeliverTo>\n" +
                    "                  <urn:NeedBy>" + qwdhrq + "</urn:NeedBy>\n" +
                    "               </urn:DefaultLineItem>\n" +
                    "               <urn:Requester>\n" +
                    "                  <urn:PasswordAdapter>" + PasswordAdapter + "</urn:PasswordAdapter>\n" +
                    "                  <urn:UniqueName>" + sqryx + "</urn:UniqueName>\n" +
                    "               </urn:Requester>\n" +
                    "               <urn:CompanyCode>\n" +
                    "                  <urn:UniqueName>" + gsdm + "</urn:UniqueName>\n" +
                    "               </urn:CompanyCode>\n" +
                    "               <urn:UniqueName>" + Util.null2String(mainData.get("lcbh")) + "</urn:UniqueName>\n" +

                    "               <urn:ImportedHeaderCommentStaging>" + Util.null2String(mainData.get("ttbz")) + "</urn:ImportedHeaderCommentStaging>\n" +
                    "               <urn:ImportedHeaderExternalCommentStaging>" + ImportedHeaderExternalCommentStaging + "</urn:ImportedHeaderExternalCommentStaging>\n" +

                    "               <urn:Operation>" + Operation + "</urn:Operation>\n" +
                    "               <urn:OriginatingSystem>" + OriginatingSystem + "</urn:OriginatingSystem>\n" +
                    "               <urn:OriginatingSystemReferenceID>" + Util.null2String(mainData.get("lcbh")) + "</urn:OriginatingSystemReferenceID>\n" +

                    "               <urn:custom>\n" +
                    "                  <urn:CustomBoolean name=\"IsContractAttachment\">" + IsContractAttachment + "</urn:CustomBoolean>\n" +
                    "               </urn:custom>\n" +

                    "                <urn:Attachments>\n"
                    + fileItemXml +
                    "               </urn:Attachments>\n" +

                    "               <urn:LineItems>\n"
                    + lineItem +
                    "               </urn:LineItems>\n" +
                    "            </urn:item>\n" +
                    "         </urn:Requisition_RequisitionImportPull_Item>\n" +
                    "      </urn:RequisitionImportPullRequest>\n" +
                    "   </soapenv:Body>\n" +
                    "</soapenv:Envelope>";
        } catch (Exception e) {
            actionError = "组装请求参数出错" + SDUtil.getExceptionDetail(e);
            appendLog(actionError);
        }
        return xml;
    }

    public void parseSuccessResponse(String soapResponse, PrResponse result) {
        try {
            // 安全解析配置
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);

            Document doc = factory.newDocumentBuilder()
                    .parse(new ByteArrayInputStream(soapResponse.getBytes(StandardCharsets.UTF_8)));

            // 检查是否成功（非Composing状态）
            String status = getElementText(doc, "StatusString");
            if ("Composing".equalsIgnoreCase(status)) {
                result.setSuccess(false);
                result.setStatus(status);
                result.setPrNumber(getElementText(doc, "UniqueName"));
                return;
            }
            // 提取关键字段
            result.setOriginatingSystem(getElementText(doc, "OriginatingSystem"));
            result.setAmendmentId(getElementText(doc, "OriginatingSystemAmendmentID"));
            result.setReferenceId(getElementText(doc, "OriginatingSystemReferenceID"));
            result.setStatus(status);
            result.setPrNumber(getElementText(doc, "UniqueName"));
            result.setSuccess(true);
        } catch (Exception e) {
            actionError = SDUtil.getExceptionDetail(e);
            appendLog("parseSuccessResponse : " + SDUtil.getExceptionDetail(e));
        }

    }

    @Override
    public String execute(RequestInfo requestInfoParam) {
        //初始化日志打印工具类
        sdLogUtil = new SDLogUtil();
        appendLog("GeneratePrTicketAction 执行 START");
        try {
            requestInfo = requestInfoParam;
            //初始化数据
            initData();
            if (StringUtils.isBlank(actionError)) {
                appendLog("GeneratePrTicketAction执行主方法executeMy()开始");
                executeMy();
                appendLog("GeneratePrTicketAction执行主方法executeMy()结束");
            }
        } catch (Exception e) {
            actionError = "GeneratePrTicketAction catch exception：" + e.getMessage();
            log.info("GeneratePrTicketAction catch exception：" + e.getMessage());
        } finally {
            appendLog("---END---requestid:" + actionInfo.getRequestId());
            //后置执行操作
            afterExecute();
        }
        appendLog("GeneratePrTicketAction 执行 END");
        return ActionUtil.handleResult("", requestInfo);
    }

    private void executeMy() {
        try {
            PrResponse prResponse = sendMtomSoapMessage();
            appendLog("sendMtomSoapMessage()方法的响应对象：PrResponse" + JSONObject.toJSONString(prResponse));
            int status = 0;
            if (prResponse.isSuccess()) {
                status = 0;
            } else {
                status = 1;
            }
            writeBackWFData(prResponse, status);
        } catch (Exception e) {
            actionError = "调用ariba的PR接口，生成PR单Action失败!";
        }
    }

    private PrResponse sendMtomSoapMessage() {
        // 1. 初始化SOAP连接
        PrResponse prResponse = new PrResponse();
        try {
            sdInterfaceCallLog.setApi_req_date(TimeUtil.getCurrentDateString());
            buildAttachments();
            String soapMessageAsString = xml();

            String encodedAuthString = Base64.getEncoder().encodeToString((username + ":" + password).getBytes(StandardCharsets.UTF_8));
            //构建请求体
            StringBuilder bodyBuilder = new StringBuilder();
            //请求体-xml部分
            bodyBuilder.append("--").append(boundary).append("\r\n");
            bodyBuilder.append("Content-Type: application/xop+xml; charset=UTF-8; type=\"text/xml\"\r\n");
            bodyBuilder.append("Content-Transfer-Encoding: 8bit\r\n");
            bodyBuilder.append("Content-ID: <<EMAIL>>\r\n\r\n");
            bodyBuilder.append(soapMessageAsString).append("\r\n");
            String bodyAttachmentStr = bodyAttachmentBuilder.toString();
            if (StringUtils.isNotBlank(bodyAttachmentStr)) {
                bodyBuilder.append(bodyAttachmentBuilder.toString()).append("\r\n");
            }
            appendLog("构建完成的SOAP请求消息：" + bodyBuilder);
            String contentType = "multipart/related; type=\"application/xop+xml\"; start=\"<<EMAIL>>\"; start-info=\"text/xml\"; boundary=\"" + boundary + "\"";
            HttpRequest request = HttpUtil.createPost(url)
                    .header("Authorization", "Basic " + encodedAuthString)
                    .body(bodyBuilder.toString(), contentType);
            sdInterfaceCallLog.setApi_req_packets(bodyBuilder.toString());
            sdInterfaceCallLog.setApi_req_time(TimeUtil.getCurrentTimeString());
            appendLog("发送请求并获取响应");
            HttpResponse response = request.execute();
            sdInterfaceCallLog.setApi_res_time(TimeUtil.getCurrentTimeString());
            if (response != null) {
                String body = response.body();
                appendLog("完整SOAP响应:" + body);
                if (200 == response.getStatus()) {
                    // 4. 处理响应
                    parseSuccessResponse(body, prResponse);
                }
                sdInterfaceCallLog.setApi_res_packets(body);
            }


            if (prResponse.isSuccess()) {
                sdInterfaceCallLog.setApi_result(0);
            } else {
                sdInterfaceCallLog.setApi_result(1);
            }
        } catch (Exception e) {
            actionError = "sendMtomSoapMessage Exception:" + SDUtil.getExceptionDetail(e);
            sdInterfaceCallLog.setApi_result(1);
            sdInterfaceCallLog.setApi_res_packets("sendMtomSoapMessage Exception:" + SDUtil.getExceptionDetail(e));
            appendLog("sendMtomSoapMessage Exception:" + SDUtil.getExceptionDetail(e));
        }
        return prResponse;
    }

    /**
     * 将SOAPMessage转换为格式化字符串
     */
    private String getSoapMessageAsString(SOAPMessage message) {
        try {
            // 创建输出流
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            // 写入输出流
            message.writeTo(out);
            // 格式化XML（可选）
            String rawXml = out.toString(StandardCharsets.UTF_8.name());
            return formatXml(rawXml);
        } catch (Exception e) {
            log.warn("无法转换SOAP消息为字符串", e);
            return "[Error: " + e.getMessage() + "]";
        }
    }

    /**
     * 格式化XML字符串
     */
    private String formatXml(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            Document doc = factory.newDocumentBuilder()
                    .parse(new InputSource(new StringReader(xml)));

            TransformerFactory tf = TransformerFactory.newInstance();
            tf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.toString();
        } catch (Exception e) {
            return xml; // 返回原始XML如果格式化失败
        }
    }


    private void buildAttachments() throws SOAPException {
        if (StringUtils.isNotBlank(docids)) {
            List<DocFileInfo> listDocFileInfo = DocUtil.getDocFileInfoByDocIds(docids);
            appendLog("docids listDocFileInfo" + JSONObject.toJSONString(listDocFileInfo));
            int count  = 1;
            for (DocFileInfo docFileInfo : listDocFileInfo) {
                String fileName = docFileInfo.getFileName();
                int fileId = Util.getIntValue(docFileInfo.getFileid());
                try (InputStream in = DocUtil.getFileInputStream(fileId)) {
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.setName(fileName);
                    String contentType = mimeTypeMap.get(fileName.substring(fileName.lastIndexOf('.') + 1));
                    appendLog(fileName + "使用mimeTypeMap获取到的contentType：" + contentType);
                    fileInfo.setFileContentType(contentType);
                    String cid = count+"_"+Base64.getEncoder().encodeToString(fileName.getBytes());
                    fileInfo.setCid(cid);
                    fileInfos.add(fileInfo);
                    // 5. 附件内容生成（保持原逻辑）
                    bodyAttachmentBuilder.append("--").append(boundary).append("\r\n")
                            .append("Content-Type: ").append(contentType)
                            .append(";name=").append(fileName).append("\r\n")
                            .append("Content-Transfer-Encoding: base64\r\n")
                            .append("Content-ID: <").append(cid).append(">\r\n\r\n") // 直接使用文件名
                            .append(Base64.getEncoder().encodeToString(IoUtil.read(in).toByteArray()))
                            .append("\r\n--").append(boundary).append("--\r\n");
                    count++;
                } catch (Exception e) {
                    actionError = "文件处理异常 " + SDUtil.getExceptionDetail(e);
                    appendLog(actionError);
                }
            }
        }
        if (StringUtils.isNotBlank(needLookDocids)) {
            List<DocFileInfo> listDocFileInfo = DocUtil.getDocFileInfoByDocIds(needLookDocids);
            appendLog("needLookDocids listDocFileInfo" + JSONObject.toJSONString(listDocFileInfo));
            int count  = 1;
            for (DocFileInfo docFileInfo : listDocFileInfo) {
                String fileName = docFileInfo.getFileName();
                int fileId = Util.getIntValue(docFileInfo.getFileid());
                try (InputStream in = DocUtil.getFileInputStream(fileId)) {
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.setName(fileName);

                    String contentType = mimeTypeMap.get(fileName.substring(fileName.lastIndexOf('.') + 1));
                    appendLog(fileName + "使用mimeTypeMap获取到的contentType：" + contentType);

                    fileInfo.setFileContentType(contentType);
                    String cid = "needLook"+count+"_"+Base64.getEncoder().encodeToString(fileName.getBytes());
                    fileInfo.setCid(cid);
                    needFileInfos.add(fileInfo);
                    // 5. 附件内容生成（保持原逻辑）
                    bodyAttachmentBuilder.append("--").append(boundary).append("\r\n")
                            .append("Content-Type: ").append(contentType)
                            .append(";name=").append(fileName).append("\r\n")
                            .append("Content-Transfer-Encoding: base64\r\n")
                            .append("Content-ID: <").append(cid).append(">\r\n\r\n") // 直接使用文件名
                            .append(Base64.getEncoder().encodeToString(IoUtil.read(in).toByteArray()))
                            .append("\r\n--").append(boundary).append("--\r\n");
                    count++;
                } catch (Exception e) {
                    actionError = "文件处理异常 " + SDUtil.getExceptionDetail(e);
                    appendLog(actionError);
                }
            }
        }
    }

    private void initData() {
        try {
            actionError = "";
            fileInfos = new ArrayList<>();
            needFileInfos = new ArrayList<>();
            mimeTypeMap = new HashMap<>();
            actionInfo = ActionUtil.getInfo(requestInfo);
            docids = getAttachmentDocIds();
            getMimeTypeMaps();
            appendLog("mimeTypeMap: " + JSONObject.toJSONString(mimeTypeMap));
            bodyAttachmentBuilder = new StringBuilder();
            boundary = UUID.randomUUID().toString().replaceAll("-", "");
            appendLog("---初始化initData-START" + actionInfo.getRequestId());
            appendLog("---此次流程的requestid:" + actionInfo.getRequestId());
            appendLog("---接口请求地址:" + url);
            appendLog("---接口用户名:" + username);
            appendLog("---接口密码:" + password);
            //初始化日志文件
            //二开功能执行记录日志
            sdFunctionLog = SDFunctionLog.initLog(actionInfo.getUser().getUID());
            sdFunctionLog.setFuc_code(this.getClass().getSimpleName());// 输出当前类名: UpdateMainManagerAction
            sdFunctionLog.setFuc_type(0);
            sdFunctionLog.setCode_path(this.getClass().getName());// 输出当前类路径名: com.engine.dfmzht4.gyl.workflow.contractTransfer.UpdateMainManagerAction
            sdFunctionLog.setFuc_desc("调用ariba的PR接口生成PR单");
            sdFunctionLog.setRelate_module("流程");
            sdFunctionLog.setRelate_table(actionInfo.getFormtableName());
            sdFunctionLog.setRelate_dataid("requestId:" + actionInfo.getRequestId());
            //接口调用日志
            sdInterfaceCallLog = SDInterfaceCallLog.initLog();
            //更新数据日志
            sdUpdateDataLog = SDUpdateDataLog.initLog();
            appendLog("---初始化END");
        } catch (Exception e) {
            actionError = "action初始化步骤异常:" + SDUtil.getExceptionDetail(e);
            appendLog("action初始化步骤异常" + SDUtil.getExceptionDetail(e));
        }
    }

    private void getMimeTypeMaps() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from uf_mtwjlx_dt1");
            while (recordSet.next()) {
                mimeTypeMap.put(recordSet.getString("wjhz"), recordSet.getString("wjmimetype"));
            }
        } catch (Exception e) {
            appendLog("getMimeTypeMaps Exception:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void writeBackWFData(PrResponse prResponse, int apiStatus) {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            //更新前的流程数据
            String queryBeforeSql = "select * from " + actionInfo.getFormtableName() + " where requestid= " + actionInfo.getRequestId();
            recordSet.executeQuery(queryBeforeSql);
            List<Map<String, Object>> dataBeforeList = QueryUtil.getMapList(recordSet);

            String status = prResponse.getStatus();
            String referenceId = prResponse.getReferenceId();
            String prNumber = prResponse.getPrNumber();

            String setUpdate_dml = "UPDATE " + actionInfo.getFormtableName() + " set aribacgsqzt = '" + status + "' ,aribacgsqdh =  '" + prNumber + "' ,apiStatus = '" + apiStatus + "' where requestid = " + actionInfo.getRequestId();
            recordSet.executeUpdate(setUpdate_dml);
            sdUpdateDataLog.setUpdate_begin_time(TimeUtil.getCurrentTimeString());
            if (recordSet.executeUpdate(setUpdate_dml)) {
                sdUpdateDataLog.setUpdate_result(0);
            } else {
                sdUpdateDataLog.setUpdate_result(1);
                sdUpdateDataLog.setError(recordSet.getExceptionMsg());
            }
            sdUpdateDataLog.setUpdate_end_time(TimeUtil.getCurrentTimeString());
            String queryAfterSql = "select * from " + actionInfo.getFormtableName() + " where requestid= " + actionInfo.getRequestId();
            recordSet.executeQuery(queryAfterSql);
            List<Map<String, Object>> dataAfterList = QueryUtil.getMapList(recordSet);
            sdUpdateDataLog.setUpdate_table(actionInfo.getFormtableName());
            sdUpdateDataLog.setUpdate_modid(0);
            sdUpdateDataLog.setUpdate_dataid(actionInfo.getRequestId());
            sdUpdateDataLog.setUpdate_fields("aribacgsqzt,aribacgsqdh,apiStatus");
            sdUpdateDataLog.setBefore_data(JSONObject.toJSONString(dataBeforeList));
            sdUpdateDataLog.setAfter_data(JSONObject.toJSONString(dataAfterList));
            sdUpdateDataLog.setUpdate_dml(setUpdate_dml);
            sdUpdateDataLog.setUpdate_desc("本次更新是执行");
            sdUpdateDataLog.setUpdate_date(TimeUtil.getToday());
            sdUpdateDataLog.saveLog(sdUpdateDataLog, 1);
        } catch (Exception e) {

        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    // 获取文档ids
    private String getAttachmentDocIds() {
        StringBuilder docIdsBuilder = new StringBuilder();
        try {
            List<String> docIdList = new ArrayList<>();
            Map<String, String> mainData = actionInfo.getMainData();
            Map<Integer, List<Map<String, String>>> detailData = actionInfo.getDetailData();

            // 1. 处理主表附件
            if (StringUtils.isNotBlank(mainAttachmentField)) {
                String[] mainAttachmentFields = mainAttachmentField.split(",");
                for (String field : mainAttachmentFields) {
                    String attachmentId = Util.null2String(mainData.get(field));
                    if (StringUtils.isNotBlank(attachmentId)) {
                        docIdList.add(attachmentId);
                    }
                }
            }

            // 2. 处理明细表附件
            if (StringUtils.isNotBlank(detailAttachmentField) && detailData != null) {
                List<Map<String, String>> details = detailData.get(1); // 假设第一个明细表
                if (details != null) {
                    String[] detailAttachmentFields = detailAttachmentField.split(",");
                    for (String field : detailAttachmentFields) {
                        for (Map<String, String> row : details) {
                            String attachmentId = Util.null2String(row.get(field));
                            if (StringUtils.isNotBlank(attachmentId)) {
                                docIdList.add(attachmentId);
                            }
                        }
                    }
                }
            }
            ArrayList<String> needLooks = new ArrayList<>();
            if (StringUtils.isNotBlank(needLookFields) && detailData != null) {
                List<Map<String, String>> details = detailData.get(1); // 假设第一个明细表
                if (details != null) {
                    String[] needLookFieldList = needLookFields.split(",");
                    for (String field : needLookFieldList) {
                        for (Map<String, String> row : details) {
                            String attachmentId = Util.null2String(row.get(field));
                            if (StringUtils.isNotBlank(attachmentId)) {
                                needLooks.add(attachmentId);
                            }
                        }
                    }
                }
            }
            needLookDocids = String.join(",", needLooks);
            appendLog("needLookDocids:" + needLookDocids);
            // 拼接结果
            if (!docIdList.isEmpty()) {
                docIdsBuilder.append(String.join(",", docIdList));
            }
        } catch (Exception e) {
            log.error("获取文档ID异常: " + SDUtil.getExceptionDetail(e), e);
        }
        return docIdsBuilder.toString();
    }


    private String getCurrentISODate() {
        return Instant.now().toString();
    }

    private String formatEmail(String email) {
        if (email == null || email.isEmpty()) {
            return email;
        }

        // 分割邮箱为本地部分和域名部分
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email; // 如果不是标准邮箱格式，直接返回原字符串
        }

        // 处理本地部分（firstName.lastName）
        String localPart = parts[0];
        String[] nameParts = localPart.split("\\.");
        if (nameParts.length != 2) {
            return email; // 如果不是标准格式，直接返回
        }

        // 格式化姓名（首字母大写）
        String formattedFirstName = capitalize(nameParts[0]);
        String formattedLastName = capitalize(nameParts[1]);

        // 重新组合邮箱
        return formattedFirstName + "." + formattedLastName + "@" + parts[1];
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    /*
     * 格式化成本中心为10位，保留字母，不足部分用前导0补齐
     * @param costCenter 原始成本中心
     * @return 格式化后的10位成本中心
     */
    private String formatCostCenter(String costCenter) {
        if (costCenter == null || costCenter.isEmpty()) {
            return "0000000000"; // 默认值
        }

        // 移除空格，保留字母和数字
        String cleaned = costCenter.replaceAll("\\s", "");

        // 如果输入为空，返回默认值
        if (cleaned.isEmpty()) {
            return "0000000000";
        }
        // 如果超过10位，截取后10位
        if (cleaned.length() > 10) {
            cleaned = cleaned.substring(cleaned.length() - 10);
        }
        // 用前导0补齐到10位
        return String.format("%10s", cleaned).replace(' ', '0');
    }

    private void afterExecute() {
        try {
            //清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            //插入二开日志
            if (sdFunctionLog != null) {
                String billid = SDFunctionLog.saveLog(sdFunctionLog, sdLogUtil.getFullLog(), actionError);
                if (StringUtils.isNotBlank(billid)) {
                    sdInterfaceCallLog.setEkgnzxjl(Integer.parseInt(billid));
                    SDInterfaceCallLog.saveLog(sdInterfaceCallLog, actionInfo.getUser().getUID());
                    sdUpdateDataLog.setEkgnzxjl(Integer.parseInt(billid));
                    SDUpdateDataLog.saveLog(sdUpdateDataLog, actionInfo.getUser().getUID());
                }
            }

        } catch (Exception e) {
            log.info("后置动作执行异常：" + SDUtil.getExceptionDetail(e));
        }
    }


    /**
     * 添加日志
     *
     * @param logMsg
     */
    private void appendLog(String logMsg) {
        log.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

}
