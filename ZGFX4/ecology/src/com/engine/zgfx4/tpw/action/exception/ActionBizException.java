package com.engine.zgfx4.tpw.action.exception;

/**
 * 异常: 普通业务类异常, 仅需要 message
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/23
 */
public class ActionBizException extends RuntimeException {
    public ActionBizException() {
        super();
    }

    public ActionBizException(String message) {
        super(message);
    }

    public ActionBizException(String message, Throwable cause) {
        super(message, cause);
    }

    public ActionBizException(Throwable cause) {
        super(cause);
    }
}
