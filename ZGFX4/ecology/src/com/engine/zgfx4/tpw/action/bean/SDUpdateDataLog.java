package com.engine.zgfx4.tpw.action.bean;

import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Data;
import weaver.general.TimeUtil;
import weaver.general.Util;

/**
    * @FileName SDUpdateDataLog
    * @Description 更新数据日志
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/6/11
    */
@Data
public class SDUpdateDataLog {
    public static final String TABLE_NAME = "uf_update_data_log";
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 二开功能执行记录
     */
    private Integer ekgnzxjl;
    /**
     * 更新的表单
     */
    private String update_table;
    /**
     * 更新的表单建模id
     */
    private Integer update_modid;
    /**
     * 更新的数据id
     */
    private String update_dataid;
    /**
     * 更新的字段s
     */
    private String update_fields;
    /**
     * 更新前数据
     */
    private String before_data;
    /**
     * 更新后数据
     */
    private String after_data;
    /**
     * 更新sql
     */
    private String update_dml;
    /**
     * 更新结果
     * 0成功
     * 1失败
     */
    private Integer update_result;
    /**
     * 更新说明
     */
    private String update_desc;
    /**
     * 失败信息
     */
    private String error;
        /**
     * 	更新日期
     */
    private String update_date;  /**
     * 	更新开始时间
     */
    private String update_begin_time;
/**
     * 更新结束时间
     */
    private String update_end_time;
    /**
     * 备注
     */
    private String remark;

    /**
     * 初始化获取bean对象
     *
     * @param
     * @return
     */
    public static SDUpdateDataLog initLog() {
        SDUpdateDataLog log = new SDUpdateDataLog();
        log.setUpdate_date(TimeUtil.getToday());
        log.setUpdate_end_time(TimeUtil.getCurrentTimeString());
        return log;
    }
    public static String saveLog(SDUpdateDataLog mainLog,int person) {
        String errMsg = "";
        //异步保存主表日志
        ModuleResult mr = ModuleDataUtil.insertObj(mainLog, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), person);
        if (mr.isSuccess()) {
            //插入日志明细
            errMsg = Util.null2String(mr.getBillid());
        }
        return errMsg;
    }
}
