package com.engine.zgfx4.tpw.web.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.zgfx4.tpw.web.service.SapSupplierDataWriteService;
import com.engine.zgfx4.tpw.web.service.impl.SapSupplierDataWriteServiceImpl;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

public class SapSupplierDataWriteWeb extends BaseBean {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private SapSupplierDataWriteService getService(User user) {
        return ServiceUtil.getService(SapSupplierDataWriteServiceImpl.class, user);
    }

    @POST
    @Path("/save")
    @Produces({MediaType.APPLICATION_JSON})
    public String supplierDataWrite(@Context HttpServletRequest request, @Context HttpServletResponse response,Map<String, Object> body) {
        Map<String, Object> result;
        //获取当前用户
        User user = new User(1);
        Map<String, Object> params = ApiUtil.request2Map(request);
        String appid = Util.null2String(request.getHeader("appid"));
        String token = Util.null2String(request.getHeader("token"));
        String userid = Util.null2String(request.getHeader("userid"));
        String ContentType = Util.null2String(request.getHeader("Content-Type"));
        String afterExecute = Util.null2String(request.getHeader("afterExecute"));
        log.info("SapSupplierDataWriteWeb appid:" + appid);
        log.info("SapSupplierDataWriteWeb token:" + token);
        log.info("SapSupplierDataWriteWeb userid:" + userid);
        log.info("SapSupplierDataWriteWeb Content-Type:" + ContentType);
        params.put("body", body);
        params.put("afterExecute", afterExecute);
        result = getService(user).supplierDataWrite(params, user);
        return JSONObject.toJSONString(result);
    }
}