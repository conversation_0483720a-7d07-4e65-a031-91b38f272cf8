package com.engine.zgfx4.tpw.web.bean;

import com.engine.parent.doc.DocUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
/**
    * @FileName SapSupplierDataWriteBeanLog
    * @Description SAP调用OA的接口（即本需求开发的接口）写入供应商的相关数据日志
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/6/20
    */

@Data
public class SapSupplierDataWriteBeanLog {
    public static final String TABLE_NAME = "uf_sap_callApi_log";
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 接口名称
     */
    private String 	api_name;
    /**
     * 接口地址
     */
    private String 	api_address;
    /**
     * 接口请求报文
     */
    private String 	api_req_packets;
    /**
     * 	接口响应报文
     */
    private String api_res_packets;
    /**
     * 接口请求文件
     */
    private String api_req_file;
    /**
     * 接口响应文件
     */
    private String 	api_res_file;
    /**
     * 接口执行日志
     */
    private String api_log_data;
    /**
     * 接口执行日志文件
     */
    private String 	api_log_file;
    /**
     * 接口请求日期
     */
    private String api_req_date;
    /**
     * 	接口请求时间
     */
    private String api_req_time;
    /**
     * 	接口响应时间
     */
    private String 	api_res_time;
    /**
     * 	备注
     */
    private String 	remark;
    /**
     * 	接口请求耗时
     */
    private String api_during;
    /**
     * 	接口成功失败
     */
    private Integer api_result;

    /**
     * 初始化获取bean对象
     *
     * @param
     * @return
     */
    public static SapSupplierDataWriteBeanLog initLog() {
        SapSupplierDataWriteBeanLog log = new SapSupplierDataWriteBeanLog();
        log.setApi_req_date(TimeUtil.getToday());
        log.setApi_req_time(TimeUtil.getCurrentTimeString());
        log.setApi_name("SAP供应商数据写入OA供应商台账");
        log.setRemark("系统管理员为日志创建者");
        log.setApi_address("/api/sd/mod/vendor/save");

        return log;
    }
    /**
     * 保存日志
     *
     * @param mainLog
     * @param
     * @param
     * @param
     */
    public String saveLog(SapSupplierDataWriteBeanLog mainLog,String fullMsg, int person) {
        String billid = "";
        try{
            int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            int secCatId = ModuleDataUtil.getDefaultSecCatId(modid); //获取默认的附件目录
            if (secCatId > 0) {
                //使用实际业务操作人，作为日志文件的创建人
                User executeUser = new User(person);
                String apiReqPackets = mainLog.getApi_req_packets();
                String apiResPackets = mainLog.getApi_res_packets();
                int fileDocId1 = DocUtil.generateStrFile2Doc(Util.null2String(apiReqPackets), secCatId, "请求报文.txt", executeUser);
                int fileDocId2 = DocUtil.generateStrFile2Doc(Util.null2String(apiResPackets), secCatId, "响应报文.txt", executeUser);
                int fileDocId3 = DocUtil.generateStrFile2Doc(Util.null2String(fullMsg), secCatId, "接口执行日志.txt", executeUser);
                mainLog.setApi_req_file(String.valueOf(fileDocId1));
                mainLog.setApi_res_file(String.valueOf(fileDocId2));
                mainLog.setApi_log_file(String.valueOf(fileDocId3));
                //不超过3500字符
                if ( StringUtils.isNotBlank(apiReqPackets) &&  apiReqPackets.length() > 3500) {
                    apiReqPackets = apiReqPackets.substring(0, 3500) + "---已截断，完整信息请查看文件";
                    mainLog.setApi_req_packets(apiReqPackets);
                }
                //不超过3500字符
                if (StringUtils.isNotBlank(apiResPackets) &&  apiResPackets.length() > 3500) {
                    apiResPackets = apiResPackets.substring(0, 3500) + "---已截断，完整信息请查看文件";
                    mainLog.setApi_res_packets(apiResPackets);
                }
                //不超过3500字符
                if (StringUtils.isNotBlank(fullMsg) && fullMsg.length() > 3500) {
                    fullMsg = fullMsg.substring(0, 3500) + "---已截断，完整信息请查看文件";
                    mainLog.setApi_log_data(fullMsg);
                }else {
                    mainLog.setApi_log_data(fullMsg);
                }
            }
            //异步保存主表日志
            ModuleResult mr = ModuleDataUtil.insertObj(mainLog, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), person);
            if (mr.isSuccess()) {
                //插入日志明细
                billid = Util.null2String(mr.getBillid());
            }
        }catch (Exception e) {
//            log.info("SapSupplierDataWriteBeanLog saveLog Exception "+ SDUtil.getExceptionDetail(e));
        }
        return billid;
    }

}
