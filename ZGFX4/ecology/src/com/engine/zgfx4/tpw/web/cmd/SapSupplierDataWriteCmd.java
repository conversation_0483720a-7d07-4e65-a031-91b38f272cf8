package com.engine.zgfx4.tpw.web.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.dto.ModuleUpdateBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zgfx4.tpw.util.SDLogUtil;
import com.engine.zgfx4.tpw.web.bean.SapSupplierDataWriteBeanLog;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.GCONST;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

/**
 * @FileName SapSupplierDataWriteCmd
 * @Description SAP供应商数据写入接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/6/19
 */
public class SapSupplierDataWriteCmd extends AbstractCommonCommand<Map<String, Object>> {

    private static final String MAIN_FORM = "uf_gystz";
    private static final String DETAIL_FORM = "uf_gystz_dt1";
    private static final String FIELD_DEF_TABLE = "uf_gyszdxx_dt1";
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    //二开日志工具类
    private SDLogUtil sdLogUtil;
    private JSONArray data;
    private ArrayList<String> mainFormFields;
    private ArrayList<String> mainBTFormFields;
    private ArrayList<String> detailFormFields;
    private ArrayList<String> detailBTFormFields;
    private String errorMsg;
    private String afterExecute;
    private SapSupplierDataWriteBeanLog sapSupplierDataWriteBeanLog;

    public SapSupplierDataWriteCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        try {
            sapSupplierDataWriteBeanLog = SapSupplierDataWriteBeanLog.initLog();
            _resetParams();
            appendLog("SAP供应商数据写入接口---START");
            appendLog("请求params--" + JSONObject.toJSONString(this.params));
            sapSupplierDataWriteBeanLog.setApi_req_packets("" + JSONObject.toJSONString(this.params));
            //初始化数据
            _initData();
            if (StringUtils.isNotBlank(errorMsg)) {
                result.put("code", "FALL");
                result.put("msg", "系统错误，初始化数据出现异常" + errorMsg);
                result.put("faildata", new ArrayList<>());
                result.put("successdata", new ArrayList<>());
                appendLog("系统错误，初始化数据出现异常" + errorMsg);
                sapSupplierDataWriteBeanLog.setApi_res_packets("系统错误，初始化数据出现异常" + errorMsg);
                sapSupplierDataWriteBeanLog.setApi_res_time(TimeUtil.getCurrentTimeString());
                sapSupplierDataWriteBeanLog.setApi_result(0);
                return result;
            } else {
                //开始执行接口主逻辑
                result = _process();
            }
            sapSupplierDataWriteBeanLog.setApi_res_packets("" + JSONObject.toJSONString(result));
            sapSupplierDataWriteBeanLog.setApi_res_time(TimeUtil.getCurrentTimeString());



        } catch (Exception e) {
            sapSupplierDataWriteBeanLog.setApi_result(1);
            sapSupplierDataWriteBeanLog.setApi_res_packets(e.getMessage());
            appendLog("：" + SDUtil.getExceptionDetail(e));
        } finally {
            long endTime = System.currentTimeMillis();
            long durationMillis = endTime - startTime;
            double durationSeconds = durationMillis / 1000.0;
            sapSupplierDataWriteBeanLog.setApi_during(durationSeconds +"s");
            DBUtil.clearThreadLocalRecordSet();
//            if(StringUtils.isNotBlank(afterExecute)){
                afterExecute();
//            }
        }
        appendLog("result:" + result);
        appendLog("SAP供应商数据写入接口---END");
        return result;
    }

    private void afterExecute() {
        try {
            log.info("afterExecute开始");
            int modid = ModuleDataUtil.getModuleIdByName("uf_sap_callApi_log");
            int secCatId = ModuleDataUtil.getDefaultSecCatId(modid); //获取默认的附件目录
            if (secCatId > 0) {
                log.info("afterExecute secCatId：" + secCatId);
                //使用实际业务操作人，作为日志文件的创建人
                String fullLog = sdLogUtil.getFullLog();
                User executeUser = new User(1);
                String apiReqPackets = sapSupplierDataWriteBeanLog.getApi_req_packets();
                String apiResPackets = sapSupplierDataWriteBeanLog.getApi_res_packets();

                if(StringUtils.isNotBlank(apiReqPackets)){
                    int fileDocId1 = generateStrFile2Doc(Util.null2String(apiReqPackets), secCatId, "请求报文.txt", executeUser);
                    log.info("afterExecute fileDocId1 ："+fileDocId1);
                    sapSupplierDataWriteBeanLog.setApi_req_file(String.valueOf(fileDocId1));
                    //不超过3500字符
                    if (apiReqPackets.length() > 3500) {
                        apiReqPackets = apiReqPackets.substring(0, 3500) + "---已截断，完整信息请查看文件";
                        sapSupplierDataWriteBeanLog.setApi_req_packets(apiReqPackets);
                    }
                }
                if(StringUtils.isNotBlank(apiResPackets)){
                    int fileDocId2 = generateStrFile2Doc(Util.null2String(apiResPackets), secCatId, "响应报文.txt", executeUser);
                    log.info("afterExecute fileDocId2 ："+fileDocId2);
                    sapSupplierDataWriteBeanLog.setApi_res_file(String.valueOf(fileDocId2));
                    //不超过3500字符
                    if (apiResPackets.length() > 3500) {
                        apiResPackets = apiResPackets.substring(0, 3500) + "---已截断，完整信息请查看文件";
                        sapSupplierDataWriteBeanLog.setApi_res_packets(apiResPackets);
                    }
                }
                if(StringUtils.isNotBlank(fullLog)){
                    int fileDocId3 = generateStrFile2Doc(Util.null2String(fullLog), secCatId, "接口执行日志.txt", executeUser);
                    log.info("afterExecute fileDocId3 ："+fileDocId3);
                    sapSupplierDataWriteBeanLog.setApi_log_file(String.valueOf(fileDocId3));
                    //不超过3500字符
                    if (fullLog.length() > 3500) {
                        fullLog = fullLog.substring(0, 3500) + "---已截断，完整信息请查看文件";
                        sapSupplierDataWriteBeanLog.setApi_log_data(fullLog);
                    } else {
                        sapSupplierDataWriteBeanLog.setApi_log_data(fullLog);
                    }
                }
            }
            log.info("afterExecute sapSupplierDataWriteBeanLog ：" + JSONObject.toJSONString(sapSupplierDataWriteBeanLog));
            //异步保存主表日志
            ModuleResult mr = ModuleDataUtil.insertObj(sapSupplierDataWriteBeanLog, "uf_sap_callApi_log", ModuleDataUtil.getModuleIdByName("uf_sap_callApi_log"), 1);
            log.info("ModuleResult mr :" + JSONObject.toJSONString(mr));
            log.info("afterExecute结束");
        } catch (Exception e) {
            log.info("后置动作执行异常：", e);
        }
    }
    public int generateStrFile2Doc( String str, int fileSecCategoryId,  String fileName,  User docUser) {

        int docId = -1;
        try {
            String rootFilePath = GCONST.getRootPath() + "sdfile" + File.separatorChar + "tempfile";
            String filePath = rootFilePath + File.separatorChar + fileName;
            File parentDir = new File(rootFilePath);
            if (!parentDir.exists()) {
                log.info("rootFilePath目录:" + rootFilePath + "不存在，开始创建目录");
                boolean mkFlag = parentDir.mkdirs();
                if (!mkFlag) {
                    log.info("rootFilePath目录:" + rootFilePath + "创建目录失败");
                }
            }
            log.info("rootFilePath目录:" + parentDir);
            //将文本写入文件
            if (com.engine.sd2.file.util.FileUtil.writeStrToFile(str, filePath)) {
                log.info("转为文件输入流");
                //
                //使用 try-with-resources ，可自动关闭资源
                try (FileInputStream inputStream = new FileInputStream(filePath)) {
                    log.info("将输入流程上传至OA文档");
                    //将输入流程上传至OA文档
                    docId = DocUtil.createDocWithFile(inputStream, docUser, fileName, fileSecCategoryId);
                    log.info("将输入流程上传至OA文档 docId"+docId);
                } catch (IOException e) {
                    log.info(fileName + "转文件流出错：", e);
                } finally {
                    // 删除临时文件
                    com.engine.sd2.file.util.FileUtil.deleteFile(filePath);
                }
            }
        } catch (Exception e) {
            log.info("generateStrFile2Doc Exception" + SDUtil.getExceptionDetail(e));
        }

        return docId;

    }

    private Map<String, Object> _process() {
        Map<String, Object> result = new HashMap<>();
        try {
            appendLog("执行主逻辑开始");

            //插入明细的数据必须添加mainid字段
            ArrayList<String> detailFormFieldsWithMainId = new ArrayList<>(detailFormFields);
            detailFormFieldsWithMainId.add("mainid");
            appendLog("插入明细的数据必须添加mainid字段 detailFormFieldsWithMainId:" + detailFormFieldsWithMainId);

            //失败的数据
            JSONArray fieldLosings = new JSONArray();

            Set<String> successData = new LinkedHashSet<>();

            for (int i = 0; i < data.size(); i++) {
                //建模台账
                JSONObject formData = (JSONObject) data.get(i);
                //接口主表信息
                JSONObject mainData = (JSONObject) formData.get("mainData");
                //接口明细表信息
                JSONArray detailData = (JSONArray) formData.get("detailData");
                //检查检查主表&明细表必填字段是否有值
                JSONObject checkResult = checkMainFields(mainData, detailData);
                appendLog("主表&明细表必填字段缺值信息：" + JSONObject.toJSONString(checkResult));
                if (checkResult != null) {
                    fieldLosings.add(checkResult);
                    continue;
                }
                //判断新增还是更新
                //建模主表信息
                List<Map<String, Object>> mainLists = determineMainAddOrUpdate(mainData);
                if (mainLists.isEmpty()) {
                    //主表信息，主表和明细表数据都是新增
                    //拼接要插入到uf_jhdrmb中的数据对象
                    ModuleInsertBean moduleInsertBean = new ModuleInsertBean();
                    List<Object> values = createInsertMainValues(mainData);
                    moduleInsertBean.setTableName(MAIN_FORM);
                    moduleInsertBean.setCreatorId(1);
                    moduleInsertBean.setFields(mainFormFields);
                    moduleInsertBean.setValue(values);
                    moduleInsertBean.setModuleId(ModuleDataUtil.getModuleIdByName(MAIN_FORM));
                    //数据插入
                    ModuleResult insert = ModuleDataUtil.insertByOne(moduleInsertBean);
                    if (insert.isSuccess()) {
                        if (detailData.isEmpty()) {
                            successData.add(Util.null2String(mainData.get("gysbm")));
                            continue;
                        }
                        int billid = insert.getBillid();
                        appendLog("插入主表数据 billid :" + billid);
                        //组装插入bean
                        moduleInsertBean = new ModuleInsertBean();
                        //明细数据
                        List<List<Object>> listValues = createDetailValues(detailData, billid, detailFormFields);
                        //组装插入的数据
                        moduleInsertBean.setTableName(DETAIL_FORM)
                                .setFields(detailFormFieldsWithMainId)
                                .setValues(listValues);
                        appendLog("插入明细数据 detail moduleInsertBean:" + moduleInsertBean);
                        ModuleResult mr = ModuleDataUtil.insertDetail(moduleInsertBean);
                        if (!mr.isSuccess()) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("code", Util.null2String(mainData.get("gysbm")));
                            jsonObject.put("msg", "明细数据插入失败");
                            fieldLosings.add(jsonObject);
                        } else {
                            successData.add(Util.null2String(mainData.get("gysbm")));
                        }
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("code", Util.null2String(mainData.get("gysbm")));
                        jsonObject.put("msg", "主表数据插入失败");
                        fieldLosings.add(jsonObject);
                    }
                } else {
                    Map<String, Object> main = mainLists.get(0);
                    String mainid = Util.null2String(main.get("id"));
                    //主表更新
                    //根据接口传输的主表数据对其更新
                    List<List<Object>> values = createUpdateValues(mainData, mainid);
                    //执行同步方法
                    ModuleUpdateBean mb = new ModuleUpdateBean();
                    mb.setFields(mainFormFields);
                    mb.setTableName(MAIN_FORM);
                    mb.setValues(values);
                    mb.setUpdater(1);
                    //同步更新批量数据
                    appendLog("同步更新主表数据" + JSONObject.toJSONString(mb));
                    ModuleResult mr = ModuleDataUtil.update(mb);
                    if (mr.isSuccess()) {
                        if (detailData.isEmpty()) {
                            successData.add(Util.null2String(mainData.get("gysbm")));
                            continue;
                        }
                        List<List<Object>> insertListValues = new ArrayList<>();
                        List<List<Object>> updateListValues = new ArrayList<>();
                        List<List<Object>> delListValues = new ArrayList<>();
                        //判断明细数据
                        HashMap<String, String> savedDetailMap = determineDetailAddOrUpdate(detailData, mainid);
                        appendLog("determineDetailAddOrUpdate savedDetailMap" + JSONObject.toJSONString(savedDetailMap));
                        for (int k = 0; k < detailData.size(); k++) {
                            JSONObject detail = (JSONObject) detailData.get(k);
                            if (savedDetailMap.containsKey(detail.getString("yxzhhm"))) {
                                if ("1".equals(Util.null2String(detail.getString("sfyx")))) {
                                    //将要删除的明细数据（逻辑删除）
                                    ArrayList<Object> list = new ArrayList<>();
                                    list.add("1");
                                    list.add(Util.null2String(savedDetailMap.get(detail.getString("yxzhhm"))));
                                    delListValues.add(list);
                                } else {
                                    //要更新的明细数据
                                    ArrayList<Object> list = new ArrayList<>();
                                    for (int m = 0; m < detailFormFields.size(); m++) {
                                        list.add(Util.null2String(detail.getString(detailFormFields.get(m))));
                                    }
                                    list.add(Util.null2String(savedDetailMap.get(detail.getString("yxzhhm"))));
                                    updateListValues.add(list);
                                }
                            } else {
                                //插入的明细数据
                                ArrayList<Object> list = new ArrayList<>();
                                for (int m = 0; m < detailFormFields.size(); m++) {
                                    list.add(Util.null2String(detail.getString(detailFormFields.get(m))));
                                }
                                list.add(mainid);
                                insertListValues.add(list);
                            }
                        }
                        if (!insertListValues.isEmpty()) {
                            //组装插入bean
                            ModuleInsertBean mb1 = new ModuleInsertBean();
                            //明细数据
                            //组装插入的数据
                            mb1.setTableName(DETAIL_FORM)
                                    .setFields(detailFormFieldsWithMainId)
                                    .setValues(insertListValues);
                            log.info("插入明细数据 detail mb1:" + mb1);
                            ModuleResult mr1 = ModuleDataUtil.insertDetail(mb1);
                            if (mr1.isSuccess()) {
                                successData.add(Util.null2String(mainData.get("gysbm")));
                            } else {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("code", Util.null2String(mainData.get("gysbm")));
                                jsonObject.put("msg", "主表数据插入失败");
                                fieldLosings.add(jsonObject);
                            }
                        }
                        if (!updateListValues.isEmpty()) {
                            //组装更新明细的数据
                            ModuleUpdateBean moduleUpdateBean = new ModuleUpdateBean();
                            moduleUpdateBean.setTableName(DETAIL_FORM)
                                    .setFields(detailFormFields)
                                    .setValues(updateListValues);
                            log.info("更新明细数据 detail mb:" + moduleUpdateBean);
                            ModuleResult moduleResult = ModuleDataUtil.update(moduleUpdateBean);
                            if (moduleResult.isSuccess()) {
                                successData.add(Util.null2String(mainData.get("gysbm")));
                            } else {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("code", Util.null2String(mainData.get("gysbm")));
                                jsonObject.put("msg", "明细数据更新失败");
                                fieldLosings.add(jsonObject);
                            }
                        }
                        if (!delListValues.isEmpty()) {
                            ArrayList<String> delFields = new ArrayList<>();
                            delFields.add("sfyx");
                            //组装删除明细的数据
                            ModuleUpdateBean moduleUpdateBean = new ModuleUpdateBean();
                            moduleUpdateBean.setTableName(DETAIL_FORM)
                                    .setFields(delFields)
                                    .setValues(delListValues);
                            log.info("删除明细数据 detail mb:" + moduleUpdateBean);
                            ModuleResult moduleResult = ModuleDataUtil.update(moduleUpdateBean);
                            if (moduleResult.isSuccess()) {
                                successData.add(Util.null2String(mainData.get("gysbm")));
                            } else {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("code", Util.null2String(mainData.get("gysbm")));
                                jsonObject.put("msg", "明细数据删除失败");
                                fieldLosings.add(jsonObject);
                            }
                        }
                    }
                }
            }

            if (fieldLosings.isEmpty()) {
                result.put("code", "SUCCESS");
                result.put("msg", "");
            } else {
                result.put("code", "FAIL");
                result.put("msg", "处理有误！");
            }
            result.put("successdata", String.join(",", successData));
            result.put("faildata", JSONArray.toJSONString(fieldLosings));
            if (!fieldLosings.isEmpty()) {
                sapSupplierDataWriteBeanLog.setApi_result(1);
            } else {
                sapSupplierDataWriteBeanLog.setApi_result(0);
            }
            appendLog("执行主逻辑结束");
        } catch (Exception e) {
            appendLog("执行主逻辑过程中出现异常" + SDUtil.getExceptionDetail(e));
        }

        return result;
    }

    private HashMap<String, String> determineDetailAddOrUpdate(JSONArray detailData, String mainid) {

        ArrayList<String> strings = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            for (int i = 0; i < detailData.size(); i++) {
                JSONObject detail = (JSONObject) detailData.get(i);
                strings.add("'" + detail.getString("yxzhhm") + "'");
            }
            String sql = "select id,yxzhhm from " + DETAIL_FORM + " where mainid = '" + mainid + "' and yxzhhm in ( " + String.join(",", strings) + ")";
            appendLog("determineDetailAddOrUpdate sql :" + sql);
            recordSet.executeQuery(sql);
            while (recordSet.next()) {
                map.put(Util.null2String(recordSet.getString("yxzhhm")), recordSet.getString("id"));
            }
        } catch (Exception e) {
            DBUtil.clearThreadLocalRecordSet();
        }
        return map;
    }


    private List<List<Object>> createUpdateValues(JSONObject mainData, String mainid) {
        List<List<Object>> listValues = new ArrayList<>();
        try {
            ArrayList<Object> list = new ArrayList<>();
            for (int j = 0; j < mainFormFields.size(); j++) {
                list.add(Util.null2String(mainData.getString(mainFormFields.get(j))));
            }
            list.add(mainid);
            listValues.add(list);
        } catch (Exception e) {

        }
        return listValues;
    }

    private List<List<Object>> createDetailValues(JSONArray detailData, int billid, ArrayList<String> detailFormFieldsWithMainId) {
        List<List<Object>> listValues = new ArrayList<>();
        try {
            for (int i = 0; i < detailData.size(); i++) {
                ArrayList<Object> list = new ArrayList<>();
                JSONObject detail = (JSONObject) detailData.get(i);
                for (int j = 0; j < detailFormFieldsWithMainId.size(); j++) {
                    list.add(Util.null2String(detail.get(detailFormFieldsWithMainId.get(j))));
                }
                list.add(String.valueOf(billid));
                listValues.add(list);
            }
        } catch (Exception e) {

        }
        return listValues;
    }

    private List<Object> createInsertMainValues(JSONObject mainData) {
        ArrayList<Object> value = new ArrayList<>();
        for (int i = 0; i < mainFormFields.size(); i++) {
            value.add(Util.null2String(mainData.get(mainFormFields.get(i))));
        }
        return value;
    }

    private List<Map<String, Object>> determineMainAddOrUpdate(JSONObject mainData) {
        List<Map<String, Object>> dataBeforeList = new ArrayList<>();
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from " + MAIN_FORM + " where gysbm = '" + Util.null2String(mainData.get("gysbm")) + "'");
            if (recordSet.next()) {
                dataBeforeList = QueryUtil.getMapList(recordSet);
            }
        } catch (Exception e) {
            appendLog("determineMainAddOrUpdate 异常" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return dataBeforeList;
    }

    private JSONObject checkMainFields(JSONObject mainData, JSONArray detailData) {
        JSONObject jsonObject = null;
        try {
            ArrayList<String> mainMissingFields = new ArrayList<>();
            for (int i = 0; i < mainBTFormFields.size(); i++) {
                String field = mainBTFormFields.get(i);
                if (StringUtils.isBlank(Util.null2String(mainData.get(field)))) {
                    mainMissingFields.add(field);
                }
            }

            ArrayList<String> detailMissingFields = new ArrayList<>();
            for (int i = 0; i < detailData.size(); i++) {
                JSONObject detail = (JSONObject) detailData.get(i);
                for (int j = 0; j < detailBTFormFields.size(); j++) {
                    String field = detailBTFormFields.get(j);
                    if (StringUtils.isBlank(Util.null2String(detail.get(field)))) {
                        detailMissingFields.add(field);
                    }
                }
                if (!detailMissingFields.isEmpty()) {
                    break;
                }
            }
            // 优化后的必填字段校验逻辑
            if (!mainMissingFields.isEmpty() || !detailMissingFields.isEmpty()) {
                JSONObject errorResult = new JSONObject();

                // 构建错误消息（使用StringBuilder优化字符串拼接）
                StringBuilder errorMsg = new StringBuilder();
                if (!mainMissingFields.isEmpty()) {
                    errorMsg.append("主表缺失必填字段[")
                            .append(String.join(",", mainMissingFields))
                            .append("]");
                }
                if (!detailMissingFields.isEmpty()) {
                    if (errorMsg.length() > 0) errorMsg.append("；");
                    errorMsg.append("明细缺失必填字段[")
                            .append(String.join(",", detailMissingFields))
                            .append("]");
                }
                // 设置错误响应
                errorResult.put("code", Util.null2String(mainData.get("gysbm"))); // 供应商编码
                errorResult.put("msg", errorMsg.toString());
                return errorResult;
            }
        } catch (Exception e) {
            appendLog("checkMainFields:" + SDUtil.getExceptionDetail(e));
        }
        return jsonObject;
    }


    private void _resetParams() {
        sdLogUtil = new SDLogUtil();
        data = new JSONArray();
        mainFormFields = new ArrayList<>();
        mainBTFormFields = new ArrayList<>();
        detailFormFields = new ArrayList<>();
        detailBTFormFields = new ArrayList<>();
        errorMsg = "";
    }

    private void _initData() {
        appendLog(this.getClass().getName() + "_initData ---START");
        try {
            if (params != null && !params.isEmpty()) {
                afterExecute = Util.null2String(params.get("afterExecute"));
                if (StringUtils.isNotBlank(Util.null2String(params.get("body")))) {
                    Map<String, Object> body = (Map<String, Object>) params.get("body");
                    if (body != null && !body.isEmpty()) {
                        if (StringUtils.isNotBlank(Util.null2String(body.get("data")))) {
                            data = JSONArray.parseArray(Util.null2String(body.get("data")));
                            appendLog("将要同步的供应商数据" + JSONObject.toJSONString(data));
                            if (data.size() > 1000) {
                                errorMsg = "供应商数据，一次接口最多可传1000条数据";
                            } else {
                                //获取主表更新字段
                                getMainFormFields();
                                //获取明细更新字段
                                getDetailFormFields();
                            }
                        } else {
                            errorMsg = "请求参数data为空";
                        }
                    }
                } else {
                    errorMsg = "请求参数body为空";
                }
            } else {
                errorMsg = "请求参数为空";
            }

            appendLog(this.getClass().getName() + "_initData ---END");
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            appendLog(errorMsg);
        }
    }

    private void getDetailFormFields() {
        try {
            appendLog(this.getClass().getName() + "-getdetailBTFormFields ---START");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from " + FIELD_DEF_TABLE + " where zdwz = 1");
            while (recordSet.next()) {
                String zdm = Util.null2String(recordSet.getString("zdm"));
                detailFormFields.add(zdm);
                if ("0".equals(recordSet.getString("bt"))) {
                    detailBTFormFields.add(zdm);
                }
            }
            appendLog("detailFormFields " + JSONObject.toJSONString(detailFormFields));
            appendLog("detailBTFormFields " + JSONObject.toJSONString(detailBTFormFields));
            appendLog("getdetailBTFormFields ---END");
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            appendLog("getdetailBTFormFields Exception:" + SDUtil.getExceptionDetail(e));
        }
    }

    private void getMainFormFields() {
        try {
            appendLog(this.getClass().getName() + "-getmainBTFormFields ---START");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from " + FIELD_DEF_TABLE + " where zdwz = 0 ");
            while (recordSet.next()) {
                String zdm = Util.null2String(recordSet.getString("zdm"));
                mainFormFields.add(zdm);
                if ("0".equals(recordSet.getString("bt"))) {
                    mainBTFormFields.add(zdm);
                }
            }
            appendLog("mainFormFields " + JSONObject.toJSONString(mainFormFields));
            appendLog("mainBTFormFields " + JSONObject.toJSONString(mainBTFormFields));
            appendLog("getmainBTFormFields ---END");
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            appendLog("getmainBTFormFields Exception:" + SDUtil.getExceptionDetail(e));
        }
    }

    /**
     * 添加日志
     *
     * @param logMsg
     */
    private void appendLog(String logMsg) {
        log.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }
}

