package com.engine.zgfx4.tpw.web.service.impl;

import com.engine.core.impl.Service;
import com.engine.zgfx4.tpw.web.cmd.SapSupplierDataWriteCmd;
import com.engine.zgfx4.tpw.web.service.SapSupplierDataWriteService;
import weaver.hrm.User;

import java.util.Map;

public class SapSupplierDataWriteServiceImpl extends Service implements SapSupplierDataWriteService {
    @Override
    public Map<String, Object> supplierDataWrite(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SapSupplierDataWriteCmd(params, user));
    }
}
