package com.engine.zgfx4.tpw.job;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.dto.ModuleUpdateBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zgfx4.tpw.job.bean.SyncCsvFileLog;
import com.engine.zgfx4.tpw.util.SDLogUtil;
import com.engine.zgfx4.tpw.util.SDUtil;
import com.opencsv.CSVReader;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;


/**
 * @FileName SyncBudgetItemsJob
 * @Description 项目预算.csv 写入建模台账
 * <AUTHOR>
 * @Version v1.01
 * @Date 2025/6/24
 */
@Getter
@Setter
public class SyncBudgetItemsJob extends BaseCronJob {
    //数据库表名
    private static final String TABLE_NAME = "uf_zgfxysxmtz";

    //插入的字段数据库名称
    private static final String[] FIELDS = {"UniqueName", "Name", "CompanyCode", "PurchasingUnit", "cus_BudgetProjectComments1", "cus_BudgetProjectComments2"};
    // 常量定义
    private static final String LOCAL_DIR = new File(GCONST.getRootPath()).getParent() + File.separator + "Order" + File.separator;
    private static final String ECOLOGY_LOCAL_DIR = GCONST.getRootPath() + "sdtmp" + File.separatorChar + "nbdd" + File.separatorChar;
    private static final String BUDGET_ITEMS_FILE = "预算项目.csv";

    // 文件路径
    private static final String LOCAL_FILE_PATH = LOCAL_DIR + BUDGET_ITEMS_FILE;
    private static final String ECOLOGY_LOCAL_FILE_PATH = ECOLOGY_LOCAL_DIR + BUDGET_ITEMS_FILE;

    private final BaseBean bb = new BaseBean();

    private SDLogUtil sdLogUtil;

    private SyncCsvFileLog syncCsvFileLog;
    // 使用ConcurrentHashMap提高并发性能
    private Map<String, String> oaXmysMap;

    private String errMsg;

    @Override
    public void execute() {
        long start = System.currentTimeMillis();
        try {
            resetParams();
            appendLog("预算项目.csv写入建模台账开始");

            init();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }

            // 1. 创建目录（如果不存在）
            createDirectoryIfNotExists();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }

            // 2. 从本地下载文件
            downloadFiles();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }

            // 3. 处理CSV文件
            Map<String, Object> baseMap = processCsvOrdersFile(ECOLOGY_LOCAL_FILE_PATH);
            appendLog("预算项目.csv数据 baseMap : " + JSONObject.toJSONString(baseMap));

            if (baseMap.isEmpty() || StringUtils.isNotBlank(errMsg)) {
                return;
            }
            // 4. 逻辑处理
            processData(baseMap);
            appendLog("预算项目.csv写入建模台账结束");
        } catch (Exception e) {
            errMsg = "执行过程中发生异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {

            long end = System.currentTimeMillis();
            long durationMillis = end - start;
            double durationSeconds = durationMillis / 1000.0;
            syncCsvFileLog.setSync_during(durationSeconds + "s");
            afterExecute();

        }
    }

    private void afterExecute() {
        try {
            appendLog("SyncBudgetItemsJob afterExecute start");
            int modid = ModuleDataUtil.getModuleIdByName(SyncCsvFileLog.TABLE_NAME);
            int secCatId = ModuleDataUtil.getDefaultSecCatId(modid);
            ArrayList<Integer> docIds = new ArrayList<>();
            try (FileInputStream inputStream = new FileInputStream(LOCAL_FILE_PATH)) {
                //将输入流程上传至OA文档
                int docWithFile = DocUtil.createDocWithFile(inputStream, new User(1), BUDGET_ITEMS_FILE, secCatId);
                appendLog("SyncBudgetItemsJob afterExecute docWithFile: +" + docWithFile);
                syncCsvFileLog.setCsv_files(String.valueOf(docWithFile));
            } catch (IOException e) {
                appendLog("转文件流出错：" + SDUtil.getExceptionDetail(e));
            }
            appendLog("SyncBudgetItemsJob afterExecute 断点1");
            String fullLog = sdLogUtil.getFullLog();
            int fileDocId = com.engine.zgfx4.tpw.util.DocUtil.generateStrFile2Doc(fullLog, secCatId, "执行日志.txt", new User(1));
            appendLog("SyncBudgetItemsJob afterExecute fileDocId ：" + fileDocId);
            syncCsvFileLog.setSync_log_file(String.valueOf(fileDocId));
            if (fullLog.length() > 3500) {
                fullLog = fullLog.substring(0, 3500) + "---已截断，完整信息请查看文件";
                syncCsvFileLog.setSync_log_data(fullLog);
            }
            appendLog("SyncBudgetItemsJob syncCsvFileLog" + JSONObject.toJSONString(syncCsvFileLog));
            ModuleResult mr = ModuleDataUtil.insertObj(syncCsvFileLog, SyncCsvFileLog.TABLE_NAME, ModuleDataUtil.getModuleIdByName(SyncCsvFileLog.TABLE_NAME), 1);
            appendLog("执行项目预算.csv 写入建模台账日志结果" + JSONObject.toJSONString(mr));
            appendLog("SyncBudgetItemsJob afterExecute end");
        } catch (Exception e) {
            appendLog("执行项目预算.csv 写入建模台账日志异常 " + SDUtil.getExceptionDetail(e));
        }
    }

    private void init() {
        try {
            appendLog("初始化开始");
            syncCsvFileLog = SyncCsvFileLog.initLog("预算项目.csv写入项目预算台账");
            syncCsvFileLog.setCsv_file_name(BUDGET_ITEMS_FILE);
            //获取系统中已经存在的预算项目
            getOABudgetItems();
            appendLog("初始化结束");
        } catch (Exception e) {
            errMsg = "初始化异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }

    private void getOABudgetItems() {
        appendLog("获取预算项目开始");
        RecordSet recordSet = null;
        try {
            recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("SELECT id,UniqueName FROM " + TABLE_NAME);
            while (recordSet.next()) {
                String id = recordSet.getString("id");
                String UniqueName = recordSet.getString("UniqueName");
                if (StringUtils.isNotBlank(UniqueName)) {
                    oaXmysMap.put(UniqueName, id);
                }
            }
            appendLog("OA中预算项目信息: " + JSONObject.toJSONString(oaXmysMap));
            appendLog("获取预算项目结束");
        } catch (Exception e) {
            errMsg = "获取预算项目异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }


    private void resetParams() {
        sdLogUtil = new SDLogUtil();
        oaXmysMap = new HashMap<>();
        errMsg = "";
    }

    private void processData(Map<String, Object> baseMap) {
        try {
            appendLog("开始处理数据");
            List<List<Object>> insertListValues = new ArrayList<>();
            List<List<Object>> updateListValues = new ArrayList<>();
            ArrayList<ModuleResult> errList = new ArrayList<>();
            // 遍历 baseMap 中的所有条目
            for (Map.Entry<String, Object> entry : baseMap.entrySet()) {
                String key = entry.getKey();
                JSONObject JsonObj = (JSONObject) entry.getValue();
                String UniqueName = Util.null2String(JsonObj.get("UniqueName"));
                String Name = Util.null2String(JsonObj.get("Name"));
                String CompanyCode = Util.null2String(JsonObj.get("CompanyCode"));
                String PurchasingUnit = Util.null2String(JsonObj.get("PurchasingUnit"));
                String cus_BudgetProjectComments1 = Util.null2String(JsonObj.get("cus_BudgetProjectComments1"));
                String cus_BudgetProjectComments2 = Util.null2String(JsonObj.get("cus_BudgetProjectComments2"));
                ArrayList<Object> list = new ArrayList<>();
                list.add(UniqueName);
                list.add(Name);
                list.add(CompanyCode);
                list.add(PurchasingUnit);
                list.add(cus_BudgetProjectComments1);
                list.add(cus_BudgetProjectComments2);
                if (oaXmysMap.containsKey(key)) {
                    String id = Util.null2String(oaXmysMap.get(key));
                    list.add(id);
                    updateListValues.add(list);
                } else {
                    insertListValues.add(list);
                }
            }
            if (!insertListValues.isEmpty()) {
                //组装插入bean
                ModuleInsertBean mb = new ModuleInsertBean();
                //明细数据
                //组装插入的数据
                mb.setTableName(TABLE_NAME)
                        .setModuleId(ModuleDataUtil.getModuleIdByName(TABLE_NAME))
                        .setCreatorId(1)
                        .setFields(Arrays.asList(FIELDS))
                        .setValues(insertListValues);

                ModuleResult mr = ModuleDataUtil.insertByOne(mb);
                if (!mr.isSuccess()) {
                    errList.add(mr);
                }
            }
            if (!updateListValues.isEmpty()) {
                //组装插入bean
                ModuleUpdateBean mb = new ModuleUpdateBean();
                //明细数据
                //组装更新的数据
                mb.setTableName(TABLE_NAME)
                        .setUpdater(1)
                        .setFields(Arrays.asList(FIELDS))
                        .setValues(updateListValues);

                ModuleResult mr = ModuleDataUtil.update(mb);
                if (!mr.isSuccess()) {
                    errList.add(mr);
                }
            }
            syncCsvFileLog.setSync_end_time(TimeUtil.getCurrentTimeString());
            syncCsvFileLog.setSync_add_size(insertListValues.size());
            syncCsvFileLog.setSync_update_size(updateListValues.size());
            syncCsvFileLog.setSync_err_info(JSONObject.toJSONString(errList));
            syncCsvFileLog.setSync_result(errList.isEmpty() ? 0 : 1);
            appendLog("数据处理完成");
        } catch (Exception e) {
            syncCsvFileLog.setSync_result(1);
            errMsg = "数据处理异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }


    private Map<String, Object> processCsvOrdersFile(String filePath) {
        Map<String, Object> dataMap = new HashMap<>();
        try (InputStreamReader reader = new InputStreamReader(
                Files.newInputStream(Paths.get(filePath)), StandardCharsets.UTF_8)) {

            // 使用OpenCSV解析器
            CSVReader csvReader = new CSVReader(reader);

            // 读取标题行
            String[] firstRow = csvReader.readNext();
            appendLog("firstRow "+Arrays.toString(firstRow));

            String[] headers = csvReader.readNext();
            appendLog("读取标题行 "+Arrays.toString(headers));

            // 获取字段索引（不区分大小写）
            int index_UniqueName = -1, index_Name = -1, index_CompanyCode = -1;
            int index_PurchasingUnit = -1, index_Comments1 = -1, index_Comments2 = -1;

            for (int i = 0; i < headers.length; i++) {
                String header = headers[i].trim();
                if (header.equalsIgnoreCase("UniqueName")) index_UniqueName = i;
                else if (header.equalsIgnoreCase("Name")) index_Name = i;
                else if (header.equalsIgnoreCase("CompanyCode")) index_CompanyCode = i;
                else if (header.equalsIgnoreCase("PurchasingUnit")) index_PurchasingUnit = i;
                else if (header.equalsIgnoreCase("cus_BudgetProjectComments1")) index_Comments1 = i;
                else if (header.equalsIgnoreCase("cus_BudgetProjectComments2")) index_Comments2 = i;
            }

            // 检查必要字段是否存在
            if (index_UniqueName == -1 || index_Name == -1) {
                throw new IOException("CSV缺少必要字段(UniqueName或Name)");
            }

            // 读取数据行
            String[] nextLine;
            while ((nextLine = csvReader.readNext()) != null) {
                JSONObject jsonObject = new JSONObject();

                // 处理每列数据（防止数组越界）
                String uniqueName = getValueSafely(nextLine, index_UniqueName);
                String name = getValueSafely(nextLine, index_Name);
                String companyCode = getValueSafely(nextLine, index_CompanyCode);
                String purchasingUnit = getValueSafely(nextLine, index_PurchasingUnit);
                String comments1 = getValueSafely(nextLine, index_Comments1);
                String comments2 = getValueSafely(nextLine, index_Comments2);

                // 填充JSON对象
                jsonObject.put("UniqueName", StringUtils.trimToEmpty(uniqueName));
                jsonObject.put("Name", StringUtils.trimToEmpty(name));
                jsonObject.put("CompanyCode", StringUtils.trimToEmpty(companyCode));
                jsonObject.put("PurchasingUnit", StringUtils.trimToEmpty(purchasingUnit));
                jsonObject.put("cus_BudgetProjectComments1", StringUtils.trimToEmpty(comments1));
                jsonObject.put("cus_BudgetProjectComments2", StringUtils.trimToEmpty(comments2));

                // 以UniqueName为key存入Map
                if (StringUtils.isNotBlank(uniqueName)) {
                    dataMap.put(uniqueName.trim(), jsonObject);
                }
            }
        } catch (Exception e) {
            errMsg = "处理CSV文件[" + filePath + "]异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
        return dataMap;
    }

    // 安全获取数组元素的方法
    private String getValueSafely(String[] array, int index) {
        if (array == null || index < 0 || index >= array.length) {
            return "";
        }
        return array[index] != null ? array[index] : "";
    }

    private void createDirectoryIfNotExists() {
        try {
            File dir = new File(ECOLOGY_LOCAL_DIR);

            if (!dir.exists() && !dir.mkdirs()) {
                errMsg = "目录创建失败: " + ECOLOGY_LOCAL_DIR;
                appendLog(errMsg);
                return;
            }

            // 清理旧文件
            cleanOldFiles(dir);
        } catch (Exception e) {
            errMsg = "目录处理异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }

    private void cleanOldFiles(File dir) {
        File[] oldFiles = dir.listFiles((d, name) ->
                name.equals(BUDGET_ITEMS_FILE));

        if (oldFiles != null) {
            for (File file : oldFiles) {
                if (!file.delete()) {
                    appendLog("文件删除失败: " + file.getName());
                }
            }
        }
    }

    private void downloadFiles() {
        downloadFile(LOCAL_FILE_PATH, ECOLOGY_LOCAL_FILE_PATH);
    }

    private void downloadFile(String sourcePath, String targetPath) {
        try {
            appendLog("开始复制文件: " + sourcePath);
            Files.copy(Paths.get(sourcePath), Paths.get(targetPath));
            appendLog("文件复制完成，保存到: " + targetPath);
        } catch (Exception e) {
            errMsg = "文件操作异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }

    private void appendLog(String logMsg) {
        bb.writeLog(logMsg);
        sdLogUtil.appendLog(logMsg);
    }
}