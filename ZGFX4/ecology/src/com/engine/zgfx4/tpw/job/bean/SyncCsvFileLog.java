package com.engine.zgfx4.tpw.job.bean;

import lombok.Data;
import weaver.general.TimeUtil;
/**
    * @FileName SyncCsvFileLog
    * @Description csv写入建模台账日志
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/6/26
    */
@Data
public class SyncCsvFileLog {
    public static final String TABLE_NAME = "uf_sync_csvfile_Log";

    /**
     * 数据id
     */
    private Integer id;
    /**
     * 同步功能名
     */
    private String 	name;
    /**
     *同步 csv同步文件名
     */
    private String 	csv_file_name;
    /**
     * 同步csv同步文件
     */
    private String 	csv_files;
    /**
     * 同步日期
     */
    private String sync_date;
    /**
     * 同步开始时间
     */
    private String sync_begin_time;
    /**
     * 同步结束时间
     */
    private String sync_end_time;
    /**
     * 同步执行日志
     */
    private String sync_log_data;
    /**
     * 执行日志文件
     */
    private String sync_log_file;

    /**
     * 	同步请求耗时
     */
    private String sync_during;
    /**
     * 	接口成功失败
     */
    private Integer sync_result;
    /**
     * 	新增数量
     */
    private Integer sync_add_size;
    /**
     * 	更新数量
     */
    private Integer sync_update_size;
    /**
     * 	同步失败信息
     */
    private String sync_err_info;
    /**
     * 	备注
     */
    private String 	remark;

    public static SyncCsvFileLog initLog(String name) {
        SyncCsvFileLog log = new SyncCsvFileLog();
        log.setName(name);
        log.setRemark(name);
        log.setSync_date(TimeUtil.getCurrentDateString());
        log.setSync_begin_time(TimeUtil.getCurrentTimeString());
        return log;
    }

}
