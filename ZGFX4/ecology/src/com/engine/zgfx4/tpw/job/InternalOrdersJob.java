package com.engine.zgfx4.tpw.job;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.dto.ModuleUpdateBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zgfx4.tpw.job.bean.SyncCsvFileLog;
import com.engine.zgfx4.tpw.util.SDLogUtil;
import com.engine.zgfx4.tpw.util.SDUtil;
import com.opencsv.CSVReader;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
/**
 * @FileName InternalOrdersJob
 * @Description 内部订单.csv及内部订单和成本中心关系.csv 写入建模台账
 * <AUTHOR>
 * @Version v1.01
 * @Date 2025/6/24
 */
@Getter
@Setter
public class InternalOrdersJob extends BaseCronJob {
    //数据库表名
    private static final String TABLE_NAME = "uf_zgfxnbddtz";
    //插入的字段数据库名称
    private static final String[] FIELDS = {"AUFNR", "KTEXT", "RightKey1", "cbzx"};
    // 常量定义

    private static final String LOCAL_DIR = new File(GCONST.getRootPath()).getParent() + File.separator + "Order" + File.separator;
    private static final String ECOLOGY_LOCAL_DIR = GCONST.getRootPath()+"sdtmp"+File.separatorChar+"nbdd"+File.separatorChar;
    private static final String INTERNAL_ORDERS_FILE = "内部订单.csv";
    private static final String RELATION_FILE = "内部订单和成本中心关系.csv";
    // 文件路径
    private static final String LOCAL_FILE_PATH_1 = LOCAL_DIR + INTERNAL_ORDERS_FILE;
    private static final String LOCAL_FILE_PATH_2 = LOCAL_DIR + RELATION_FILE;
    private static final String ECOLOGY_LOCAL_FILE_PATH_1 = ECOLOGY_LOCAL_DIR + INTERNAL_ORDERS_FILE;
    private static final String ECOLOGY_LOCAL_FILE_PATH_2 = ECOLOGY_LOCAL_DIR + RELATION_FILE;
    private final BaseBean bb = new BaseBean();

    private SDLogUtil sdLogUtil;

    private SyncCsvFileLog syncCsvFileLog;

    // 使用ConcurrentHashMap提高并发性能
    private Map<String, String> oaNbddMap;
    //OA中成本中心浏览框中的数据
    private Map<String, String> oaCbzxMap;
    private String errMsg;

    @Override
    public void execute() {
        long start = System.currentTimeMillis();
        try {
            resetParams();
            appendLog("内部订单.csv及内部订单和成本中心关系.csv写入建模台账开始");
            init();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }

            // 1. 创建目录（如果不存在）
            createDirectoryIfNotExists();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }

            // 2. 从本地下载文件
            downloadFiles();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }

            // 3. 处理CSV文件
            Map<String, String> baseMap = processCsvOrdersFile(ECOLOGY_LOCAL_FILE_PATH_1);
            appendLog("处理CSV文件 baseMap"+JSONObject.toJSONString(baseMap));
            appendLog("处理CSV文件 baseMap"+baseMap.size());
            if (baseMap.isEmpty() || StringUtils.isNotBlank(errMsg)) {
                return;
            }
            Map<String, String> associatedMap = processCsvRelationFile(ECOLOGY_LOCAL_FILE_PATH_2);
            appendLog("处理CSV文件 associatedMap"+JSONObject.toJSONString(associatedMap));
            if (associatedMap.isEmpty() || StringUtils.isNotBlank(errMsg)) {
                return;
            }
            // 4. 逻辑处理
            processData(baseMap, associatedMap);
            appendLog("内部订单.csv及内部订单和成本中心关系.csv写入建模台账结束");
        } catch (Exception e) {
            errMsg = "执行过程中发生异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            long end = System.currentTimeMillis();
            long durationMillis = end - start;
            double durationSeconds = durationMillis / 1000.0;
            syncCsvFileLog.setSync_during(durationSeconds +"s");
            afterExecute();
        }
    }

    private void afterExecute() {
        try{
            appendLog("InternalOrdersJob afterExecute start");
            int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            int secCatId = ModuleDataUtil.getDefaultSecCatId(modid);
            ArrayList<Integer> docIds = new ArrayList<>();
            try (FileInputStream inputStream = new FileInputStream(LOCAL_FILE_PATH_1)) {
                //将输入流程上传至OA文档
                int docWithFile = DocUtil.createDocWithFile(inputStream, new User(1), INTERNAL_ORDERS_FILE, secCatId);
                docIds.add(docWithFile);
            } catch (IOException e) {
                appendLog("转文件流出错："+SDUtil.getExceptionDetail(e));
            }

            try (FileInputStream inputStream = new FileInputStream(LOCAL_FILE_PATH_2)) {
                //将输入流程上传至OA文档
                int docWithFile = DocUtil.createDocWithFile(inputStream, new User(1), RELATION_FILE, secCatId);
                docIds.add(docWithFile);
            } catch (IOException e) {
                appendLog("转文件流出错："+SDUtil.getExceptionDetail(e));
            }

            syncCsvFileLog.setCsv_files(StringUtils.join(docIds, ","));
            String fullLog = sdLogUtil.getFullLog();
            int fileDocId = com.engine.zgfx4.tpw.util.DocUtil.generateStrFile2Doc(fullLog, secCatId, "执行日志.txt", new User(1));
            syncCsvFileLog.setSync_log_file(String.valueOf(fileDocId));
            if (fullLog.length() > 3500) {
                fullLog = fullLog.substring(0, 3500) + "---已截断，完整信息请查看文件";
                syncCsvFileLog.setSync_log_data(fullLog);
            }
            appendLog("InternalOrdersJob syncCsvFileLog"+JSONObject.toJSONString(syncCsvFileLog));
            ModuleResult mr = ModuleDataUtil.insertObj(syncCsvFileLog, SyncCsvFileLog.TABLE_NAME, ModuleDataUtil.getModuleIdByName(SyncCsvFileLog.TABLE_NAME), 1);
            appendLog("执行内部订单.csv及内部订单和成本中心关系.csv 写入建模台账日志结果"+JSONObject.toJSONString(mr));
            appendLog("InternalOrdersJob afterExecute end");
        }catch (Exception e) {
            appendLog("执行内部订单.csv及内部订单和成本中心关系.csv 写入建模台账日志异常 "+SDUtil.getExceptionDetail(e));
        }
    }
    private void init() {
        try {
            appendLog("初始化开始");
            syncCsvFileLog = SyncCsvFileLog.initLog("内部订单.csv及内部订单和成本中心关系.csv写入内部订单台账");
            syncCsvFileLog.setCsv_file_name(INTERNAL_ORDERS_FILE+" , "+RELATION_FILE);
            //获取系统中已经存在的内部订单
            getOAInternalOrders();
            //得到浏览框-成本中心信息
            getOaCbzx();
            appendLog("初始化结束");
        } catch (Exception e) {
            errMsg = "初始化异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }

    private void getOAInternalOrders() {
        appendLog("获取系统中已经存在的内部订单开始");
        RecordSet recordSet = null;
        try {
            recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("SELECT id,AUFNR  FROM "+TABLE_NAME);
            while (recordSet.next()) {
                String id = recordSet.getString("id");
                String AUFNR = recordSet.getString("AUFNR");
                if (StringUtils.isNotBlank(AUFNR)) {
                    oaNbddMap.put(AUFNR, id);
                }

            }
            appendLog("OA中已存在的内部订单信息: " + JSONObject.toJSONString(oaNbddMap));
            appendLog("获取系统中已经存在的内部订单结束");
        } catch (Exception e) {
            errMsg = "获取OA内部订单异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    //得到浏览框-成本中心信息
    private void getOaCbzx() {
        appendLog("得到浏览框-成本中心信息开始");
        RecordSet recordSet = null;
        try {
            recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("SELECT id,bh FROM uf_cbzx");
            while (recordSet.next()) {
                String id = recordSet.getString("id");
                String bh = recordSet.getString("bh");
                if (StringUtils.isNotBlank(bh)) {
                    oaCbzxMap.put(bh, id);
                }
            }
            appendLog("得到浏览框-成本中心信息: " + JSONObject.toJSONString(oaCbzxMap));
            appendLog("得到浏览框-成本中心信息结束");
        } catch (Exception e) {
            errMsg = "得到浏览框-成本中心信息异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void resetParams() {
        sdLogUtil = new SDLogUtil();
        oaCbzxMap = new HashMap<>();
        oaNbddMap = new HashMap<>();
        errMsg = "";
    }

    private void processData(Map<String, String> baseMap, Map<String, String> associatedMap) {
        try {
            appendLog("开始处理数据");
            List<List<Object>> insertListValues = new ArrayList<>();
            List<List<Object>> updateListValues = new ArrayList<>();
            ArrayList<ModuleResult> errList = new ArrayList<>();
            // 遍历 baseMap 中的所有条目
            for (Map.Entry<String, String> entry : baseMap.entrySet()) {
                List<Object> list = new ArrayList<>();
                //内部订单编号
                String AUFNR = entry.getKey();
                //订单描述　
                String KTEXT = entry.getValue();
                list.add(AUFNR);
                list.add(KTEXT);
                String cbzxCode = "";
                String cbzxId = "";
                String id = "";

                // 判断当前 key 是否存在于 oaNbddMap 中
                if (oaNbddMap.containsKey(AUFNR)) {
                    id = Util.null2String(oaNbddMap.get(AUFNR));
                    if (associatedMap.containsKey(AUFNR)) {
                        //成本中心编码
                        cbzxCode = Util.null2String(associatedMap.get(AUFNR));
                        if (oaNbddMap.containsKey(cbzxCode)) {
                            //成本中心浏览框id
                            cbzxId = Util.null2String(oaCbzxMap.get(cbzxCode));
                        }
                    }
                } else {
                    if (associatedMap.containsKey(AUFNR)) {
                        //成本中心编码
                        cbzxCode = Util.null2String(associatedMap.get(AUFNR));
                        if (oaNbddMap.containsKey(cbzxCode)) {
                            //成本中心浏览框id
                            cbzxId = Util.null2String(oaCbzxMap.get(cbzxCode));
                        }
                    }
                }
                list.add(cbzxCode);
                list.add(cbzxId);
                if (StringUtils.isNotBlank(id)) {
                    list.add(id);
                    updateListValues.add(list);
                } else {
                    insertListValues.add(list);
                }
            }

            if (!insertListValues.isEmpty()) {
                //组装插入bean
                ModuleInsertBean mb = new ModuleInsertBean();
                //明细数据
                //组装插入的数据
                mb.setTableName(TABLE_NAME)
                        .setModuleId(ModuleDataUtil.getModuleIdByName(TABLE_NAME))
                        .setCreatorId(1)
                        .setFields(Arrays.asList(FIELDS))
                        .setValues(insertListValues);

                ModuleResult mr = ModuleDataUtil.insertByOne(mb);
                if(!mr.isSuccess()){
                    errList.add(mr);
                }
            }
            if (!updateListValues.isEmpty()) {
                //组装插入bean
                ModuleUpdateBean mb = new ModuleUpdateBean();
                //明细数据
                //组装更新的数据
                mb.setTableName(TABLE_NAME)
                        .setUpdater(1)
                        .setFields(Arrays.asList(FIELDS))
                        .setValues(updateListValues);

                ModuleResult mr = ModuleDataUtil.update(mb);
                if(!mr.isSuccess()){
                    errList.add(mr);
                }
            }
            syncCsvFileLog.setSync_end_time(TimeUtil.getCurrentTimeString());
            syncCsvFileLog.setSync_add_size(insertListValues.size());
            syncCsvFileLog.setSync_update_size(updateListValues.size());
            syncCsvFileLog.setSync_err_info(JSONObject.toJSONString(errList));
            syncCsvFileLog.setSync_result(errList.isEmpty() ? 0:1);
            appendLog("数据处理完成");
        } catch (Exception e) {
            syncCsvFileLog.setSync_result(1);
            errMsg = "数据处理异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }



    private Map<String, String> processCsvOrdersFile(String filePath) {
        Map<String, String> dataMap = new HashMap<>();
        try (CSVReader reader = new CSVReader(new InputStreamReader(
                Files.newInputStream(Paths.get(filePath)), StandardCharsets.UTF_8))) {

            // 读取标题行（跳过可能的BOM头）
            reader.readNext();
            String[] titles = reader.readNext();
            if (titles == null) {
                return dataMap; // 空文件
            }

            int index_AUFNR = Arrays.asList(titles).indexOf("AUFNR");
            int index_KTEXT = Arrays.asList(titles).indexOf("KTEXT");
            if (index_AUFNR == -1 || index_KTEXT == -1) {
                return dataMap; // 列名不存在
            }

            // 逐行处理数据
            String[] values;
            while ((values = reader.readNext()) != null) {
                if (values.length > Math.max(index_AUFNR, index_KTEXT)) {
                    String key = values[index_AUFNR].trim();
                    String value = values[index_KTEXT].trim();
                    if (StringUtils.isNotBlank(key)) {
                        dataMap.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            errMsg = "处理CSV文件[" + filePath + "]异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
        return dataMap;
    }

    private Map<String, String> processCsvRelationFile(String filePath) {
        Map<String, String> dataMap = new HashMap<>();
        try (CSVReader reader = new CSVReader(new InputStreamReader(
                Files.newInputStream(Paths.get(filePath)), StandardCharsets.UTF_8))) {
            reader.readNext();
            String[] titles = reader.readNext();
            if (titles == null) {
                return dataMap;
            }

            int index_LeftKey1 = Arrays.asList(titles).indexOf("LeftKey1");
            int index_RightKey1 = Arrays.asList(titles).indexOf("RightKey1");
            if (index_LeftKey1 == -1 || index_RightKey1 == -1) {
                return dataMap;
            }

            String[] values;
            while ((values = reader.readNext()) != null) {
                if (values.length > Math.max(index_LeftKey1, index_RightKey1)) {
                    String key = values[index_LeftKey1].trim();
                    String value = values[index_RightKey1].trim();
                    if (StringUtils.isNotBlank(key)) {
                        dataMap.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            errMsg = "处理CSV文件[" + filePath + "]异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
        return dataMap;
    }

    private void createDirectoryIfNotExists() {
        try {
            File dir = new File(ECOLOGY_LOCAL_DIR);

            if (!dir.exists() && !dir.mkdirs()) {
                errMsg = "目录创建失败: " + ECOLOGY_LOCAL_DIR;
                appendLog(errMsg);
                return;
            }

            // 清理旧文件
            cleanOldFiles(dir);
        } catch (Exception e) {
            errMsg = "目录处理异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }

    private void cleanOldFiles(File dir) {
        File[] oldFiles = dir.listFiles((d, name) ->
                name.equals(INTERNAL_ORDERS_FILE) || name.equals(RELATION_FILE));

        if (oldFiles != null) {
            for (File file : oldFiles) {
                if (!file.delete()) {
                    appendLog("文件删除失败: " + file.getName());
                }
            }
        }
    }

    private void downloadFiles() {
        downloadFile(LOCAL_FILE_PATH_1, ECOLOGY_LOCAL_FILE_PATH_1);
        if (StringUtils.isNotBlank(errMsg)) return;

        downloadFile(LOCAL_FILE_PATH_2, ECOLOGY_LOCAL_FILE_PATH_2);
    }

    private void downloadFile(String sourcePath, String targetPath) {
        try {
            appendLog("开始复制文件: " + sourcePath);
            Files.copy(Paths.get(sourcePath), Paths.get(targetPath));
            appendLog("文件复制完成，保存到: " + targetPath);
        } catch (Exception e) {
            errMsg = "文件操作异常: " + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        }
    }


    private void appendLog(String logMsg) {
        bb.writeLog(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

}