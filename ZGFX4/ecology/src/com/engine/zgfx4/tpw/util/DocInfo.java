package com.engine.zgfx4.tpw.util;

import lombok.Data;

@Data
public class DocInfo {
    /**
     * 原文档id
     */
    private int id;
    /**
     * 最新的文档id
     */
    private int maxid;
    /**
     * 文档版本id(只有文档目录开启的版本管理，才有值，才会大于0)
     * 不同版本的文档，文档id不同，doceditionid相同，最新的取文档id最大的
     */
    private int doceditionid;
    /**
     * 原文档标题
     */
    private String docsubject;
    /**
     * 最新文档标题
     */
    private String maxdocsubject;
    
}
