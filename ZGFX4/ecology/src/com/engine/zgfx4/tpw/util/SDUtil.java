package com.engine.zgfx4.tpw.util;


import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.integration.logging.Logger;
import weaver.integration.logging.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @FileName SDUtil.java
 * @Description 公共工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/1/4
 */
public class SDUtil {
    //二开log类
    private static final Logger log = LoggerFactory.getLogger(SDUtil.class);


    /**
     * 格式化日期（毫秒格式）
     *
     * @param date
     * @return
     */
    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 获取系统管理员id，根据loginId
     *
     * @param loginId
     * @return
     */
    public static int getSystemMangerByLoginId(String loginId) {
        RecordSet rs = new RecordSet();
        String sql = "select id from HrmResourceManager where loginid = ? ";
        rs.executeQuery(sql, loginId);
        if (rs.next()) {
            return rs.getInt("id");
        }
        return 1;
    }

    /**
     * 获取系统管理员id(默认sysadmin)
     *
     * @return
     */
    public static int getSystemMangerByLoginId() {
        RecordSet rs = new RecordSet();
        String sql = "select id from HrmResourceManager where loginid = 'sysadmin' ";
        rs.executeQuery(sql);
        if (rs.next()) {
            return rs.getInt("id");
        }
        return 1;
    }

    /**
     * 获取精确到秒的时间戳
     *
     * @param date
     * @return
     */
    public static int getSecondTimestamp(Date date) {
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime() / 1000);
        return Integer.parseInt(timestamp);
    }

    /**
     * 全角转半角
     *
     * @param input 待处理文本
     * @return 处理结果
     */
    public static String toDBC(String input) {
        if (input == null) {
            return null;
        }
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == 12288) {
                c[i] = (char) 32;
            } else if (c[i] > 65280 && c[i] < 65375) {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    /**
     * 获取BigDecimal类的值
     *
     * @param v 值
     * @return BigDecimal
     */
    public static BigDecimal getBigDecimalValue(String v) {
        return getBigDecimalValue(Util.null2String(v), BigDecimal.valueOf(0));
    }

    /**
     * 获取BigDecimal类的值
     *
     * @param v 值
     * @return BigDecimal
     */
    public static BigDecimal getBigDecimalValue(Object v) {
        return getBigDecimalValue(Util.null2String(v), BigDecimal.valueOf(0));
    }

    /**
     * 获取BigDecimal值
     *
     * @param v
     * @param def
     * @return
     */
    public static BigDecimal getBigDecimalValue(String v, BigDecimal def) {
        try {
            if (StringUtils.isBlank(v)) {
                return new BigDecimal(0);
            }
            return new BigDecimal(v);
        } catch (Exception var3) {
            return def;
        }
    }

    /**
     * 获取异常详细信息，知道出了什么错，错在哪个类的第几行
     *
     * @param e
     * @return
     */
    public static String getExceptionDetail(Exception e) {
        String ret;
        //try-with-resource语法，可实现自动close资源
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                PrintStream pout = new PrintStream(out)
        ) {
            e.printStackTrace(pout);
            ret = "catch exception: " + out;
        } catch (Exception ex) {
            ret = "getExceptionDetail exception:" + Arrays.toString(ex.getStackTrace());
        }
        // 确保 endIndex 不超过字符串长度
        int endIndex = Math.min(1000, ret.length());
        return ret.substring(0, endIndex);
    }

    /**
     * 判断字符串是否为Integer
     *
     * @param input
     * @return true为Integer
     */
    public static boolean isInteger(String input) {
        try {
            Integer.parseInt(input);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 将List<Map<String, Object>> 转为 转为List对象
     *
     * @param mapList
     * @param clazz
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public static <T> List<T> mapListToBeanList(List<Map<String, Object>> mapList, Class<T> clazz) {
        try {
            List<T> resultList = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                T bean = mapToBean(map, clazz);
                if (bean != null) {
                    resultList.add(bean);
                }
            }
            return resultList;
        } catch (Exception e) {
            log.error("mapListToBeanList error :" + ",clazz:" + clazz, e);
            if (mapList != null && !mapList.isEmpty()) {
                log.error("mapListToBeanList error first map :" + mapList.get(0) + ",clazz:" + clazz);
            }
        }
        return null;
    }

    /**
     * 将Map转为java object
     * java bean的字段大小写可以不一致
     *
     * @param map
     * @param clazz
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public static <T> T mapToBean(Map<String, Object> map, Class<T> clazz) {
        Object value, convertedValue;
        if (map == null || map.isEmpty()) {
            return null;
        }
        try {
            //现将map转小写
            Map<String, Object> mapLow = lowerMapKey(map);
            T instance = clazz.newInstance();
            Field[] fields = clazz.getDeclaredFields();
            //便利所有字段
            for (Field field : fields) {
                // 跳过static和final字段
                if (Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers())) {
                    continue;
                }
                //原始字段
                String fieldName = field.getName();
                //小写字段
                String lowerFieldName = fieldName.toLowerCase();
                if (mapLow.containsKey(lowerFieldName)) {
                    field.setAccessible(true);
                    value = mapLow.get(lowerFieldName);
                    convertedValue = value;

                    if (value != null) {
                        Class<?> fieldType = field.getType();
                        Class<?> valueClass = value.getClass();
                        // 判断当前值的类型是否与目标字段的类型一致,不一致进行转换
                        if (!fieldType.isAssignableFrom(valueClass)) {
                            convertedValue = convertValue(value, fieldType);
                        }
                    }
                    //给字段设置值
                    //field.set(instance, convertedValue);
                    String setterMethodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Method setterMethod = clazz.getMethod(setterMethodName, field.getType());
                    setterMethod.invoke(instance, convertedValue);
                }
            }
            return instance;
        } catch (Exception e) {
            log.error("mapToBean error,map:" + map + ",clazz:" + clazz, e);
        }
        return null;
    }

    /**
     * 转换对象值
     *
     * @param value      要转换的值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    private static Object convertValue(Object value, Class<?> targetType) {
        try {
            // 如果值为null
            if (value == null || Util.null2String(value).isEmpty()) {
                // 针对基本类型返回默认值
                if (targetType.isPrimitive()) {
                    if (targetType == int.class) return 0;
                    if (targetType == long.class) return 0L;
                    if (targetType == double.class) return 0.0;
                    if (targetType == float.class) return 0.0f;
                    if (targetType == boolean.class) return false;
                    if (targetType == byte.class) return (byte) 0;
                    if (targetType == short.class) return (short) 0;
                    if (targetType == char.class) return '\u0000'; // 默认char值
                }
                // 包装类返回null
                return null;
            }
            // 针对基本类型返回默认值
            if (targetType.isPrimitive()) {
                if (targetType == int.class) {
                    if (value instanceof Number) {
                        return ((Number) value).intValue();
                    } else if (value instanceof String) {
                        return Integer.parseInt((String) value);
                    }
                    return 0;
                }
                if (targetType == long.class) {
                    if (value instanceof Number) {
                        return ((Number) value).longValue();
                    } else if (value instanceof String) {
                        return Long.parseLong((String) value);
                    }
                    return 0L;
                }
                if (targetType == double.class) {
                    if (value instanceof Number) {
                        return ((Number) value).doubleValue();
                    } else if (value instanceof String) {
                        return Double.parseDouble((String) value);
                    }
                    return 0;
                }
                if (targetType == float.class) {
                    if (value instanceof Number) {
                        return ((Number) value).floatValue();
                    } else if (value instanceof String) {
                        return Float.parseFloat((String) value);
                    }
                    return 0.0f;
                }
                if (targetType == boolean.class) {
                    if (value instanceof Boolean) {
                        return value;
                    } else if (value instanceof String) {
                        return Boolean.parseBoolean((String) value);
                    } else if (value instanceof Number) {
                        return ((Number) value).intValue() != 0;
                    }
                    return false;
                }
                if (targetType == byte.class) {
                    if (value instanceof Number) {
                        return ((Number) value).byteValue();
                    } else if (value instanceof String) {
                        return Byte.parseByte((String) value);
                    }
                    return (byte) 0;
                }
                if (targetType == short.class) {
                    if (value instanceof Number) {
                        return ((Number) value).shortValue();
                    } else if (value instanceof String) {
                        return Short.parseShort((String) value);
                    }
                    return (short) 0;
                }
                if (targetType == char.class) {
                    if (value instanceof Character) {
                        return value;
                    } else if (value instanceof String) {
                        return ((String) value).charAt(0);
                    }
                    return '\u0000';
                }
            } else {
                //----以下是对包装类的数据转换----
                // 字符串转换
                if (targetType == String.class) {
                    return value.toString();
                }
                // BigDecimal转换
                if (targetType == BigDecimal.class) {
                    if (value instanceof BigDecimal) {
                        return value;
                    } else if (value instanceof String) {
                        return new BigDecimal((String) value);
                    } else if (value instanceof Number) {
                        return BigDecimal.valueOf(((Number) value).doubleValue());
                    }
                    return null;
                }

                // Integer转换
                if (targetType == Integer.class) {
                    if (value instanceof Number) {
                        return ((Number) value).intValue();
                    } else if (value instanceof String) {
                        return Integer.parseInt((String) value);
                    }
                    return null;
                }

                // Double转换
                if (targetType == Double.class) {
                    if (value instanceof Number) {
                        return ((Number) value).doubleValue();
                    } else if (value instanceof String) {
                        return Double.parseDouble((String) value);
                    }
                    return null;
                }

                // Boolean转换
                if (targetType == Boolean.class) {
                    if (value instanceof Boolean) {
                        return value;
                    } else if (value instanceof String) {
                        return Boolean.parseBoolean((String) value);
                    } else if (value instanceof Number) {
                        return ((Number) value).intValue() != 0;
                    }
                    return null;
                }

                // Date转换（假设格式为"yyyy-MM-dd"）
                if (targetType == Date.class) {
                    if (value instanceof Date) {
                        return value;
                    } else if (value instanceof String) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        try {
                            return sdf.parse((String) value);
                        } catch (ParseException e) {
                            log.error("Error parsing date: " + value, e);
                            return null;
                        }
                    }
                    return null;
                }

                // Long转换
                if (targetType == Long.class) {
                    if (value instanceof Number) {
                        return ((Number) value).longValue();
                    } else if (value instanceof String) {
                        return Long.parseLong((String) value);
                    }
                    return null;
                }

                // Float转换
                if (targetType == Float.class) {
                    if (value instanceof Number) {
                        return ((Number) value).floatValue();
                    } else if (value instanceof String) {
                        return Float.parseFloat((String) value);
                    }
                    return null;
                }
                // Byte转换
                if (targetType == Byte.class) {
                    if (value instanceof Number) {
                        return ((Number) value).byteValue();
                    } else if (value instanceof String) {
                        return Byte.parseByte((String) value);
                    }
                    return null;
                }

                // Character转换
                if (targetType == Character.class) {
                    if (value instanceof Character) {
                        return value;
                    } else if (value instanceof String) {
                        return ((String) value).charAt(0);
                    }
                    return null;
                }
            }
            // 如果没有匹配到类型，返回null
            log.error("Unsupported target type: " + targetType);
            return null;

        } catch (Exception e) {
            log.error("Error converting value: " + value + " to target type: " + targetType, e);
            return null;
        }
    }
//    /**
//     * 转换对象值
//     *
//     * @param value
//     * @param targetType
//     * @return
//     */
//    private static Object convertValue(Object value, Class<?> targetType) {
//        try {
//            // 如果值为null，直接返回null
//            if (value == null || Util.null2String(value).isEmpty()) {
//                return null;
//            }
//            // 如果目标类型为字符串，直接转换为字符串
//            if (targetType == String.class) {
//                return value.toString();
//            }
//            // 如果目标类型为 BigDecimal，尝试将值转换为 BigDecimal
//            if (targetType == BigDecimal.class) {
//                try {
//                    if (value instanceof BigDecimal) {
//                        return value;
//                    } else if (value instanceof String) {
//                        return new BigDecimal((String) value);
//                    } else if (value instanceof Number) {
//                        return new BigDecimal(value.toString());
//                    } else {
//                        throw new ConversionException("Cannot convert value to BigDecimal: " + value);
//                    }
//                } catch (NumberFormatException e) {
//                    throw new ConversionException("Error converting value to BigDecimal: " + value, e);
//                }
//            }
//            // 使用BeanUtils转换值的类型
//            try {
//                return org.apache.commons.beanutils.ConvertUtils.convert(value, targetType);
//            } catch (Exception e) {
//                log.error("Error converting value: " + value + " to target type: " + targetType, e);
//                return null;
//            }
//        } catch (Exception e) {
//            log.info("convertValue异常", e);
//            return null;
//        }
//    }

    /**
     * 将List<Map<String, Object>>的每个Map的key转小写
     *
     * @param list
     */
    public static List<Map<String, Object>> lowerMapKey(List<Map<String, Object>> list) {
        try {
            for (Map<String, Object> map : list) {

                // 创建新的 Map 用于存放转换后的键值对
                Map<String, Object> newMap = new LinkedHashMap<>();
                // 遍历原始 Map 中的键值对
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    // 将键转为小写，并放入新的 Map 中
                    newMap.put(entry.getKey().toLowerCase(), entry.getValue());
                }
                // 替换原始 Map 中的键值对
                map.clear();
                map.putAll(newMap);
            }
        } catch (Exception e) {
            log.error("lowerMapKey 异常", e);
        }
        return list;
    }

    /**
     * 将Map<String, Object>的所有key转小写
     *
     * @param map
     */
    public static Map<String, Object> lowerMapKey(Map<String, Object> map) {
        try {
            // 创建新的 Map 用于存放转换后的键值对
            Map<String, Object> newMap = new LinkedHashMap<>();
            // 遍历原始 Map 中的键值对
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                // 将键转为小写，并放入新的 Map 中
                newMap.put(entry.getKey().toLowerCase(), entry.getValue());
            }
            // 替换原始 Map 中的键值对
            map.clear();
            map.putAll(newMap);
        } catch (Exception e) {
            log.error("lowerMapKey 异常", e);
        }
        return map;
    }


}
