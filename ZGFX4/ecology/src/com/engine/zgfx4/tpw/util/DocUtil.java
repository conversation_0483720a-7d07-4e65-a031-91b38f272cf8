package com.engine.zgfx4.tpw.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.doc.DateUtil;
import com.engine.parent.doc.FileUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.docs.category.SecCategoryComInfo;
import weaver.docs.docs.*;
import weaver.file.FileUpload;
import weaver.file.ImageFileManager;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.integration.logging.Logger;
import weaver.integration.logging.LoggerFactory;
import weaver.system.SystemComInfo;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文档类工具类
 */
public class DocUtil {
    //使用二开log类
    private static final Logger log = LoggerFactory.getLogger(DocUtil.class);

    /**
     * 根据文档id获取文件下载链接
     *
     * @param docid
     * @return
     */
    public static JSONArray getFileDownloadPathByDocId(String docid) {
        JSONArray result = new JSONArray();
        JSONObject eachResult;
        String downloadPath;
        RecordSet rs = new RecordSet();
        //根据文档id取版本最大的附件列表(其中row_number()函数注意数据库兼容性)
        boolean rsFlag = rs.executeQuery("select a.* " +
                " from ( " +
                "    select imagefileid,imagefilename,docid,versionid, " +
                "    (row_number() over (partition by id order by versionid desc)) as order_num " +
                "    from docimagefile " +
                " ) a " +
                " where 1=1 " +
                " and a.order_num = 1 " +
                " and a.docid = ? ", docid);
        if (rsFlag) {
            while (rs.next()) {
                eachResult = new JSONObject();
                String fileId = Util.null2String(rs.getString("imagefileid"));
                String fileName = Util.null2String(rs.getString("imagefilename"));
                downloadPath = "/weaver/weaver.file.gyl.DownloadFileForNoUser?download=1&fileid=" + fileId;
                eachResult.put("downloadPath", downloadPath);
                eachResult.put("fileName", fileName);
                result.add(eachResult);
            }
        }
        return result;
    }

    /**
     * 根据文档id获取最新版本的所有附件信息
     *
     * @param docid
     * @return DocFileInfo
     */
    public static List<DocFileInfo> getDocFileInfoByDocId(String docid) {
        return getDocFileInfoByDocIds(docid);
    }

    /**
     * 根据文档id获取最新版本的所有附件信息，平铺列举所有的文档的最新版本附件，可能存在多行相同的docid，对应不同的fileid
     * 文档id支持多个，用英文逗号隔开
     *
     * @param docids
     * @return DocFileInfo
     */
    public static List<DocFileInfo> getDocFileInfoByDocIds(String docids) {
        List<DocFileInfo> result = new ArrayList<>();
        DocFileInfo eachInfo;
        String fileId, docid;
        RecordSet rs = new RecordSet();
        if (StringUtils.isNotBlank(docids)) {
            //根据文档id取版本最大的附件列表，即取所有文档的最新的附件列表
            String sql = "select t1.*,t2.filerealpath from (select a.* " +
                    " from ( " +
                    "    select *, " +
                    "    (row_number() over (partition by id order by versionid desc)) as order_num " +
                    "    from docimagefile " +
                    " ) a " +
                    " where 1=1 " +
                    " and a.order_num = 1 " +
                    " and a.docid in (" + docids + ") ) t1 " +
                    " left join imagefile t2 on (t1.imagefileid = t2.imagefileid) ";
            log.info("getDocFileInfoByDocIds sql:" + sql);
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    eachInfo = new DocFileInfo();
                    fileId = Util.null2String(rs.getString("imagefileid"));
                    docid = Util.null2String(rs.getString("docid"));

                    eachInfo.setDocid(docid);
                    eachInfo.setFileid(fileId);
                    eachInfo.setFileName(Util.null2String(rs.getString("imagefilename")));
                    eachInfo.setVersionId(Util.null2String(rs.getString("versionId")));
                    eachInfo.setFileDownloadUrl(getHttpFileUrlByFileId(fileId));
                    eachInfo.setOperateDate(Util.null2String(rs.getString("operatedate")));
                    eachInfo.setOperateUserId(Util.null2String(rs.getString("operateuserid")));
                    eachInfo.setFilerealpath(Util.null2String(rs.getString("filerealpath")));
                    result.add(eachInfo);
                }
            } else {
                log.error("getDocFileInfoByDocIds sql error:" + rs.getExceptionMsg());
            }
        }
        return result;
    }

    /**
     * 根据文档id获取最新文档版本的所有附件信息，平铺列举所有的文档的最新版本附件，可能存在多行相同的docid，对应不同的fileid
     * 文档id支持多个，用英文逗号隔开
     *
     * @param docids
     * @return DocFileInfo
     */
    public static List<DocFileInfo> getLatestDocFileInfoByDocIds(String docids) {
        List<DocFileInfo> result = new ArrayList<>();
        List<DocFileInfo> listDocOrigin = new ArrayList<>();
        List<DocFileInfo> listDocNew;
        String allNewDocids = "";
        DocFileInfo eachDocFileInfo;
        String docid, maxdocid;
        StringBuilder sb = new StringBuilder();
        try {
            if (StringUtils.isNotBlank(docids)) {
                //获取最新版本的文档
                List<DocInfo> listDocInfo = getLatestDocs(docids);
                for (DocInfo info : listDocInfo) {
                    eachDocFileInfo = new DocFileInfo();
                    eachDocFileInfo.setDocid(String.valueOf(info.getId()));
                    eachDocFileInfo.setMaxdocid(String.valueOf(info.getMaxid()));
                    listDocOrigin.add(eachDocFileInfo);
                    sb.append(info.getMaxid()).append(CommonCst.COMMA_EN);
                }
                if (!sb.toString().isEmpty()) {
                    allNewDocids = sb.substring(0, sb.length() - 1);
                }
                //获取最新版本文档的附件列表
                listDocNew = getDocFileInfoByDocIds(allNewDocids);
                for (DocFileInfo originDocFile : listDocOrigin) {
                    docid = originDocFile.getDocid();
                    maxdocid = originDocFile.getMaxdocid();
                    //匹配最新文档的附件，设置附件相关信息，并恢复原有的docid和maxdocid
                    for (DocFileInfo newDocFile : listDocNew) {
                        //根据原始的maxdocid和新的docid匹配
                        if (originDocFile.getMaxdocid().equals(newDocFile.getDocid())) {
                            eachDocFileInfo = (DocFileInfo) newDocFile.clone();
                            eachDocFileInfo.setDocid(docid);
                            eachDocFileInfo.setMaxdocid(maxdocid);
                            result.add(eachDocFileInfo);
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            new BaseBean().writeLog("getLatestDocFileInfoByDocIds异常：" + SDUtil.getExceptionDetail(e));
        }

        return result;
    }


    /**
     * 获取最新版本的文档id
     *
     * @param docid
     * @return
     */
    public static DocInfo getLatestDoc(String docid) {
        List<DocInfo> list = getLatestDocs(docid);
        if (!list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 获取祖新版本的文档列表
     * 查几个文档，返回几条数据
     *
     * @param docids
     * @return
     */
    public static List<DocInfo> getLatestDocs(String docids) {
        List<DocInfo> list = new ArrayList<>();
        DocInfo info;
        try {
            String sql = "SELECT " +
                    " a.id, " +
                    " ( CASE WHEN tba.id IS NULL THEN a.id ELSE tba.id END ) AS maxid, " +
                    "  a.docsubject AS docsubject,  " +
                    "  ( CASE WHEN tba.id IS NULL THEN a.docsubject ELSE tba.docsubject END ) AS maxdocsubject  " +
                    "FROM " +
                    " docdetail a " +
                    " LEFT JOIN ( " +
                    " SELECT " +
                    "  a.*  " +
                    " FROM " +
                    "  ( " +
                    "  SELECT " +
                    "   b.id, " +
                    "   b.doceditionid, " +
                    "   b.docsubject, " +
                    "   ( " +
                    "   row_number() over ( PARTITION BY b.doceditionid ORDER BY b.id DESC )) AS order_num  " +
                    "  FROM " +
                    "   ( SELECT id, doceditionid,docsubject FROM docdetail WHERE doceditionid IN ( SELECT doceditionid FROM docdetail WHERE id IN ( " + docids + " )) ) b  " +
                    "  ) a  " +
                    " WHERE " +
                    "  1 = 1  " +
                    "  AND a.order_num = 1  " +
                    " ) tba ON ( " +
                    " tba.doceditionid = a.doceditionid) " +
                    "  where a.id in (" + docids + ")";
            RecordSet rs = new RecordSet();
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    info = new DocInfo();
                    info.setId(rs.getInt("id"));
                    info.setMaxid(rs.getInt("maxid"));
                    info.setDocsubject(Util.null2String(rs.getString("docsubject")));
                    info.setMaxdocsubject(Util.null2String(rs.getString("maxdocsubject")));
                    list.add(info);
                }
            }
        } catch (Exception e) {
            new BaseBean().writeLog("getLatestDoc异常：" + SDUtil.getExceptionDetail(e));
        }
        return list;
    }

    /**
     * 根据文档id获取第一个附件的文件流
     *
     * @param docid
     * @return
     */
    public static Map<String, Object> getFirstFileInputStream(String docid) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        //根据文档id取版本最大的附件列表(其中row_number()函数注意数据库兼容性)
        if (rs.executeQuery("select a.* " +
                " from ( " +
                "    select imagefileid,imagefilename,docid,versionid, " +
                "    (row_number() over (partition by id order by versionid desc)) as order_num " +
                "    from docimagefile " +
                " ) a " +
                " where 1=1 " +
                " and a.order_num = 1 " +
                " and a.docid = ? ", docid)) {
            if (rs.next()) {
                //文件id
                int fileId = rs.getInt("imagefileid");
                //文件名，带后缀的
                String fileName = Util.null2String(rs.getString("imagefilename"));
                result.put("fileId", fileId);
                result.put("fileName", fileName);
                //根据文件id获取流
                result.put("inputStream", getFileInputStream(fileId));
            }
        }
        return result;
    }

    /**
     * 根据文档id获取第一个附件的文件流
     *
     * @param docid
     * @return
     */
    public static Map<String, Object> getFirstFileStream64(String docid) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        //根据文档id取版本最大的附件列表(其中row_number()函数注意数据库兼容性)
        if (rs.executeQuery("select a.* " +
                " from ( " +
                "    select imagefileid,imagefilename,docid,versionid, " +
                "    (row_number() over (partition by id order by versionid desc)) as order_num " +
                "    from docimagefile " +
                " ) a " +
                " where 1=1 " +
                " and a.order_num = 1 " +
                " and a.docid = ? ", docid)) {
            if (rs.next()) {
                //文件id
                int fileId = rs.getInt("imagefileid");
                //文件名，带后缀的
                String fileName = Util.null2String(rs.getString("imagefilename"));
                result.put("fileId", fileId);
                result.put("fileName", fileName);
                //根据文件id获取流
                InputStream is = getFileInputStream(fileId);
                result.put("file64", convertInputStreamToBase64(is));
            }
        }
        return result;
    }


    /**
     * 将文件输入流转为Base64
     *
     * @param inputStream
     * @return
     */
    public static String convertInputStreamToBase64(InputStream inputStream) {
        String result = "";
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            byte[] data = outputStream.toByteArray();
            result = Base64.getEncoder().encodeToString(data);
        } catch (IOException ignored) {
        }
        return result;
    }

    /**
     * 根据文件id获取流
     *
     * @param imagefileid
     * @return
     */
    public static InputStream getFileInputStream(int imagefileid) {
        ImageFileManager imageFileManager = new ImageFileManager();
        imageFileManager.getImageFileInfoById(imagefileid);
        return imageFileManager.getInputStream();
    }


    /**
     * 从网络url 获取下载出来的文件名称，带后缀
     *
     * @param urlString
     * @return
     * @throws IOException
     */
    public static String getFileNameFromURL(String urlString) {
        BaseBean bb = new BaseBean();
        String result = "";
        try {
            URL url = new URL(urlString);
            URLConnection connection = url.openConnection();
            // 获取Content-Disposition头部字段，该字段通常包含文件名信息
            String contentDisposition = connection.getHeaderField("Content-Disposition");
            if (contentDisposition != null && contentDisposition.contains("filename=")) {
                // 如果Content-Disposition中包含filename信息，从中提取文件名
                String encodedFileName = getEncodedFileName(contentDisposition);
                // 解码文件名
                result = URLDecoder.decode(encodedFileName, "UTF-8");
            } else {
                // 如果没有Content-Disposition头部字段，从URL中获取文件名
                String path = url.getPath();
                int slashIndex = path.lastIndexOf('/');
                if (slashIndex != -1 && slashIndex < path.length() - 1) {
                    result = path.substring(slashIndex + 1);
                } else {
                    //如果无法从Content-Disposition或URL中获取文件名，返回空字符串或自定义默认值
                    result = "";
                }
            }
        } catch (Exception e) {
            bb.writeLog("getFileNameFromURL 异常：" + SDUtil.getExceptionDetail(e));
        }
        return result;
    }

    /**
     * 获取编码后的文件名
     *
     * @param contentDisposition
     * @return
     */
    private static String getEncodedFileName(String contentDisposition) {
        int startIndex = contentDisposition.indexOf("filename=") + 9;
        int endIndex = contentDisposition.indexOf(";", startIndex);
        if (endIndex == -1) {
            // 如果没有分号，取整个字符串
            endIndex = contentDisposition.length();
        }
        String encodedFileName = contentDisposition.substring(startIndex, endIndex);
        // 去掉双引号
        if (encodedFileName.startsWith("\"") && encodedFileName.endsWith("\"")) {
            encodedFileName = encodedFileName.substring(1, encodedFileName.length() - 1);
        }
        // 替换'%2F'为 '/'
        encodedFileName = encodedFileName.replace("%2F", "/");
        return encodedFileName;
    }

    /**
     * 根据文件id获取下载url,需要登录OA
     *
     * @param fileId OA中的
     * @return
     */
    private static String getHttpFileUrlByFileId(String fileId) {
        return "/weaver/weaver.file.FileDownload?download=1&fileid=" + fileId;
    }


    public static void main(String[] args) throws IOException {
//        //不包含中文名的
        // String aa = "https://fp.baiwang.com/format/d?d=FA198EFD411ECFBF9B38145600D7229265FEC3E43631C9F130396E243BAC72C74890B54D622032EF455C8A64D51BA643";
//        //包含中文名的
//        String cc = "https://api.mypiaojia.com/api/invoiceApi/file/download?id=913130074597851136&token=WEAPPDXADzrLR70O0yPqUrOWOgPqfPLrB16ldYVL5-MsNOJM&fid=918587878735536129";
//        String bb = getFileNameFromURL(cc);
//        //出来是 23312000000093683081.pdf
//        System.out.println(bb);
        //  String aa = "123";
        // String bb = getDdcode("1", "237887");
        //  String ddcode = SystemDocUtil.takeddcode(new User(1), "237887", null);

        //  System.out.println(bb);
    }

    /**
     * 将文本写入一个文件，并上传到OA文档，返回文档id
     * 默认临时文件生成在 ../ecology/sdfile/tempfile/下
     *
     * @param str               文件内容
     * @param fileSecCategoryId OA文档目录id
     * @param fileName          文件名（最后一级的文件名 例如：test.txt）
     * @param docUser           文档创建人
     * @return
     */
    public static int generateStrFile2Doc( String str, int fileSecCategoryId,  String fileName, User docUser) {
        int docId = -1;
        String rootFilePath = GCONST.getRootPath() + "sdfile" + File.separatorChar + "tempfile";
        String filePath = rootFilePath + File.separatorChar + fileName;
        File parentDir = new File(rootFilePath);
        if (!parentDir.exists()) {
            log.info("rootFilePath目录:" + rootFilePath + "不存在，开始创建目录");
            boolean mkFlag = parentDir.mkdirs();
            if (!mkFlag) {
                log.info("rootFilePath目录:" + rootFilePath + "创建目录失败");
            }
        }
        log.info("generateStrFile2Doc rootFilePath目录 "+rootFilePath);
        //将文本写入文件
        if (com.engine.sd2.file.util.FileUtil.writeStrToFile(str, filePath)) {
            log.info("将文本写入文件");
            // 转为文件输入流
            //使用 try-with-resources ，可自动关闭资源
            try (FileInputStream inputStream = new FileInputStream(filePath)) {
                //将输入流程上传至OA文档
                docId = com.engine.parent.doc.DocUtil.createDocWithFile(inputStream, docUser, fileName, fileSecCategoryId);
            } catch (IOException e) {
                log.error(fileName + "转文件流出错：", e);
            } finally {
                // 删除临时文件
                com.engine.sd2.file.util.FileUtil.deleteFile(filePath);
            }
        }
        return docId;

    }


    /**
     * 根据输入流和创建人、文件名、目录创建文档，
     * 返回文档id
     *
     * @param doc
     * @param creator
     * @param fileName    文件名，包含后缀 例如：测试Excel文件.xlsx
     * @param secCategory
     * @return
     */
    public static int createDocWithFile(InputStream doc, User creator, String fileName, int secCategory) {
        log.info("createDocWithFile 开始");
        RecordSet rs = new RecordSet();
        int docId = -1;
        int mainCategory = -1;
        int subCategory = -1;

        try {
            int imgId = saveFile(doc, fileName);
            if (imgId > 0) {
                SecCategoryComInfo scc = new SecCategoryComInfo();
                DocComInfo dc = new DocComInfo();
                DocManager dm = new DocManager();
                DocViewer dv = new DocViewer();
                DocImageManager imgManger = new DocImageManager();
                docId = dm.getNextDocId(rs);
                String ext = FileUtil.extName(fileName);
                imgManger.resetParameter();
                imgManger.setImagefilename(fileName);
                if (ext.equalsIgnoreCase("doc")) {
                    imgManger.setDocfiletype("3");
                } else if (ext.equalsIgnoreCase("xls")) {
                    imgManger.setDocfiletype("4");
                } else if (ext.equalsIgnoreCase("ppt")) {
                    imgManger.setDocfiletype("5");
                } else if (ext.equalsIgnoreCase("wps")) {
                    imgManger.setDocfiletype("6");
                } else if (ext.equalsIgnoreCase("docx")) {
                    imgManger.setDocfiletype("7");
                } else if (ext.equalsIgnoreCase("xlsx")) {
                    imgManger.setDocfiletype("8");
                } else if (ext.equalsIgnoreCase("pptx")) {
                    imgManger.setDocfiletype("9");
                } else if (ext.equalsIgnoreCase("et")) {
                    imgManger.setDocfiletype("10");
                } else {
                    imgManger.setDocfiletype("2");
                }

                imgManger.setDocid(docId);
                imgManger.setImagefileid(imgId);
                imgManger.setIsextfile("1");
                imgManger.AddDocImageInfo();
                String date = DateUtil.today();
                String time = DateUtil.present();
                dm.setId(docId);
                dm.setMaincategory(mainCategory);
                dm.setSubcategory(subCategory);
                dm.setSeccategory(secCategory);
                dm.setLanguageid(creator.getLanguage());
                dm.setDoccontent("");
                dm.setDocstatus("1");
                dm.setDocsubject(FileUtil.mainName(fileName));
                dm.setDoccreaterid(creator.getUID());
                dm.setUsertype(creator.getLogintype());
                dm.setOwnerid(creator.getUID());
                dm.setDoclastmoduserid(creator.getUID());
                dm.setDoccreatedate(date);
                dm.setDoclastmoddate(date);
                dm.setDoccreatetime(time);
                dm.setDoclastmodtime(time);
                dm.setDoclangurage(creator.getLanguage());
                dm.setKeyword(FileUtil.mainName(fileName));
                dm.setIsapprover("0");
                dm.setIsreply("");
                dm.setDocdepartmentid(creator.getUserDepartment());
                dm.setDocreplyable("1");
                dm.setAccessorycount(1);
                dm.setParentids("" + docId);
                dm.setOrderable("" + scc.getSecOrderable(secCategory));
                dm.setClientAddress("内部");
                dm.setUserid(creator.getUID());
                dm.AddDocInfo();
                dm.AddShareInfo();
                dc.addDocInfoCache("" + docId);
                dv.setDocShareByDoc("" + docId);
            }
            log.info("createDocWithFile  docId  "+docId);
            log.info("createDocWithFile 结束");
            return docId;
        } catch (Exception var18) {
            log.info("文件" + fileName + "上传出错！！"+ SDUtil.getExceptionDetail(var18));
            return -1;
        }
    }

    /**
     * 保存文件，返回文件id
     *
     * @param fis      文件输入流
     * @param fileName
     * @return
     */
    private static int saveFile(InputStream fis, String fileName) {
        boolean needZip = false;
        boolean needZipEncrypt = false;
        SystemComInfo sysComInfo = new SystemComInfo();
        if (sysComInfo.getNeedzip().equals("1")) {
            needZip = true;
        }

        String createDir = FileUpload.getCreateDir(sysComInfo.getFilesystem());
        File parentDir = new File(createDir);
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }

        String newFileName = weaver.general.Util.getRandom();
        if (needZip) {
            newFileName = newFileName + ".zip";
        }

        File newFile;
        for (newFile = new File(createDir, newFileName); newFile.exists(); newFile = new File(createDir, newFileName)) {
            newFileName = weaver.general.Util.getRandom();
            if (needZip) {
                newFileName = newFileName + ".zip";
            }
        }

        try {
            byte[] buffer = new byte[65536];
            long filesize = 0L;
            String contentType = "text/plain";
            int buf;
            if (needZip) {
                ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(Files.newOutputStream(newFile.toPath())));
                zos.setMethod(8);
                zos.putNextEntry(new ZipEntry(FileUtil.mainName(newFileName)));

                while ((buf = fis.read(buffer)) != -1) {
                    zos.write(buffer, 0, buf);
                    filesize += (long) buf;
                }

                zos.flush();
                zos.close();
            } else {
                OutputStreamWriter osw = new OutputStreamWriter(new BufferedOutputStream(Files.newOutputStream(newFile.toPath())), StandardCharsets.UTF_8);
                BufferedWriter writer = new BufferedWriter(osw);

                while ((buf = fis.read(buffer)) != -1) {
                    writer.write(new String(buffer, 0, buf, StandardCharsets.UTF_8));
                    filesize += (long) buf;
                }

                writer.flush();
                writer.close();


//                BufferedOutputStream zos;
//                for (zos = new BufferedOutputStream(Files.newOutputStream(newFile.toPath())); (buf = fis.read(buffer)) != -1; filesize += (long) buf) {
//                    zos.write(buffer, 0, buf);
//                }
//
//                zos.flush();
//                zos.close();
            }

            fis.close();
            String imagefileused = "1";
            String iszip = "0";
            String isencrypt = "0";
            if (needZip) {
                iszip = "1";
            }

            if (needZipEncrypt) {
                isencrypt = "1";
            }

            ImageFileIdUpdate imageFileIdUpdate = new ImageFileIdUpdate();
            int imgId = imageFileIdUpdate.getImageFileNewId();
            char separator = weaver.general.Util.getSeparator();
            String para = "" + imgId + separator + fileName + separator + contentType + separator + imagefileused + separator + createDir + newFileName + separator + iszip + separator + isencrypt + separator + filesize;
            RecordSet rs = new RecordSet();
            boolean flagBool = rs.executeProc("ImageFile_Insert", para);
            return !flagBool ? 0 : imgId;
        } catch (Exception var24) {
            log.error("文件" + fileName + "上传出错！！");
            return 0;
        }
    }

}
