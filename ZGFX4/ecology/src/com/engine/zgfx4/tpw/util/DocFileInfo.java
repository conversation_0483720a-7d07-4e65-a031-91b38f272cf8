package com.engine.zgfx4.tpw.util;


import lombok.Data;

/**
 * @FileName DocFileInfo.java
 * @Description OA中的文档文件信息
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/7
 */
@Data
public class DocFileInfo implements Cloneable {
    /**
     * 文档id
     */
    private String docid;
    /**
     * 最新版本的文档id
     */
    private String maxdocid;
    /**
     * 文件id
     * 即 imagefileid
     */
    private String fileid;
    /**
     * 版本id
     */
    private String versionId;
    /**
     * 文件名 带后缀
     * 例如 index.js
     */
    private String fileName;
    /**
     * 文件http下载url 有登录OA的请求适用
     */
    private String fileDownloadUrl;
    /**
     * 文件http下载url 未登录OA的请求适用
     */
    private String noLoginFileDownloadUrl;
    /**
     * 文档创建日期
     */
    private String operateDate;
    /**
     * 文档上传人
     */
    private String operateUserId;
    /**
     * 文件物理存储路径
     */
    private String filerealpath;

    /**
     * 浅拷贝
     * 浅拷贝可以在不复制整个对象图的情况下创建一个新对象，并在新对象中修改需要更改的属性。
     *
     * @return
     * @throws CloneNotSupportedException
     */
    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
