package com.engine.zgfx4.tpw.util;

import lombok.Data;
import weaver.general.TimeUtil;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @FileName SDLogUtil.java
 * @Description 二开工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/5/22
 */
@Data
public class SDLogUtil {

    // 使用并发安全的队列存储日志条目
    private final ConcurrentLinkedQueue<String> logQueue = new ConcurrentLinkedQueue<>();

    // 使用AtomicInteger保证计数唯一性
    private final AtomicInteger logCount = new AtomicInteger(0);


    /**
     * 添加日志（无锁实现）
     *
     * @param msg 日志消息
     */
    public void appendLog(String msg) {
        // 无锁方式添加日志
        int count = logCount.incrementAndGet();
        String logEntry = count + ".  " + TimeUtil.getCurrentTimeString() + " : " + msg;
        logQueue.add(logEntry);
    }

    /**
     * 获取完整日志内容
     */
    public String getFullLog() {
        StringBuilder sb = new StringBuilder();
        // 遍历队列构建完整日志
        for (String entry : logQueue) {
            sb.append(entry).append("\n");
        }
        return sb.toString();

    }

    /**
     * 获取日志数量
     */
    public int getLogCount() {
        return logCount.get();
    }

    /**
     * 清空日志
     */
    public void clear() {
        logQueue.clear();
        logCount.set(0);
    }
}