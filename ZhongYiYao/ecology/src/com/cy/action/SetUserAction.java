package com.cy.action;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;

public class SetUserAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {

        HashMap<String,String> map = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }

        int userId = requestInfo.getRequestManager().getUserId();
        String contractname = Util.null2String(map.get("hymc"));
        if("".equals(contractname)){
            requestInfo.getRequestManager().setMessagecontent("hymc不能为空");
           return Action.FAILURE_AND_CONTINUE;
        }
        String check = Util.null2String(map.get("sfcj"));//0 shi
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT chry,bchry,chrs,bchrs FROM uf_hyhz where hymc = ?";
        recordSet.executeQuery(sql,contractname);
        String attend = "";
        String unattend = "";
        int attendnum = 0;
        int unattendnum = 0;
        while(recordSet.next()){
             attend = Util.null2String(recordSet.getString("chry")) ;
             unattend = Util.null2String(recordSet.getString("bchry"));
             attendnum = recordSet.getInt("chrs");
             unattendnum = recordSet.getInt("bchrs");
        }
        attend="".equals(attend)?"0":attend;
        unattend="".equals(unattend)?"0":unattend;
        attendnum = attendnum==-1?0:attendnum;
        unattendnum = unattendnum ==-1?0:unattendnum;
        if ("0".equals(check)){
            attend+=","+userId;
            attendnum++;
        }else{
            unattend +=","+userId;
            unattendnum++;
        }

        String update = "UPDATE uf_hyhz SET  chry = ? ,bchry = ?,chrs = ?,bchrs = ? where hymc = ?";
        recordSet.executeQuery(update,attend,unattend,attendnum,unattendnum,contractname);
        return Action.SUCCESS;
    }
}
