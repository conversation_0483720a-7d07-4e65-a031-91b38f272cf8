package com.cloudstore.eccom;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.esb.EsbUtil;
import com.weaver.base.msgcenter.channel.IMessageReceive;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.List;
import java.util.Map;

public class SubTest extends BaseBean implements IMessageReceive {


    public SubTest() {

    }

    @Override
    public void onMessage(String paramString) {
        writeLog("onMessage：" + paramString);
        JSONArray array = JSONArray.parseArray(paramString);
        RecordSet recordSet = new RecordSet();
        if (array != null) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                writeLog("onMessage：--" + jsonObject.toJSONString());
                JSONArray users = jsonObject.getJSONArray("userList");
                for (int j = 0; j < users.size(); j++) {
                    String userid = Util.null2String(users.getString(j));
                    if (!"".equals(userid)) {
                        String sql = "SELECT MOBILE,LASTNAME from hrmresource where id = '" + userid + "'";
                        recordSet.execute(sql);
                        if (recordSet.next()) {
                            String mobile = Util.null2String(recordSet.getString("MOBILE"));
                            String lastname = Util.null2String(recordSet.getString("LASTNAME"));
                            if (!"".equals(mobile)) {
                                String title = getindexString(jsonObject.getString("detailTitle"), "7");
                                JSONObject ans = new JSONObject();
                                JSONObject theobj = new JSONObject();
                                JSONArray jsonArray = new JSONArray();
                                theobj.put("mobile", mobile);
                                theobj.put("name", "尊敬的 " + lastname);
                                jsonArray.add(theobj);
                                ans.put("receivers", jsonArray);
                                ans.put("content", title + "。" + jsonObject.getString("title"));
                                EsbUtil.executeESBUseName("SendMessageActivity", ans);
                            }
                        }
                    }
                }
            }
        }
    }


    public String getindexString(String string, String index) {
        String[] strings = string.split("`");
        for (String str : strings) {
            if (str.indexOf('~') < 0 && str.indexOf('$') < 0) {
                String[] array = str.split(" ");
                if (array.length > 1) {
                    if (index.equals(array[0])) {
                        return array[1];
                    }
                }
            }
        }
        return string.indexOf('$') >= 0 ? string.replace('$', ' ') : string;
    }

    public String gettoken() {
        return "";
    }

    @Override
    public void onStart() {
        writeLog("onStart");
    }

    @Override
    public void onEnd() {
        writeLog("onEnd");
    }

    @Override
    public void setProp(Map<String, String> paramMap) {
        writeLog("setProp");
    }

    @Override
    public void setList(List<IMessageReceive> paramList) {
        writeLog("setList");
    }

}