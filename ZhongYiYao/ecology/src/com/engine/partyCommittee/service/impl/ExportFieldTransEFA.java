package com.engine.partyCommittee.service.impl;


import weaver.conn.RecordSet;
import weaver.formmode.interfaces.ExportFieldTransAction;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Map;

public class ExportFieldTransEFA implements ExportFieldTransAction {


    @SuppressWarnings("unchecked")
    @Override
    public String getTransValue(Map<String, Object> param, User user) {
        BaseBean _bb = new BaseBean();
        String sql;
        String eachValue;
        String returnValue = "";
        try {
            _bb.writeLog("ExportFieldTransEFA ---START");
            // 获取当前登录人员ID
            Integer userId = user.getUID();
            // 获取模块ID
            int modeId = Util.getIntValue(param.get("modeid").toString());
            //表单id
            int formId = Util.getIntValue(param.get("formid").toString());
            //当前字段id
            String fieldid = Util.null2String(param.get("fieldid"));
            //查询列表id
            String customid = Util.null2String(param.get("customid"));
            //当前列名称(明细表会有d_前缀)
            String columnname = Util.null2String(param.get("columnname"));
            //当前列数据
            StringBuilder sbValue = new StringBuilder(Util.null2String(param.get("value")));
            //当前行数据
            Map<String, Object> data = (Map<String, Object>) param.get("data");
            RecordSet rs = new RecordSet();
            //流程id
            String requestId = Util.null2String(data.get("lylc"));
            _bb.writeLog(userId + " " + modeId + " " + formId + " " + fieldid + " " + customid + " " + columnname + " " + requestId);
            String hbbmyjNodeIds = Util.null2String(_bb.getPropValue("ZYY_workflow", "hbbmyj_nodeids"));
            String fgbmyjNodeIds = Util.null2String(_bb.getPropValue("ZYY_workflow", "fgbmyj_nodeids"));
            String nodeids = "";
            //会办部门意见：
            if ("hbbmyj".equals(columnname)) {
                nodeids = hbbmyjNodeIds;
                //分管校领导意见
            } else if ("fgbmyj".equals(columnname)) {
                nodeids = fgbmyjNodeIds;
            }
            sql = "SELECT " +
                    " d.DEPARTMENTNAME || '/' || h.LASTNAME || '：' || regexp_replace( a.remark, '</?[^>]*>|nbsp;|&', '' ) AS remark  " +
                    "FROM " +
                    " WORKFLOW_REQUESTLOG a " +
                    " LEFT JOIN HRMRESOURCE h ON ( a.OPERATOR = h.id ) " +
                    " LEFT JOIN HRMDEPARTMENT d ON ( h.DEPARTMENTID = d.id )  " +
                    "WHERE " +
                    " a.requestid = ?  " +
                    " AND a.nodeid in ("+nodeids+") ";
            rs.executeQuery(sql, requestId);
            while (rs.next()) {
                eachValue = Util.null2String(rs.getString("remark"));
                sbValue.append("\n").append(eachValue);
            }
            returnValue = sbValue.toString();

        } catch (Exception e) {
            e.printStackTrace();
            _bb.writeLog(e.getMessage());
        }
        _bb.writeLog("ExportFieldTransEFA ---END");
        return returnValue;
    }
}
