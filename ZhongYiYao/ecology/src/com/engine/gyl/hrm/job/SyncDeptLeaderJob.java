package com.engine.gyl.hrm.job;

import com.engine.parent.common.util.SDUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName SyncDeptLeaderJob
 * @Description 同步部门负责人，同步人员排序
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/2
 */
public class SyncDeptLeaderJob extends BaseCronJob {

    private final BaseBean bb = new BaseBean();

    @Override
    public void execute() {
        bb.writeLog("SyncDeptLeaderJob --START");
        //上级部门id，部门负责人员列表
        String supdepid, ry_list;
        //第1项内容，更新部门负责人
        try {
            //step 1 : 查询所有的负责人部门的上级部门和负责人部门的所有人员，这些人员代表部门负责人
            //查询规则：所有CC=3，并且末3位是001的，排除DM为'001001'的部门，这些部门下的人员为部门负责人
            String sql = "SELECT " +
                    " a.supdepid, " +
                    " LISTAGG ( b.id, ',' ) WITHIN GROUP ( ORDER BY b.dsporder ) AS ry_list  " +
                    " FROM " +
                    " HRMDEPARTMENT a " +
                    " LEFT JOIN HRMRESOURCE b ON ( a.id = b.DEPARTMENTID AND b.status < 5 )  " +
                    " WHERE " +
                    " DEPARTMENTCODE IN ( SELECT a.dm FROM UF_DWST a WHERE a.CC = 3 AND SUBSTR( a.DM, - 3 ) = '001' AND a.DM != '001001' )  " +
                    " GROUP BY " +
                    " a.supdepid, " +
                    " a.DEPARTMENTNAME ";
            RecordSet rs = new RecordSet();
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    //上级部门id
                    supdepid = Util.null2String(rs.getString("SUPDEPID"));
                    ry_list = Util.null2String(rs.getString("RY_LIST"));
                    bb.writeLog("supdepid:" + supdepid);
                    bb.writeLog("ry_list:" + ry_list);
                    //step 2:找到这些所有的下级部门,并更新矩阵里的部门负责人字段
                    updateSubDeptLeader(supdepid, ry_list);
                }
            }
        } catch (Exception e) {
            bb.writeLog(SDUtil.getExceptionDetail(e));
        }

        //第2项内容，更新人员排序-2023-06-27 去除该逻辑，需要手动调整排序
//        RecordSet rs = new RecordSet();
//        //按照工号的顺序，更新人员排序字段
//        String sql = "UPDATE hrmresource a " +
//                "SET a.dsporder = ( " +
//                "SELECT new_order " +
//                "FROM ( " +
//                "SELECT id, ROW_NUMBER() OVER (ORDER BY workcode) AS new_order " +
//                "FROM hrmresource " +
//                ") temp " +
//                "WHERE a.id = temp.id " +
//                ") ";
//        if (!rs.executeUpdate(sql)) {
//            bb.writeLog("更新人员排序sql出错：" + rs.getMsg() + ";" + rs.getExceptionMsg());
//        }
        bb.writeLog("SyncDeptLeaderJob --END");
    }

    /**
     * 查询一个部门的所有下级部门，并更新部门负责人
     *
     * @param supdepid
     * @param ryList
     */
    private void updateSubDeptLeader(String supdepid, String ryList) {
        RecordSet rs = new RecordSet();
        String id;
        //查询该上级部门的所有下级部门，包含自身
        String sql = " " +
                "SELECT id,DEPARTMENTNAME FROM hrmdepartment " +
                "CONNECT BY PRIOR id = supdepid " +
                "START WITH id = ? ";
        if (rs.executeQuery(sql, supdepid)) {
            while (rs.next()) {
                //部门id
                id = Util.null2String(rs.getString("id"));
                bb.writeLog("supdepid:" + supdepid + ",deptid:" + id);
                doUpdateSubDeptLeader(id, ryList);
            }
        }
    }

    /**
     * 更新部门的部门负责人值，部门矩阵表，和部门自定义表
     *
     * @param deptId
     * @param ryList
     */
    private void doUpdateSubDeptLeader(String deptId, String ryList) {
        RecordSet rs = new RecordSet();
        String sql = "update matrixtable_2 set BMFZR = ? where id = ?";
        rs.executeUpdate(sql, ryList, deptId);
        RecordSet rs1 = new RecordSet();
        String sql1 = "update hrmdepartmentdefined set BMFZR = ? where deptid = ?";
        rs1.executeUpdate(sql1, ryList, deptId);

    }

}
