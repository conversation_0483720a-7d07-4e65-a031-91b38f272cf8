package com.engine.workflow.service.impl;

import com.engine.core.impl.Service;
import com.engine.workflow.service.NewButtonConfigService;
import com.engine.workflow.service.cmd.NewButtonConfigCmd;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 */
public class NewButtonConfigServiceImpl extends Service implements NewButtonConfigService {
    @Override
    public Map<String, Object> refreshData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new NewButtonConfigCmd(params, user));
    }
}
  