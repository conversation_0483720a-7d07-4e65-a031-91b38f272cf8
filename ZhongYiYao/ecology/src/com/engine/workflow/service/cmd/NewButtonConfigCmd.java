package com.engine.workflow.service.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 新按钮配置CMD
 */
public class NewButtonConfigCmd extends AbstractCommonCommand<Map<String, Object>> {

    public NewButtonConfigCmd() {
    }

    public NewButtonConfigCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String nodedid = Util.null2String(params.get("nodeid"));
        String table = Util.null2String(params.get("table"));
        if("".equals(nodedid)){
            result.put("status","-1");
            result.put("dec","前端获取节点信息错误");
            return result;
        }
        RecordSet recordSet = new RecordSet();
        String sql ="SELECT\n" +
                "\t* \n" +
                "FROM " +
                table +
                " WHERE\n" +
                "\tlcjdxz = '"+nodedid+"'";
        recordSet.execute(sql);
        String mainid = "";
        JSONObject ans = new JSONObject();
        while (recordSet.next()){
            String cols  = Util.null2String(recordSet.getString("SJZD"));
            String detailcols = Util.null2String(recordSet.getString("mxzd"));
            String detailcondition = Util.null2String(recordSet.getString("mxqztj"));
            mainid = Util.null2String(recordSet.getString("id"));
            String condition =  Util.null2String(recordSet.getString("qztj"));
            String[] colarray = cols.split(",");
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(Arrays.asList(colarray));
            String[] colarraydetail = detailcols.split(",");
            JSONArray jsonArraydetail = new JSONArray();
            jsonArraydetail.addAll(Arrays.asList(colarraydetail));
            ans.put("cols",jsonArray);
            ans.put("detailcols",jsonArraydetail);
            ans.put("condition",condition);
            ans.put("detailcondition",detailcondition);
        }
        ans.put("words",getdetail(mainid,table));
        result.put("jsondata",ans);
        result.put("status","1");
        return result;
    }

    public JSONArray getdetail(String mainid,String table){
        JSONArray jsonArray =new JSONArray();
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT\n" +
                "\t* \n" +
                "FROM\n" +
                table+"_DT1 \n" +
                "WHERE\n" +
                "\tMAINID = '"+mainid+"'";
        recordSet.execute(sql);
        while(recordSet.next()){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("word",Util.null2String(recordSet.getString("MGC")).trim().replace (" ",""));
            jsonObject.put("wordext",Util.null2String(recordSet.getString("TL")).trim().replace (" ",""));
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

}
