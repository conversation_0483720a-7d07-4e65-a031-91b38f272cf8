package com.engine.workflow.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.workflow.service.NewButtonConfigService;
import com.engine.workflow.service.impl.NewButtonConfigServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 新按钮配置Web
 */
public class NewButtonConfigWeb {

    private NewButtonConfigService getService(User user) {
        return ServiceUtil.getService(NewButtonConfigServiceImpl.class, user);
    }


    /**
     * 重新生成数据
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshData")
    @Produces(MediaType.TEXT_PLAIN)
    public String refreshData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).refreshData(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }

}
