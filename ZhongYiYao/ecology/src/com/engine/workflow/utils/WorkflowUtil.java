package com.engine.workflow.utils;

import com.weaver.general.Util;
import weaver.conn.RecordSet;

public class WorkflowUtil {

    public static String getworkflowid(String nodeid){
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT\n" +
                "\tt1.workflowid,\n" +
                "\tt2.workflowname,\n" +
                "\tt1.nodeid,\n" +
                "\tt3.nodename,\n" +
                "\tt2.version,\n" +
                "\tt2.isvalid \n" +
                "FROM\n" +
                "\tworkflow_flownode t1\n" +
                "\tLEFT JOIN workflow_base t2 ON t1.workflowid = t2.id\n" +
                "\tLEFT JOIN workflow_nodebase t3 ON t1.nodeid = t3.id \n" +
                "\tWHERE NODEID = '"+ nodeid+"'";
        recordSet.execute(sql);
        if(recordSet.next()){
            return Util.null2String(recordSet.getString("workflowid"));
        }
        return "";
    }

}
