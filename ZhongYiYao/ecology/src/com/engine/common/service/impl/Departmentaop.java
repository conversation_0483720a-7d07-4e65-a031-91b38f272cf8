package com.engine.common.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.weaverboot.frame.ioc.anno.classAnno.WeaIocReplaceComponent;
import com.weaverboot.frame.ioc.anno.methodAnno.WeaReplaceAfter;
import com.weaverboot.frame.ioc.anno.methodAnno.WeaReplaceBefore;
import com.weaverboot.frame.ioc.handler.replace.weaReplaceParam.impl.WeaAfterReplaceParam;
import com.weaverboot.frame.ioc.handler.replace.weaReplaceParam.impl.WeaBeforeReplaceParam;
import com.weaverboot.tools.logTools.LogTools;
import weaver.conn.RecordSet;
import weaver.general.Util;



/**
 * 无侵入式接口拦截处理
 */
@WeaIocReplaceComponent
public class Departmentaop {

    @WeaReplaceAfter(value = "/api/hrm/organization/editDepartment",order = 1,description = "测试拦截后置")
    public String depchange(WeaAfterReplaceParam weaAfterReplaceParam){
        String depid = Util.null2String(weaAfterReplaceParam.getParamMap().get("id"));
        String type =  Util.null2String(weaAfterReplaceParam.getParamMap().get("cjlx"));
        String code = Util.null2String(weaAfterReplaceParam.getParamMap().get("departmentcode"));
        String data =  weaAfterReplaceParam.getData();
        JSONObject result = JSONObject.parseObject(data);
        if(!"1".equals(result.getString("status"))||"".equals(type)||"".equals(depid)){
            return result.toJSONString();
        }
        RecordSet recordSet = new RecordSet();
        recordSet.execute("select * from uf_bmbh where bmid = '"+depid+"'");
        if(recordSet.next()){
            if(!"0".equals(type)){
                RecordSet rs = new RecordSet();
                String depcode = Util.null2String(recordSet.getString("BH"));
                if(depcode.equals(code)){
                    rs.execute("update hrmdepartment set DEPARTMENTCODE = null where id = '"+depid+"'");
                }
                rs.execute("update uf_bmbh set BMID = null where bmid = '"+depid+"'");
            }
        }else{
            if("0".equals(type)){
                this.adddepcode(depid);
            }
        }
        return result.toJSONString();
    }

    @WeaReplaceAfter(value = "/api/hrm/organization/addDepartment",order = 1,description = "测试拦截后置")
    public String depadd(WeaAfterReplaceParam weaAfterReplaceParam){
        String data =  weaAfterReplaceParam.getData();
        JSONObject result = JSONObject.parseObject(data);
        String id =  Util.null2String(result.getString("id"));
        String status = Util.null2String(result.getString("status"));
        String type =  Util.null2String(weaAfterReplaceParam.getParamMap().get("cjlx"));
        if(!"1".equals(status)||"".equals(id)){
            return result.toJSONString();
        }
        if(!"0".equals(type)){
            return result.toJSONString();
        }
        this.adddepcode(id);
        return result.toJSONString();
    }



    @WeaReplaceBefore(value = "/api/hrm/organization/addDepartment",order = 1,description = "测试拦截前置")
    public void testbefore(WeaBeforeReplaceParam weaBeforeReplaceParam){
        //一顿操作

        LogTools.info("before:/api/workflow/reqlist/splitPageKey");
    }


    private void adddepcode(String id){
        RecordSet recordSet = new RecordSet();
        recordSet.execute("select * from uf_bmbh ORDER by sx desc ");
        String index = "";
        boolean flag = false;
        while(recordSet.next()) {
            if(!"".equals(Util.null2String(recordSet.getString("BMID")))){
                index = Util.null2String(recordSet.getString("SX"));
                break;
            }
            flag = true;
        }
        if(!flag){
            return ;
        }
        if("".equals(index)){
            index = "0";
        }
        int indexaddone = Integer.parseInt(index)+1;
        recordSet.execute("select * from uf_bmbh where SX = '"+indexaddone+"'");
        if(recordSet.next()){
            String bh =  Util.null2String(recordSet.getString("BH"));
            RecordSet rs = new RecordSet();
            rs.executeUpdate("update uf_bmbh set bmid = ? where bh = '"+bh+"'",id);
            rs.executeUpdate("update hrmdepartment set DEPARTMENTCODE = ?  where id = '"+id+"' ",bh);
        }
    }

}