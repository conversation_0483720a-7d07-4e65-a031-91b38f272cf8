package com.action.common;

import com.action.utils.Webserviceutils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.company.DepartmentComInfo;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.toolbox.doc.DownloadManager;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

public class Oa2webservice extends BaseBean implements Action {

    private String url;
    private String oaparams;
    private String values;
    private String fileparams;

    @Override
    public String execute(RequestInfo requestInfo) {
        String[] oaparam = oaparams.split(",");
        JSONObject jsonObject = JSON.parseObject(values);
        if(oaparam.length!=17){
            requestInfo.getRequestManager().setMessage("action配置不正确");
            return Action.FAILURE_AND_CONTINUE;
        }
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        HashMap<String,String> map = new HashMap<>();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }
        String doctid = map.get(fileparams);
        String depart1 = map.get(oaparam[3]);
        String depart2 = map.get(oaparam[10]);
        String depart3 = map.get(oaparam[14]);
        String c3="",zrz="",dw="";
        DepartmentComInfo departmentComInfo = new DepartmentComInfo();
        try {
            c3 = departmentComInfo.getDepartmentName(depart1);
            zrz = departmentComInfo.getDepartmentName(depart2);
            dw =   departmentComInfo.getDepartmentName(depart3);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String[] cols = {"lx","c1","c2","c3","mlh","ajh","wjh","bgq","mj","ztm","zrz","sl","gg","sj","dw","wh","flag"};
        HashMap<String,String> hashMap = new HashMap<>();
        for (int i = 0; i < cols.length; i++) {
            hashMap.put(cols[i], Util.null2String(map.get(oaparam[i].trim())));
        }
        hashMap.put("c3",c3);
        hashMap.put("mlh",hashMap.get("c1")+"-"+Util.null2String(jsonObject.get("c2"))+"-"+c3);
        hashMap.put("zrz",zrz);
        hashMap.put("dw",dw);

        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");//设置日期格式
        String time = df.format(new Date());
        hashMap.put("sj",time);

        for(Object str: jsonObject.keySet()){
            hashMap.put((String)str,(String)jsonObject.get(str));
        }
        String result =  Webserviceutils.doSelectRiskReportForm(url,hashMap);
        if(result==null|| Objects.equals(result, "")){
            requestInfo.getRequestManager().setMessagecontent("接口返回不正确");
            return Action.FAILURE_AND_CONTINUE;
        }
        if(!"".equals(doctid)){
            File[] files = new File[0];
            try {
                files = Oa2webservice.getfilepath(doctid);
            } catch (IOException e) {
                requestInfo.getRequestManager().setMessagecontent("获取文件出错");
                return Action.FAILURE_AND_CONTINUE;
            }
            for (int i = 0; i < files.length; i++) {
                String name = files[i].getName();
                Webserviceutils.add_tx(url,Webserviceutils.encryptToBase64(files[i].getPath()),result,name.substring(name.indexOf(".")+1),name);
             }
        }
        return Action.SUCCESS;
    }

    public static File[] getfilepath(String docids) throws IOException {
        String[] docid= docids.split(",");
        File[] files = new File[docid.length];
        for (int j = 0; j < docid.length; j++) {
            files[j] = new File(DownloadManager.getFileByDocId(Integer.parseInt(docid[j])));;
        }
        return files;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getOaparams() {
        return oaparams;
    }

    public void setOaparams(String oaparams) {
        this.oaparams = oaparams;
    }

    public String getValues() {
        return values;
    }

    public void setValues(String values) {
        this.values = values;
    }

    public String getFileparams() {
        return fileparams;
    }

    public void setFileparams(String fileparams) {
        this.fileparams = fileparams;
    }
}
