package com.action.utils;
import com.sap.db.jdbc.Hash;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import javax.xml.namespace.QName;
import javax.xml.rpc.ParameterMode;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Set;


public class Webserviceutils {


    // 字段类型全为string
    public static String doSelectRiskReportForm(String url, HashMap<String,String> hashMap){
        //调用接口
        //方法一:直接AXIS调用远程的web service
        try {
            Object[] value = new Object[hashMap.size()];
            String endpoint =  url;
//           "http://**************/Service/Service.asmx?wsdl";
            Service service = new Service();
            Call call = (Call) service.createCall();
            call.setTargetEndpointAddress(endpoint);
//            call.setOperationName("HelloWorld");  		// 调用的方法名//当这种调用不到的时候,可以使用下面的,加入命名空间名
            call.setOperationName(new QName("Archives", "add_da"));// 调用的方法名
//            call.addParameter(lx, XMLType.XSD_STRING, ParameterMode.IN);//参数名//XSD_STRING:String类型//.IN入参

            Set<String> keySets = hashMap.keySet();
            int i = 0;
            for(String keySet:keySets){					//迭代输出键
                call.addParameter((new QName("Archives",keySet)),XMLType.XSD_STRING, ParameterMode.IN);
                value[i] = hashMap.get(keySet);
                i++;
            }
            call.setReturnType(XMLType.XSD_STRING); 	// 返回值类型：String
//           Object[] value = {"1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19"};
            String result = (String) call.invoke(value);// 远程调用
            System.out.println("result is " + result);
            return result;
        } catch (Exception e) {
            System.err.println(e.toString());
            return "";
        }
    }

    public static String add_tx(String url, String data ,String addvalue,String filend,String filetm){
        //调用接口
        //方法一:直接AXIS调用远程的web service
        try {
            String endpoint =  url;
//           "http://**************/Service/Service.asmx?wsdl";
            Service service = new Service();
            Call call = (Call) service.createCall();
            call.setTargetEndpointAddress(endpoint);
//            String[] strs = {"lx","c1","c2","c3","mlh","mlh","ajh","wjh","wjh","bgq","mj","ztm","zrz","sl","gg","sj","dw","wh","flag"};
//            call.setOperationName("HelloWorld");  		// 调用的方法名//当这种调用不到的时候,可以使用下面的,加入命名空间名
            call.setOperationName(new QName("Archives", "add_da_tx"));// 调用的方法名
////            call.addParameter(lx, XMLType.XSD_STRING, ParameterMode.IN);//参数名//XSD_STRING:String类型//.IN入参
            //迭代输出键
            call.addParameter((new QName("Archives","add_da_values")),XMLType.XSD_STRING, ParameterMode.IN);
            call.addParameter((new QName("Archives","filebyte")), XMLType.XSD_BASE64, ParameterMode.IN);
            call.addParameter((new QName("Archives","filename_ext")),XMLType.XSD_STRING, ParameterMode.IN);
            call.addParameter((new QName("Archives","filetm")),XMLType.XSD_STRING, ParameterMode.IN);
            call.setReturnType(XMLType.XSD_STRING); 	// 返回值类型：String
//           Object[] value = {"1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19"};
            String result = (String) call.invoke(new Object[]{addvalue,data,filend,filetm});// 远程调用
            System.out.println("result is " + result);
            return result;
        } catch (Exception e) {
            System.err.println(e.toString());
            return "";
        }
    }

    public static byte[] getBytes(File file) throws IOException {
        byte[] buffer = null;
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        return buffer;
    }


    public static String encryptToBase64(String filePath) {
        if (filePath == null) {
            return "";
        }
        try {
            byte[] b = Files.readAllBytes(Paths.get(filePath));
            return Base64.getEncoder().encodeToString(b);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return "";
    }


//    public static void sendReportTest(byte[] reportData, String date){
//        try {
//            // 服务端的url，需要根据情况更改。
//            String endpointURL = "http://**************/Service/Service.asmx?wsdl";
//            Service service = new Service();
//            Call call = (Call) service.createCall();
//            call.setTimeout(60000);
//            call.setTargetEndpointAddress(new URL(endpointURL));
////            call.setSOAPActionURI("http://tempuri.org/SendXMLFile");
////            call.setOperationName(new QName("http://tempuri.org/","SendXMLFile"));// 设置操作的名称。
//            // 由于需要认证，故需要设置调用的用户名和密码。
////            SOAPHeaderElement soapHeaderElement = new SOAPHeaderElement("http://tempuri.org/", "UserSoapHeader");
////            soapHeaderElement.setNamespaceURI("http://tempuri.org/");
////            try {
////                soapHeaderElement.addChildElement("UserId").setValue("USER_ID");
////                soapHeaderElement.addChildElement("PassWord").setValue("");
////            } catch (SOAPException e) {
////                e.printStackTrace();
////            }
////            call.addHeader(soapHeaderElement);
//            call.setReturnType(XMLType.XSD_STRING);// 返回的数据类型
////            call.addParameter(new QName("http://tempuri.org/","xmlProjectData"), XMLType.XSD_BASE64, ParameterMode.IN);// 参数的类型
//            call.addParameter(new QName("Archives","reportDate"), XMLType.XSD_STRING, ParameterMode.IN);// 参数的类型
//
//            String result = (String) call.invoke(new Object[]{reportData,date});// 执行调用
//            // 结果信息解析
//            Document document = DocumentHelper.parseText(result);
//            Element rootElement = document.getRootElement();
//            Iterator iter = rootElement.elementIterator("State");
//            while(iter.hasNext()){
//                Element recordEle = (Element) iter.next();
//                String code = recordEle.getTextTrim();// State值
//                if("0".equals(code)){ //成功
//                    Logger.getRootLogger().error("调用接口成功");
//                }else{ // 失败保存log
//                    Logger.getRootLogger().error(result);
//                }
//            }
//        } catch (Exception e) {
//            Logger.getRootLogger().error("调用接口失败",e);
//        }
//    }

}
