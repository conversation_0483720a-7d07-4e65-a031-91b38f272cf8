package com.engine.zhongzhi3.gyl.module.working.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi3.gyl.module.working.bean.ArchiveTravelCost;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName ArchiveTravelCostCmd.java
 * @Description 归档保存工时的差旅费
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
public class ArchiveTravelCostCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 错误信息
     */
    private String error;
    /**
     * 数据库类
     */
    private RecordSet rs;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public ArchiveTravelCostCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * 初始化
     */
    private void init() {
        error = "";
        rs = DBUtil.getThreadLocalRecordSet();
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        ArchiveTravelCost travelCost;
        try {
            //初始化
            init();
            //校验参数
            checkParams();
            if (error.isEmpty()) {
                //查工时的视图数据
                String sql = Util.null2String(params.get("sql"));
                log.info("sql:" + sql);
                if (rs.executeQuery(sql)) {
                    List<ArchiveTravelCost> list = new ArrayList<>();
                    while (rs.next()) {
                        travelCost = new ArchiveTravelCost();
                        travelCost.setProjectid(Util.getIntValue(rs.getString("prjid")));
                        travelCost.setProject_name(Util.null2String(rs.getString("prjname")));
                        travelCost.setProject_code(Util.null2String(rs.getString("procode")));
                        travelCost.setTravel_cost(SDUtil.getBigDecimalValue(rs.getString("clf"), null));
                        list.add(travelCost);
                    }
                    if (!list.isEmpty()) {
                        //保存数据
                        error = ArchiveTravelCost.save(list);
                    } else {
                        error = "没有查询到工时视图数据";
                    }
                } else {
                    error = "查询工时视图出错:" + rs.getExceptionMsg();
                }
            }

        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 校验参数
     */
    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        String[] requiredCols = new String[]{"sql"};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
        }
    }


}
