package com.engine.zhongzhi3.gyl.module.caiwudaoru.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.Modeviewlog;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.vo.CaiwuDaoruConst;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName ChangeAmountWrongStatusCmd.java
 * @Description 将匹配状态从【金额异常】-改为【金额异常（已核对）】
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/2/12
 */
public class ChangeAmountWrongStatusCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 错误信息
     */
    private String error;
    /**
     * 建模数据操作日志
     */
    private final List<Modeviewlog> modeviewlogList;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public ChangeAmountWrongStatusCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        error = "";

        modeviewlogList = new ArrayList<>();
    }

    /**
     * 这里加锁，防止多人同时操作,导致查询和更新不一致
     *
     * @param commandContext
     * @return
     */
    @Override
    public synchronized Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        try {
            //校验参数 无必填项校验
            //checkParams();
            if (error.isEmpty()) {
                //校验状态
                checkStatus();
                if (error.isEmpty()) {
                    //处理当前勾选的财务导入的数据
                    handleImportData();
                    //插入建模操作日志
                    if (!modeviewlogList.isEmpty()) {
                        int modid = ModuleDataUtil.getModuleIdByName(CaiwuDaoruConst.CAIWUDAORU_TABLE);
                        ModuleDataUtil.insertModuleLog(modeviewlogList, modid);
                    }
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 处理财务导入的数据
     */
    private void handleImportData() {
        String updateSql;
        int daoruBillid;
        String checkedIds = Util.null2String(params.get("checkedIds"));
        //获取所有状态为空的导入数据
        String sql = "select * from " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
                " where 1=1  ";
        if (!checkedIds.isEmpty()) {
            sql += " and id in (" + checkedIds + ") ";
        }
        log.info("handleImportData sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                daoruBillid = rs.getInt("id");//财务导入数据id
                updateSql = "update " + CaiwuDaoruConst.CAIWUDAORU_TABLE + " set ppzt = 3 where id =" + daoruBillid;
                if (!rs.executeUpdate(updateSql)) {
                    log.error("updateStatus 出错:" + rs.getExceptionMsg() + ";sql:" + sql);
                } else {
                    Modeviewlog modeviewlog = new Modeviewlog();
                    modeviewlog.setRelatedid(daoruBillid);
                    modeviewlog.setRelatedname("-");
                    modeviewlog.setOperatetype(2);
                    modeviewlog.setOperatedesc("二开ChangeAmountWrongStatusCmd更新分配状态,更新字段如下：1、ppzt：3");
                    modeviewlog.setOperateuserid(user.getUID());
                    modeviewlog.setOperatedate(TimeUtil.getToday());
                    modeviewlog.setOperatetime(TimeUtil.getOnlyCurrentTimeString());
                    modeviewlog.setClientaddress("");
                    modeviewlogList.add(modeviewlog);
                }
            }
        }
    }

    /**
     * 校验状态
     * 只能操作【金额异常】/ 【失败】这两种状态的数据，并且要有绑定的GT2数据
     */
    private void checkStatus() {
        int count = 0;
        String checkedIds = Util.null2String(params.get("checkedIds"));
        //校验勾选的数据，或者全部数据，是否有符合条件的数据
        String sql = "select count(id) as cnt from " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
                " where 1=1 " +
                " and ppzt in (1,2) " +
                " and bdgt2bxsj is not null ";
        if (!checkedIds.isEmpty()) {
            sql += " and id in (" + checkedIds + ") ";
        }
        log.info("checkStatus sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                count = Util.getIntValue(rs.getString("cnt"));
            }
        }
        int checkedDataCount;
        if (!checkedIds.isEmpty()) {
            String[] checkedIdArray = checkedIds.split(",");
            checkedDataCount = checkedIdArray.length;
        } else {
            checkedDataCount = getTotalDataCount();
        }

        if (count != checkedDataCount) {
            error = "只能更新匹配状态为【金额异常】/【失败】的，且绑定了GT2报销数据的，进行状态更新!";
            log.error(error);
        }
    }

    private int getTotalDataCount() {
        int count = 0;
        //获取所有状态为空的导入数据
        String sql = "select count(id) as cnt from " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
                " where 1=1  ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                count = Util.getIntValue(rs.getString("cnt"));
            }
        }
        return count;
    }


    /**
     * 校验参数
     */
    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        //勾选的数据id
        //建模表单主表名,uf_cwdrsj
        //建模模块id
        String[] requiredCols = new String[]{"checkedIds"};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
            log.error(error);
        }
    }


}
