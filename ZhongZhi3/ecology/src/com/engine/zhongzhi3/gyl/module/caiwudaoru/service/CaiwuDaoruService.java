package com.engine.zhongzhi3.gyl.module.caiwudaoru.service;

import weaver.hrm.User;

import java.util.Map;


public interface CaiwuDaoruService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> allocate(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> changeAmountWrongStatus(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> splitData(Map<String, Object> params, User user);
}
