package com.engine.zhongzhi3.gyl.module.working.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi3.gyl.module.working.cmd.ArchiveTravelCostCmd;
import com.engine.zhongzhi3.gyl.module.working.service.WorkingService;
import weaver.hrm.User;

import java.util.Map;


public class WorkingServiceImpl extends Service implements WorkingService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> archiveTravelCost(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ArchiveTravelCostCmd(params, user));
    }
}
