package com.engine.zhongzhi3.gyl.module.working.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi3.gyl.module.working.service.WorkingService;
import com.engine.zhongzhi3.gyl.module.working.service.impl.WorkingServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName WorkingWeb.java
 * @Description 工时
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
public class WorkingWeb {

    private WorkingService getService(User user) {
        return ServiceUtil.getService(WorkingServiceImpl.class, user);
    }

    /**
     * 归档保存工时的差旅费
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/archiveTravelCost")
    @Produces(MediaType.TEXT_PLAIN)
    public String archiveTravelCost(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).archiveTravelCost(params, user)
        );
    }

}
