package com.engine.zhongzhi3.gyl.module.working.bean;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import lombok.Data;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;

import java.math.BigDecimal;
import java.util.List;

/**
 * @FileName ArchiveTravelCost.java
 * @Description 工时归档的差旅费
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
@Data
public class ArchiveTravelCost {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 归档日期
     */
    private String archive_date;
    /**
     * 归档时间
     */
    private String archive_time;
    /**
     * 项目id
     */
    private Integer projectid;
    /**
     * 项目名称
     */
    private String project_name;
    /**
     * 项目编号
     */
    private String project_code;
    /**
     * 差旅费
     */
    private BigDecimal travel_cost;

    /**
     * 建模表名
     */
    public static final String TABLE_NAME = "uf_gsgdclf";

    /**
     * 保存数据
     *
     * @param beanList
     * @return 错误信息
     */
    public static String save(List<ArchiveTravelCost> beanList) {
        String error = "";
        try {
            //获取建模id
            int modeid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            String today = TimeUtil.getToday();//归档日期
            String currentTime = SDTimeUtil.getCurrentTimeMillliString();//归档时间
            for (ArchiveTravelCost bean : beanList) {
                bean.setArchive_date(today);
                bean.setArchive_time(currentTime);
            }
            //先删除数据
            clearTable();
            ModuleResult mr = ModuleDataUtil.insertObjList(beanList, TABLE_NAME, modeid, 1);
            if (!mr.isSuccess()) {
                error = mr.getErroMsg();
            }
        } catch (Exception e) {
            error = "保存数据异常:" + SDUtil.getExceptionDetail(e);
        }

        return error;
    }

    private static void clearTable() {
        String sql = "TRUNCATE TABLE " + TABLE_NAME;
        RecordSet rs = new RecordSet();
        rs.executeUpdate(sql);
    }
}
