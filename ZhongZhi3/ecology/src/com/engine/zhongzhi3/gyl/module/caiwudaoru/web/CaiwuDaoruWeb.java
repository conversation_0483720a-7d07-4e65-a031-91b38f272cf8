package com.engine.zhongzhi3.gyl.module.caiwudaoru.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.service.CaiwuDaoruService;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.service.impl.CaiwuDaoruServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName CaiwuDaoruWeb.java
 * @Description 财务导入数据
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/2/12
 */
public class CaiwuDaoruWeb {

    private CaiwuDaoruService getService(User user) {
        return ServiceUtil.getService(CaiwuDaoruServiceImpl.class, user);
    }

    /**
     * 分配
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/allocate")
    @Produces(MediaType.TEXT_PLAIN)
    public String allocate(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).allocate(params, user)
        );
    }

    /**
     * 将匹配状态从【金额异常】-改为【金额异常（已核对）】
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/changeAmountWrongStatus")
    @Produces(MediaType.TEXT_PLAIN)
    public String changeAmountWrongStatus(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).changeAmountWrongStatus(params, user)
        );
    }

    /**
     * 拆分
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/splitData")
    @Produces(MediaType.TEXT_PLAIN)
    public String splitData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).splitData(params, user)
        );
    }

}
