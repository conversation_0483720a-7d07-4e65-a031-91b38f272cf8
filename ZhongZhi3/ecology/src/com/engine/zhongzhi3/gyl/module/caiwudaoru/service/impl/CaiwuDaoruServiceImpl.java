package com.engine.zhongzhi3.gyl.module.caiwudaoru.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.cmd.AllocateCmd;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.cmd.ChangeAmountWrongStatusCmd;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.cmd.SplitDataCmd;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.service.CaiwuDaoruService;
import weaver.hrm.User;

import java.util.Map;


public class CaiwuDaoruServiceImpl extends Service implements CaiwuDaoruService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> allocate(Map<String, Object> params, User user) {
        return commandExecutor.execute(new AllocateCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> changeAmountWrongStatus(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ChangeAmountWrongStatusCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> splitData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SplitDataCmd(params, user));
    }
}
