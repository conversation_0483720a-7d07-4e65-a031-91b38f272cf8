package com.engine.zhongzhi3.gyl.module.caiwudaoru.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @FileName GT2WriteBackData.java
 * @Description GT2回写数据
 * uf_yshxtz
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/2/11
 */
@Data
public class GT2WriteBackData {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 申请单号
     */
    private String sqdh;
    /**
     * 项目编号
     */
    private String xmbh;
    /**
     * 不含税金额
     */
    private BigDecimal bhsje;
}
