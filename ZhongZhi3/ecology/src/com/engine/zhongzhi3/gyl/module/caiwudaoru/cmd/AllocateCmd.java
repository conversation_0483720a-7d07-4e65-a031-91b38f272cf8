package com.engine.zhongzhi3.gyl.module.caiwudaoru.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.Modeviewlog;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.vo.CaiwuDaoruConst;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.vo.GT2WriteBackData;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName AllocateCmd.java
 * @Description 分配，匹配财务导入的数据和GT2报销回写数据，更新匹配状态
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/2/12
 */
public class AllocateCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 错误信息
     */
    private String error;

    /**
     * GT2回写报销数据的list
     */
    private List<GT2WriteBackData> gt2WriteBackDataList;
    /**
     * 建模数据操作日志
     */
    private final List<Modeviewlog> modeviewlogList;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public AllocateCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        error = "";
        gt2WriteBackDataList = null;
        modeviewlogList = new ArrayList<>();
    }

    /**
     * 这里加锁，防止多人同时操作分配，导致查询和更新不一致
     *
     * @param commandContext
     * @return
     */
    @Override
    public synchronized Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        try {
            //校验参数 无必填项校验
            // checkParams();
            if (error.isEmpty()) {
                //获取状态为空的数量
                int wrongStatusCount = getWrongStatusCount();
                result.put("wrongStatusCount", wrongStatusCount);
                if (wrongStatusCount > 0) {
                    error = "请对匹配状态为空的数据进行匹配!";
                    log.error(error);
                }
                if (error.isEmpty()) {
                    //获取所有GT2回写的数据
                    getGT2WriteData();
                    log.info("gt2WriteBackDataList :" + gt2WriteBackDataList);
                    //处理当前勾选的财务导入的数据
                    handleImportData();
                    //插入建模操作日志
                    if (!modeviewlogList.isEmpty()) {
                        int modid = ModuleDataUtil.getModuleIdByName(CaiwuDaoruConst.CAIWUDAORU_TABLE);
                        ModuleDataUtil.insertModuleLog(modeviewlogList, modid);
                    }
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 处理财务导入的数据
     */
    private void handleImportData() {
        String gt2dh, xmbm;
        BigDecimal bhsje;
        int daoruBillid;
        String checkedIds = Util.null2String(params.get("checkedIds"));
        //获取所有状态为空的导入数据
        String sql = "select * from " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
                " where 1=1 and ppzt is null ";
        if (!checkedIds.isEmpty()) {
            sql += " and id in (" + checkedIds + ") ";
        }
        log.info("handleImportData sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                daoruBillid = rs.getInt("id");//财务导入数据id
                gt2dh = Util.null2String(rs.getString("gt2dh")); //GT2单号
                xmbm = Util.null2String(rs.getString("xmbm"));//项目编码
                bhsje = SDUtil.getBigDecimalValue(rs.getString("bhsje"));//不含税金额
                log.info("daoruBillid:" + daoruBillid + ",gt2dh:" + gt2dh + ",xmbm:" + xmbm + ",bhsje:" + bhsje + "，开始匹配状态");
                //匹配状态并且更新
                matchStatusAndUpdate(daoruBillid, gt2dh, xmbm, bhsje);
            }
        }
    }


    /**
     * 匹配状态并且更新
     *
     * @param daoruBillid
     * @param gt2dh
     * @param xmbm
     * @param bhsje
     */
    private void matchStatusAndUpdate(int daoruBillid, String gt2dh, String xmbm, BigDecimal bhsje) {
        //默认失败
        //0成功 1金额异常 2失败
        //该匹配功能只处理这3个状态
        int status = 2;
        //判断是否成功
        //【GT2单号一致】且【项目编码一致】且【不含税金额一致】且只能找到唯一的一条数据
        int successGT2Billid = -1;
        int successCount = 0;

        if (gt2WriteBackDataList != null) {
            log.info("gt2WriteBackDataList不为空");
            for (GT2WriteBackData gt2Data : gt2WriteBackDataList) {
                if (gt2dh.equals(Util.null2String(gt2Data.getSqdh())) &&
                        xmbm.equals(Util.null2String(gt2Data.getXmbh())) &&
                        bhsje.compareTo(SDUtil.getBigDecimalValue(gt2Data.getBhsje())) == 0) {
                    successCount++;
                    successGT2Billid = gt2Data.getId();
                }
            }
            //如果找到唯一的一条全匹配的，则是匹配成功
            if (successCount == 1) {
                log.info("找到唯一的一条全匹配的，匹配成功");
                status = 0;
            } else {
                successGT2Billid = -1; //非成功的，successGT2Billid置为-1
                //判断非成功，则判断是否是金额异常
                //如则判断是否是【GT2单号一致】且【项目编码一致】，【不含税金额不一致】，这样的话是金额异常状态
                for (GT2WriteBackData gt2Data : gt2WriteBackDataList) {
                    if (gt2dh.equals(Util.null2String(gt2Data.getSqdh())) &&
                            xmbm.equals(Util.null2String(gt2Data.getXmbh())) &&
                            bhsje.compareTo(SDUtil.getBigDecimalValue(gt2Data.getBhsje())) != 0) {
                        status = 1;//金额异常
                        log.info("金额异常");
                        break;
                    }
                }
            }
        } else {
            log.warn("gt2WriteBackDataList is null");
        }
        //其他情况都是失败
        //更新匹配状态和回写成功的GT2报销数据id
        log.info("daoruBillid:" + daoruBillid + ",gt2dh:" + gt2dh + ", xmbm:" + xmbm + "bhsje:" + bhsje + ", status:" + status + ",successCount:" + successCount + ",successGT2Billid:" + successGT2Billid);
        updateMatchStatus(daoruBillid, status, successGT2Billid);
    }


    /**
     * 更新匹配状态
     *
     * @param daoruBillid      财务导入的数据id
     * @param status           匹配状态
     * @param successGT2Billid 成功匹配到的GT2报销数据id
     */
    private void updateMatchStatus(int daoruBillid, int status, int successGT2Billid) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String updateStr = "二开AllocateCmd更新分配结果,更新字段如下：1、匹配状态：" + status;
        String sql = "update " + CaiwuDaoruConst.CAIWUDAORU_TABLE + " set ppzt = " + status;
        //如果是成功的匹配到GT2报销数据，则回写该GT2的报销数据id
        if (successGT2Billid > 0) {
            sql += ",bdgt2bxsj=" + successGT2Billid;
            updateStr += "2、成功匹配的报销数据id:" + successGT2Billid;
        }
        sql += " where id =" + daoruBillid;
        log.info("updateMatchStatus sql:" + sql);
        if (!rs.executeUpdate(sql)) {
            log.error("updateStatus 出错:" + rs.getExceptionMsg() + ";sql:" + sql);
        } else {
            Modeviewlog modeviewlog = new Modeviewlog();
            modeviewlog.setRelatedid(daoruBillid);
            modeviewlog.setRelatedname("-");
            modeviewlog.setOperatetype(2);
            modeviewlog.setOperatedesc(updateStr);
            modeviewlog.setOperateuserid(user.getUID());
            modeviewlog.setOperatedate(TimeUtil.getToday());
            modeviewlog.setOperatetime(TimeUtil.getOnlyCurrentTimeString());
            modeviewlog.setClientaddress("");
            modeviewlogList.add(modeviewlog);
        }
    }

    /**
     * 获取非空匹配状态的数据数量
     * 如果有非空状态的，则报错
     */
    private int getWrongStatusCount() {
        int count = 0;
        String checkedIds = Util.null2String(params.get("checkedIds"));
        //校验勾选的数据，或者全部数据，是否有符合条件的数据
        String sql = "select count(id) as cnt from " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
                " where 1=1 " +
                " and ppzt is not null ";
        if (!checkedIds.isEmpty()) {
            sql += " and id in (" + checkedIds + ") ";
        }
        log.info("getWrongStatusCount sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                count = Util.getIntValue(rs.getString("cnt"));
            }
        }
        return count;
    }

    /**
     * 获取GT2回写的数据
     */
    private void getGT2WriteData() {
        String checkedIds = Util.null2String(params.get("checkedIds"));
        String sql = " select " +
                " a.id," +//GT2数据id
                " a.sqdh," +
                " a.xmbh," +
                " a.bhsje " +
                " from " +
                CaiwuDaoruConst.GT2_DATA_TABLE + " a " + //GT2推送的报销原始数据
                " where 1=1 " +
                " and (a.sjly = 0 or a.sjly is null) " + //只取GT2推送来的数据，排除掉KMP拆分出来的数据
                " and a.cwdrcfbj is null ";//只取没有被财务拆分标记的数据
        if (!checkedIds.isEmpty()) {
            sql += "and a.sqdh in (select gt2dh from " + CaiwuDaoruConst.CAIWUDAORU_TABLE + " where id in (" + checkedIds + ") and gt2dh is not null)"; //只取相同GT2单号的财务导入数据
        }
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        log.info("getGT2WriteData sql:" + sql);
        if (rs.executeQuery(sql)) {
            gt2WriteBackDataList = QueryUtil.getObjList(rs, GT2WriteBackData.class);
        }

    }

    /**
     * 校验参数
     */
    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        //勾选的数据id
        //建模表单主表名,uf_cwdrsj
        //建模模块id
        String[] requiredCols = new String[]{""};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
            log.error(error);
        }
    }


}
