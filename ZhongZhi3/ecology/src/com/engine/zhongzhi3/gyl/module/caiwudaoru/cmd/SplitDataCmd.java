package com.engine.zhongzhi3.gyl.module.caiwudaoru.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.Modeviewlog;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.bean.SplitedData;
import com.engine.zhongzhi3.gyl.module.caiwudaoru.vo.CaiwuDaoruConst;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SplitDataCmd.java
 * @Description 拆分数据
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/2/12
 */
public class SplitDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 错误信息
     */
    private String error;
    /**
     * 财务导入-建模数据操作日志
     */
    private final List<Modeviewlog> daoruModeviewlogList;
    /**
     * GT2报销回写-建模数据操作日志
     */
    private final List<Modeviewlog> gt2ModeviewlogList;
    /**
     * 需要拆分的原始数据id列表
     */
    private final List<String> needSplitSourceIdList;
    /**
     * 拆分后新增的数据id列表
     */
    private final List<String> newSplitIdList;
    /**
     * 建模id-拆分后数据
     */
    private Integer modid_splited;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public SplitDataCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        error = "";
        daoruModeviewlogList = new ArrayList<>();
        gt2ModeviewlogList = new ArrayList<>();
        needSplitSourceIdList = new ArrayList<>();
        newSplitIdList = new ArrayList<>();
        modid_splited = -1;
    }

    /**
     * 这里加锁，防止多人同时操作,导致查询和更新不一致
     *
     * @param commandContext
     * @return
     */
    @Override
    public synchronized Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        try {
            //校验参数 无必填项校验
            //checkParams();
            if (error.isEmpty()) {
                //获取状态为空的数量
                int wrongStatusCount = getWrongStatusCount();
                result.put("wrongStatusCount", wrongStatusCount);
                if (wrongStatusCount > 0) {
                    error = "请对匹配状态为【成功/金额异常（已人工匹配）/无需拆分】且没有拆分成功的数据进行拆分!";
                    log.error(error);
                }
                if (error.isEmpty()) {
                    modid_splited = ModuleDataUtil.getModuleIdByName(CaiwuDaoruConst.NEW_SPLIT_TABLE);
                    log.info("modid_splited:" + modid_splited);
                    //处理当前勾选的财务导入的数据
                    handleImportData();
                    //插入财务导入建模操作日志
                    if (!daoruModeviewlogList.isEmpty()) {
                        int modid = ModuleDataUtil.getModuleIdByName(CaiwuDaoruConst.CAIWUDAORU_TABLE);
                        ModuleDataUtil.insertModuleLog(daoruModeviewlogList, Util.getIntValue(modid));
                    }
                    //插入GT2回写建模操作日志
                    if (!gt2ModeviewlogList.isEmpty()) {
                        int gt2modid = ModuleDataUtil.getModuleIdByName(CaiwuDaoruConst.GT2_DATA_TABLE);
                        ModuleDataUtil.insertModuleLog(gt2ModeviewlogList, gt2modid);
                    }
                    //对拆分后的数据进行权限重构
                    rebuildSplitedDataAuth();
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 处理财务导入的数据
     */
    private void handleImportData() {
        String projectid;
        int daoruBillid;
        String ppzt;//匹配状态
        String checkedIds = Util.null2String(params.get("checkedIds"));
        //获取所有状态为空的导入数据
        String sql = "select a.*," +
                " b.xmmc as projectid " +
                " from " + CaiwuDaoruConst.CAIWUDAORU_TABLE + " a " +
                " left join " + CaiwuDaoruConst.GT2_DATA_TABLE + " b on (a.bdgt2bxsj = b.id) " +
                " where 1=1  ";
        if (!checkedIds.isEmpty()) {
            sql += " and a.id in (" + checkedIds + ") ";
        }
        log.info("handleImportData sql:" + sql);
        RecordSet recordSet = new RecordSet();
        if (recordSet.executeQuery(sql)) {
            while (recordSet.next()) {
                daoruBillid = recordSet.getInt("id");//财务导入数据id
                projectid = Util.null2String(recordSet.getString("projectid")); //GT2报销回写的项目id
                ppzt = Util.null2String(recordSet.getString("ppzt"));//匹配状态
                //如果匹配状态=4（无需拆分），则一比一的将导入数据插入到拆分后数据
                if ("4".equals(ppzt)) {
                    insertOriginData(recordSet);
                } else {
                    //判断项目对应的，是否有拆分比例数据
                    List<Map<String, Object>> splitConfig = getSplitConfig(projectid);
                    log.info("daoruBillid:" + daoruBillid + "projectid:" + projectid + ",splitConfig:" + splitConfig);
                    if (splitConfig == null || splitConfig.isEmpty()) {
                        log.info("daoruBillid:" + daoruBillid + "，项目id:" + projectid + "，项目无对应的预算的拆分比例数据，不执行拆分逻辑");
                        //更新状态为无拆分配置
                        updateDaoruSplitStatus(String.valueOf(daoruBillid), 1);
                    } else {
                        //当前行导入数据进行拆分
                        doSplit(recordSet, splitConfig);
                    }
                }
            }
        }
    }

    /**
     * 插入原始的数据，不拆分
     * 不拆分，插入原始数据
     */
    private void insertOriginData(RecordSet currentdaoruRecordset) {
        //拆分的动作执行是否有出错
        boolean splitSuccess = true;
        String sourceid = "", bdgt2bxsj = "";
        try {
            //来源
            //原始财务导入数据id
            sourceid = Util.null2String(currentdaoruRecordset.getString("id"));
            //原绑定GT2报销数据id
            bdgt2bxsj = Util.null2String(currentdaoruRecordset.getString("bdgt2bxsj"));
            //来源-不含税金额
            BigDecimal source_bhsje = SDUtil.getBigDecimalValue(currentdaoruRecordset.getString("bhsje"));
            //导入数据的项目编号
            String source_projectCode = Util.null2String(currentdaoruRecordset.getString("xmbm"));
            //导入数据的项目名称
            String source_projectName = getProjectName(source_projectCode);

            //拆分后的数据
            SplitedData splitedData = new SplitedData();
            splitedData.setGt2dh(Util.null2String(currentdaoruRecordset.getString("gt2dh")));  //GT2单号
            splitedData.setXmbh(source_projectCode); //拆分后的-项目编号(使用原始编号)
            splitedData.setXmmc(source_projectName);//拆分后的-项目名称（使用原始项目名称）
            splitedData.setN(Util.getIntValue(Util.null2String(currentdaoruRecordset.getString("n")))); //年
            splitedData.setYf(Util.getIntValue(Util.null2String(currentdaoruRecordset.getString("yf"))));//月份
            splitedData.setR(Util.getIntValue(Util.null2String(currentdaoruRecordset.getString("r")))); //日
            splitedData.setKmmc(Util.null2String(currentdaoruRecordset.getString("kmmc")));//科目名称
            splitedData.setCbfl(Util.null2String(currentdaoruRecordset.getString("cbfl"))); //成本分类
            splitedData.setBmmc(Util.null2String(currentdaoruRecordset.getString("bmmc")));//部门名称
            splitedData.setPzh(Util.null2String(currentdaoruRecordset.getString("pzh")));//凭证号
            splitedData.setZy(Util.null2String(currentdaoruRecordset.getString("zy")));//摘要
            splitedData.setBhsje(source_bhsje);//不含税金额（使用原始导入的）
            splitedData.setCfsj(TimeUtil.getCurrentTimeString());//拆分时间
            splitedData.setCfr(user.getUID());//拆分人
            splitedData.setYcwdrsjid(sourceid); //原财务导入数据id
            splitedData.setYbdgt2bxsjid(bdgt2bxsj); //原绑定GT2报销数据id
            splitedData.setCfrq(TimeUtil.getToday()); //拆分日期
            splitedData.setYxmbh(source_projectCode);//原项目编号
            splitedData.setGlxnxmlxbm(null); //关联虚拟项目立项部门
            //建模插入数据
            log.info("splitedData:" + splitedData);
            ModuleResult mr = ModuleDataUtil.insertObj(splitedData, CaiwuDaoruConst.NEW_SPLIT_TABLE, modid_splited, user.getUID());
            if (mr.isSuccess()) {
                log.info("新增的拆分后数据id:" + mr.getBillid());
                needSplitSourceIdList.add(sourceid);
            } else {
                log.error("插入拆分数据出错:" + mr.getErroMsg());
                splitSuccess = false;
            }
        } catch (Exception e) {
            error = "拆分异常：" + SDUtil.getExceptionDetail(e);
            log.error("拆分异常", e);
            splitSuccess = false;
        }
        log.info("sourceid:" + sourceid + ",splitSuccess:" + splitSuccess);
        // 更新原始财务数据、拆分成功
        if (splitSuccess && !sourceid.isEmpty()) {
            updateDaoruSplitStatus(sourceid, 0);
        } else {
            //否则拆分失败
            updateDaoruSplitStatus(sourceid, 2);
        }
        //更新对应的GT2回写拆分标记，已拆分
        if (splitSuccess && !bdgt2bxsj.isEmpty()) {
            updateGT2SplitStatus(bdgt2bxsj);
        }

    }

    /**
     * 根据项目编号获取项目名称
     *
     * @param projectCode
     * @return
     */
    private String getProjectName(String projectCode) {
        String sql = "select name from Prj_ProjectInfo where proCode = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, projectCode)) {
            return Util.null2String(rs.getString("name"));
        }
        return "";
    }

    /**
     * 执行拆分
     *
     * @param currentdaoruRecordset 当前行的财务导入数据RecordSet
     * @param splitConfig           拆分的配置
     */
    private void doSplit(RecordSet currentdaoruRecordset, List<Map<String, Object>> splitConfig) {
        //目标
        //目标项目id。项目编号
        String targetProjectid, targetProjectCode, targetProjectName;
        //不含税金额
        BigDecimal target_bhsje;
        //分配比例
        BigDecimal ratio;
        int xuniDept;
        //拆分的动作执行是否有出错
        boolean splitSuccess = true;
        String sourceid = "", bdgt2bxsj = "";
        try {
            //来源
            //原始财务导入数据id
            sourceid = Util.null2String(currentdaoruRecordset.getString("id"));
            //原绑定GT2报销数据id
            bdgt2bxsj = Util.null2String(currentdaoruRecordset.getString("bdgt2bxsj"));
            //来源-不含税金额
            BigDecimal source_bhsje = SDUtil.getBigDecimalValue(currentdaoruRecordset.getString("bhsje"));
            //导入数据的项目编号
            String source_projectCode = Util.null2String(currentdaoruRecordset.getString("xmbm"));
            for (Map<String, Object> eachConfig : splitConfig) {
                targetProjectCode = "";
                targetProjectName = "";
                xuniDept = -1;
                //配置-分配比例
                ratio = SDUtil.getBigDecimalValue(eachConfig.get("fpbl"));
                //配置-关联虚拟项目
                targetProjectid = Util.null2String(eachConfig.get("glxnxm"));

                Map<String, Object> targetProjectMap = getProjectMap(targetProjectid);
                log.info("targetProjectMap:" + targetProjectMap);
                if (targetProjectMap != null) {
                    //拆分后的-项目编号、名称
                    targetProjectCode = Util.null2String(targetProjectMap.get("proCode"));
                    targetProjectName = Util.null2String(targetProjectMap.get("name"));
                    //关联虚拟项目立项部门
                    xuniDept = Util.getIntValue(Util.null2String(targetProjectMap.get("glbmd")));
                }
                //拆分后的-不含税金额
                target_bhsje = (source_bhsje.multiply(ratio)).setScale(2, RoundingMode.HALF_UP);

                log.info("sourceid:" + sourceid + ";" +
                        "targetProjectid :" + targetProjectid + ";" +
                        "targetProjectCode:" + targetProjectCode + ";" +
                        "targetProjectName:" + targetProjectName + ";" +
                        "target_bhsje:" + target_bhsje);

                SplitedData splitedData = new SplitedData();
                splitedData.setGt2dh(Util.null2String(currentdaoruRecordset.getString("gt2dh")));  //GT2单号
                splitedData.setXmbh(targetProjectCode); //项目编号
                splitedData.setXmmc(targetProjectName);//项目名称
                splitedData.setN(Util.getIntValue(Util.null2String(currentdaoruRecordset.getString("n")))); //年
                splitedData.setYf(Util.getIntValue(Util.null2String(currentdaoruRecordset.getString("yf"))));//月份
                splitedData.setR(Util.getIntValue(Util.null2String(currentdaoruRecordset.getString("r")))); //日
                splitedData.setKmmc(Util.null2String(currentdaoruRecordset.getString("kmmc")));//科目名称
                splitedData.setCbfl(Util.null2String(currentdaoruRecordset.getString("cbfl"))); //成本分类
                splitedData.setBmmc(Util.null2String(currentdaoruRecordset.getString("bmmc")));//部门名称
                splitedData.setPzh(Util.null2String(currentdaoruRecordset.getString("pzh")));//凭证号
                splitedData.setZy(Util.null2String(currentdaoruRecordset.getString("zy")));//摘要
                splitedData.setBhsje(target_bhsje);//不含税金额
                splitedData.setCfsj(TimeUtil.getCurrentTimeString());//拆分时间
                splitedData.setCfr(user.getUID());//拆分人
                splitedData.setYcwdrsjid(sourceid); //原财务导入数据id
                splitedData.setYbdgt2bxsjid(bdgt2bxsj); //原绑定GT2报销数据id
                splitedData.setCfrq(TimeUtil.getToday()); //拆分日期
                splitedData.setYxmbh(source_projectCode);//原项目编号
                splitedData.setGlxnxmlxbm(xuniDept); //关联虚拟项目立项部门
                //建模插入数据
                log.info("splitedData:" + splitedData);
                ModuleResult mr = ModuleDataUtil.insertObj(splitedData, CaiwuDaoruConst.NEW_SPLIT_TABLE, modid_splited, user.getUID());
                if (mr.isSuccess()) {
                    log.info("新增的拆分后数据id:" + mr.getBillid());
                    needSplitSourceIdList.add(sourceid);
                } else {
                    log.error("插入拆分数据出错:" + mr.getErroMsg());
                    splitSuccess = false;
                    break;
                }
//                insertSql = "insert into " + CaiwuDaoruConst.NEW_SPLIT_TABLE + " (gt2dh,xmbh,n,yf,r,kmmc,cbfl,bmmc,pzh,zy,bhsje,cfsj,cfr,ycwdrsjid,ybdgt2bxsjid,cfrq)  " +
//                        " select gt2dh," +
//                        " '" + targetProjectCode + "' as xmbh," +//新的项目编号
//                        " n,yf,r,kmmc,cbfl,bmmc,pzh,zy," +//
//                        target_bhsje + " as bhsje," +//拆分后的-不含税金额
//                        " '" + TimeUtil.getCurrentTimeString() + "'  as cfsj," +//拆分时间
//                        user.getUID() + "  as cfr," +//拆分人
//                        sourceid + " as ycwdrsjid, " +//原财务导入数据id
//                        bdgt2bxsj + " as ybdgt2bxsjid," +//原绑定GT2报销数据id
//                        " '" + TimeUtil.getToday() + "'  as cfrq " +//拆分日期
//                        " from  " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
//                        " where 1=1 " +
//                        " and id = " + sourceid;
//                log.info("insertSql:" + insertSql);
//                if (!rs.executeUpdate(insertSql)) {
//                    log.error("插入拆分数据出错:" + rs.getExceptionMsg());
//                    splitSuccess = false;
//                    break;
//                } else {
//                    needSplitSourceIdList.add(sourceid);
//                }
            }
        } catch (Exception e) {
            error = "拆分异常：" + SDUtil.getExceptionDetail(e);
            log.error("拆分异常", e);
            splitSuccess = false;
        }
        log.info("sourceid:" + sourceid + ",splitSuccess:" + splitSuccess);
        // 更新原始财务数据、拆分成功
        if (splitSuccess && !sourceid.isEmpty()) {
            updateDaoruSplitStatus(sourceid, 0);
        } else {
            //否则拆分失败
            updateDaoruSplitStatus(sourceid, 2);
        }
        //更新对应的GT2回写拆分标记，已拆分
        if (splitSuccess && !bdgt2bxsj.isEmpty()) {
            updateGT2SplitStatus(bdgt2bxsj);
        }
    }


    /**
     * 更新拆分状态到原始的导入数据上
     *
     * @param daoruBillid 原始导入的数据id
     * @param cfzt        拆分状态 0成功 1无拆分比例 2拆分失败
     */
    private void updateDaoruSplitStatus(String daoruBillid, int cfzt) {
        String sql = "update " + CaiwuDaoruConst.CAIWUDAORU_TABLE + " set cfzt = " + cfzt + " where id =" + daoruBillid;
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (!rs.executeUpdate(sql)) {
            log.error("updateDaoruSplitStatus 出错:" + rs.getExceptionMsg() + ";sql:" + sql);
        } else {
            Modeviewlog modeviewlog = new Modeviewlog();
            modeviewlog.setRelatedid(Util.getIntValue(daoruBillid));
            modeviewlog.setRelatedname("-");
            modeviewlog.setOperatetype(2);
            modeviewlog.setOperatedesc("二开SplitDataCmd更新拆分状态,更新字段如下：1、cfzt：" + cfzt);
            modeviewlog.setOperateuserid(user.getUID());
            modeviewlog.setOperatedate(TimeUtil.getToday());
            modeviewlog.setOperatetime(TimeUtil.getOnlyCurrentTimeString());
            modeviewlog.setClientaddress("");
            daoruModeviewlogList.add(modeviewlog);
        }
    }

    private void updateGT2SplitStatus(String bdgt2bxsj) {
        String sql = "update  " + CaiwuDaoruConst.GT2_DATA_TABLE + " set cwdrcfbj = 0 where id =" + bdgt2bxsj;
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (!rs.executeUpdate(sql)) {
            log.error("updateGT2SplitStatus 出错:" + rs.getExceptionMsg() + ";sql:" + sql);
        } else {
            Modeviewlog modeviewlog = new Modeviewlog();
            modeviewlog.setRelatedid(Util.getIntValue(bdgt2bxsj));
            modeviewlog.setRelatedname("-");
            modeviewlog.setOperatetype(2);
            modeviewlog.setOperatedesc("二开SplitDataCmd更新拆分状态,更新字段如下：1、cwdrcfbj：0");
            modeviewlog.setOperateuserid(user.getUID());
            modeviewlog.setOperatedate(TimeUtil.getToday());
            modeviewlog.setOperatetime(TimeUtil.getOnlyCurrentTimeString());
            modeviewlog.setClientaddress("");
            gt2ModeviewlogList.add(modeviewlog);
        }
    }

    /**
     * 获取非成功状态的数据量
     * 只能对，成功/金额异常（已核对） 状态进行拆分
     * 且不能对拆分成功的再拆分
     */
    private int getWrongStatusCount() {
        int count = 0;
        String checkedIds = Util.null2String(params.get("checkedIds"));
        //校验勾选的数据，或者全部数据，是否有符合条件的数据
        String sql = "select count(id) as cnt from " + CaiwuDaoruConst.CAIWUDAORU_TABLE +
                " where 1=1 " +
                " and (" +
                " ppzt is null" +
                " or ppzt not in (0,3,4) " +
                " or cfzt = 0" +
                ") ";
        if (!checkedIds.isEmpty()) {
            sql += " and id in (" + checkedIds + ") ";
        }
        log.info("getWrongStatusCount sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                count = Util.getIntValue(rs.getString("cnt"));
            }
        }
        return count;
    }

    /**
     * 获取拆分比例配置
     *
     * @param projectid
     * @return
     */
    private List<Map<String, Object>> getSplitConfig(String projectid) {
        String sql = "select d.* from uf_ysxx_dt2 d " +
                " inner join uf_ysxx m on (d.mainid = m.id) " +
                " where m.xmmc = " + projectid;
        log.info("projectid:" + projectid + ",getSplitConfig sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getMapList(rs);
        } else {
            log.error("getSplitConfig sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return null;
    }

    /**
     * 根据项目id获取项目编号
     *
     * @param projectid
     * @return
     */
    private String getProjectCode(String projectid) {
        String sql = "select * from Prj_ProjectInfo where id = " + projectid;
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return Util.null2String(rs.getString("procode"));
            }
        } else {
            log.error("getProjectCode sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return "";
    }

    /**
     * 根据项目id获取项目信息
     *
     * @param projectid
     * @return
     */
    private Map<String, Object> getProjectMap(String projectid) {
        ;
        String sql = "select * from Prj_ProjectInfo where id = " + projectid;
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getMap(rs);
        } else {
            log.error("getProjectMap sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return null;
    }


    /**
     * 校验参数
     */
    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        //勾选的数据id
        String[] requiredCols = new String[]{"checkedIds"};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
            log.error(error);
        }
    }

    /**
     * 拆分后的数据-权限重构
     */
    private void rebuildSplitedDataAuth() {
        //获取本批次新拆分出来的数据id列表
        getAllNewIdList();
        if (!newSplitIdList.isEmpty()) {
            log.info("执行拆分后的新数据id有:" + newSplitIdList + "，针对该部分权限重构");
            int modid = ModuleDataUtil.getModuleIdByName(CaiwuDaoruConst.NEW_SPLIT_TABLE);
            log.info("NEW_SPLIT_TABLE modid:" + modid);
            ModuleDataUtil.resetModShare(modid, newSplitIdList);
        } else {
            log.info("未能查到新拆分出来的数据，不执行数据重构");
        }
    }

    /**
     * 获取本批次新拆分出来的数据id列表
     */
    private void getAllNewIdList() {
        String sourceIds = String.join(",", needSplitSourceIdList);
        String sql = "select id from " + CaiwuDaoruConst.NEW_SPLIT_TABLE + " where ycwdrsjid in (" + sourceIds + ")";
        log.info("getAllNewIdList sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                newSplitIdList.add(Util.null2String(rs.getString("id")));
            }
            log.info("newSplitIdList:" + newSplitIdList);
        } else {
            log.error("getAllNewIdList sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
    }


}
