package com.engine.zhongzhi3.gyl.module.caiwudaoru.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @FileName SplitedData.java
 * @Description 拆分后的数据 uf_cfhsjb
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/2/14
 */
@Data
public class SplitedData {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * GT2单号
     */
    private String gt2dh;
    /**
     * 项目编号
     */
    private String xmbh;
    /**
     * 项目名称
     */
    private String xmmc;
    /**
     * 年
     */
    private Integer n;
    /**
     * 月份
     */
    private Integer yf;
    /**
     * 日
     */
    private Integer r;
    /**
     * 科目名称
     */
    private String kmmc;
    /**
     * 成本分类
     */
    private String cbfl;
    /**
     * 部门名称
     */
    private String bmmc;
    /**
     * 凭证号
     */
    private String pzh;
    /**
     * 摘要
     */
    private String zy;
    /**
     * 不含税金额
     */
    private BigDecimal bhsje;
    /**
     * 拆分时间
     */
    private String cfsj;
    /**
     * 拆分人
     */
    private Integer cfr;
    /**
     * 原财务导入数据id
     */
    private String ycwdrsjid;
    /**
     * 原绑定GT2报销数据id
     */
    private String ybdgt2bxsjid;
    /**
     * 拆分日期
     */
    private String cfrq;
    /**
     * 原项目编号
     * 记录原始导入数据的项目编号
     */
    private String yxmbh;
    /**
     * 关联虚拟项目立项部门
     * 取该虚拟项目（即读取分配比例的项目）项目卡片的“立项部门”字段
     */
    private Integer glxnxmlxbm;

}
