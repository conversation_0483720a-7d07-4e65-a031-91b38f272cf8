package com.engine.zhongzhi3.gyl.workflow.prjbudget.action;


import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi3.gyl.workflow.prjbudget.bean.UpdateOldPrjSplitActionLog;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName UpdateOldPrjSplitAction.java
 * @Description 更新uf_ysxx_dt2表中虚拟项目为当前新申请的项目，更新历年虚拟项目
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
@Getter
@Setter
public class UpdateOldPrjSplitAction extends BaseBean implements Action {
    //Action参数---START---
    /**
     * 主表字段名-本次新项目
     */
    private String field_newproject;
    /**
     * 主表字段名-关联的老项目
     */
    private String field_oldproject;
    //Action参数---END---

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 错误信息
     */
    private String error;
    /**
     * 请求Request
     */
    private RequestInfo requestInfo;
    /**
     * action信息
     */
    private ActionInfo actionInfo;
    /**
     * 数据库类
     */
    private RecordSet rs;
    /**
     * 主表字段名-本次新项目--的值
     */
    private Integer field_newproject_value;
    /**
     * 主表字段名-关联的老项目-的值
     */
    private Integer field_oldproject_value;
    /**
     * 要更新的数据列表
     */
    private List<Map<String, Object>> oldList;
    /**
     * 备注计数
     */
    protected int remarkCount;
    /**
     * 备注
     */
    protected StringBuilder sbRemark;
    /**
     * action日志
     */
    private UpdateOldPrjSplitActionLog actionLog;
    /**
     * action日志明细
     */
    private List<UpdateOldPrjSplitActionLog.Detail> logDetailList;
    /**
     * 信息
     */
    private String msg;
    /**
     * 新项目的信息
     */
    private Map<String, Object> newProjectInfo;


    //初始化
    private void init() {
        error = "";
        actionInfo = ActionUtil.getInfo(requestInfo);
        rs = DBUtil.getThreadLocalRecordSet();
        field_newproject_value = -1;
        field_oldproject_value = -1;
        oldList = null;
        remarkCount = 0;
        sbRemark = new StringBuilder();
        actionLog = new UpdateOldPrjSplitActionLog();
        actionLog.setWfreqid(actionInfo.getRequestId());
        actionLog.setOperate_user(actionInfo.getUser().getUID());
        logDetailList = new ArrayList<>();
        msg = "";
        newProjectInfo = null;
        log.info(this.getClass().getName() + "---START---requestid:" + actionInfo.getRequestId());
    }

    /**
     * action执行入口
     *
     * @param requestInfoParam
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfoParam) {
        requestInfo = requestInfoParam;
        try {
            //初始化
            init();
            //校验参数
            checkParams();
            if (error.isEmpty()) {
                appendRemark("参数field_newproject:" + field_newproject);
                appendRemark("参数field_oldproject:" + field_oldproject);
                //主表数据
                Map<String, String> mainData = actionInfo.getMainData();
                String field_newproject_str = Util.null2String(mainData.get(field_newproject));
                String field_oldproject_str = Util.null2String(mainData.get(field_oldproject));
                log.info("field_newproject_str:" + field_newproject_str);
                log.info("field_oldproject_str:" + field_oldproject_str);
                if (field_newproject_str.isEmpty() || field_oldproject_str.isEmpty()) {
                    msg = "缺失新项目或者关联老项目，不执行更新";
                    log.warn(msg);
                    appendRemark(msg);
                } else {
                    field_newproject_value = Util.getIntValue(field_newproject_str);
                    field_oldproject_value = Util.getIntValue(field_oldproject_str);
                    actionLog.setOld_project(field_oldproject_value);
                    actionLog.setNew_project(field_newproject_value);
                    //获取当前有老的项目
                    getUpdateOldData();
                    if (oldList != null && !oldList.isEmpty()) {
                        msg = "查询到关联项目的明细分配数据，共有:" + oldList.size() + "条";
                        log.info(msg);
                        appendRemark(msg);
                        //获取新项目的信息
                        getNewProjectInfo();
                        log.info("newProjectInfo:" + newProjectInfo);
                        if (newProjectInfo == null) {
                            error = "新项目未获取到数据";
                            log.error(error);
                        }
                        if (error.isEmpty()) {
                            //更新预算台账的的分配明细
                            updateBudgetDetail();
                        }

                    } else {
                        msg = "未查询到关联项目的明细分配数据，不执行更新";
                        log.warn(msg);
                        appendRemark(msg);
                    }

                }
            }

        } catch (Exception e) {
            error = "执行异常：" + e.getMessage();
            log.error("执行异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        //保存action的日志
        saveActionLog();
        log.info(this.getClass().getName() + "---END---requestid:" + actionInfo.getRequestId());
        return ActionUtil.handleResult(error, requestInfo);
    }


    private void updateBudgetDetail() {
        int budget_mainid, budget_prjid, budget_dtid, old_project;
        String detailRemark;
        int detailSuccess;

        UpdateOldPrjSplitActionLog.Detail logDetail;
        String updateSql;
        String newPrjCode = Util.null2String(newProjectInfo.get("proCode"));//新的项目编号
        String newXmzj = Util.null2String(newProjectInfo.get("xmzj"));//新的项目总监
        appendRemark("新项目编号:" + newPrjCode);
        appendRemark("新项目总监:" + newXmzj);

        for (Map<String, Object> oldMap : oldList) {
            detailRemark = "- 老数据:" + oldMap + "\n";
            detailSuccess = 0;//默认成功
            logDetail = new UpdateOldPrjSplitActionLog.Detail();
            budget_mainid = Util.getIntValue(Util.null2String(oldMap.get("mainid")));
            budget_prjid = Util.getIntValue(Util.null2String(oldMap.get("xmmc")));
            budget_dtid = Util.getIntValue(Util.null2String(oldMap.get("id")));
            old_project = Util.getIntValue(Util.null2String(oldMap.get("glxnxm")));

            logDetail.setBudget_mainid(budget_mainid);
            logDetail.setBudget_prjid(budget_prjid);
            logDetail.setBudget_dtid(budget_dtid);
            logDetail.setOld_project(old_project);
            logDetail.setNew_project(field_newproject_value);

            updateSql = "update uf_ysxx_dt2 set glxnxm =" + field_newproject_value;
            if (!newPrjCode.isEmpty()) {
                updateSql += ",xmbm='" + newPrjCode + "'";
            } else {
                updateSql += ",xmbm='' ";
            }
            if (!newXmzj.isEmpty()) {
                updateSql += ",xmzj= " + newXmzj;
            } else {
                updateSql += ",xmzj= null ";
            }
            updateSql += " where id=" + budget_dtid;
            detailRemark += "- 更新sql:" + updateSql + "\n";

            if (rs.executeUpdate(updateSql)) {
                detailRemark += "- 更新成功！";
            } else {
                detailRemark += "- 更新失败！" + rs.getExceptionMsg();
                detailSuccess = 1;
            }

            logDetail.setRemark(detailRemark);
            logDetail.setSuccess(detailSuccess);
            logDetailList.add(logDetail);
        }
    }

    private void getNewProjectInfo() {
        String sql = "select * from Prj_ProjectInfo where id = " + field_newproject_value;
        if (rs.executeQuery(sql)) {
            newProjectInfo = QueryUtil.getMap(rs);
        }

    }

    private void saveActionLog() {
        try {
            if (!error.isEmpty()) {
                actionLog.setError(error);
                actionLog.setSuccess(1);
            } else {
                actionLog.setSuccess(0);
            }
            actionLog.setRemark(sbRemark.toString());
            //保存日志
            // 启动新线程，异步保存
            new Thread(() ->
                    UpdateOldPrjSplitActionLog.saveAync(actionLog, logDetailList, actionInfo.getUser().getUID())
            ).start();
        } catch (Exception e) {
            log.error("保存action日志出错", e);
        }
    }

    private void getUpdateOldData() {
        String sql = "select " +
                " d.*,m.xmmc from uf_ysxx_dt2 d " +
                " inner join uf_ysxx m on (d.mainid = m.id) " +
                " where 1=1 " +
                " and d.glxnxm = " + field_oldproject_value;
        log.info("getUpdateOldData sql:" + sql);
        if (rs.executeQuery(sql)) {
            oldList = QueryUtil.getMapList(rs);
            log.info("oldList:" + oldList);
        } else {
            log.error("getUpdateOldData sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
    }

    /**
     * 校验参数
     */
    private void checkParams() {
        List<String> missCols = new ArrayList<>();

        if (StringUtils.isBlank(field_newproject)) {
            missCols.add("field_newproject");
        }
        if (StringUtils.isBlank(field_oldproject)) {
            missCols.add("field_oldproject");
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
            log.error(error);
        }
    }

    /**
     * 拼接备注
     *
     * @param msg
     */
    protected void appendRemark(String msg) {
        remarkCount++;
        sbRemark.append(remarkCount).append(".  ").append(msg).append("\n");
    }
}
