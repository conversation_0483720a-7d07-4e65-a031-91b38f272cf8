package com.engine.zhongzhi3.gyl.workflow.prjbudget.bean;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Data;

import java.util.List;

/**
 * @FileName UpdateOldPrjSplitActionLog.java
 * @Description 更新uf_ysxx_dt2表中虚拟项目为当前新申请的项目的日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/26
 */
@Data
public class UpdateOldPrjSplitActionLog {
    private Integer id;
    /**
     * 流程requestid
     */
    private String wfreqid;
    /**
     * 操作人
     */
    private Integer operate_user;
    /**
     * 表单上选的关联老项目id
     */
    private Integer old_project;
    /**
     * 表单上新申请的项目
     */
    private Integer new_project;
    /**
     * 错误信息
     */
    private String error;
    /**
     * 备注
     */
    private String remark;
    /**
     * 0 成功
     * 1 失败
     */
    private Integer success;

    @Data
    public static class Detail {
        private Integer id;
        private Integer mainid;
        /**
         * 预算主表的数据id
         */
        private Integer budget_mainid;
        /**
         * 预算的项目
         */
        private Integer budget_prjid;
        /**
         * 预算明细2的id
         */
        private Integer budget_dtid;
        /**
         * 老项目id
         */
        private Integer old_project;
        /**
         * 新项目id
         */
        private Integer new_project;
        /**
         * 备注
         */
        private String remark;
        /**
         * 0 成功
         * 1 失败
         */
        private Integer success;
    }

    /**
     * 建模表名-更新历年虚拟项目
     */
    public static final String TABLE_NAME = "uf_gxlnxnxm";

    /**
     * 保存数据
     *
     * @param bean
     * @param userid
     * @return
     */
    public static String saveAync(UpdateOldPrjSplitActionLog bean, List<Detail> detailList, int userid) {
        String error = "";
        try {
            //获取建模id
            int modeid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            ModuleResult mr = ModuleDataUtil.insertObj(bean, TABLE_NAME, modeid, userid);
            if (!mr.isSuccess()) {
                error = mr.getErroMsg();
            } else {
                int newbillid = mr.getBillid();
                //插入明细
                ModuleDataUtil.insertObjDetail(detailList, TABLE_NAME + "_dt1", newbillid);
            }
        } catch (Exception e) {
            error = "保存数据异常:" + SDUtil.getExceptionDetail(e);
        }

        return error;
    }
}
