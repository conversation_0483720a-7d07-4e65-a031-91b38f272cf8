package com.engine.zhongzhi3.gyl.project.webservice.service.impl;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.zhongzhi3.gyl.project.webservice.log.ServiceCallLog;
import com.engine.zhongzhi3.gyl.project.webservice.service.ProjectQueryService;
import com.engine.zhongzhi3.gyl.project.webservice.service.extra.ProjectQueryServiceImplExtra;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.ThreadPoolUtil;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.resource.ResourceComInfo;
import weaver.proj.PrjViewer;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

public class ProjectQueryServiceImpl implements ProjectQueryService {
    private BaseBean baseBean = null;
    private PrjViewer prjViewer = null;
    private ResourceComInfo rComInfo = null;
    private HashMap<String, String> userMap = null;
    private HashMap<String, String> baseMap = null;
    private HashMap<String, String> manageMap = null;
    private HashMap<String, String> otherMap = null;
    private HashMap<String, String> coworkMap = null;
    private Document retDoc = null;

    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public ProjectQueryServiceImpl() {
        this.baseBean = new BaseBean();
        this.prjViewer = new PrjViewer();
        this.userMap = new HashMap();
        this.baseMap = new HashMap();
        this.manageMap = new HashMap();
        this.otherMap = new HashMap();
        this.coworkMap = new HashMap();

        try {
            this.rComInfo = new ResourceComInfo();
            this.retDoc = DocumentHelper.createDocument();
        } catch (Exception var2) {
            var2.printStackTrace();
        }

    }

    public String queryAllProject(String var1) {
        ServiceCallLog serviceCallLog = new ServiceCallLog();
        String result = "";
        try {
            log.info("queryAllProject---START");
            log.info("queryAllProject---param:" + var1);
            //日志设置请求参数
            serviceCallLog.setRequest_param(var1);
            serviceCallLog.setApi_class(this.getClass().getName() + "#queryAllProject");
            RecordSet rs = new RecordSet();
            HashMap<Object, Object> hashMap = new HashMap<>();
            /**
             * SELECT distinct(proj.id),proj.procode,proj.name,proj.description,proj.prjtype,proj.worktype,proj.securelevel,proj.status,proj.isblock,proj.managerview,prjlog.submitdate,proj.parentview,proj.budgetmoney,proj.budgetincome,proj.imcomeindeed,proj.planbegindate,proj.planbegintime,proj.members
             * FROM Prj_ProjectInfo proj LEFT JOIN
             * Prj_Log prjlog
             * on proj.id=prjlog.projectid  WHERE prjlog.submitdate = '2020-12-21'
             * */
            Document documentHelper = DocumentHelper.parseText(var1);
                /*if (!this.checkPermission(documentHelper)) {
                    throw new Exception("查询项目失败,原因:没有权限!");
                }*/
            List var4 = documentHelper.selectNodes("project/base/*");
            Iterator var5 = var4.iterator();

            Element var6;
            while (var5.hasNext()) {
                var6 = (Element) var5.next();
                hashMap.put(var6.getName().toLowerCase(), var6.getText());
            }

            String modifydate = Util.null2String((String) hashMap.get("modifydate"));
            if ("".equals(Util.null2String(modifydate))) {
                throw new Exception("查询项目失败,原因:修改日期为空!");
            }

            String sql = "SELECT " +
                    " distinct(proj.id)," +
                    " proj.procode," +
                    " proj.name," +
                    " proj.prjtype," +
                    " proj.worktype," +
                    " proj.xmlx," +
                    " proj.xmjb," +
                    " proj.xmfxjb," +
                    " proj.xmzt," +
                    " proj.nf," +
                    " proj.xmkssj," +
                    " proj.xmjssj," +
                    " proj.xmzj," +
                    " proj.isblock," +
                    " proj.managerview," +
                    " prjlog.submitdate," +
                    " proj.manager," +
                    " proj.members" +
                    " FROM Prj_ProjectInfo proj " +
                    " LEFT JOIN Prj_Log prjlog on (proj.id=prjlog.projectid) " +
                    " WHERE 1=1 " +
                    " and prjlog.submitdate ='" + modifydate + "' ";
            //2024-12-24新增逻辑 过滤过滤未申请预算的项目
            sql += " and EXISTS ( " +
                    "    SELECT 1 " +
                    "    FROM uf_ysxx u " +
                    "    WHERE u.xmmc = proj.id " +
                    " ) ";
            rs.execute(sql);
            //var2.executeSql(" select * from Prj_ProjectInfo where procode='" + procode + "' ");
            this.retDoc = DocumentHelper.createDocument();
            var6 = this.retDoc.addElement("result").addAttribute("time", TimeUtil.getCurrentTimeString()).addAttribute("status", "1").addAttribute("msg", "查询项目成功!");
            log.info("初始节点加载完毕");
            while (rs.next()) {
                Element projectElement = var6.addElement("project");
                Element baseElement = projectElement.addElement("base");
                int id = rs.getInt("id");//项目id
                id = id + 100000;
                baseElement.addElement("id").addText(id + "");
                baseElement.addElement("procode").addText(Util.null2String(rs.getString("procode")));
                baseElement.addElement("name").addText(Util.null2String(rs.getString("name")));
                baseElement.addElement("prjtype").addText(Util.null2String(rs.getString("prjtype")));
                baseElement.addElement("worktype").addText(Util.null2String(rs.getString("worktype")));
                baseElement.addElement("status").addText(Util.null2String(rs.getString("xmzt")));
                String members = Util.null2String(rs.getString("members"));
                members = getWorkCodeByIds(members);
                baseElement.addElement("members").addText(members);
                baseElement.addElement("isblock").addText(Util.null2String(rs.getString("isblock")));
                String manager = Util.null2String(rs.getString("manager"));
                manager = getWorkCodeByIds(manager);
                baseElement.addElement("manager").addText(manager);
                String xmzj = Util.null2String(rs.getString("xmzj"));
                xmzj = getWorkCodeByIds(xmzj);
                baseElement.addElement("xmzj").addText(xmzj);
                baseElement.addElement("xmlx").addText(Util.null2String(rs.getString("xmlx")));
                baseElement.addElement("xmjb").addText(Util.null2String(rs.getString("xmjb")));
                baseElement.addElement("xmfxjb").addText(Util.null2String(rs.getString("xmfxjb")));
                baseElement.addElement("nf").addText(Util.null2String(rs.getString("nf")));
                baseElement.addElement("xmkssj").addText(Util.null2String(rs.getString("xmkssj")));
                baseElement.addElement("xmjssj").addText(Util.null2String(rs.getString("xmjssj")));
                String ws_other = Util.null2String(rs.getString("ws_other"));
                if (!"".equals(ws_other)) {
                    Element var11 = this.addChildElement(projectElement, "other", new HashMap());
                    Document var12 = DocumentHelper.parseText(ws_other);
                    List var13 = var12.selectNodes("xmlinfo/*");
                    Iterator var14 = var13.iterator();

                    while (var14.hasNext()) {
                        Element var15 = (Element) var14.next();
                        var11.addElement(var15.getName()).addAttribute("label", var15.attribute("label").getText()).addText(var15.getText());
                    }
                }
            }
            result = this.retDoc.asXML();
            log.info("queryAllProject--- SUCCESS END");
        } catch (Exception e) {
            log.error("queryAllProject---ERROR END", e);
            result = "查询出错:" + e.getMessage();
        }
        log.info("result:" + result);
        serviceCallLog.setResponse_param(result);
        //异步处理
        ExecutorService executorService = ThreadPoolUtil.getThreadPool("ProjectQueryServiceImpl", "5");
        executorService.execute(() -> {
            ProjectQueryServiceImplExtra extra = new ProjectQueryServiceImplExtra();
            extra.execute(serviceCallLog);
        });
        return result;
    }

    private boolean checkPermission(Document var1) {
        List var2 = var1.selectNodes("project/user/*");
        Iterator var3 = var2.iterator();

        while (var3.hasNext()) {
            Element var4 = (Element) var3.next();
            this.userMap.put(var4.getName().toLowerCase(), var4.getText());
        }

        boolean var8 = false;
        String var9 = Util.null2String((String) this.userMap.get("loginid"));
        String var5 = Util.null2String((String) this.userMap.get("logintype"));
        String var6 = Util.null2String((String) this.userMap.get("password"));
        this.rComInfo.setTofirstRow();
        String var7 = "";

        while (this.rComInfo.next()) {
            var7 = this.rComInfo.getResourceid();
            if (var9.equals(this.rComInfo.getLoginID()) && Util.getEncrypt(var6).equals(this.rComInfo.getPWD(var7))) {
                var8 = true;
                this.userMap.put("id", var7);
                break;
            }
        }

        return var8;
    }

    private Element addChildElement(Element projectElement, String base, Map<String, String> hashmap) {
        Element var4 = projectElement.addElement(base);
        Iterator var5 = hashmap.keySet().iterator();

        while (var5.hasNext()) {
            String key = (String) var5.next();
            var4.addAttribute(key, (String) hashmap.get(key));
        }

        return var4;
    }

    private static String getWorkCodeByIds(String ids) {
        String codeString = ",";
        if (StringUtils.isNotBlank(ids)) {
            RecordSet rs = new RecordSet();
            String sql = "select workcode from hrmresource where id in(" + ids + ")";
            rs.execute(sql);
            while (rs.next()) {
                codeString += Util.null2String(rs.getString("workcode")) + ",";
            }
            if (codeString.length() >= 2) {
                codeString = codeString.substring(1, codeString.length() - 1);
            }
        }
        return ",".equals(codeString) ? "" : codeString;
    }
}
