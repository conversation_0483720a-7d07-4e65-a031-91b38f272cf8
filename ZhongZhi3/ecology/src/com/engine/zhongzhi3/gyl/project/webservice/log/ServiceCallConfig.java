package com.engine.zhongzhi3.gyl.project.webservice.log;

import com.engine.parent.query.util.QueryUtil;
import lombok.Data;
import weaver.conn.RecordSet;

/**
 * @FileName ServiceCallConfig.java
 * @Description 接口被调用日志配置
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/24
 */
@Data
public class ServiceCallConfig {
    private Integer id;
    /**
     * 是否启用
     */
    private Integer enable;
    /**
     * 附件目录id
     */
    private Integer file_cat_id;
    /**
     * 备注
     */
    private String remark;

    public static final String TABLE_NAME = "uf_service_call_pz";

    public static ServiceCallConfig getInstance() {
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + TABLE_NAME + " where enable = 1")) {
            return QueryUtil.getObj(rs, ServiceCallConfig.class);
        }
        return null;
    }
}
