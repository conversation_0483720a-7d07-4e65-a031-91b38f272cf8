package com.engine.zhongzhi3.gyl.project.webservice.service.extra;

import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import com.engine.zhongzhi3.gyl.project.webservice.log.ServiceCallLog;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName BxmxServiceImplExtra.java
 * @Description GT2报销回写额外处理类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/24
 */
public class BxmxServiceImplExtraExt {

    /**
     * GT2回写的建模表名
     */
    protected static final String WRITE_BACK_TABLE = "uf_yshxtz";
    /**
     * 日志bean
     */
    protected ServiceCallLog serviceCallLog;
    /**
     * 当前批次的预算回写的数据列表
     */
    protected List<Map<String, Object>> serialList;
    /**
     * 备注计数
     */
    protected int remarkCount;
    /**
     * 备注
     */
    protected StringBuilder sbRemark;
    /**
     * 数据库链接类
     */
    protected RecordSet rs;
    /**
     * 需要拆分的原始数据id列表
     */
    protected List<String> needSplitIdList;
    /**
     * 拆分后新增的数据id列表
     */
    protected List<String> newSplitIdList;
    /**
     * 插入表的字段名s
     */
    protected String insertCols;
    /**
     * 查询原始表的字段名s
     */
    protected String selectCols;


    /**
     * 初始化
     */
    protected void _init() {
        serialList = null;
        remarkCount = 0;
        sbRemark = new StringBuilder();
        rs = DBUtil.getThreadLocalRecordSet();
        needSplitIdList = new ArrayList<>();
        newSplitIdList = new ArrayList<>();
        insertCols = "";
        selectCols = "";
    }

    /**
     * 记录日志
     */
    protected void saveCallLog() {
        if (serviceCallLog != null) {
            serviceCallLog.setApi_name("GT2费用报销明细回写");
            String reponseParam = Util.null2String(serviceCallLog.getResponse_param());
            if (reponseParam.contains("操作成功")) {
                serviceCallLog.setSuccess(0);
            } else {
                serviceCallLog.setSuccess(1);
            }
            serviceCallLog.setRemark(sbRemark.toString());
            ServiceCallLog.saveLogAync(serviceCallLog);
        }
    }


    /**
     * 拼接备注
     *
     * @param msg
     */
    protected void appendRemark(String msg) {
        remarkCount++;
        String currentTime = SDTimeUtil.getCurrentTimeMillliString();
        sbRemark.append(remarkCount).append(".  ").append(currentTime).append(" : ").append(msg).append("\n");
    }
}
