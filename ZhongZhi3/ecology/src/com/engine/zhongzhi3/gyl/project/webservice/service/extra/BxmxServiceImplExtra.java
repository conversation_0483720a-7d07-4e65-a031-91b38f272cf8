package com.engine.zhongzhi3.gyl.project.webservice.service.extra;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import com.engine.zhongzhi3.gyl.project.webservice.log.ServiceCallLog;
import weaver.general.Util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName BxmxServiceImplExtra.java
 * @Description GT2报销回写额外处理类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/24
 */
public class BxmxServiceImplExtra extends BxmxServiceImplExtraExt {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 执行
     *
     * @param callLog
     */
    public void execute(ServiceCallLog callLog) {
        log.info("BxmxServiceImplExtra---START");
        try {
            //初始化
            _init();
            serviceCallLog = callLog;
            //设置响应时间
            serviceCallLog.setResponse_time(SDTimeUtil.getCurrentTimeMillliString());
            //处理拆分
            hanldeSplit();
            //记录日志
            saveCallLog();
        } catch (Exception e) {
            log.error("BxmxServiceImplExtra异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        log.info("BxmxServiceImplExtra---END");
    }

    /**
     * 处理拆分逻辑
     */
    private void hanldeSplit() {
        //项目id，数据id
        String projectid, id;
        //获取当前批次的数据
        getCurrentSerialData();
        log.info("serialList:" + serialList);
        int serialListSize = serialList == null ? 0 : serialList.size();
        appendRemark("本批次更新数据量:" + serialListSize);
        //处理拆分逻辑
        if (serialListSize > 0) {
            //获取字段列表
            getColumnList();
            //遍历本批次的原始数据
            for (Map<String, Object> sourceData : serialList) {
                id = Util.null2String(sourceData.get("id"));
                projectid = Util.null2String(sourceData.get("xmmc"));
                log.info("id:" + id + ",sourceData:" + sourceData);
                if (projectid.isEmpty()) {
                    appendRemark("数据id:" + id + "，项目id为空，不执行拆分逻辑");
                } else {
                    //判断项目对应的，是否有拆分比例数据
                    List<Map<String, Object>> splitConfig = getSplitConfig(projectid);
                    log.info("id:" + id + ",splitConfig:" + splitConfig);
                    if (splitConfig == null || splitConfig.isEmpty()) {
                        appendRemark("数据id:" + id + "，项目id:" + id + "，项目无对应的预算的拆分比例数据，不执行拆分逻辑");
                    } else {
                        needSplitIdList.add(id);
                        //执行拆分
                        doSplit(sourceData, splitConfig);
                    }
                }
            }

            if (!needSplitIdList.isEmpty()) {
                //拆分后的数据，重构建模权限
                appendRemark("执行拆分的原数据id有:" + needSplitIdList);
                //处理尾差 TODO
                //给新拆分的数据权限重构
                rebuildAuth();
            }

        } else {
            appendRemark("本批次更新数据量为0，不处理拆分逻辑");
        }
    }

    /**
     * 执行拆分
     *
     * @param sourceData  原始的GT2回写数据
     * @param splitConfig 拆分的配置
     */
    private void doSplit(Map<String, Object> sourceData, List<Map<String, Object>> splitConfig) {
        //目标
        //目标项目id。项目编号
        String targetProjectid, targetProjectCode;
        //申请金额、支付金额、成本金额、不含税金额
        BigDecimal target_sqje, target_AmountActual, target_Amount, target_bhsje;

        //来源
        //原始数据id
        String sourceid = Util.null2String(sourceData.get("id"));
        //原项目id
        String sourceProjectId = Util.null2String(sourceData.get("xmmc"));
        //来源-申请金额
        BigDecimal source_sqje = SDUtil.getBigDecimalValue(sourceData.get("sqje"));
        //来源-支付金额
        BigDecimal source_AmountActual = SDUtil.getBigDecimalValue(sourceData.get("AmountActual"));
        //来源-成本金额
        BigDecimal source_Amount = SDUtil.getBigDecimalValue(sourceData.get("Amount"));
        //来源-不含税金额
        BigDecimal source_bhsje = SDUtil.getBigDecimalValue(sourceData.get("bhsje"));

        //配置信息
        BigDecimal ratio;
        String insertSql;
        for (Map<String, Object> eachConfig : splitConfig) {
            //配置-分配比例
            ratio = SDUtil.getBigDecimalValue(eachConfig.get("fpbl"));
            //配置-关联虚拟项目
            targetProjectid = Util.null2String(eachConfig.get("glxnxm"));
            //目标项目编号
            targetProjectCode = getProjectCode(targetProjectid);
            //目标——申请金额
            target_sqje = (source_sqje.multiply(ratio)).setScale(2, RoundingMode.HALF_UP);
            //目标——支付金额
            target_AmountActual = (source_AmountActual.multiply(ratio)).setScale(2, RoundingMode.HALF_UP);
            //目标——成本金额
            target_Amount = (source_Amount.multiply(ratio)).setScale(2, RoundingMode.HALF_UP);
            //目标——不含税金额
            target_bhsje = (source_bhsje.multiply(ratio)).setScale(2, RoundingMode.HALF_UP);
            log.info("targetProjectid:" + targetProjectid + ";" +
                    "targetProjectCode:" + targetProjectCode + ";" +
                    "target_sqje:" + target_sqje + ";" +
                    "target_AmountActual:" + target_AmountActual + ";" +
                    "target_Amount:" + target_Amount + ";" +
                    "target_bhsje:" + target_bhsje);

            insertSql = "insert into " + WRITE_BACK_TABLE + " (" + insertCols + ")  " +
                    " select " + selectCols + "," +
                    targetProjectid + " as xmmc," +//新的项目名称id
                    " '" + targetProjectCode + "' as xmbh," +//新的项目编号
                    target_sqje + " as sqje," +//分配后的-申请金额
                    " null as flowid," +//分配后的GT2流程ID，这里赋空值
                    target_AmountActual + " as AmountActual," +//分配后的-支付金额
                    target_Amount + " as Amount," +//分配后的-成本金额
                    " 1 as sjly," +//数据来源，这里是系统拆分
                    sourceProjectId + " as glywxm, " +//关联业务项目，存储来源的项目id
                    ratio + " as fpsbl," +//分配时比例
                    target_bhsje + " as bhsje," +//分配后的-成本金额
                    " null as gt2gxsj," +//GT2更新时间
                    " null as gt2gxrq," +//GT2更新日期
                    " null as gt2gxpc," +//GT2更新批次
                    sourceid + " as cflyid " + //拆分来源id
                    " from  " + WRITE_BACK_TABLE +
                    " where 1=1 " +
                    " and id = " + sourceid;
            log.info("insertSql:" + insertSql);
            appendRemark("原数据id：" + sourceid + ",执行拆分插入sql:" + insertSql);
            if (!rs.executeUpdate(insertSql)) {
                appendRemark("原数据id:" + sourceid + "插入拆分数据出错:" + rs.getExceptionMsg());
                log.error("插入拆分数据出错:" + rs.getExceptionMsg());
            } else {
                appendRemark("原数据id:" + sourceid + "插入拆分数据成功");
            }
        }
    }


    /**
     * 获取回写表的字段
     */
    private void getColumnList() {
        //获取要计算的字段列表
        List<String> calculateColums = getCalculateColums();
        List<String> columnList = new ArrayList<>();
        String sql = "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH " +
                " FROM INFORMATION_SCHEMA.COLUMNS " +
                " WHERE TABLE_NAME = '" + WRITE_BACK_TABLE + "' " +
                "  AND TABLE_SCHEMA = 'dbo' " +
                " order by COLUMN_NAME ";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                columnList.add(Util.null2String(rs.getString("column_name")));
            }
        } else {
            log.error("getColumnList sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        if (!columnList.isEmpty()) {
            columnList.remove("id");//数据id
            for (String column : calculateColums) {
                columnList.remove(column);
            }
            //查询的字段
            selectCols = String.join(",", columnList);

            //插入的字段
            columnList.addAll(calculateColums);
            insertCols = String.join(",", columnList);

            log.info("columnList:" + columnList);
            log.info("selectCols:" + selectCols);
            log.info("insertCols:" + insertCols);
        }
    }

    /**
     * 获取要计算的字段列表
     *
     * @return
     */
    private List<String> getCalculateColums() {
        List<String> calculateColums = new ArrayList<>();
        calculateColums.add("xmmc");//项目名称
        calculateColums.add("xmbh");//项目编号
        calculateColums.add("sqje");//申请金额
        calculateColums.add("flowid");//GT2流程ID
        calculateColums.add("AmountActual");//支付金额
        calculateColums.add("Amount");//成本金额
        calculateColums.add("sjly");//数据来源
        calculateColums.add("glywxm");//关联业务项目
        calculateColums.add("fpsbl");//分配时比例
        calculateColums.add("bhsje");//不含税金额
        calculateColums.add("gt2gxsj");//GT2更新时间
        calculateColums.add("gt2gxrq");//GT2更新日期
        calculateColums.add("gt2gxpc");//GT2更新批次
        calculateColums.add("cflyid");//拆分来源id
        return calculateColums;
    }

    /**
     * 根据项目id获取项目编号
     *
     * @param projectid
     * @return
     */
    private String getProjectCode(String projectid) {
        String sql = "select procode from Prj_ProjectInfo where id = " + projectid;
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return Util.null2String(rs.getString("procode"));
            }
        } else {
            log.error("getProjectCode sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return "";
    }

    /**
     * 获取拆分比例配置
     *
     * @param projectid
     * @return
     */
    private List<Map<String, Object>> getSplitConfig(String projectid) {
        String sql = "select d.* from uf_ysxx_dt2 d " +
                " inner join uf_ysxx m on (d.mainid = m.id) " +
                " where m.xmmc = " + projectid;
        log.info("projectid:" + projectid + ",getSplitConfig sql:" + sql);
        if (rs.executeQuery(sql)) {
            return QueryUtil.getMapList(rs);
        } else {
            log.error("getSplitConfig sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return null;
    }


    /**
     * 获取当前批次的数据
     */
    private void getCurrentSerialData() {
        String sql = "select * from " + WRITE_BACK_TABLE + " where gt2gxpc = '" + serviceCallLog.getSerial_number() + "' ";
        if (rs.executeQuery(sql)) {
            serialList = QueryUtil.getMapList(rs);
        } else {
            log.error("getCurrentSerialData sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
    }

    /**
     * 权限重构
     */
    private void rebuildAuth() {
        //获取本批次新拆分出来的数据id列表
        getAllNewIdList();
        if (!newSplitIdList.isEmpty()) {
            appendRemark("执行拆分后的新数据id有:" + newSplitIdList + "，针对该部分权限重构");
            int modid = ModuleDataUtil.getModuleIdByName(WRITE_BACK_TABLE);
            log.info("modid:" + modid);
            appendRemark("根据表名:" + WRITE_BACK_TABLE + ",获取到建模id:" + modid);
            ModuleDataUtil.resetModShare(modid, newSplitIdList);
        } else {
            appendRemark("未能查到新拆分出来的数据，不执行数据重构");
        }
    }

    /**
     * 获取本批次新拆分出来的数据id列表
     */
    private void getAllNewIdList() {
        String sourceIds = String.join(",", needSplitIdList);
        String sql = "select id from " + WRITE_BACK_TABLE + " where cflyid in (" + sourceIds + ")";
        log.info("getAllNewIdList sql:" + sql);
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                newSplitIdList.add(Util.null2String(rs.getString("id")));
            }
            log.info("newSplitIdList:" + newSplitIdList);
        } else {
            log.error("getAllNewIdList sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
    }
}
