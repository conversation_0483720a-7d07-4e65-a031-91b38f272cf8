package com.engine.zhongzhi3.gyl.project.webservice.log;

import com.engine.parent.doc.DocUtil;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import lombok.Data;
import weaver.general.ThreadPoolUtil;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.UUID;
import java.util.concurrent.ExecutorService;

/**
 * @FileName ServiceCallLog.java
 * @Description 接口被调用日志记录
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/24
 */
@Data
public class ServiceCallLog {
    private Integer id;
    /**
     * 批次号
     */
    private String serial_number;
    /**
     * 接口名称
     */
    private String api_name;
    /**
     * 接口执行类
     */
    private String api_class;
    /**
     * 请求日期
     */
    private String request_date;
    /**
     * 请求日期时间
     */
    private String request_time;
    /**
     * 请求报文
     */
    private String request_param;
    /**
     * 请求报文文件
     */
    private String request_param_file;
    /**
     * 响应日期时间
     */
    private String response_time;
    /**
     * 响应耗时(ms)
     */
    private String during;
    /**
     * 响应报文
     */
    private String response_param;
    /**
     * 响应报文文件
     */
    private String response_param_file;
    /**
     * 成功失败
     * 0成功
     * 1失败
     */
    private Integer success;
    /**
     * 备注
     */
    private String remark;

    public static final String TABLE_NAME = "uf_service_call_log";

    public ServiceCallLog() {
        this.request_date = TimeUtil.getToday();
        this.request_time = SDTimeUtil.getCurrentTimeMillliString();
        this.serial_number = getSerialNum();
    }

    /**
     * 保存日志
     *
     * @param callLog
     */
    public static void saveLogAync(ServiceCallLog callLog) {
        ExecutorService executorService = ThreadPoolUtil.getThreadPool("ServiceCallLog", "5");
        executorService.execute(() -> {
            //使用系统管理员
            User user = new User(1);
            //计算耗时
            if (callLog.getRequest_time() != null && callLog.getResponse_time() != null) {
                callLog.setDuring(String.valueOf(SDTimeUtil.timeDifferInMillliSeconds(callLog.getRequest_time(), callLog.getResponse_time())));
            }
            ServiceCallConfig config = ServiceCallConfig.getInstance();

            //请求报文
            String requestParam = Util.null2String(callLog.getRequest_param());
            String requestParamWhole = requestParam;
            if (!requestParam.isEmpty()) {
                //不超过3500字符
                if (requestParam.length() > 3500) {
                    requestParam = requestParam.substring(0, 3500) + "---已截断，完整报文请查看文件";
                }
                callLog.setRequest_param(requestParam);
                //转文档
                if (config != null) {
                    int fileDocId = DocUtil.generateStrFile2Doc(requestParamWhole, config.getFile_cat_id(), "请求报文.txt", user);
                    callLog.setRequest_param_file(String.valueOf(fileDocId));
                }
            }

            //响应报文
            String responseParam = Util.null2String(callLog.getResponse_param());
            String responseParamWhole = responseParam;
            if (!responseParam.isEmpty()) {
                //不超过3500字符
                if (responseParam.length() > 3500) {
                    responseParam = responseParam.substring(0, 3500) + "---已截断，完整报文请查看文件";
                }
                callLog.setResponse_param(responseParam);
                //转文档
                if (config != null) {
                    int fileDocId = DocUtil.generateStrFile2Doc(responseParamWhole, config.getFile_cat_id(), "响应报文.txt", user);
                    callLog.setResponse_param_file(String.valueOf(fileDocId));
                }
            }

            int moduleId = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            //插入主表日志
            ModuleDataUtil.insertObj(callLog, TABLE_NAME, moduleId, 1, false);
        });
    }

    /**
     * 获取批次号
     *
     * @return
     */
    private String getSerialNum() {
        //UUID
        String uuid = String.valueOf(UUID.randomUUID().getMostSignificantBits());
        return "BXMX" + uuid;
    }
}
