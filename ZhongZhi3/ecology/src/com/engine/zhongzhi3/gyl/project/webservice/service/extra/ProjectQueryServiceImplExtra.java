package com.engine.zhongzhi3.gyl.project.webservice.service.extra;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.time.util.SDTimeUtil;
import com.engine.zhongzhi3.gyl.project.webservice.log.ServiceCallLog;
import weaver.general.Util;

/**
 * @FileName ProjectQueryServiceImplExtra.java
 * @Description GT2项目查询额外处理类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/24
 */
public class ProjectQueryServiceImplExtra {
    private ServiceCallLog serviceCallLog;
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public void execute(ServiceCallLog callLog) {
        log.info("ProjectQueryServiceImplExtra---START");
        try {
            serviceCallLog = callLog;
            //设置响应时间
            serviceCallLog.setResponse_time(SDTimeUtil.getCurrentTimeMillliString());
            //记录日志
            saveCallLog();
        } catch (Exception e) {
            log.error("ProjectQueryServiceImplExtra异常", e);
        }
        log.info("ProjectQueryServiceImplExtra---END");
    }

    /**
     * 记录日志
     */
    private void saveCallLog() {
        if (serviceCallLog != null) {
            serviceCallLog.setApi_name("GT2项目卡片查询");
            String reponseParam = Util.null2String(serviceCallLog.getResponse_param());
            if (reponseParam.contains("查询项目成功")) {
                serviceCallLog.setSuccess(0);
            } else {
                serviceCallLog.setSuccess(1);
            }
            ServiceCallLog.saveLogAync(serviceCallLog);
        }
    }
}
