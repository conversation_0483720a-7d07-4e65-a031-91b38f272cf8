package com.engine.zyydx3.tpw.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @FileName verifyESBStatusAction
 * @Description 审批历史同步
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/10/10
 */
@Getter
@Setter
public class SyncApprovalHistoryAction extends BaseBean implements Action {
    /*
     * 审批历史同步接口地址
     * */
    private String url;

    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(SyncApprovalHistoryAction.class + "---START");
        RecordSet recordSet = new RecordSet();
        String errorMsg = "";
        List<Map<String, Object>> list = new ArrayList<>();
        JSONArray params = new JSONArray();
        HashMap<String, String> hrmMap = new HashMap<>();
        try {
            recordSet.executeQuery("select id,loginid from hrmresource");
            while (recordSet.next()) {
                hrmMap.put(recordSet.getString("id"), recordSet.getString("loginid"));
            }
            // 获取流程表单数据
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            Map<String, String> mainData = actionInfo.getMainData();
            String requestid = requestInfo.getRequestid();
            String sql = "select * from workflow_requestlog where requestid = '" + requestid + "'";
            if (recordSet.executeQuery(sql)) {
                list = QueryUtil.getMapList(recordSet);
            }
            writeLog("流程日志信息：" + JSONObject.toJSONString(list));
            if (!list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map = list.get(i);
                    JSONObject jsonObject = new JSONObject();
                    //请求id
                    jsonObject.put("requestid", requestid);
                    //操作代码
                    String logtype = Util.null2String(map.get("LOGTYPE"));
                    if ("3".equals(logtype)) {
                        jsonObject.put("operationCode", "REJECTED");
                    } else {
                        jsonObject.put("operationCode", "COMPLETELY_APPROVED");
                    }
                    //操作者
                    String operator = Util.null2String(map.get("OPERATOR"));
                    if (hrmMap.containsKey(operator)) {
                        jsonObject.put("userName", StringUtils.leftPad(hrmMap.get(operator), 10, "0"));
                    }
                    if ("1".equals(operator)) {
                        jsonObject.put("userName", "00sysadmin");
                    }
                    //操作时间
                    String operatedate = Util.null2String(map.get("OPERATEDATE"));
                    String operatetime = Util.null2String(map.get("OPERATETIME"));
                    jsonObject.put("operationTime", operatedate + "T" + operatetime);
                    String remarkHtml = Util.null2String(map.get("REMARK"));
                    String remark = StringEscapeUtils.unescapeHtml4(remarkHtml.replaceAll("<[^>]*>", ""));
                    jsonObject.put("description", remark);
                    params.add(jsonObject);
                }
            }

            JSONObject jo = new JSONObject();
            //请求id
            jo.put("requestid", requestid);
            //操作代码
            if ("0".equals(actionInfo.getNextNodeType())) {
                jo.put("operationCode", "REJECTED");
            } else {
                jo.put("operationCode", "COMPLETELY_APPROVED");
            }
            //操作者
            String operator = Util.null2String(actionInfo.getUser().getUID());
            if (hrmMap.containsKey(operator)) {
                jo.put("userName", StringUtils.leftPad(hrmMap.get(operator), 10, "0"));
            }
            if ("1".equals(operator)) {
                jo.put("userName", "00sysadmin");
            }
            //操作时间
            RequestManager requestManager = requestInfo.getRequestManager();
            String currentDate = requestManager.getCurrentDate();
            String currentTime = requestManager.getCurrentTime();
            String operatedate = Util.null2String(currentDate);
            String operatetime = Util.null2String(currentTime);
            jo.put("operationTime", operatedate + "T" + operatetime);
            String remarkHtml = actionInfo.getRemark();
            String remark = StringEscapeUtils.unescapeHtml4(remarkHtml.replaceAll("<[^>]*>", ""));
            if(StringUtils.isNotBlank(remark)){
                remark = remark.replaceAll("[\\u0000-\\u001F]+", "");
            }
            jo.put("description", remark);
            params.add(jo);


            writeLog("接口请求参数：" + params.toJSONString());
            RestResult restResult = HttpUtil.postData(url, params.toJSONString());
            writeLog("接口响应参数：" + JSONObject.toJSONString(restResult));
            if (restResult.isSuccess()) {
                RestResult.ResponseInfo responseInfo = restResult.getResponseInfo();
                String body = responseInfo.getBody();
                JSONObject jsonObject = JSONObject.parseObject(body);
                if (jsonObject != null && "success".equals(jsonObject.getString("status")) && "推送成功".equals(jsonObject.getString("message"))) {

                } else {
                    errorMsg = "审批历史同步失败！";
                }
            } else {
                errorMsg = "请求审批历史同步接口失败！";
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        writeLog(SyncApprovalHistoryAction.class + "---END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

}



