package com.engine.zyydx3.tpw.util;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.weaver.general.BaseBean;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;


public class AESUtil {

    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final BaseBean bb = new BaseBean();

    /**
     * @param content
     * @param key     加密后的AESkey 即encodingAESKey
     * @return 加密后字符串
     * @description AES加密操作
     */
    public static String encrypt(String content, String key) {
        try {
            String password = decodeKey(key);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec secretKeySpec = getSecretKey(password);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);// 初始化为加密模式的密码器
            byte[] result = cipher.doFinal(byteContent);// 加密
            return Base64.encodeBase64String(result);//通过Base64转码返回
        } catch (Exception e) {
            bb.writeLog("加密异常" + SDUtil.getExceptionDetail(e));

        }

        return null;
    }


    /**
     * @param content:要加密内容
     * @param key           加密后的AESkey 即encodingAESKey
     * @return 解密后字符串
     * @description AES 解密操作
     */
    public static String decrypt(String content, String key) {
        try {
            String password = decodeKey(key);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(password));
            //执行操作
            byte[] result = cipher.doFinal(Base64.decodeBase64(content));

            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            bb.writeLog("解密异常" + SDUtil.getExceptionDetail(e));
        }
        return null;
    }


    private static SecretKeySpec getSecretKey(final String password) {
        //返回生成指定算法密钥生成器的 KeyGenerator 对象
        KeyGenerator kg = null;

        try {
            kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(password.getBytes(StandardCharsets.UTF_8));// 使用固定种子
            // 根据密钥初始化密钥生成器
            kg.init(128, secureRandom);
            // kg.init(128, new SecureRandom(password.getBytes()));

            SecretKey secretKey = kg.generateKey();
            bb.writeLog("生成的密钥: " + Base64.encodeBase64String(secretKey.getEncoded()));

            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);// 转换为AES专用密钥
        } catch (Exception e) {
            bb.writeLog("getSecretKey异常" + SDUtil.getExceptionDetail(e));
        }

        return null;
    }

    private static String decodeKey(String encodingAESKey) {
        byte[] keys = Base64.decodeBase64(encodingAESKey + "=");
        try {
            return new String(keys, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static void main(String[] args) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", "6656");
        System.out.println("123:" + encrypt(jsonObject.toJSONString(), "xyYrL56U43inm3Uf"));
        System.out.println(decrypt("LZ5OLTzQLitg3zpeedtRAg==", "xyYrL56U43inm3Uf"));

    }

}
