package com.engine.zyydx3.tpw.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.zyydx3.tpw.service.WorkFlowToDoCountsService;
import com.engine.zyydx3.tpw.service.impl.WorkFlowToDoCountsServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

public class WorkFlowToDoCountsWeb extends BaseBean {
    private WorkFlowToDoCountsService getService(User user) {
        return ServiceUtil.getService(WorkFlowToDoCountsServiceImpl.class, user);
    }

    @POST
    @Path("/getCustWorkFlowToDoCounts")
    @Produces({MediaType.APPLICATION_JSON})
    public String getWorkFlowToDoCounts(@Context HttpServletRequest request, @Context HttpServletResponse response, Map<String, Object> body) {
        Map<String, Object> result;
        //获取当前用户
        User user = new User(1);
        result = getService(user).getWorkFlowToDoCounts(body, user);
        return JSONObject.toJSONString(result);
    }
}