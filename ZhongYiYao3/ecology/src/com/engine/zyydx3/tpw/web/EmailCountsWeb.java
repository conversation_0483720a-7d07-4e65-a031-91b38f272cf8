package com.engine.zyydx3.tpw.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.zyydx3.tpw.service.EmailCountsService;
import com.engine.zyydx3.tpw.service.impl.EmailCountsServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;


public class EmailCountsWeb extends BaseBean {
    private EmailCountsService getService(User user) {
        return ServiceUtil.getService(EmailCountsServiceImpl.class, user);
    }

    @POST
    @Path("/getEmailCounts")
    @Produces({MediaType.APPLICATION_JSON})
    public String getEmailCounts(@Context HttpServletRequest request, @Context HttpServletResponse response, Map<String, Object> body) {
        Map<String, Object> result;
        //获取当前用户
        User user = new User(1);
        result = getService(user).getEmailCounts(body, user);
        return JSONObject.toJSONString(result);
    }
}
