package com.engine.zyydx3.tpw.service.impl;

import com.engine.core.impl.Service;
import com.engine.zyydx3.tpw.cmd.WorkFlowToDoCountsCmd;
import com.engine.zyydx3.tpw.service.WorkFlowToDoCountsService;
import weaver.hrm.User;

import java.util.Map;

public class WorkFlowToDoCountsServiceImpl extends Service implements WorkFlowToDoCountsService {
    @Override
    public Map<String, Object> getWorkFlowToDoCounts(Map<String, Object> params, User user) {
        return commandExecutor.execute(new WorkFlowToDoCountsCmd(params, user));
    }
}
