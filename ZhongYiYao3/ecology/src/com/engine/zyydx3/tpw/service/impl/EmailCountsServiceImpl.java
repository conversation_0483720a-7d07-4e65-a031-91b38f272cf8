package com.engine.zyydx3.tpw.service.impl;

import com.engine.core.impl.Service;
import com.engine.zyydx3.tpw.cmd.EmailCountsCmd;
import com.engine.zyydx3.tpw.service.EmailCountsService;
import weaver.hrm.User;

import java.util.Map;

public class EmailCountsServiceImpl extends Service implements EmailCountsService {
    @Override
    public Map<String, Object> getEmailCounts(Map<String, Object> params, User user) {
        return commandExecutor.execute(new EmailCountsCmd(params, user));
    }
}
