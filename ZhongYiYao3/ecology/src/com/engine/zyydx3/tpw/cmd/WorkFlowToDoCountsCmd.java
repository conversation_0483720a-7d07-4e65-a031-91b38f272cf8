package com.engine.zyydx3.tpw.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.common.util.ServiceUtil;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.workflow.publicApi.WorkflowRequestTodoPA;
import com.engine.workflow.publicApi.impl.WorkflowRequestListPAImpl;
import com.engine.zyydx3.tpw.util.AESUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class WorkFlowToDoCountsCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();

    public WorkFlowToDoCountsCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        WorkflowRequestTodoPA requestTodoPA = ServiceUtil.getService(WorkflowRequestListPAImpl.class);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        String userId = "";
        String datas = "";
        String nonce = "";
        String echostr = "";
        String password = "";
        try {
            rs.execute("select password from uf_pwd where qd = 0");
            if (rs.next()) {
                password = rs.getString("password");
                bb.writeLog("密码：" + password);
                //AES解密
                if (params != null && !params.isEmpty()) {
                    nonce = (String) params.get("nonce");
                    echostr = (String) params.get("echostr");
                    //对echostr进行解密操作
                    if (StringUtils.isNotBlank(nonce) && StringUtils.isNotBlank(echostr)) {
                        bb.writeLog("echostr：" + echostr);
                        String returnJson = AESUtil.decrypt(echostr, password);
                        bb.writeLog("解密后字符串：" + returnJson);
                        if (StringUtils.isNotBlank(returnJson)) {
                            JSONObject returnJsonObj = JSONObject.parseObject(returnJson);
                            if (returnJsonObj != null) {
                                userId = returnJsonObj.getString("userId");
                            }
                        } else {
                            errorMsg = "解密异常";
                        }
                    }
                    if (StringUtils.isNotBlank(userId)) {
                        rs.executeQuery("select id from hrmresource where workcode = '" + userId+"'");
                        if (rs.next()) {
                            userId = rs.getString("id");
                        }
                        Map<String, String> var2 = new HashMap<>();
                        String toDoWorkflowRequestCount = String.valueOf(requestTodoPA.getToDoWorkflowRequestCount(new User(Integer.parseInt(userId)), var2, false, false));
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("count", toDoWorkflowRequestCount);
                        //加密
                        datas = AESUtil.encrypt(jsonObject.toJSONString(), password);
                        bb.writeLog("加密后字符串：" + datas);
                        if (StringUtils.isBlank(datas)) {
                            errorMsg = "加密失败";
                        }
                    }
                } else {
                    errorMsg = "请求参数为空";
                }
            } else {
                errorMsg = "密码为空";
            }

        } catch (Exception e) {
            bb.writeLog("获取待办流程数量异常：" + SDUtil.getExceptionDetail(e));
            errorMsg = SDUtil.getExceptionDetail(e);
        }

        if (StringUtils.isNotBlank(errorMsg)) {
            result.put("code", -1);
            result.put("message", "操作失败信息：" + errorMsg);
            result.put("datas", datas);
        } else {
            result.put("code", 0);
            result.put("message", "操作成功");
            result.put("datas", datas);
        }
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}
