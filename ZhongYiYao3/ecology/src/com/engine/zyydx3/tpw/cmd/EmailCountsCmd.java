package com.engine.zyydx3.tpw.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.core.interceptor.CommandContext;
import com.engine.core.interceptor.CommandExecutor;
import com.engine.email.service.EmailBaseService;
import com.engine.email.service.impl.EmailBaseServiceImpl;
import com.engine.parent.common.util.SDUtil;
import com.engine.zyydx3.tpw.util.AESUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class EmailCountsCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();
    protected CommandExecutor commandExecutor;
    private EmailBaseService getService(User user) {
        return (EmailBaseServiceImpl) ServiceUtil.getService(EmailBaseServiceImpl.class, user);
    }

    public EmailCountsCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        String userId = "";
        String totalMailCount = "";
        String datas = "";
        String nonce = "";
        String echostr = "";
        String password = "";
        try {
            rs.execute("select password from uf_pwd where qd = 0");
            if(rs.next()){
                password = rs.getString("password");
                bb.writeLog("密码：" + password);
                //AES解密
                if (params != null && !params.isEmpty()) {
                    nonce = (String) params.get("nonce");
                    echostr = (String) params.get("echostr");
                    //对echostr进行解密操作
                    if (StringUtils.isNotBlank(nonce) && StringUtils.isNotBlank(echostr)) {
//                    String returnJson = AesUtils.decryptResult(echostr, "xyYrL56U43inm3Uf", nonce);
                        bb.writeLog("echostr：" + echostr);
                        String returnJson = AESUtil.decrypt(echostr, password);
                        bb.writeLog("解密后字符串：" + returnJson);
                        JSONObject returnJsonObj = JSONObject.parseObject(returnJson);
                        if (returnJsonObj != null) {
                            userId = returnJsonObj.getString("userId");
                        }else {
                            errorMsg = "解密失败";
                        }
                    }
                }else {
                    errorMsg = "请求参数为空";
                }
                //判断解密后的值是否存在
                Map<String, Object> apidatas = new HashMap<String, Object>();
                if (StringUtils.isNotBlank(userId)) {
                    rs.executeQuery("select id from hrmresource where workcode = '" + userId+"'");
                    if (rs.next()) {
                        userId = rs.getString("id");
                    }
//                    String sql = "select count(id) as total from mailresource where resourceid = " + userId+" and status = 0";
//                    rs.execute(sql);
//                    if (rs.next()) {
//                        totalMailCount = String.valueOf(rs.getString("total"));
//                    }

                    User user = new User(Integer.parseInt(userId));
                    Map<String, Object> params = new HashMap<>();
                    apidatas = getService(user).refreshCount(user, params);

                }
//                if (StringUtils.isNotBlank(totalMailCount)) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("count", Util.null2String(apidatas.get("unreadMailCount")));
                    //加密
//                datas = AesUtils.encryptResult(jsonObject.toJSONString(), "xyYrL56U43inm3Uf", nonce);
                    datas = AESUtil.encrypt(jsonObject.toJSONString(), password);
                    bb.writeLog("加密后字符串：" + datas);
                    if(StringUtils.isBlank(datas)){
                        errorMsg = "加密失败";
                    }
//                }
            }else {
                errorMsg = "密码为空";
            }

        } catch (Exception e) {
            bb.writeLog("获取邮件未读数量操作异常：" + SDUtil.getExceptionDetail(e));
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            result.put("code", -1);
            result.put("message", "操作失败信息：" + errorMsg);
            result.put("datas", datas);
        } else {
            result.put("code", 0);
            result.put("message", "操作成功");
            result.put("datas", datas);
        }
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}


