package com.engine.zhongshi2.gyl.workflow.audit.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName GroupFile2MainAction.java
 * @Description 将建模台账的明细附件合并到主表字段上, 正常在流转转数据之后，走这个aciton
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/6
 */
@Getter
@Setter
public class GroupFile2MainAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 功能列表的功能id
     */
    private String functionId;

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg = "";
        //建模表名，建模key字段，流程key字段
        String moduleTableName, detailModuleTableName, moduleKeyField, wfKeyField, wfKeyFieldValue;
        //明细index
        String detailIndex;
        JSONArray detailConfig;
        int moduleBillId;
        JSONObject jo;
        List<Map<String, Object>> detailData;
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //主表信息
            Map<String, String> wfMainData = actionInfo.getMainData();
            //根据workflowid，读取配置
            JSONObject config = WfConfigUtil.getObjConfigWithoutEnable(functionId, actionInfo.getWorkflowId());
            writeLog("config obj:" + config);
            if (config != null && !config.isEmpty()) {
                moduleTableName = Util.null2String(config.get("module_table_name"));
                moduleKeyField = Util.null2String(config.get("module_key_field"));
                wfKeyField = Util.null2String(config.get("wf_key_field"));
                wfKeyFieldValue = wfMainData.get(wfKeyField);
                detailConfig = config.getJSONArray("config");
                if (!moduleTableName.isEmpty() &&
                        !moduleKeyField.isEmpty() &&
                        !wfKeyField.isEmpty() &&
                        !detailConfig.isEmpty() &&
                        !wfKeyFieldValue.isEmpty()) {
                    //step 1: 校验是否能匹配到对应的建模表数据
                    moduleBillId = queryModuleData(moduleTableName, moduleKeyField, wfKeyFieldValue);
                    if (moduleBillId != -1) {
                        //主表字段-文档id的map
                        Map<String, String> mainFieldMap = new HashMap<>();
                        // step 2: 读取明细配置
                        for (int i = 0; i < detailConfig.size(); i++) {
                            jo = detailConfig.getJSONObject(i);
                            writeLog("each detail config:" + jo);
                            detailIndex = Util.null2String(jo.get("detail_index"));
                            //明细表
                            detailModuleTableName = moduleTableName + "_dt" + detailIndex;
                            //step 3: 获取建模明细数据
                            detailData = getModuleDetailData(moduleTableName, detailModuleTableName, moduleBillId);
                            if (!detailData.isEmpty()) {
                                //step 4: 处理明细数据，将明细的附件字段的文档id放到mainFieldMap中
                                handleDetailData(detailData, jo, mainFieldMap);
                            }
                        }
                        writeLog("mainFieldMap:" + mainFieldMap);
                        //更新主表map所有附件字段
                        if (!mainFieldMap.isEmpty()) {
                            String mainFieldName, docIds;
                            StringBuilder updateSql = new StringBuilder("update " + moduleTableName + " set ");
                            int count = 0;
                            for (Map.Entry<String, String> entry : mainFieldMap.entrySet()) {
                                mainFieldName = entry.getKey();
                                docIds = entry.getValue();
                                updateSql.append(mainFieldName).append(" = '").append(docIds).append("' ");
                                // 判断是否是最后一次循环，如果不是则加逗号
                                if (count < mainFieldMap.size() - 1) {
                                    updateSql.append(", ");
                                }
                                count++;
                            }
                            updateSql.append(" where id = ").append(moduleBillId);
                            writeLog("updateSql:" + updateSql);
                            RecordSet rs = new RecordSet();
                            if (!rs.executeUpdate(updateSql.toString())) {
                                errorMsg = "更新建模主表字段出错：" + rs.getExceptionMsg();
                            }
                        }
                    } else {
                        errorMsg = "流程字段：" + wfKeyField + ",未匹配到对应的建模数据，请检查！";
                    }
                } else {
                    writeLog("存在空的配置或关键字段值为空，跳过执行逻辑");
                }
            } else {
                writeLog("未找到对应的配置信息，跳过执行逻辑");
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        writeLog("errorMsg：" + errorMsg);
        writeLog(this.getClass().getName() + "---END");
        //默认无错误信息
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    /**
     * 处理明细数据
     *
     * @param detailData
     */
    private void handleDetailData(List<Map<String, Object>> detailData, JSONObject detailConfig, Map<String, String> mainFieldMap) {

        String fieldType, fieldTypeValue, fieldAttach, fieldAttachValue;
        //类型字段名
        fieldType = Util.null2String(detailConfig.get("field_type"));
        //附件字段名
        fieldAttach = Util.null2String(detailConfig.get("field_attach"));
        String mainFieldName;
        //遍历明细数据
        for (Map<String, Object> eachDetail : detailData) {
            fieldTypeValue = Util.null2String(eachDetail.get(fieldType));
            fieldAttachValue = Util.null2String(eachDetail.get(fieldAttach));
            //根据类型获取对应的主表字段
            mainFieldName = getMainFieldByType(detailConfig, fieldTypeValue);
            writeLog("fieldTypeValue：" + fieldTypeValue);
            writeLog("fieldAttachValue：" + fieldAttachValue);
            writeLog("mainFieldName：" + mainFieldName);
            //将附件字段put到对应的map中
            putAttach2Map(mainFieldName, fieldAttachValue, mainFieldMap);

        }
    }

    private String getMainFieldByType(JSONObject detailConfig, String fieldTypeValue) {
        String result = "";
        JSONArray typeConfig = detailConfig.getJSONArray("type_config");
        JSONObject joTypeConfig;
        String typeValue, fieldName;
        if (!typeConfig.isEmpty()) {
            for (int j = 0; j < typeConfig.size(); j++) {
                joTypeConfig = typeConfig.getJSONObject(j);
                typeValue = Util.null2String(joTypeConfig.get("type_value"));
                fieldName = Util.null2String(joTypeConfig.get("field_name"));
                if (fieldTypeValue.equals(typeValue)) {
                    result = fieldName;
                    break;
                }

            }
        }
        return result;
    }

    private void putAttach2Map(String mainFieldName, String attachValue, Map<String, String> mainFieldMap) {
        String existAttachValue;
        if (!mainFieldName.isEmpty() && !attachValue.isEmpty()) {
            if (mainFieldMap.containsKey(mainFieldName)) {
                existAttachValue = mainFieldMap.get(mainFieldName);
                mainFieldMap.put(mainFieldName, existAttachValue + "," + attachValue);
            } else {
                mainFieldMap.put(mainFieldName, attachValue);
            }
        }
    }

    /**
     * 获取建模主表数据
     *
     * @param moduleTableName
     * @param moduleKeyField
     * @param keyFieldValue
     * @return
     */
    private int queryModuleData(String moduleTableName, String moduleKeyField, String keyFieldValue) {
        int billid = -1;
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select id from " + moduleTableName + " where " + moduleKeyField + "=?", keyFieldValue)) {
            if (rs.next()) {
                billid = rs.getInt("id");
            }
        }
        return billid;
    }

    /**
     * 获取建模明细数据
     *
     * @param moduleTableName
     * @param moduleDetailTableName
     * @param billid
     * @return
     */
    private List<Map<String, Object>> getModuleDetailData(String moduleTableName, String moduleDetailTableName, int billid) {
        List<Map<String, Object>> list = new ArrayList<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select d.* from " + moduleDetailTableName + " d " +
                " left join " + moduleTableName + " m on (d.mainid = m.id) where m.id = ?", billid)) {
            list = QueryUtil.getMapList(rs);

        }
        return list;
    }
}
