package com.engine.dfmz4.tpw.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @FileName TestAction
 * @Description 支出合同财务数据台账更新项目总额字段
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/1
 */
@Getter
@Setter
public class SpendContractDataAction extends BaseSDAction implements Action {
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), null, "支出合同财务数据台账更新项目总额字段");
        if (getActionError().isEmpty()) {
            try {
                if (getActionError().isEmpty()) {
                    //执行业务逻辑
                    executeMy();
                }
            } catch (Exception e) {
                log.error("action执行异常:", e);
            } finally {
                DBUtil.clearThreadLocalRecordSet();
                appendLog(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
            }
        }

        return actionReturn();
    }


    /**
     * 执行业务逻辑
     */
    private void executeMy() {
        try {
            appendLog("executeMy start");
            Map<String, String> wfMainData = getThreadLocalBaseParam().wfMainData;
            Map<Integer, List<Map<String, String>>> detailDatas = getThreadLocalBaseParam().actionInfo.getDetailData();
            appendLog("主表数据wfMainData:" + JSONObject.toJSONString(wfMainData));
            List<Map<String, String>> detailData1 = detailDatas.get(1);
            appendLog("明细数据detailData1:" + JSONObject.toJSONString(detailData1));
            ArrayList<String> cwlyjdList = new ArrayList<>();
            for (int i = 0; i < detailData1.size(); i++) {
                Map<String, String> detailData = detailData1.get(i);
                //财务履约进度
                String cwlyjd = Util.null2String(detailData.get("cwlyjd"));
                //合同含税金额
                String hthsje = Util.null2String(detailData.get("hthsje"));
                //合同不含税金额
                String htbhsje = Util.null2String(detailData.get("htbhsje"));
//                appendLog("cwlyjd ：" + cwlyjd + " ，hthsje ：" + hthsje + " ，htbhsje ：" + htbhsje);
                if (StringUtils.isNotBlank(cwlyjd)) {
                    cwlyjdList.add(cwlyjd);
//                    //更新支出合同财务数据档案台账明细4 uf_zchtcwsjda_dt4
//                    RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
//                    String sql = "update uf_zchtcwsjda_dt4 set hsje = ? , bhsje = ? where id = ?";
//                    recordSet.executeUpdate(sql, hthsje, htbhsje, cwlyjd);
//                    DBUtil.clearThreadLocalRecordSet();
                }
            }

            HashSet<String> dt3Ids = new HashSet<>();
            if (!cwlyjdList.isEmpty()) {
                String cwlyjds = String.join(",", cwlyjdList);
//                appendLog("execuetMy cwlyjds : " + cwlyjds);

                String sql1 = "SELECT \n" +
                        "    b.id\n" +
                        "FROM \n" +
                        "    uf_zchtcwsjda_dt4 a\n" +
                        "INNER JOIN \n" +
                        "    uf_zchtcwsjda_dt3 b ON a.mainid = b.mainid \n" +
                        "                        AND a.glfylx = b.fylx \n" +
                        "                        AND a.khmc = b.khmc\n" +
                        "where\n" +
                        "\ta.id in (?)\n" +
                        "\tAND b.cwjelx = 2";
                appendLog("查找符合条件的支出合同财务数据明细3 sql " + sql1);
                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                recordSet.executeQuery(sql1, cwlyjds);
                while (recordSet.next()) {
                    dt3Ids.add(recordSet.getString("id"));
                }
//                DBUtil.clearThreadLocalRecordSet();
            }
            appendLog("dt3Ids :" + dt3Ids);
            if (!dt3Ids.isEmpty()) {
                String sql = "SELECT \n" +
                        "    a.id,a.hsje\n" +
                        "FROM \n" +
                        "    uf_zchtcwsjda_dt4 a\n" +
                        "INNER JOIN \n" +
                        "    uf_zchtcwsjda_dt3 b ON a.mainid = b.mainid \n" +
                        "                        AND a.glfylx = b.glfylx \n" +
                        "                        AND a.khmc = b.khmc\n" +
                        "WHERE \n" +
                        "\tb.id = ?\n" +
                        "    AND a.hsje <> ''\n" +
                        "    AND a.hsje IS NOT NULL\n" +
                        "    AND b.cwjelx = 2\n" +
                        "ORDER BY \n" +
                        "    CASE WHEN a.cwszsj IS NOT NULL THEN 0 ELSE 1 END, \n" +
                        "    a.cwszsj,\n" +
                        "    a.id";
                appendLog("查找符合条件的支出合同财务数据明细4 sql " + sql);

                for (String dt3Id : dt3Ids) {
                    ArrayList<HashMap<String, String>> list = new ArrayList<>();
                    RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                    recordSet.executeQuery(sql, dt3Id);
                    while (recordSet.next()) {
                        HashMap<String, String> stringStringHashMap = new HashMap<>();
                        stringStringHashMap.put("id", recordSet.getString("id"));
                        stringStringHashMap.put("hsje", recordSet.getString("hsje"));
                        list.add(stringStringHashMap);
                    }
                    DBUtil.clearThreadLocalRecordSet();
                    //更新支出合同财务数据台账总额字段
                    if (!list.isEmpty()) {
                        updateContractData(list);
                    }
                }
            }


        } catch (Exception e) {
            log.error("action execuetMy执行异常:", e);
        }
        appendLog("execuetMy end");
    }

    private void updateContractData(ArrayList<HashMap<String, String>> list) {
        // 使用BigDecimal进行精确累加
        BigDecimal total = BigDecimal.ZERO;
        for (int i = 0; i < list.size(); i++) {
            HashMap<String, String> row = list.get(i);
            String id = row.get("id");
            String hsjeStr = row.get("hsje");
            // 转换为BigDecimal并累加
            if (hsjeStr != null && !hsjeStr.isEmpty()) {
                BigDecimal hsje = SDUtil.getBigDecimalValue(hsjeStr);
                total = total.add(hsje);
                appendLog("ID: " + id + ", 当前hsje: " + hsje + ", 累计总和: " + total);
                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                recordSet.executeUpdate("update uf_zchtcwsjda_dt4 set fyxmze = ? where id = ?", total.setScale(2, RoundingMode.HALF_UP), id);
            }
        }
    }


}
