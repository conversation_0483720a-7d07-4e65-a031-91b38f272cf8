package com.engine.dfmz4.tpw.modeexpand;

import com.engine.dfmz4.tpw.modeexpand.util.ModeExpandUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;


/**
 * 说明
 * 修改时
 * 类名要与文件名保持一致
 * class文件存放位置与路径保持一致。
 * 请把编译后的class文件，放在对应的目录中才能生效
 * 注意 同一路径下java名不能相同。
 *
 * <AUTHOR>
 */
public class ClaimWorkflowRePushCreateExpand extends AbstractModeExpandJavaCodeNew {
    /**
     * 执行模块扩展动作
     *
     * @param param param包含(但不限于)以下数据
     *              user 当前用户
     *              importtype 导入方式(仅在批量导入的接口动作会传输) 1 追加，2覆盖,3更新，获取方式(int)param.get("importtype")
     *              导入链接中拼接的特殊参数(仅在批量导入的接口动作会传输)，比如a=1，可通过param.get("a")获取参数值
     *              页面链接拼接的参数，比如b=2,可以通过param.get("b")来获取参数
     * @return
     */
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<String, String>();
        try {
            User user = (User) param.get("user");
            int billid = -1;//数据id
            int modeid = -1;//模块id
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");
            if (requestInfo != null) {
                // 直接获取int值，避免不必要的字符串转换
                billid = Util.getIntValue(requestInfo.getRequestid(), -1);
                modeid = Util.getIntValue(requestInfo.getWorkflowid(), -1);

                // 严格的参数有效性校验
                if (!isValidBillId(billid)) {
                    log.warn("无效的billid参数: " + billid);
                    result.put("errmsg", "无效的数据ID参数");
                    result.put("flag", "false");
                    return result;
                }

                if (!isValidModeId(modeid)) {
                    log.warn("无效的modeid参数: " + modeid);
                    result.put("errmsg", "无效的模块ID参数");
                    result.put("flag", "false");
                    return result;
                }


                //------请在下面编写业务逻辑代码------
                String tableName = ModuleDataUtil.getModuleNameById(String.valueOf(modeid));
//                    log.info("billid:" + billid + ",modeid:" + modeid + ",tableName:" + tableName);
                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                String gljm = "";
                recordSet.executeQuery("select gljm from uf_xxx where status = 1 and id = ?", billid);
                if (recordSet.next()) {
                    gljm = recordSet.getString("gljm");
                }
                if (StringUtils.isNotBlank(gljm)) {
                    ModeExpandUtil modeExpandUtil = new ModeExpandUtil();
                    modeExpandUtil.expandModeAgain(Integer.parseInt(gljm), billid);
                }

            }
        } catch (Exception e) {
            result.put("errmsg", "执行异常:" + e.getMessage());
            result.put("flag", "false");
            log.error("Exception", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return result;
    }

    /**
     * 校验billid的有效性
     *
     * @param billid 数据ID
     * @return 是否有效
     */
    private boolean isValidBillId(int billid) {
        // billid必须大于0，且不能超过合理的最大值（防止整数溢出攻击）
        return billid > 0 && billid <= Integer.MAX_VALUE / 2;
    }

    /**
     * 校验modeid的有效性
     *
     * @param modeid 模块ID
     * @return 是否有效
     */
    private boolean isValidModeId(int modeid) {
        // modeid必须在合理范围内：大于0且小于10000（根据业务实际情况调整）
        return modeid > 0 && modeid < 10000;
    }
}