package com.engine.dfmz4.tpw.modeexpand.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.NewRequestDto;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ModeExpandUtil {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public void expandMode(int billid) {
        JSONArray ja = new JSONArray();
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        String workflowId = "";
        HashMap<String, String> map = new HashMap<>();
        recordSet.executeQuery("select a.*,b.* from uf_yyxjjmlcpz a inner join uf_yyxjjmlcpz_dt1 b on a.id = b.mainid ");
        while (recordSet.next()) {
            workflowId = recordSet.getString("lc");
            String jmzdm = recordSet.getString("jmzdm");
            String lczdm = recordSet.getString("lczdm");
            if (StringUtils.isNotBlank(jmzdm) && StringUtils.isNotBlank(lczdm)) {
                map.put(jmzdm, lczdm);
            }
        }
        recordSet.executeQuery("select * from uf_xxx where id = 123");
        while (recordSet.next()) {
            ja = QueryUtil.getJSONList(recordSet);
        }

        if (!ja.isEmpty()) {
            List<NewRequestDto.MainData> mainDataList = new ArrayList<NewRequestDto.MainData>();
            JSONObject jsonObject = ja.getJSONObject(0);
            String lssj = Util.null2String(jsonObject.get("lssj"));
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String jmz = Util.null2String(jsonObject.get(key));
                NewRequestDto.MainData mainData = new NewRequestDto.MainData();
                mainData.setFieldName(value);
                mainData.setFieldValue(jmz);
                mainDataList.add(mainData);
            }
            String jmz = Util.null2String(jsonObject.get("wfskdwbm"));
            recordSet.executeQuery("select id from uf_fkdwxx where nccbm= ?", jmz);
            String wfhtzt = "";
            if (recordSet.next()) {
                wfhtzt = recordSet.getString("id");
            }

            NewRequestDto.MainData mainData = new NewRequestDto.MainData();
            mainData.setFieldName("wfhtzt");
            mainData.setFieldValue(wfhtzt);
            mainDataList.add(mainData);

            NewRequestDto newRequestDto = new NewRequestDto();
            newRequestDto.setWorkflowId(workflowId);
            newRequestDto.setRequestName("收款认领" + lssj);
            newRequestDto.setMainData(mainDataList);
//            log.info("newRequestDto:" + JSONObject.toJSONString(newRequestDto));
            String createResult = WfUtil.createRequest(new User(1), newRequestDto);
            JSONObject createResultObj = JSONObject.parseObject(createResult);
//            log.info("createResultObj :" + JSONObject.toJSONString(createResultObj));
            String requestid = "";
            // 检查创建接口响应
            if (createResultObj == null) {
                log.info("未获取到流程创建接口响应");
            } else {
                String code = Util.null2String(createResultObj.get("code"));
                if (!"SUCCESS".equals(code)) {
                    log.info("流程创建出错：" + createResult);
                } else {
                    JSONObject dataObj = createResultObj.getJSONObject("data");
                    if (dataObj != null && dataObj.containsKey("requestid")) {
                        requestid = Util.null2String(dataObj.get("requestid"));
                    }
                }
            }

            //向日志主表表中插入记录
            ArrayList<Object> values = new ArrayList<>();
            values.add(TimeUtil.getCurrentTimeString());
            values.add(StringUtils.isNotBlank(requestid) ? 0 : 1);
            values.add("123");
            values.add(createResult);
            values.add(billid);
            values.add(requestid);

            ArrayList<String> fields = new ArrayList<>();
            //流程创建时间
            fields.add("lccjsj");
            //流程创建状态 0 成功 1 失败
            fields.add("status");
            //流程创建请求信息
            fields.add("lccjqqxx");
            //流程创建响应信息
            fields.add("lccjxyxx");
            //关联建模
            fields.add("gljm");
            fields.add("lccjrequestid");

            ModuleInsertBean moduleInsertBean = new ModuleInsertBean();
            moduleInsertBean.setTableName("uf_yycjskrllcctrzb");
            moduleInsertBean.setCreatorId(1);
            moduleInsertBean.setFields(fields);
            moduleInsertBean.setValue(values);
            moduleInsertBean.setModuleId(ModuleDataUtil.getModuleIdByName("uf_yycjskrllcctrzb"));
            ModuleResult moduleResult = ModuleDataUtil.insertOne(moduleInsertBean);
            if (!moduleResult.isSuccess()) {
                log.info("创建台账数据失败！：" + moduleResult.getErroMsg());
            } else {
                log.info("创建台账数据成功！" + moduleResult.getBillid());
            }
        }
    }

    public void expandModeAgain(int billid, int rzid) {
        JSONArray ja = new JSONArray();
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        String workflowId = "";
        HashMap<String, String> map = new HashMap<String, String>();
        recordSet.executeQuery("select a.*,b.* from uf_yyxjjmlcpz a inner join uf_yyxjjmlcpz_dt1 b on a.id = b.mainid ");
        while (recordSet.next()) {
            workflowId = recordSet.getString("lc");
            String jmzdm = recordSet.getString("jmzdm");
            String lczdm = recordSet.getString("lczdm");
            if (StringUtils.isNotBlank(jmzdm) && StringUtils.isNotBlank(lczdm)) {
                map.put(jmzdm, lczdm);
            }
        }
        recordSet.executeQuery("select * from uf_skrljsda where id =123");
        while (recordSet.next()) {
            ja = QueryUtil.getJSONList(recordSet);
        }
        if (!ja.isEmpty()) {
            List<NewRequestDto.MainData> mainDataList = new ArrayList<NewRequestDto.MainData>();
            JSONObject jsonObject = ja.getJSONObject(0);
            String lssj = Util.null2String(jsonObject.get("lssj"));
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String jmz = Util.null2String(jsonObject.get(key));
                NewRequestDto.MainData mainData = new NewRequestDto.MainData();
                mainData.setFieldName(value);
                mainData.setFieldValue(jmz);
                mainDataList.add(mainData);
            }
            String jmz = Util.null2String(jsonObject.get("wfskdwbm"));
            recordSet.executeQuery("select id from uf_fkdwxx where nccbm= ?", jmz);
            String wfhtzt = "";
            if (recordSet.next()) {
                wfhtzt = recordSet.getString("id");
            }
            NewRequestDto.MainData mainData = new NewRequestDto.MainData();
            mainData.setFieldName("wfhtzt");
            mainData.setFieldValue(wfhtzt);
            mainDataList.add(mainData);

            NewRequestDto newRequestDto = new NewRequestDto();
            newRequestDto.setWorkflowId(workflowId);
            newRequestDto.setRequestName("收款认领" + lssj);
            newRequestDto.setMainData(mainDataList);
//            log.info("newRequestDto:" + JSONObject.toJSONString(newRequestDto));

            String createResult = WfUtil.createRequest(new User(1), newRequestDto);
            JSONObject createResultObj = JSONObject.parseObject(createResult);
//            log.info("createResultObj :" + JSONObject.toJSONString(createResultObj));
            String requestid = "";
            // 检查创建接口响应
            if (createResultObj == null) {
                log.info("未获取到流程创建接口响应");
            } else {
                String code = Util.null2String(createResultObj.get("code"));
                if (!"SUCCESS".equals(code)) {
                    log.info("流程创建出错：" + createResult);
                } else {
                    JSONObject dataObj = createResultObj.getJSONObject("data");
                    if (dataObj != null && dataObj.containsKey("requestid")) {
                        requestid = Util.null2String(dataObj.get("requestid"));
                    }
                }
            }
            if (StringUtils.isNotBlank(requestid)) {
                //如果成功更新主表状态
                recordSet.executeUpdate("update uf_yycjskrllcctrzb set status = 0, lccjrequestid = ? where id = ?", requestid, rzid);
            }
            //明细表中插入一条记录
            ArrayList<String> fields = new ArrayList<>();
            //流程创建时间
            fields.add("lccjsj");
            //流程创建状态 0 成功 1 失败
            fields.add("status");
            //流程创建请求信息
            fields.add("lccjqqxx");
            //流程创建响应信息
            fields.add("lccjxyxx");
            //关联建模
            fields.add("gljm");
            fields.add("mainid");

            List<List<Object>> valueList = new ArrayList<>();
            ArrayList<Object> values = new ArrayList<>();
            values.add(TimeUtil.getCurrentTimeString());
            values.add(StringUtils.isNotBlank(requestid) ? 0 : 1);
            values.add(JSONObject.toJSONString(newRequestDto));
            values.add(createResult);
            values.add(billid);
            values.add(rzid);
            valueList.add(values);

            ModuleInsertBean moduleInsertBean = new ModuleInsertBean();
            moduleInsertBean.setTableName("uf_yycjskrllcctrzb_dt1");
            moduleInsertBean.setFields(fields);
            moduleInsertBean.setValues(valueList);
            ModuleResult moduleResult = ModuleDataUtil.insertDetail(moduleInsertBean);

        }
    }
}
