package com.engine.dfmz4.security;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 安全哈希工具类
 * 解决CWE-328: 使用不安全的哈希算法
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025/8/12
 */
public class SecureHashUtil {
    
    private static final String SHA256_ALGORITHM = "SHA-256";
    private static final String SHA512_ALGORITHM = "SHA-512";
    private static final int SALT_LENGTH = 32; // 32字节盐值
    
    /**
     * 使用SHA-256算法对字符串进行哈希
     * 
     * @param input 待哈希的字符串
     * @return 哈希后的十六进制字符串
     */
    public static String sha256Hash(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256_ALGORITHM);
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
    
    /**
     * 使用SHA-256算法对字符串进行加盐哈希
     * 
     * @param input 待哈希的字符串
     * @param salt 盐值
     * @return 哈希后的十六进制字符串
     */
    public static String sha256HashWithSalt(String input, String salt) {
        if (input == null || salt == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256_ALGORITHM);
            digest.update(salt.getBytes(StandardCharsets.UTF_8));
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
    
    /**
     * 使用SHA-512算法对字符串进行哈希
     * 
     * @param input 待哈希的字符串
     * @return 哈希后的十六进制字符串
     */
    public static String sha512Hash(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA512_ALGORITHM);
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-512算法不可用", e);
        }
    }
    
    /**
     * 使用SHA-512算法对字符串进行加盐哈希
     * 
     * @param input 待哈希的字符串
     * @param salt 盐值
     * @return 哈希后的十六进制字符串
     */
    public static String sha512HashWithSalt(String input, String salt) {
        if (input == null || salt == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA512_ALGORITHM);
            digest.update(salt.getBytes(StandardCharsets.UTF_8));
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-512算法不可用", e);
        }
    }
    
    /**
     * 生成随机盐值
     * 
     * @return Base64编码的盐值字符串
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 验证输入字符串与哈希值是否匹配（带盐值）
     * 
     * @param input 原始输入字符串
     * @param salt 盐值
     * @param expectedHash 期望的哈希值
     * @param algorithm 哈希算法（"SHA-256" 或 "SHA-512"）
     * @return 是否匹配
     */
    public static boolean verifyHash(String input, String salt, String expectedHash, String algorithm) {
        if (input == null || salt == null || expectedHash == null || algorithm == null) {
            return false;
        }
        
        String actualHash;
        if (SHA256_ALGORITHM.equals(algorithm)) {
            actualHash = sha256HashWithSalt(input, salt);
        } else if (SHA512_ALGORITHM.equals(algorithm)) {
            actualHash = sha512HashWithSalt(input, salt);
        } else {
            throw new IllegalArgumentException("不支持的哈希算法: " + algorithm);
        }
        
        return expectedHash.equals(actualHash);
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * 对敏感数据进行安全哈希处理
     * 推荐用于处理敏感字段，如用户ID、密码等
     * 
     * @param sensitiveData 敏感数据
     * @return 安全哈希后的数据
     */
    public static String hashSensitiveData(String sensitiveData) {
        if (sensitiveData == null || sensitiveData.trim().isEmpty()) {
            return sensitiveData;
        }
        
        // 生成盐值
        String salt = generateSalt();
        
        // 使用SHA-256进行加盐哈希
        String hashedData = sha256HashWithSalt(sensitiveData, salt);
        
        // 返回格式: salt:hash
        return salt + ":" + hashedData;
    }
    
    /**
     * 验证敏感数据
     * 
     * @param originalData 原始数据
     * @param hashedData 哈希后的数据（格式: salt:hash）
     * @return 是否匹配
     */
    public static boolean verifySensitiveData(String originalData, String hashedData) {
        if (originalData == null || hashedData == null) {
            return false;
        }
        
        String[] parts = hashedData.split(":", 2);
        if (parts.length != 2) {
            return false;
        }
        
        String salt = parts[0];
        String expectedHash = parts[1];
        
        return verifyHash(originalData, salt, expectedHash, SHA256_ALGORITHM);
    }
}
