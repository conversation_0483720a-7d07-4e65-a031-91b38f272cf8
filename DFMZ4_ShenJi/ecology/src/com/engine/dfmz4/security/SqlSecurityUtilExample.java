package com.engine.dfmz4.security;

/**
 * SQL安全工具类使用示例
 * 演示如何正确使用SqlSecurityUtil防止SQL注入
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025/8/12
 */
public class SqlSecurityUtilExample {
    
    public static void main(String[] args) {
        demonstrateSqlSecurity();
    }
    
    /**
     * 演示SQL安全防护的各种用法
     */
    public static void demonstrateSqlSecurity() {
        System.out.println("=== SQL安全工具类使用示例 ===\n");
        
        // 1. ID验证示例
        demonstrateIdValidation();
        
        // 2. SQL注入检测示例
        demonstrateSqlInjectionDetection();
        
        // 3. 参数清理示例
        demonstrateParameterSanitization();
        
        // 4. 在WhetherPreReceiptAction中的应用
        demonstrateBusinessUsage();
        
        // 5. 错误处理示例
        demonstrateErrorHandling();
    }
    
    /**
     * 演示ID验证功能
     */
    private static void demonstrateIdValidation() {
        System.out.println("1. ID验证示例:");
        
        String[] testIds = {
            "123",           // 有效数字ID
            "abc123",        // 有效字母数字ID
            "user_001",      // 有效字母数字ID（含下划线）
            "123'; DROP TABLE users; --",  // SQL注入尝试
            "",              // 空字符串
            "a".repeat(100), // 过长ID
            "123.456",       // 包含特殊字符
            "user@domain"    // 包含特殊字符
        };
        
        for (String id : testIds) {
            boolean isNumeric = SqlSecurityUtil.isValidNumericId(id);
            boolean isAlphanumeric = SqlSecurityUtil.isValidAlphanumericId(id);
            
            System.out.printf("ID: %-30s | 数字: %-5s | 字母数字: %-5s%n", 
                truncate(id, 25), isNumeric, isAlphanumeric);
        }
        System.out.println();
    }
    
    /**
     * 演示SQL注入检测功能
     */
    private static void demonstrateSqlInjectionDetection() {
        System.out.println("2. SQL注入检测示例:");
        
        String[] testInputs = {
            "normal_value",                    // 正常值
            "123",                            // 数字
            "'; DROP TABLE users; --",        // 经典SQL注入
            "1' OR '1'='1",                   // 条件注入
            "UNION SELECT * FROM passwords",   // 联合查询注入
            "admin'/**/OR/**/1=1#",           // 注释绕过
            "<script>alert('xss')</script>",  // XSS尝试
            "normal-value_123"                // 正常值
        };
        
        for (String input : testInputs) {
            boolean hasRisk = SqlSecurityUtil.hasSqlInjectionRisk(input);
            String status = hasRisk ? "❌ 危险" : "✅ 安全";
            
            System.out.printf("输入: %-35s | %s%n", truncate(input, 30), status);
        }
        System.out.println();
    }
    
    /**
     * 演示参数清理功能
     */
    private static void demonstrateParameterSanitization() {
        System.out.println("3. 参数清理示例:");
        
        String[] testParams = {
            "  normal_value  ",               // 带空格
            "valid123",                       // 有效参数
            "'; DROP TABLE users; --",        // SQL注入
            "",                              // 空字符串
            "a".repeat(1500),                // 超长参数
            "user_id_123"                    // 正常用户ID
        };
        
        for (String param : testParams) {
            String cleaned = SqlSecurityUtil.sanitizeParameter(param, "testParam");
            String result = cleaned != null ? cleaned : "[被拒绝]";
            
            System.out.printf("原始: %-30s | 清理后: %s%n", 
                truncate(param, 25), truncate(result, 25));
        }
        System.out.println();
    }
    
    /**
     * 演示在业务代码中的实际应用
     */
    private static void demonstrateBusinessUsage() {
        System.out.println("4. 业务代码应用示例:");
        
        // 模拟从前端接收的参数
        String[] userIds = {"123", "user_001", "'; DROP TABLE users; --", ""};
        String[] cwlyjdValues = {"FIN001", "valid_id", "1' OR '1'='1", "normal"};
        
        for (int i = 0; i < userIds.length; i++) {
            String userId = userIds[i];
            String cwlyjd = cwlyjdValues[i % cwlyjdValues.length];
            
            System.out.printf("处理请求 %d: userId=%s, cwlyjd=%s%n", 
                i + 1, truncate(userId, 15), truncate(cwlyjd, 15));
            
            try {
                // 安全验证
                String cleanUserId = SqlSecurityUtil.validateAndCleanId(userId, "userId");
                String cleanCwlyjd = SqlSecurityUtil.validateAndCleanId(cwlyjd, "cwlyjd");
                
                // 模拟SQL执行
                executeSecureUpdate(cleanUserId, cleanCwlyjd);
                System.out.println("  ✅ 处理成功");
                
            } catch (SecurityException e) {
                System.out.println("  ❌ 安全验证失败: " + e.getMessage());
                SqlSecurityUtil.logSecurityEvent("参数验证失败", "userId,cwlyjd", userId + "," + cwlyjd);
            }
            System.out.println();
        }
    }
    
    /**
     * 演示错误处理
     */
    private static void demonstrateErrorHandling() {
        System.out.println("5. 错误处理示例:");
        
        // 测试各种错误情况
        testErrorCase("空参数", null, "userId");
        testErrorCase("SQL注入", "'; DROP TABLE users; --", "userId");
        testErrorCase("超长参数", "a".repeat(100), "userId");
        testErrorCase("特殊字符", "<EMAIL>", "userId");
        testErrorCase("正常参数", "user123", "userId");
    }
    
    /**
     * 测试错误情况
     */
    private static void testErrorCase(String testName, String value, String paramName) {
        System.out.printf("测试 %s: ", testName);
        try {
            String result = SqlSecurityUtil.validateAndCleanId(value, paramName);
            System.out.println("✅ 验证通过: " + result);
        } catch (SecurityException e) {
            System.out.println("❌ 验证失败: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("⚠️  异常: " + e.getMessage());
        }
    }
    
    /**
     * 模拟安全的SQL更新操作
     */
    private static void executeSecureUpdate(String id, String cwlyjd) {
        // 这里模拟实际的SQL执行
        // 在真实环境中，这里会调用RecordSet.executeUpdate()
        
        // 最终安全检查
        if (!SqlSecurityUtil.isValidAlphanumericId(id)) {
            throw new SecurityException("ID最终验证失败");
        }
        
        // 模拟SQL执行
        String sql = "update formtable_main_111_dt1 set sfyf = ? where id = ?";
        System.out.println("  执行SQL: " + sql);
        System.out.println("  参数: id=" + id + ", cwlyjd=" + cwlyjd);
    }
    
    /**
     * 截断字符串用于显示
     */
    private static String truncate(String str, int maxLength) {
        if (str == null) return "[null]";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * 演示安全编码最佳实践对比
     */
    public static void demonstrateSecurityComparison() {
        System.out.println("\n=== 安全编码对比 ===");
        
        String userId = "123";
        String maliciousId = "'; DROP TABLE users; --";
        
        System.out.println("❌ 不安全的做法:");
        System.out.println("String sql = \"UPDATE table SET field = '\" + userInput + \"'\";");
        System.out.println("// 直接拼接用户输入，存在SQL注入风险");
        
        System.out.println("\n✅ 安全的做法:");
        System.out.println("// 1. 参数验证");
        System.out.println("String cleanId = SqlSecurityUtil.validateAndCleanId(userInput, \"userId\");");
        System.out.println("// 2. 参数化查询");
        System.out.println("String sql = \"UPDATE table SET field = ? WHERE id = ?\";");
        System.out.println("recordSet.executeUpdate(sql, value, cleanId);");
        
        System.out.println("\n测试结果:");
        try {
            String cleanUserId = SqlSecurityUtil.validateAndCleanId(userId, "userId");
            System.out.println("正常ID验证: ✅ " + cleanUserId);
        } catch (SecurityException e) {
            System.out.println("正常ID验证: ❌ " + e.getMessage());
        }
        
        try {
            String cleanMaliciousId = SqlSecurityUtil.validateAndCleanId(maliciousId, "userId");
            System.out.println("恶意ID验证: ✅ " + cleanMaliciousId);
        } catch (SecurityException e) {
            System.out.println("恶意ID验证: ❌ " + e.getMessage() + " (正确拦截)");
        }
    }
}
