package com.engine.dfmz4.security;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import weaver.security.core.SecurityCore;
import weaver.security.util.StringUtil;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * SQL安全工具类
 * 解决CWE-89: SQL注入漏洞
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025/8/12
 */
public class SqlSecurityUtil {
    
    private static final Logger log = LoggerFactory.getLogger(SqlSecurityUtil.class);
    
    // SQL注入常见关键字模式
    private static final String SQL_INJECTION_PATTERN = 
        "(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror|alert|confirm|prompt|eval|expression|iframe|object|embed|form|input|meta|link|style|base|applet|body|html|xml|import|include|require).*";
    
    // 数字ID验证模式
    private static final Pattern NUMERIC_ID_PATTERN = Pattern.compile("^[0-9]+$");
    
    // 字母数字ID验证模式（允许字母、数字、下划线、连字符）
    private static final Pattern ALPHANUMERIC_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]+$");
    
    /**
     * 验证数字ID参数
     * 
     * @param id ID参数
     * @return 是否为有效的数字ID
     */
    public static boolean isValidNumericId(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }
        
        // 检查长度限制（防止过长的ID）
        if (id.length() > 20) {
            log.warn("ID长度超过限制: " + id.length());
            return false;
        }
        
        return NUMERIC_ID_PATTERN.matcher(id).matches();
    }
    
    /**
     * 验证字母数字ID参数
     * 
     * @param id ID参数
     * @return 是否为有效的字母数字ID
     */
    public static boolean isValidAlphanumericId(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }
        
        // 检查长度限制
        if (id.length() > 50) {
            log.warn("ID长度超过限制: " + id.length());
            return false;
        }
        
        return ALPHANUMERIC_ID_PATTERN.matcher(id).matches();
    }
    
    /**
     * 检查SQL注入风险
     * 
     * @param input 输入参数
     * @return 是否存在SQL注入风险
     */
    public static boolean hasSqlInjectionRisk(String input) {
        if (StringUtils.isBlank(input)) {
            return false;
        }
        
        // 使用系统内置的SQL注入检查
        try {
            SecurityCore sc = new SecurityCore();
            Map rules = sc.getRules();
            if (rules != null) {
                return !sc.checkSqlInjection("", "param", input, rules, true, false, false);
            }
        } catch (Exception e) {
            log.error("SQL注入检查异常", e);
        }
        
        // 备用检查：使用正则表达式
        return input.matches(SQL_INJECTION_PATTERN);
    }
    
    /**
     * 清理和验证SQL参数
     * 
     * @param param 参数值
     * @param paramName 参数名称（用于日志）
     * @return 清理后的参数，如果不安全则返回null
     */
    public static String sanitizeParameter(String param, String paramName) {
        if (StringUtils.isBlank(param)) {
            return param;
        }
        
        // 去除首尾空格
        String cleanParam = param.trim();
        
        // 检查SQL注入风险
        if (hasSqlInjectionRisk(cleanParam)) {
            log.error("检测到SQL注入风险，参数: " + paramName + ", 值: " + cleanParam);
            return null;
        }
        
        // 检查长度限制
        if (cleanParam.length() > 1000) {
            log.warn("参数长度超过限制，参数: " + paramName + ", 长度: " + cleanParam.length());
            return null;
        }
        
        return cleanParam;
    }
    
    /**
     * 验证表名是否安全
     * 
     * @param tableName 表名
     * @return 是否为安全的表名
     */
    public static boolean isValidTableName(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        
        // 表名只允许字母、数字、下划线
        Pattern tableNamePattern = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
        
        // 检查长度限制
        if (tableName.length() > 64) {
            return false;
        }
        
        return tableNamePattern.matcher(tableName).matches();
    }
    
    /**
     * 验证列名是否安全
     * 
     * @param columnName 列名
     * @return 是否为安全的列名
     */
    public static boolean isValidColumnName(String columnName) {
        if (StringUtils.isBlank(columnName)) {
            return false;
        }
        
        // 列名只允许字母、数字、下划线
        Pattern columnNamePattern = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
        
        // 检查长度限制
        if (columnName.length() > 64) {
            return false;
        }
        
        return columnNamePattern.matcher(columnName).matches();
    }
    
    /**
     * 安全的数字转换
     * 
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return 转换后的整数
     */
    public static int safeParseInt(String value, int defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        
        try {
            // 验证是否为纯数字
            if (!isValidNumericId(value)) {
                log.warn("无效的数字格式: " + value);
                return defaultValue;
            }
            
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.warn("数字转换失败: " + value, e);
            return defaultValue;
        }
    }
    
    /**
     * 安全的长整型转换
     * 
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return 转换后的长整型
     */
    public static long safeParseLong(String value, long defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        
        try {
            // 验证是否为纯数字
            if (!isValidNumericId(value)) {
                log.warn("无效的数字格式: " + value);
                return defaultValue;
            }
            
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            log.warn("长整型转换失败: " + value, e);
            return defaultValue;
        }
    }
    
    /**
     * 验证并清理ID参数（用于数据库操作）
     * 
     * @param id ID参数
     * @param paramName 参数名称
     * @return 验证通过的ID，失败时抛出异常
     * @throws SecurityException 当ID不安全时抛出
     */
    public static String validateAndCleanId(String id, String paramName) throws SecurityException {
        if (StringUtils.isBlank(id)) {
            throw new SecurityException("参数 " + paramName + " 不能为空");
        }
        
        // 清理参数
        String cleanId = sanitizeParameter(id, paramName);
        if (cleanId == null) {
            throw new SecurityException("参数 " + paramName + " 包含不安全字符");
        }
        
        // 验证ID格式（支持数字和字母数字格式）
        if (!isValidNumericId(cleanId) && !isValidAlphanumericId(cleanId)) {
            throw new SecurityException("参数 " + paramName + " 格式无效");
        }
        
        return cleanId;
    }
    
    /**
     * 记录SQL安全事件
     * 
     * @param event 事件描述
     * @param param 相关参数
     * @param value 参数值
     */
    public static void logSecurityEvent(String event, String param, String value) {
        String hashedValue = SecureHashUtil.sha256Hash(value);
        log.warn("SQL安全事件: " + event + ", 参数: " + param + ", 值哈希: " + hashedValue);
    }
}
