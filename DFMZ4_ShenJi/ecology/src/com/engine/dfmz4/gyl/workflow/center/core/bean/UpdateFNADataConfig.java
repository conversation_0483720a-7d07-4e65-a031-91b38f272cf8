package com.engine.dfmz4.gyl.workflow.center.core.bean;

import com.engine.parent.query.util.QueryUtil;
import lombok.Data;
import weaver.conn.RecordSet;

import java.util.List;

/**
 * @FileName UpdateFNADataConfig.java
 * @Description 合同台账财务数据更新接口配置
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/28
 */
@Data
public class UpdateFNADataConfig {
    private Integer id;
    /**
     * 流程表单名
     */
    private String formtable;
    /**
     * 流程表单名-明细序号
     */
    private Integer formtable_dtindex;
    /**
     * 单据类型
     * 0-支出付款
     * 1-支出计提
     * 2-收款认领
     * 3-合同预提
     */
    private Integer order_type;
    /**
     * 流程主表字段_数据标记
     */
    private String mfield_sjbj;
    /**
     * 流程主表字段_凭证号
     */
    private String mfield_pzh;
    /**
     * 流程明细字段_财务明细数据id
     */
    private String dtfield_fna_detailid;
    /**
     * 对应财务台账主表
     */
    private String fna_uftable;
    /**
     * 对应财务台账明细序号
     */
    private Integer fna_uftable_dtindex;

    /**
     * 明细1
     * 配置接口字段-到-流程明细字段
     */
    @Data
    public static class Detail1 {
        private Integer id;
        private Integer mainid;
        /**
         * 接口字段名
         */
        private String field_api;
        /**
         * 字段含义
         */
        private String field_desc;
        /**
         * 接口字段必填
         */
        private Integer required;
        /**
         * 流程明细字段名
         */
        private String field_work;
        /**
         * 财务台账明细字段名
         */
        private String field_uf;
        /**
         * 0-取流程明细字段值
         * 1-取流程主表字段值
         * 2-固定值
         */
        private Integer uf_convert;
        /**
         * 转财务台账流程主表字段名
         */
        private String field_main_work;
        /**
         * 固定值
         */
        private String fixed_value;

    }

    /**
     * 明细2
     * 配置流程字段-到-财务台账明细字段
     */
    @Data
    public static class Detail2 {
        private Integer id;
        private Integer mainid;
        /**
         * 流程字段名
         */
        private String field_work;
        /**
         * 财务明细字段
         */
        private String field_fna;
        /**
         * 财务明细字段
         */
        private Integer required;
        /**
         * 字段转换
         * 0-取当前流程明细字段值
         * 1-取流程主表字段值
         * 2-固定值
         */
        private Integer convert_type;
        /**
         * 固定值
         */
        private String fixed_value;

    }

    public static final String TABLE_NAME = "uf_htcwjk_pz";

    /**
     * 根据流程表单名获取配置
     *
     * @param formtable
     * @return
     */
    public static UpdateFNADataConfig getConfigByFormTable(String formtable) {
        String sql = "select * from uf_htcwjk_pz where formtable = '123123'";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getObj(rs, UpdateFNADataConfig.class);
        }
        return null;
    }

    /**
     * 获取明细1配置（接口字段映射配置）
     *
     * @param mainid
     * @return
     */
    public static List<Detail1> getDetailConfig1(int mainid) {
        String sql = "select * from uf_htcwjk_pz_dt1 where mainid = 123";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getObjList(rs, Detail1.class);
        }
        return null;
    }

    /**
     * 获取明细2配置（财务台账同步配置）
     *
     * @param mainid
     * @return
     */
    public static List<Detail2> getDetailConfig2(int mainid) {
        String sql = "select * from uf_htcwjk_pz_dt2 where mainid = 123";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getObjList(rs, Detail2.class);
        }
        return null;
    }
}
