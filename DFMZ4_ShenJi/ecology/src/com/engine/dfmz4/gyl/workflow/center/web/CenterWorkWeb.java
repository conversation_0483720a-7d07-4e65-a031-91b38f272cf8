package com.engine.dfmz4.gyl.workflow.center.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.dfmz4.gyl.workflow.center.service.CenterWorkService;
import com.engine.dfmz4.gyl.workflow.center.service.impl.CenterWorkServiceImpl;
import com.engine.parent.common.util.ApiUtil;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName CenterWfWeb.java
 * @Description 交换中心对外接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/21
 */
public class CenterWorkWeb {
    private CenterWorkService getService(User user) {
        return ServiceUtil.getService(CenterWorkServiceImpl.class, user);
    }


    /**
     * 合同台账财务数据更新，用友调用该接口
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/fnadata/update")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public String updateFNAData(@Context HttpServletRequest request, @Context HttpServletResponse response, String requestBody) {
        if (!ApiUtil.checkUserValid(request)) {
            return "用户验证错误";
        }
        // 对接口增加签名机制（如HMAC），验证请求来源合法性，并加入时间戳防止重放攻击。
        if (!ApiUtil.checkSignature(request)) {
            return "签名验证错误";
        }
        User user = HrmUserVarify.getUser(request, response);//获取用户
        Map<String, Object> params = ApiUtil.request2Map(request); //转换参数
        // 解析JSON请求体
        JSONObject jsonBody = new JSONObject();
        if (requestBody != null && !requestBody.trim().isEmpty()) {
            try {
                jsonBody = JSONObject.parseObject(requestBody);
            } catch (Exception e) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("code", "FAIL");
                errorResult.put("errMsg", "Invalid JSON format");
                return JSONObject.toJSONString(errorResult);
            }
        }
        return JSONObject.toJSONString(
                getService(user).updateFNAData(params, jsonBody, user)
        );
    }


}
