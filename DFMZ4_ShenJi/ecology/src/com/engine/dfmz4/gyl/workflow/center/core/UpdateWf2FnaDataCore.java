package com.engine.dfmz4.gyl.workflow.center.core;

import com.alibaba.fastjson.JSONObject;
import com.engine.dfmz4.gyl.workflow.center.core.bean.UpdateFNADataConfig;
import com.engine.dfmz4.gyl.workflow.center.core.ext.UpdateWf2FnaDataCoreExt;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;

import java.util.List;

/**
 * @FileName UpdateWf2FnaDataCore.java
 * @Description 更新交换中心流程数据到财务台账，在UpdateFNADataCore执行成功后再执行该类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/28
 */
public class UpdateWf2FnaDataCore extends UpdateWf2FnaDataCoreExt {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public UpdateWf2FnaDataCore(String prefixLog,
                                SDLog sdLog,
                                UpdateFNADataConfig config,
                                WfInfo wfInfo,
                                List<String> updatedDetailIds,
                                String requestId,
                                String serialNumber) {
        this.prefixLog = prefixLog;
        this.sdLog = sdLog;
        this.config = config;
        this.wfInfo = wfInfo;
        this.updatedDetailIds = updatedDetailIds;
        this.requestId = requestId;
        this.serialNumber = serialNumber;
    }

    /**
     * 主执行器
     */
    public void execute() {
        try {
            appendLog("=== 开始异步同步数据到财务台账 ===");

            // 2. 获取明细2配置（财务台账同步配置）
            detailConfig2 = UpdateFNADataConfig.getDetailConfig2(config.getId());
            if (detailConfig2 == null || detailConfig2.isEmpty()) {
                error = "未能获取到财务台账同步配置";
                appendLog(error);
                return;
            }
            appendLog("获取到财务台账同步配置 " + detailConfig2.size() + " 条");

//            // 如果没有指定更新的明细ID，返回空列表
//            if (updatedDetailIds == null || updatedDetailIds.isEmpty()) {
//                error = "没有指定的流程明细id";
//                appendLog(error);
//                return;
//            }

            //获取流程主表数据
            wfMainData = WfUtil.getMainDataByReqId(wfInfo.getRequestid(), wfInfo.getFormtableName());
            appendLog("获取到流程主表数据: " + JSONObject.toJSONString(wfMainData));
            if (wfMainData == null || wfMainData.isEmpty()) {
                error = "未获取到对应的流程主表信息";
                appendLog(error);
                return;
            }

//            // 3. 构建财务台账同步SQL
//            List<String> syncSqlList = FnaDataUtil.buildFinancialSyncSqlList(config, detailConfig2, requestId, updatedDetailIds, sdLogUtil);
//            if (syncSqlList.isEmpty()) {
//                appendLog("没有需要同步到财务台账的数据");
//                return;
//            }
//            if (error.isEmpty()) {
//                appendLog("构建财务台账同步SQL " + syncSqlList.size() + " 条");
//                // 4. 执行财务台账同步
//                String executeResult = DBUtil.executeTransactionSqlList(syncSqlList);
//                if (!executeResult.isEmpty()) {
//                    error = "财务台账同步结束（失败）：" + executeResult;
//                    appendLog(error);
//                    return;
//                }
//                appendLog("财务台账同步成功，共同步 " + syncSqlList.size() + " 条记录");
//                appendLog("=== 财务台账同步结束（成功） ===");
//            }

        } catch (Exception e) {
            log.error("同步财务台账异常", e);

        } finally {
            aferExecute();
        }
    }

    private void aferExecute() {
        try {
            // 插入二开日志
            if (sdLog != null && sdLogUtil != null) {
                String fullLog = sdLogUtil.getFullLog();
                String finalLog = prefixLog + "\n----处理财务台账日志-----\n" + fullLog;
                //判断如果本次财务同步，有error，则需要更新状态字段
                sdLog.setExtend2(error); //本财务接口执行错误信息
                sdLog.setExtend3(error.isEmpty() ? "0" : "1"); //0成功 1失败
                sdLog.setExtend4(serialNumber);//序列号
                //这里传给error默认为空，因为前置更新流程必需成功，才会走到这个接口
                SDLog.saveLogAsync(sdLog, finalLog, "");
                appendLog("已插入最终处理日志");
            }
            try {
                DBUtil.clearThreadLocalRecordSet();
            } catch (Exception e) {
                log.error("清理DBUtil异常", e);
            }

        } catch (Exception e) {
            log.error("插入最终日志异常", e);
        }
    }


}
