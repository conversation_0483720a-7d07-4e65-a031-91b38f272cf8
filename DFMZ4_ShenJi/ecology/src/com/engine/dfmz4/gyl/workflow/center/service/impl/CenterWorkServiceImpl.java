package com.engine.dfmz4.gyl.workflow.center.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.engine.core.impl.Service;
import com.engine.dfmz4.gyl.workflow.center.core.UpdateFNADataCore;
import com.engine.dfmz4.gyl.workflow.center.service.CenterWorkService;
import weaver.hrm.User;

import java.util.Map;


public class CenterWorkServiceImpl extends Service implements CenterWorkService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> updateFNAData(Map<String, Object> params, JSONObject jsonBody, User user) {
        return new UpdateFNADataCore(params, jsonBody, user).execute();
    }

}
