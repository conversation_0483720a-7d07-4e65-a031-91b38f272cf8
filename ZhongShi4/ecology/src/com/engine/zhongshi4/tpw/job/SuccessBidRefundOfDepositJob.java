package com.engine.zhongshi4.tpw.job;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.bean.SDUpdateDataLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * @FileName SuccessBidRefundOfDepositJob
 * @Description 中标单位退保证金倒计时
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/22
 */

public class SuccessBidRefundOfDepositJob extends BaseCronJob {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private SDLogUtil sdLogUtil;
    private SDLog sdLog;
    private String errMsg;
    //项目信息
    private JSONArray projecInfors;
    private SDUpdateDataLog sdUpdateDataLog;

    @Override
    public void execute() {
        try {
            _init();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }
            appendLog("SuccessBidRefundOfDepositJob----Start");
            executeMy();
        } catch (Exception e) {
            appendLog("execute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("SuccessBidRefundOfDepositJob----END");
            afterExecute();
        }
    }

    private void executeMy() {
        try {
            appendLog("executeMy---start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            if (!projecInfors.isEmpty()) {
                for (int i = 0; i < projecInfors.size(); i++) {
                    JSONObject projecInfor = (JSONObject) projecInfors.get(i);
                    String id = Util.null2String(projecInfor.get("id"));
                    String rq = Util.null2String(projecInfor.get("rq"));
                    long daysRemaining = calculateDaysRemaining(rq);

                    String sql = "update uf_cwcc_xmrw set zbdwtbzjdjs = ? where id = ?";
                    recordSet.executeUpdate(sql, daysRemaining, id);
                }
            }
            failedBidUpdateStatus();
        } catch (Exception e) {
            errMsg = "executeMy:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("executeMy---end");
        }
    }

    private void _init() {
        try {
            errMsg = "";
            sdLogUtil = new SDLogUtil();
            //初始化日志bean
            sdLog = new SDLog(1,
                    this.getClass().getSimpleName(),
                    this.getClass().getName(),
                    SDLog.TYPE_JOB,
                    "中标单位退保证金倒计时");
            sdLog.setRelate_module("定时任务");
            sdLog.setRelate_table("");
            sdLog.setRelate_dataid("");
            projecInfors = new JSONArray();
            sdUpdateDataLog = new SDUpdateDataLog();
            filterProjecInforsData();
        } catch (Exception e) {
            errMsg = "SuccessBidRefundOfDepositJob----_init:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        }
    }

    private void failedBidUpdateStatus() {
        try {
            appendLog("failedBidUpdateStatus---start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            ArrayList<String> ids = new ArrayList<>();
            String sql = "SELECT \n" +
                    "    a.id\n" +
                    " FROM \n" +
                    "    uf_cwcc_xmrw a\n" +
                    "WHERE \n" +
                    "    a.zbdwtbzjdjs <> '' and a.zbdwtbzjdjs is not null";
            appendLog("failedBidUpdateStatus---sql " +sql);
            recordSet.executeQuery(sql);
            while (recordSet.next()) {
                String id = recordSet.getString("id");
                ids.add(id);
            }
            for (int i = 0; i < ids.size(); i++) {
                String id = ids.get(i);
                String str1 = "";
                String str2 = "";
                String sql1 = "SELECT COUNT(*) AS sl \n" +
                        "FROM uf_cwcc_xmrw_dt2 b\n" +
                        "WHERE (b.bzjthrq <> '' AND b.bzjthrq IS NOT NULL)\n" +
                        "AND EXISTS (\n" +
                        "    SELECT 1 \n" +
                        "    FROM uf_cwcc_xmrw_dt4 d4 \n" +
                        "    WHERE d4.mainid = ?\n" +
                        "    AND d4.bjh = b.bjh\n" +
                        "    AND d4.dwmcwb = b.dwmc\n" +
                        ")\n" +
                        "AND b.mainid = ?";
                recordSet.executeQuery(sql1,id,id);
                if (recordSet.next()) {
                    str1 = recordSet.getString("sl");
                }
                String sql2 = "SELECT COUNT(*) AS sl \n" +
                        "FROM uf_cwcc_xmrw_dt2 b\n" +
                        "WHERE EXISTS (\n" +
                        "    SELECT 1 \n" +
                        "    FROM uf_cwcc_xmrw_dt4 d4 \n" +
                        "    WHERE d4.mainid = ?\n" +
                        "    AND d4.bjh = b.bjh\n" +
                        "    AND d4.dwmcwb = b.dwmc\n" +
                        ")\n" +
                        "AND b.mainid = ?";
                recordSet.executeQuery(sql2,id,id);
                if (recordSet.next()) {
                    str2 = recordSet.getString("sl");
                }
                if(StringUtils.isNotBlank(str1) && StringUtils.isNotBlank(str2)){
                    if(str1.equals(str2)){
                        ids.remove(id);
                    }
                }
            }
            appendLog("failedBidUpdateStatus---ids " + StringUtils.join(ids, ","));
            int batchSize = 500;
            int totalBatches = (int) Math.ceil((double) ids.size() / batchSize);

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, ids.size());
                List<String> batchIds = ids.subList(fromIndex, toIndex);
                String updateSql = "UPDATE uf_cwcc_xmrw SET zbdwtbzjdjs = '' " +
                        "WHERE id IN ('" + String.join("','", batchIds) + "')";
                appendLog("Executing batch " + (i + 1) + "/" + totalBatches +
                        ", IDs: " + fromIndex + "-" + (toIndex - 1));
                recordSet.executeUpdate(updateSql);
            }

        } catch (Exception e) {
            errMsg = "failedBidUpdateStatus:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("failedBidUpdateStatus---end");
        }
    }



    //筛选项目数据
    private void filterProjecInforsData() {
        try {
            appendLog("filterProjecInforsData---start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String sql = "SELECT DISTINCT\n" +
                    "    b.id,\n" +
                    "    b.htqdrq\n" +
                    "FROM \n" +
                    "    htxxreport a\n" +
                    "INNER JOIN \n" +
                    "    uf_cwcc_xmrw b ON a.theid = b.ah\n" +
                    "INNER JOIN \n" +
                    "    uf_cwcc_xmrw_dt2 c ON b.id = c.mainid\n" +
                    "WHERE \n" +
                    "    a.zbjje > 0\n" +
                    "    AND (c.bzjthrq = '' OR c.bzjthrq IS NULL)\n" +
                    "    AND (b.htqdrq <> '' AND b.htqdrq IS NOT NULL)\n" +
                    "    AND EXISTS (\n" +
                    "        SELECT 1 \n" +
                    "        FROM uf_cwcc_xmrw_dt4 d4 \n" +
                    "        WHERE d4.mainid = b.id \n" +
                    "        AND d4.bjh = c.bjh\n" +
                    "        AND d4.dwmcwb = c.dwmc\n" +
                    "    )\n" +
                    "GROUP BY \n" +
                    "    b.id,b.htqdrq";
            appendLog("filterProjecInforsData---sql :" + sql);
            recordSet.executeQuery(sql);
            while (recordSet.next()) {
                String id = Util.null2String(recordSet.getString("id"));
                String rq = Util.null2String(recordSet.getString("htqyrq"));
                if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(rq)) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", id);
                    jsonObject.put("rq", rq);
                    projecInfors.add(jsonObject);
                }
            }
            String projecInforsStr = JSONObject.toJSONString(projecInfors);
            int size = projecInfors.size();
            appendLog("filterProjecInforsData---projecInfors :" + projecInforsStr);
            appendLog("filterProjecInforsData---projecInfors size :" + size);
            appendLog("filterProjecInforsData---end");
        } catch (Exception e) {
            errMsg = "筛选项目数据异常" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void appendLog(String logMsg) {
        logger.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

    private void afterExecute() {
        try {
            appendLog("afterExecute----Start");
            //清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            //插入二开日志
            ModuleResult moduleResult = SDLog.saveLog(sdLog, sdLogUtil.getFullLog());
            appendLog("插入二开日志 moduleResult " + JSONObject.toJSONString(moduleResult));
        } catch (Exception e) {
            appendLog("afterExecute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("afterExecute----end");
        }
    }

    /**
     * 计算中标通知书日期+5天后的剩余天数
     * @param notificationDateStr yyyy-MM-dd格式的日期字符串
     * @return 剩余天数（通知书日期+5天 - 当前日期）
     * @throws IllegalArgumentException 如果日期格式无效
     */

    public  long calculateDaysRemaining(String notificationDateStr) {
        // 解析日期字符串
        LocalDate notificationDate = parseDate(notificationDateStr);

        // 计算通知书日期+5天
        LocalDate deadlineDate = notificationDate.plusDays(5);

        // 计算剩余天数（可能为负数）
        return ChronoUnit.DAYS.between(LocalDate.now(), deadlineDate);
    }

    /**
     * 解析yyyy-MM-dd格式的日期字符串
     */

    private LocalDate parseDate(String dateStr) {
        try {
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的日期格式，请使用yyyy-MM-dd格式", e);
        }
    }
}
