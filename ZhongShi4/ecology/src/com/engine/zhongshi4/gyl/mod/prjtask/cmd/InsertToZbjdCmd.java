package com.engine.zhongshi4.gyl.mod.prjtask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class InsertToZbjdCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private String error;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public InsertToZbjdCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.error = "";
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        List<String> sqlList = new ArrayList<>();
        String insertSql;
        try {
            //校验参数
            checkParams();
            if (error.isEmpty()) {
                String checkedIds = Util.null2String(params.get("checkedIds"));//勾选的明细2的数据ids
                String sql = "select * from uf_cwcc_xmrw_dt7 where id in (" + checkedIds + ") order by id";
                log.info("sql:" + sql);
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                if (rs.executeQuery(sql)) {
                    int recordCount = 0;
                    while (rs.next()) {
                        recordCount++;
                        String mainid = Util.null2String(rs.getString("mainid")); //主表id
                        String ah = Util.null2String(rs.getString("ah")); //案号（单浏览）
                        String bjh = Util.null2String(rs.getString("bjh")); //包件号
                        String detailid = Util.null2String(rs.getString("id")); //明细的数据id -> 对应中标单位
                        String dwmc = Util.null2String(rs.getString("gysmcwb")); //单位名称

                        //插入中标阶段的数据
                        insertSql = "insert into uf_cwcc_xmrw_dt4 (mainid,ah,bjh,zbdw,dwmcwb) values" +
                                " (" + mainid + "," + ah + ",'" + bjh + "','" + detailid + "','" + dwmc + "')";
                        sqlList.add(insertSql);
                    }
                    log.info("查询到 " + recordCount + " 条记录，构建了 " + sqlList.size() + " 条插入SQL");
                } else {
                    error = "查询明细数据失败";
                    log.error("查询SQL执行失败: " + sql);
                }
                log.info("sqlList: " + sqlList);
                if (!sqlList.isEmpty()) {
                    //用事务执行插入
                    error = DBUtil.executeTransactionSqlList(sqlList);
                } else {
                    log.warn("没有需要插入的数据");
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        //勾选的明细2的数据ids，主表任务台账数据id
        String[] requiredCols = new String[]{"checkedIds"};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
        }
    }

}
