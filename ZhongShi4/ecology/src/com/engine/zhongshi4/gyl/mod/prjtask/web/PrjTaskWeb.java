package com.engine.zhongshi4.gyl.mod.prjtask.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongshi4.gyl.mod.prjtask.service.PrjTaskService;
import com.engine.zhongshi4.gyl.mod.prjtask.service.impl.PrjTaskServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;


public class PrjTaskWeb {

    private PrjTaskService getService(User user) {
        return ServiceUtil.getService(PrjTaskServiceImpl.class, user);
    }

    /**
     * 发标阶段-勾选数据-带入到开标阶段
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/insertToKbjd")
    @Produces(MediaType.TEXT_PLAIN)
    public String insertToKbjd(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).insertToKbjd(params, user)
        );
    }

    /**
     * 开标阶段-勾选数据-带入到中标阶段
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/insertToZbjd")
    @Produces(MediaType.TEXT_PLAIN)
    public String insertToZbjd(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).insertToZbjd(params, user)
        );
    }


    /**
     * 发标阶段-点击一键退保证金逻辑（项目1中的原代码保留 com.engine.interfaces.gyl.module.projectTask.web.retreatBail，用新接口重写）
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/fabiaoTbzj")
    @Produces(MediaType.TEXT_PLAIN)
    public String fabiaoTbzj(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).fabiaoTbzj(params, user)
        );
    }

    /**
     * 中标阶段-点击退保证金-更新流程字段
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/zhongbiaoUpdateWork")
    @Produces(MediaType.TEXT_PLAIN)
    public String zhongbiaoUpdateWork(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).zhongbiaoUpdateWork(params, user)
        );
    }


}
