package com.engine.zhongshi4.gyl.mod.prjtask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.util.WorkFlowUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;


/**
 * 发标阶段- 一键退保证金
 *
 * <AUTHOR>
 */
public class FabiaoTbzjCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public FabiaoTbzjCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        error = "";
    }

    //错误信息
    private String error;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        Map<String, Object> result = new HashMap<>(2);
        try {
            //校验参数
            //明细行id
            if (Util.null2String(params.get("detailIds")).isEmpty()) {
                error = "detailIds为空";
            }
            //保证金流程可退的节点id
            if (Util.null2String(params.get("validNodeId")).isEmpty()) {
                error = "validNodeId为空";
            }
            if (error.isEmpty()) {
                //处理明细逻辑
                handleDetail();
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("result :" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }


    private void handleDetail() {
        String reqId;
        String submitResult;
        int creator;
        String reqTableName, sfzflx;
        //可以执行退回的节点id
        String validNodeId = Util.null2String(params.get("validNodeId"));
        try {
            //获取对应的明细数据
            List<Map<String, Object>> detailList = getDetailData();
            if (detailList != null && !detailList.isEmpty()) {
                List<String> reqIdList = new ArrayList<>();
                for (Map<String, Object> map : detailList) {
                    reqId = Util.null2String(map.get("bzjrequestid"));
                    reqIdList.add(reqId);
                }
                String reqIds = String.join(",", reqIdList);
                //校验流程是否匹配可退的节点id
                Map<String, String> mapReqTable = new HashMap<>();
                Map<String, String> mapSubmitor = checkReqNode(reqIds, validNodeId, mapReqTable);
                log.info("mapCreator:" + mapSubmitor);
                log.info("mapReqTable:" + mapReqTable);
                String[] reqIdsArray = reqIds.split(CommonCst.COMMA_EN);
                if (mapSubmitor.size() != reqIdsArray.length) {
                    error = "流程数量和流程创建人数量不匹配，请检查流程请求id是否存在，reqIds:" + reqIds + ";mapSubmitor:" + mapSubmitor;
                }
                if (error.isEmpty()) {
                    //实际退保证金操作（提交流程）
                    for (Map<String, Object> map : detailList) {
                        reqId = Util.null2String(map.get("bzjrequestid"));
                        sfzflx = Util.null2String(map.get("sfzflx"));//是否支付利息
                        creator = Integer.parseInt(mapSubmitor.get(reqId));
                        submitResult = WorkFlowUtil.submitWorkflowRequest(Integer.parseInt(reqId), creator, "submit");
                        if (!"success".equals(submitResult)) {
                            error = "流程提交出错：" + submitResult + "，提交人id为：" + creator + ",流程请求id为：" + reqId + "，请确认该人是否有当前流程节点的提交权限！";
                            break;
                        } else {
                            //退成功之后，更新流程中退还金额=保证金金额
                            RecordSet rs = new RecordSet();
                            //先拿到requestid对应的表单
                            reqTableName = mapReqTable.get(reqId);
                            writeLog("reqId:" + reqId);
                            writeLog("reqTableName:" + reqTableName);
                            //2025-07-24修改，
                            // 若选中供应商于中标阶段中存在，则自动带入是否中标字段
                            boolean sfzbFlag = checkZhongBiao(map);
                            String sfzb = sfzbFlag ? "0" : "1";
                            log.info("是否存在中标单位:" + sfzb);
                            //更新流程
                            String sql = "update " + reqTableName + " set thje = bzjje,sfzb =?,sfzflx= ? where requestid = ?";
                            log.info("提交后更新流程sql:" + sql + ",sfzb:" + sfzb + ",sfzflx:" + sfzflx + ",reqId:" + reqId);
                            if (rs.executeUpdate(sql, sfzb, sfzflx, reqId)) {
                                log.info("提交后更新流程成功reqId:" + reqId);
                            } else {
                                log.info("提交后更新流程失败:" + rs.getExceptionMsg() + ",reqId:" + reqId);
                            }
                        }
                    }
                }
            } else {
                error = "未获取到相应的明细数据";
            }
        } catch (Exception e) {
            error = "执行异常:" + Arrays.toString(e.getStackTrace());
            log.info(error);
        }
    }

    /**
     * 校验当前明细的单位，是否中标
     *
     * @param map
     * @return
     */
    private boolean checkZhongBiao(Map<String, Object> map) {
        String detail2Id = Util.null2String(map.get("id"));
        String mainid = Util.null2String(map.get("mainid"));
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String sql = "select id from uf_cwcc_xmrw_dt4 where mainid = ? and zbdw in (select id from uf_cwcc_xmrw_dt7 where gysmc = ? and mainid = ?)";
        if (rs.executeQuery(sql, mainid, detail2Id, mainid)) {
            return rs.next();
        }
        return false;
    }

    private List<Map<String, Object>> getDetailData() {
        String detailIds = Util.null2String(params.get("detailIds"));
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String sql = "select d.*,m.bzjsfzflx from uf_cwcc_xmrw_dt2 d " +
                " inner join uf_cwcc_xmrw m on (d.mainid = m.id)" +
                " where d.id in (" + detailIds + ")" +
                " and d.bzjrequestid is not null " +
                " order by d.id";
        if (rs.executeQuery(sql)) {
            return SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
        } else {
            log.info("getDetailData sql error:" + rs.getExceptionMsg());
        }
        return null;
    }


    /**
     * 校验流程节点
     *
     * @param reqIds
     * @param validNodeId
     * @return
     */
    private Map<String, String> checkReqNode(String reqIds, String validNodeId, Map<String, String> mapReqTable) {
        Map<String, String> mapSubmitor = new HashMap<>();
        String currentnodeid, requestid, requestname, gongchengshi, tableName;
        String[] validNodeArray = validNodeId.split(CommonCst.COMMA_EN);
        List<String> listValidNode = new ArrayList<>(Arrays.asList(validNodeArray));
        boolean rsFlag;
        String sql = "select " +
                " a.requestid, " +
                " a.requestname, " +
                " a.creater, " +
                " a.currentnodeid, " +
                " a.lastoperator, " +
                " a.workflowid,  " +
                " c.tablename,  " +
                " b.workflowname  " +
                " from " +
                " workflow_requestbase a " +
                " left join workflow_base b on a.workflowid = b.id " +
                " left join workflow_bill c on c.id = b.formid  " +
                " where " +
                " a.requestid  in (" + reqIds + ") ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        rsFlag = rs.executeQuery(sql);
        if (!rsFlag) {
            error = "查询流程对应的节点数据出错：" + rs.getExceptionMsg();
        } else {
            while (rs.next()) {
                currentnodeid = Util.null2String(rs.getString("currentnodeid"));
                requestid = Util.null2String(rs.getString("requestid"));
                requestname = Util.null2String(rs.getString("requestname"));

                tableName = Util.null2String(rs.getString("tablename"));
                gongchengshi = getGongChengShi(tableName, requestid);
                if (gongchengshi.isEmpty()) {
                    error = "流程标题为：【" + requestname + "】，请求Id为：【" + requestid + "】的流程未找到有效的工程师人员，请检查流程和节点配置！";
                    break;
                }
                mapReqTable.put(requestid, tableName);
                if (!listValidNode.contains(currentnodeid)) {
                    error = "流程标题为：【" + requestname + "】，请求Id为：【" + requestid + "】的流程当前节点不可进行退保证金操作，请检查流程和节点配置！";
                    break;
                } else {
                    mapSubmitor.put(requestid, gongchengshi);
                }
            }
        }
        return mapSubmitor;
    }


    /**
     * 获取流程的工程师即项目组成员字段，使用工程师中任意一个人进行流程的提交
     *
     * @param tableName
     * @param requestid
     * @return
     */
    private String getGongChengShi(String tableName, String requestid) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String users, user = "";
        rs.executeQuery("select xmzcy from " + tableName + " where requestid = ?", requestid);
        if (rs.next()) {
            users = Util.null2String(rs.getString("xmzcy"));
            if (!users.isEmpty()) {
                user = users.split(CommonCst.COMMA_EN)[0];
            }
        }
        return user;
    }

}
