package com.engine.zhongshi4.gyl.mod.prjtask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ZhongBiaoUpdateWorkCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private String error;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public ZhongBiaoUpdateWorkCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.error = "";
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        List<String> sqlList = new ArrayList<>();
        String sql;
        try {
            //校验参数
            checkParams();
            if (error.isEmpty()) {
                String checkedIds = Util.null2String(params.get("checkedIds"));//勾选的明细4的数据ids
                sql = "select d.*,m.bzjsfzflx " +
                        " from uf_cwcc_xmrw_dt4 d " +
                        " inner join uf_cwcc_xmrw m on (d.mainid = m.id) " +
                        " where d.id in (" + checkedIds + ") " +
                        " order by d.id";
                log.info("sql:" + sql);
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                if (rs.executeQuery(sql)) {
                    while (rs.next()) {
                        String lc = Util.null2String(rs.getString("lc")); //流程requestid
                        String bzjsfzflx = Util.null2String(rs.getString("bzjsfzflx")); //是否退利息
                        WfInfo wfInfo = WfUtil.getWfInfoByReqId(lc); //根据流程requestid查询流程信息
                        //插入开标阶段的数据
                        if (bzjsfzflx.isEmpty()) {
                            sql = "update " + wfInfo.getFormtableName() + " set sfzb = 0 where requestid=" + lc;
                        } else {
                            sql = "update " + wfInfo.getFormtableName() + " set sfzb = 0,sfzflx = " + bzjsfzflx + " where requestid=" + lc;
                        }
                        sqlList.add(sql);
                    }
                    log.info("构建了 " + sqlList.size() + " 条更新SQL");
                } else {
                    error = "查询明细数据失败";
                    log.error("查询SQL执行失败: " + sql);
                }
                log.info("sqlList: " + sqlList);
                if (!sqlList.isEmpty()) {
                    //用事务执行更新
                    error = DBUtil.executeTransactionSqlList(sqlList);
                } else {
                    log.warn("没有需要插入的数据");
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }


        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        //勾选的明细2的数据ids，主表任务台账数据id
        String[] requiredCols = new String[]{"checkedIds"};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
        }
    }


}
