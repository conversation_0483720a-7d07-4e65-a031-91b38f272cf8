package com.engine.zhongshi4.gyl.mod.prjtask.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongshi4.gyl.mod.prjtask.cmd.FabiaoTbzjCmd;
import com.engine.zhongshi4.gyl.mod.prjtask.cmd.InsertToKbjdCmd;
import com.engine.zhongshi4.gyl.mod.prjtask.cmd.InsertToZbjdCmd;
import com.engine.zhongshi4.gyl.mod.prjtask.cmd.ZhongBiaoUpdateWorkCmd;
import com.engine.zhongshi4.gyl.mod.prjtask.service.PrjTaskService;
import weaver.hrm.User;

import java.util.Map;


public class PrjTaskServiceImpl extends Service implements PrjTaskService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> insertToKbjd(Map<String, Object> params, User user) {
        return commandExecutor.execute(new InsertToKbjdCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> insertToZbjd(Map<String, Object> params, User user) {
        return commandExecutor.execute(new InsertToZbjdCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> fabiaoTbzj(Map<String, Object> params, User user) {
        return commandExecutor.execute(new FabiaoTbzjCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> zhongbiaoUpdateWork(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ZhongBiaoUpdateWorkCmd(params, user));
    }

}
