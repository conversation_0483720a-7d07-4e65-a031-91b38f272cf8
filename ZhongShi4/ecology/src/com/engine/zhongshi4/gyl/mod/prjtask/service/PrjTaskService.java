package com.engine.zhongshi4.gyl.mod.prjtask.service;

import weaver.hrm.User;

import java.util.Map;


public interface PrjTaskService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> insertToKbjd(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> insertToZbjd(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> fabiaoTbzj(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> zhongbiaoUpdateWork(Map<String, Object> params, User user);
}

