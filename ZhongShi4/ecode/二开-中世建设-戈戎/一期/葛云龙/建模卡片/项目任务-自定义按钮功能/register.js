let isRun = false
let cfgParam
let isAddButtonEvent = false
//手动发起的保证金流程 formtable名称
const bzjFormTableName = "formtable_main_43"
/**
 * 执行主方法，方法名固定
 * @param newProps 组件参数，可对其更改
 * @param params 主控的配置信息
 */
const execute = (newProps, params) => {

    //只需要执行一次的逻辑
    if (!isRun) {
        // console.log('params', params)
        //初始化
        cfgParam = params
    }
    isRun = true

    //以下为需要多次执行的逻辑
    //添加自定义按钮
    addCustomButton()
}


//添加自定义按钮
const addCustomButton = () => {
    let copy1Lenth = $("#copy1").length
    console.log('copy1Lenth', copy1Lenth * 1)
    if (copy1Lenth * 1 > 0) return
    //todo 控制按钮权限
    console.log('执行addCustomButton copy')
    $(".custombutton").css({
        'display': 'flex',
        'float': 'right',
    })

    //基本信息
    //复制按钮
    let copyElm = '<div style="margin:10px" class="custombtnEdit" id="copy1"><button class="ant-btn ant-btn-primary" style="font-style:initial">复制</button></div>'
    $("#custombutton1").prepend(copyElm)

    //发标信息
    //一键退还保证金按钮
    let bzjElm = '<div style="margin:10px" class="custombtnEdit" id="bzj2"><button  class="ant-btn ant-btn-primary" style="font-style:initial">一键退还保证金</button></div>'
    $("#custombutton2").prepend(bzjElm)


    //开评标阶段
    let zhuangjiafeiElm = '<div style="margin:10px" class="custombtnEdit" id="zjf3"><button class="ant-btn ant-btn-primary" style="font-style:initial">发起专家费流程</button></div>'
    let chouquElm = '<div style="margin:10px" class="custombtnEdit" id="chouqu3"><button class="ant-btn ant-btn-primary" style="font-style:initial">专家抽取</button></div>'
    let jieguoElm = '<div style="margin:10px" class="custombtnEdit" id="chakan3"><button class="ant-btn ant-btn-primary" style="font-style:initial">查看结果</button></div>'
    $("#custombutton3").prepend(zhuangjiafeiElm)
    $("#custombutton3").prepend(chouquElm)
    $("#custombutton3").prepend(jieguoElm)

    //归档阶段
    // let gdElm = '<div style="margin:10px" class="custombtnEdit" id="gd5"><button class="ant-btn ant-btn-primary" style="font-style:initial">总公司归档流程</button></div>'
    // $("#custombutton5").prepend(gdElm)

    //分配阶段
    // let fpElm = '<div style="margin:10px" class="custombtnEdit" id="fp6"><button class="ant-btn ant-btn-primary" style="font-style:initial">工程师分配流程</button></div>'
    // $("#custombutton6").prepend(fpElm)

    //按钮事件只能添加一次，否则会出现点一次多次执行的问题
    if (!isAddButtonEvent) {
        $(document).on("click", "#copy1", function (event) {
            handleCopy1()
        })
        $(document).on("click", "#bzj2", function (event) {
            handleBzj2("detail_2")
        })

        $(document).on("click", "#zjf3", function (event) {
            createWorkFlow("detail_3")
        })

        $(document).on("click", "#chouqu3", function (event) {
            handleChouqu3()
        })
        $(document).on("click", "#chakan3", function (event) {
            handleChakan3()
        })

        // $(document).on("click", "#gd5", function (event) {
        //     createWorkFlow("detail_5")
        // })
        // $(document).on("click", "#fp6", function (event) {
        //     createWorkFlow("detail_6")
        // })
        isAddButtonEvent = true
    }

}


//处理基本信息的复制功能
const handleCopy1 = () => {
    //判断当前任务是否是已经复制的任务，不可以复制已经复制的项目
    let ysah = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ysah"))
    if (ysah) {
        ModeList.showMessage("不可复制已经复制的项目！请复制原项目", 1, 3)
        return
    }
    //调用查询已复制的项目
    let url = '/api/projectTask/mod/v1/queryCopyProject'
    //项目id取案号字段的值
    let projectId = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ah"))
    let projectName = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("xmmc"))
    let anhaoName = ModeForm.getBrowserShowName(ModeForm.convertFieldNameToId("ah"))
    console.log('anhaoName', anhaoName)
    let params = {
        projectId: projectId,
    }
    let newProjectName, newAnhaoName
    window.sdCommonUtil.callOaApiAysnc(url, params, function (resultData) {
        console.log('resultData', resultData)
        let status = resultData.status
        if (status == '1') {
            let copyCnt = resultData.cnt
            let newTimes = copyCnt * 1 + 2
            newProjectName = projectName + "(第" + newTimes + "次)"
            newAnhaoName = anhaoName + "(第" + newTimes + "次)"

            //首次复制
            if (copyCnt * 1 == 0) {

                ModeList.showConfirm("请确认下是否需要复制此项目【" + projectName + "】，复制前请确保有完整的包件信息再复制，复制错误需承担责任", function () {
                    ModeList.showConfirm("即将复制出新的项目名为：【" + newProjectName + "】,案号为：【" + newAnhaoName + "】，请确认是否复制？", function () {
                        doCopy1(copyCnt)
                    })
                })

            }
            //第2次复制
            // if (copyCnt * 1 > 0) {
            //   ModeList.showConfirm("请确认下是否需要复制此项目【" + projectName + "】，复制错误需承担责任", function () {
            //         ModeList.showConfirm("即将复制出新的项目名为：【" + newProjectName + "】,案号为：【" + newAnhaoName + "】，请确认是否复制？", function () {
            //             doCopy1(copyCnt)
            //         })
            //     })
            // }
            //第3次复制以上
            if (copyCnt * 1 > 0) {


                ecCom.WeaTools.createDialog({
                    title: "项目",
                    url: "/spa/cube/index.html#/main/cube/search?customid=17&ysxm=" + projectId,
                    icon: "icon-coms-workflow",
                    iconBgcolor: "#0079DE",
                    style: {width: "900px", height: 500},
                    callback: (datas) => { // 数据通信
                        ModeList.showConfirm("请确认下是否需要复制此项目【" + projectName + "】，复制前请确保有完整的包件信息再复制，复制错误需承担责任", function () {
                            ModeList.showConfirm("即将复制出新的项目名为：【" + newProjectName + "】,案号为：【" + newAnhaoName + "】，请确认是否复制？", function () {
                                doCopy1(copyCnt)
                            })
                        })

                    },
                    onCancel: () => { // 关闭通信
                        console.log("取消复制！")
                    }
                }, undefined, (dialog) => {
                    // 由于组件异步化可能导致第一次没有加载到组件，所以需要在回调中调用
                    dialog.show()
                })
            }
        } else {
            ModeList.showMessage("查询已复制的项目信息出错：" + resultData.erroMsg, 2, 5)
        }
    })
}
//复制
const doCopy1 = (copyTimes) => {
    let url = '/api/projectTask/mod/v1/copyProject'
    let projectId = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ah"))
    let taskId = ModeForm.getCardUrlInfo().billid
    let projectModuleId = window.sdCommonUtil.getBrowserUrlParam("modeId")
    console.log('projectModuleId', projectModuleId)
    let taskModuleId = ModeForm.getCardUrlInfo().modeId
    let params = {
        projectId: projectId,
        taskId: taskId,
        copyTimes: copyTimes,
        projectModuleId: projectModuleId,
        taskModuleId: taskModuleId
    }

    window.sdCommonUtil.callOaApiAysnc(url, params, function (resultData) {
        let status = resultData.status
        if (status == '1') {
            let newProjectId = resultData.newProjectId
            let newUrl = "/spa/cube/index.html#/main/cube/card?billid=" + newProjectId + "&type=0&modeId=6"
            window.open(newUrl)
        } else {
            ModeList.showMessage("复制的项目信息出错：" + resultData.erroMsg, 2, 5)
        }
    })

}

/**
 * 一键退还保证金
 */
const handleBzj2 = (detailStr) => {
    //1.判断是否勾选了明细
    let checkedKeys = ModeForm.getDetailCheckedRowKey(detailStr)
    console.log('勾选明细', checkedKeys)
    if (!checkedKeys) {
        ModeList.showMessage("请先勾选供应商信息!", 1, 2)
        return
    } else {
        ModeList.showConfirm("请检查中标阶段中标单位是否填写", function () {
            //2.判断是否选中了中标单位
            let confirmMsg = '是否确认退保证金？'
            //获取中标明细数据
            let mapVendor = new Map()
            let detail4 = "detail_4"
            //中标单位id
            let fi_zbdw = ModeForm.convertFieldNameToId("zbdw", detail4)
            console.log('中标单位id', fi_zbdw)
            if (ModeForm.getDetailAllRowIndexStr(detail4)) {
                let rowArr = ModeForm.getDetailAllRowIndexStr(detail4).split(",")
                for (let i = 0; i < rowArr.length; i++) {
                    let rowIndex = rowArr[i]
                    if (rowIndex !== "") {
                        let fieldMark = fi_zbdw + "_" + rowIndex
                        let fv = ModeForm.getFieldValue(fieldMark)
                        mapVendor.set(fv, '')
                    }
                }
            }
            //有中标单位的单位
            let allZhongBiaoDw = ""
            //缺失来源流程BZJrequestid的单位
            let allWithoutWf = ""
            //单位名称
            let fi_vendor = ModeForm.convertFieldNameToId("dwmc", detailStr)
            //保证金fieldid
            let fi_wf = ModeForm.convertFieldNameToId("BZJrequestid", detailStr)
            console.log('保证金fieldid', fi_wf)
            //保证金状态
            let fi_status = ModeForm.convertFieldNameToId("bzjsfth", detailStr)
            var ids = []
            if (ModeForm.getDetailAllRowIndexStr(detailStr)) {
                let rowArr = ModeForm.getDetailAllRowIndexStr(detailStr).split(",")
                for (let i = 0; i < rowArr.length; i++) {
                    let rowIndex = rowArr[i]
                    if (rowIndex !== "") {
                        let fieldMark = fi_vendor + "_" + rowIndex
                        let currentKey = ModeForm.getDetailRowKey(fieldMark)
                        let fieldMark_status = fi_status + "_" + rowIndex
                        //只校验勾选的行
                        if (checkedKeys.indexOf(currentKey) >= 0) {
                            //检查来源流程是否有值
                            let lc_fieldMark = fi_wf + "_" + rowIndex
                            //保证金requestid
                            let lc_fv = ModeForm.getFieldValue(lc_fieldMark)
                            ids.push(lc_fv)
                            //保证金状态
                            let fv_status = ModeForm.getFieldValue(fieldMark_status)
                            if (fv_status == '1') {
                                ModeList.showMessage("存在已退的保证金流程 ,请检查！", 2, 5)
                                return
                            }
                            //这里取明细的主键id，因为明细4中的单位为浏览框，存的是ID

                            let fvName = ModeForm.getFieldValue(fieldMark)
                            if (!lc_fv) {
                                allWithoutWf += fvName + ","
                            }
                            if (mapVendor.has(currentKey)) {
                                allZhongBiaoDw += fvName + ","
                            }
                        }
                    }
                }
            }
            console.log('传明细id测试', checkedKeys)
            //updateZbzt(checkedKeys)
            if (allWithoutWf) {
                allWithoutWf = allWithoutWf.substr(0, allWithoutWf.length - 1)
                ModeList.showMessage("以下选的单位缺失来源流程：【" + allWithoutWf + "】 ,请检查！", 2, 5)
            } else {
                if (allZhongBiaoDw) {
                    allZhongBiaoDw = allZhongBiaoDw.substr(0, allZhongBiaoDw.length - 1)
                    confirmMsg = "以下单位选的单位有中标单位：【" + allZhongBiaoDw + "】 是否确认退保证金？"
                }
                ModeList.showConfirm(confirmMsg, function () {
                    //updateZbzt(ids)
                    updateZbzt(checkedKeys)
                    doBzj2(checkedKeys, detailStr)
                })
            }
            // //2023-02-13修改，缺失来源流程requestid的数据的明细行也可以执行，代表的是手动发起的退保证金
            // if (allZhongBiaoDw) {
            //     allZhongBiaoDw = allZhongBiaoDw.substr(0, allZhongBiaoDw.length - 1)
            //     confirmMsg = "以下选的单位有中标单位：【" + allZhongBiaoDw + "】 是否确认退保证金？"
            // }
            // ModeList.showConfirm(confirmMsg, function () {
            //     doBzj2(checkedKeys,detailStr)
            // })
        })
    }
}

/**
 * 更新流程的中标状态
 *
 */

const updateZbzt = (ids) => {
    //更新保证金的流程状态
    let gxwzbUrl = 'http://test.cwcc.sh.cn/api/Ecology/UpdateBidState?ids=' + ids
    //window.open(gxwzbUrl)
    jQuery.ajax({
        url: gxwzbUrl,
        type: 'get',
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function (data) {
            console.log('调用更新保证金中标状态接口')
        }
    })

}
/**
 * 退保证金
 */
const doBzj2 = (checkedKeys, detailStr) => {
    let anhao = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ah"))
    //let url = '/api/projectTask/mod/v1/retreatBail'
    //2025-07-24 换为新的接口，有新的逻辑
    let url = '/api/sd/zs4/gyl/prjtask/fabiaoTbzj'
    let validNodeId = cfgParam.validNodeId
    let params = {
        anhao: anhao,
        detailIds: checkedKeys,
        validNodeId: validNodeId,
        bzjFormTableName: bzjFormTableName,
    }
    window.sdCommonUtil.callOaApiAysnc(url, params, function (resultData) {
        let status = resultData.status
        //2025-07-24 换为新的接口的返回值为boolean类型的了
        if (status) {
            ModeList.showMessage("退保证金成功！", 3, 2)
            //回写退保证金状态，为已退保证金
            updateBzjStatus(checkedKeys, detailStr)

        } else {
            ModeList.showMessage("退保证金操作出错：" + resultData.error, 2, 5)
        }
    })
}

/**
 * 更新专家费状态为支付中，目标流程id
 */
const updateBzjStatus = (checkedKeys, detailStr) => {
    //保证金状态字段
    let fi_status = ModeForm.convertFieldNameToId("bzjsfth", detailStr)

    if (ModeForm.getDetailAllRowIndexStr(detailStr)) {
        let rowArr = ModeForm.getDetailAllRowIndexStr(detailStr).split(",")
        for (let i = 0; i < rowArr.length; i++) {
            let rowIndex = rowArr[i]
            if (rowIndex !== "") {
                let fieldMark = fi_status + "_" + rowIndex
                let currentKey = ModeForm.getDetailRowKey(fieldMark)
                //只校验勾选的行
                if (checkedKeys.indexOf(currentKey) >= 0) {
                    ModeForm.changeFieldValue(fieldMark, {
                        value: "1",
                        showhtml: "1"
                    })
                }
            }
        }
        window.sdModuleFunc.saveTask("detail_2", "发标阶段")
    }
}


/**
 * 发起专家费流程
 */
const createWorkFlow = (detailStr) => {
    let anHao = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ah"))
    let currentConfig
    //勾选的明细
    let checkedKeys = ModeForm.getDetailCheckedRowKey(detailStr)

    //校验
    //专家费
    if (detailStr == "detail_3") {

        //是否业主
        let sfyzField = 'sfyz'
        let checkFields = ['zjxm', 'dh', 'gzdw', 'sfzh', 'zc', 'zy', 'fy', 'sj', 'khx', 'yxzh', 'sfyz']

        let fi_sfyzField = ModeForm.convertFieldNameToId(sfyzField, detailStr)

        currentConfig = cfgParam.zjflc
        //校验是否已经发起过专家费流程
        let wfTableName = currentConfig.workflowMainTableName
        if (!wfTableName) {
            ModeList.showMessage("未配置专家费流程主表名参数，请检查！", 2, 3)
            return
        }
        //专家费，必须要勾选明细
        if (!checkedKeys) {
            ModeList.showMessage("未勾选专家信息！", 2, 3)
            return
        }

        //状态字段校验
        let fi_status = ModeForm.convertFieldNameToId("zt", detailStr)
        //检查勾选的专家，只能勾选【未支付】状态的专家
        if (ModeForm.getDetailAllRowIndexStr(detailStr)) {
            let rowArr = ModeForm.getDetailAllRowIndexStr(detailStr).split(",")
            for (let i = 0; i < rowArr.length; i++) {
                let rowIndex = rowArr[i]
                if (rowIndex !== "") {

                    let fieldMark = fi_status + "_" + rowIndex
                    let currentKey = ModeForm.getDetailRowKey(fieldMark)
                    //状态
                    let fv_status = ModeForm.getFieldValue(fieldMark)


                    //获取是否业主字段的mark
                    let fieldMark2 = fi_sfyzField + "_" + rowIndex
                    let fieldValue2 = ModeForm.getFieldValue(fieldMark2)

                    //只校验勾选的行
                    if (checkedKeys.indexOf(currentKey) >= 0) {
                        //只能勾选【未支付】状态的专家
                        if (fv_status != '0') {
                            ModeList.showMessage("只能勾选【未支付】状态的专家！请检查", 2, 3)
                            return
                        }

                        //2023-08-29 增加校验，校验勾选的行必填项
                        let allMustLable = ''
                        //未支付状态且是非业主，进行校验
                        if (fv_status == '0' && fieldValue2 != '1') {
                            for (let j = 0; j < checkFields.length; j++) {
                                let fi_check = ModeForm.convertFieldNameToId(checkFields[j], detailStr)
                                let mark_check = fi_check + "_" + rowIndex
                                let value_check = ModeForm.getFieldValue(mark_check)
                                let show_check = ModeForm.getBrowserShowName(mark_check)
                                let lable_check = ModeForm.getFieldInfo(fi_check).fieldlabel
                                //专业编号，
                                if (checkFields[j] == "zy") {
                                    if (show_check == "") {
                                        allMustLable += lable_check + ","
                                    }
                                } else if (checkFields[j] == "fy") {
                                    //费用
                                    if (value_check * 1 == 0) {
                                        allMustLable += lable_check + ","
                                    }
                                } else {
                                    if (value_check == "") {
                                        allMustLable += lable_check + ","
                                    }
                                }

                            }
                            if (allMustLable != '') {
                                allMustLable = allMustLable.substr(0, allMustLable.length - 1)
                                ModeList.showMessage("勾选的数据在未支付且非业主状态下，以下字段必填：" + allMustLable, 2, 3);  //错误信息，10s后消失
                                return
                            }
                        }


                    }
                }
            }
        }

    }
    //归档
    if (detailStr == "detail_5") {
        currentConfig = cfgParam.gdlc
        //校验1：
        let wfTableName = currentConfig.workflowMainTableName
        if (!wfTableName) {
            ModeList.showMessage("未配置归档流程主表名参数，请检查！", 2, 3)
            return
        }
        let sql = "select id,requestid from " + wfTableName + " where ah = ?";
        let sqlParams = [anHao]
        let sqlData = window.sdCommonUtil.queryData(sql, sqlParams)
        //校验2：根据案号只能发起一次归档流程
        if (sqlData && sqlData.length && sqlData.length > 0) {
            let firstRequestId = sqlData[0].requestid
            ModeList.showMessage("当前案号已发起过归档流程，有流程请求id为：" + firstRequestId + "，不可再次发起！", 2, 3)
            return
        }
        //2023-11-09增加校验 必须有成果资料（流转类型=12）才能发起
        //校验3：明细
        let hasValidType = false
        let fi_lzlx = ModeForm.convertFieldNameToId("lzlx", detailStr)
        if (ModeForm.getDetailAllRowIndexStr(detailStr)) {
            let rowArr = ModeForm.getDetailAllRowIndexStr(detailStr).split(",")
            for (let i = 0; i < rowArr.length; i++) {
                let rowIndex = rowArr[i]
                if (rowIndex !== "") {
                    let fieldMark = fi_lzlx + "_" + rowIndex
                    //流转类型
                    let fv_lzlx = ModeForm.getFieldValue(fieldMark)
                    if (fv_lzlx == "12") {
                        hasValidType = true;
                        break;
                    }
                }
            }
        }
        if (!hasValidType) {
            ModeList.showMessage("缺少成果资料的归档文件！", 2, 3)
            return
        }

    }
    //分配
    if (detailStr == "detail_6") {
        currentConfig = cfgParam.fplc
    }

    if (!checkedKeys) {
        ModeList.showConfirm("未勾选明细信息，是否继续创建流程？", function () {
            doCreateWorkFlow(currentConfig, checkedKeys, detailStr)
        })


    } else {
        ModeList.showConfirm("确认创建流程？", function () {
            doCreateWorkFlow(currentConfig, checkedKeys, detailStr)
        })


    }

}

/**
 * 创建流程
 */
const doCreateWorkFlow = (currentConfig, checkedKeys, detailStr) => {
    ModeList.showMessage("创建流程接口执行中...", 4, 2)
    let url = '/api/projectTask/mod/v1/createWorkFlow'

    let params = {
        billid: ModeForm.getCardUrlInfo().billid,
        detailIds: checkedKeys,
        moduleDetailIndex: currentConfig.moduleDetailIndex,
        workflowDetailTableName: currentConfig.workflowDetailTableName,
        mainField: JSON.stringify(currentConfig.mainField),
        detailField: JSON.stringify(currentConfig.detailField),
        requestName: currentConfig.requestName,
        workflowId: currentConfig.workflowId,
        appid: cfgParam.appid,
        esbEvent: cfgParam.esbEvent,
    }

    window.sdCommonUtil.callOaApiAysnc(url, params, function (resultData) {
        console.log('resultData', resultData)
        let status = resultData.status
        if (status == '1') {
            let newRequestId = resultData.newRequestId
            if (newRequestId) {

                ModeList.showConfirm("发起流程成功！流程请求id为：" + newRequestId + ",是否立即打开流程？", function () {
                    let openUrl = "/spa/workflow/index_form.jsp#/main/workflow/req?requestid=" + newRequestId + "&isovertime=0"
                    console.log('openUrl', openUrl)
                    window.open(openUrl)
                })
                //专家费
                if (detailStr == 'detail_3') {
                    //更新状态为【支付中】
                    updateZjfStatus(checkedKeys, detailStr, newRequestId)
                }
            }
        } else {
            ModeList.showMessage("发起流程出错:" + resultData.erroMsg, 2, 10)
        }
    })
}
/**
 * 更新专家费状态为支付中，目标流程id
 */
const updateZjfStatus = (checkedKeys, detailStr, newRequestId) => {
    //状态字段
    let fi_status = ModeForm.convertFieldNameToId("zt", detailStr)
    //目标流程字段
    let fi_mblc = ModeForm.convertFieldNameToId("mblc", detailStr)
    if (ModeForm.getDetailAllRowIndexStr(detailStr)) {
        let rowArr = ModeForm.getDetailAllRowIndexStr(detailStr).split(",")
        for (let i = 0; i < rowArr.length; i++) {
            let rowIndex = rowArr[i]
            if (rowIndex !== "") {

                let fieldMark = fi_status + "_" + rowIndex
                let currentKey = ModeForm.getDetailRowKey(fieldMark)
                let fieldMark_mblc = fi_mblc + "_" + rowIndex
                console.log('fieldMark_mblc', fieldMark_mblc)
                let fv_status = ModeForm.getFieldValue(fieldMark)
                //只校验勾选的行
                if (checkedKeys.indexOf(currentKey) >= 0) {
                    if (fv_status == '0') {
                        ModeForm.changeFieldValue(fieldMark, {
                            value: "1",
                            showhtml: "1"
                        })
                    }
                    //设置目标流程requestid
                    ModeForm.changeFieldValue(fieldMark_mblc, {
                        value: newRequestId,
                        showhtml: newRequestId
                    })
                }
            }
        }

        console.log('newRequestId', newRequestId)
        window.sdModuleFunc.saveTask("detail_3", "开评标阶段")
    }
}


/**
 * 抽取
 */
const handleChouqu3 = () => {
    let anhao = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ah"))
    var userid = ecodeSDK.getEcodeParams(['ecode_params'])._user.id;
    let url1 = cfgParam.cqurl + 'projectid=' + anhao + '&uid=' + userid
    if (url1) {
        window.open(url1)
    } else {
        ModeList.showMessage("未配置专家抽取链接，请检查配置！", 1, 2)
    }
}

/**
 * 查看结果
 */
const handleChakan3 = () => {
    let anhao = ModeForm.getFieldValue(ModeForm.convertFieldNameToId("ah"))
    var userid = ecodeSDK.getEcodeParams(['ecode_params'])._user.id;
    let url2 = cfgParam.ckurl + 'projectid=' + anhao + '&uid=' + userid
    if (url2) {
        window.open(url2)
    } else {
        ModeList.showMessage("未配置专家查看结果链接，请检查配置！", 1, 2)
    }
}


//导出到window全局的sdModuleFunc对象中,固定语句
if (window.sdModuleFunc) {
    window.sdModuleFunc['${appId}'] = execute
} else {
    window.sdModuleFunc = {
        '${appId}': execute,
    }
}

