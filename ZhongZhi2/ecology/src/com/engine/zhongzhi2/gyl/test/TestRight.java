package com.engine.zhongzhi2.gyl.test;

import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName TestRight.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/9/29
 */
@Getter
@Setter
public class TestRight extends BaseCronJob {
    private String modid;

    /**
     *
     */
    @Override
    public void execute() {
        ModuleDataUtil.resetModShare(Integer.parseInt(modid));
    }
}
