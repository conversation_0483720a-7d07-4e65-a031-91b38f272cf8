package com.engine.zhongzhi2.gyl.crm.job;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.util.ChaceShare2ModUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName UpdateContactLogAuthJob.java
 * @Description 更新联系记录的权限，将对应商机的权限更新到建模中
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/10/21
 */
@Getter
@Setter
public class UpdateContactLogAuthJob extends BaseCronJob {
    /**
     * 指定的联系记录建模的数据id
     */
    private String executeBillId;
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(UpdateContactLogAuthJob.class);
    /**
     * 建模表名-联系记录
     */
    private String modTableName;
    /**
     * 建模id-联系记录
     */
    private int moduleId;
    /**
     * 错误信息
     */
    private String error;

    private void _init() {
        modTableName = "uf_lxjlrzqt";
        moduleId = ModuleDataUtil.getModuleIdByName(modTableName);
        error = "";
    }

    /**
     *
     */
    @Override
    public void execute() {
        log.info("---UpdateContactLogAuthJob---START");
        try {
            //初始化
            _init();
            //先删除二开插入的非默认共享
            delSDshare();
            if (error.isEmpty()) {
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                List<String> allBillIds = new ArrayList<>();
                //查询有商机的联系记录数据
                String sql = "select id,chanceid from " + modTableName + " where 1=1 and chanceid is not null and chanceid != ''";
                if (StringUtils.isNotBlank(executeBillId)) {
                    sql += " and id = " + executeBillId;
                }
                log.info("query sql:" + sql);
                if (rs.executeQuery(sql)) {
                    List<Map<String, Object>> list = QueryUtil.getMapList(rs);
                    if (!list.isEmpty()) {
                        log.info("查到到建模数量:" + list.size());
                        //使用多线程插入非默认共享
                        ExecutorService executorService = Executors.newFixedThreadPool(5);
                        CountDownLatch latch = new CountDownLatch(list.size());
                        for (Map<String, Object> eachData : list) {
                            executorService.submit(() -> {
                                try {
                                    //处理明细的每一行数据
                                    int finalBillid = Util.getIntValue(Util.null2String(eachData.get("id")));
                                    String finalChanceid = Util.null2String(eachData.get("chanceid"));
                                    allBillIds.add(Util.null2String(eachData.get("id")));
                                    ChaceShare2ModUtil.insertOtherShare(finalChanceid, finalBillid, moduleId);
                                } finally {
                                    latch.countDown(); // 线程完成任务后减少计数器
                                }
                            });
                        }
                        try {
                            // 等待所有线程完成任务
                            if (!latch.await(5, TimeUnit.HOURS)) {
                                log.error("超时未执行完所有线程任务，请检查!");
                            }
                        } catch (InterruptedException e) {
                            log.error("latch.await() 出错：", e);
                        } finally {
                            executorService.shutdown();
                        }
                        log.info("allBillIds:" + allBillIds);
                        //重构建模权限
                        ModuleDataUtil.resetModShare(moduleId, allBillIds);
                    } else {
                        log.info("未查询到有商机的联系记录");
                    }
                } else {
                    log.error("query sql error:" + rs.getExceptionMsg());
                }
            }
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        log.info("---UpdateContactLogAuthJob---END");
    }

    /**
     * 删除二开插入的非默认共享
     */
    private void delSDshare() {
        String modeDataShareSetTable = "modeDataShare_" + moduleId + "_set";
        String modeDataShareTable = "modeDataShare_" + moduleId;
        String setSql = getSetSql();
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String delSetSql = "delete from " + modeDataShareSetTable + " where id in (" + setSql + ")";
        String delShareSql = "delete from " + modeDataShareTable + " where setid in (" + setSql + ")";
        log.info("delSetSql:" + delSetSql);
        log.info("delShareSql:" + delShareSql);
        if (!rs.executeUpdate(delSetSql)) {
            error = "delSetSql error:" + rs.getExceptionMsg();
            log.error(error);
        }
        if (!rs.executeUpdate(delShareSql)) {
            error = "delShareSql error:" + rs.getExceptionMsg();
            log.error(error);
        }
    }

    /**
     * 获取建模set权限表的数据
     *
     * @return
     */
    private String getSetSql() {
        String modeDataShareSetTable = "modeDataShare_" + moduleId + "_set";
        String sql = "select id from " + modeDataShareSetTable + " where 1=1 " +
                " and isdefault = 0 " +
                " and sourceid in (" +
                " select id from " + modTableName + " where 1=1 and chanceid is not null and chanceid != '' ";
        if (StringUtils.isNotBlank(executeBillId)) {
            sql += " and id = " + executeBillId;
        }
        sql += " ) ";
        sql += " and jobleveltext = '" + ChaceShare2ModUtil.SD_SHARE_CODE + "' ";
        return sql;

    }
}
