package com.engine.zhongzhi2.gyl.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi2.gyl.crm.service.CrmSdService;
import com.engine.zhongzhi2.gyl.crm.service.impl.CrmSdServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName CrmWeb.java
 * @Description CRM模块二开接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/19
 */

public class CrmWeb {
    private CrmSdService getService(User user) {
        return ServiceUtil.getService(CrmSdServiceImpl.class, user);
    }

    /**
     * 联系记录保存时，记录别的相关信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveContactLogOthers")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveContactLogOthers(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).saveContactLogOthers(params, user)
        );
    }

    /**
     * 联系记录保存时，记录别的相关信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveContactorOthers")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveContactorOthers(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).saveContactorOthers(params, user)
        );
    }


    /**
     * 联系记录保存时，记录别的相关信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/editContactorOthers")
    @Produces(MediaType.TEXT_PLAIN)
    public String editContactorOthers(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).editContactorOthers(params, user)
        );
    }

    /**
     * 联系记录保存时，记录别的相关信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/delContactorOthers")
    @Produces(MediaType.TEXT_PLAIN)
    public String delContactorOthers(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).delContactorOthers(params, user)
        );
    }

    /**
     * 联系记录保存时，记录别的相关信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveQixin2Customer")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveQixin2Customer(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).saveQixin2Customer(params, user)
        );
    }

    /**
     * 新建客户保存时候，默认保存其他信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveCustomerOtherData")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveCustomerOtherData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).saveCustomerOtherData(params, user)
        );
    }

    /**
     * 新建商机保存后，自动保存部门助理等数据
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveSellchanceDeptHelperData")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveSellchanceDeptHelperData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).saveSellchanceDeptHelperData(params, user)
        );
    }
}
