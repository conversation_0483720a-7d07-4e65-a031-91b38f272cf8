package com.engine.zhongzhi2.gyl.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.cmd.bean.ContactLogOthers;
import com.engine.zhongzhi2.gyl.crm.util.ChaceShare2ModUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SaveContactLogOthers.java
 * @Description 保存标准crm联系人记录到建模表
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/15
 */
public class SaveContactLogOthersCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 联系记录日志 其他
     * 表名
     */
    private static final String TABLE_NAME = "uf_lxjlrzqt";

    public SaveContactLogOthersCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            int logid = Integer.parseInt(Util.null2String(params.get("logid"))); //日志id
            int crmid = Integer.parseInt(Util.null2String(params.get("crmid"))); //客户id
            String zhongzhihrm = Util.null2String(params.get("zhongzhihrm")); //中智合作方
            String chanceid = Util.null2String(params.get("chanceid")); //商机id
            if (logid > 0 && crmid > 0 && !zhongzhihrm.isEmpty()) {
                int moduleId = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
                if (moduleId != -1) {
                    ContactLogOthers contactLogOthers = new ContactLogOthers();
                    contactLogOthers.setLogid(logid); //；联系记录id，也就是workplan表的id
                    contactLogOthers.setCrmid(crmid); //客户id
                    contactLogOthers.setZhongzhihrm(zhongzhihrm); //中智合作方
                    //商机id不一定有，有的话则存储
                    if (!chanceid.isEmpty()) {
                        contactLogOthers.setChanceid(Integer.parseInt(chanceid)); //商机id
                        Map<String, Object> chanceData = getChanceData(chanceid);
                        if (chanceData != null) {
                            log.info("chanceData:" + chanceData);
                            //商机编号
                            String sjbh = Util.null2String(chanceData.get("sjbh"));
                            contactLogOthers.setSjbh(sjbh);

                            //商机负责人,及跟进人
                            Integer sjfzr = Util.getIntValue(Util.null2String(chanceData.get("creater")));
                            contactLogOthers.setSjfzr(sjfzr);
                            //商机接单人
                            Integer sjjdr = Util.getIntValue(Util.null2String(chanceData.get("jdr")));
                            contactLogOthers.setSjjdr(sjjdr);
                            //商机部门助理
                            String sjbmzl = Util.null2String(chanceData.get("bmzl"));
                            contactLogOthers.setSjbmzl(sjbmzl);
                        }
                    }
                    //获取其他一些联系记录里的信息，一并记录
                    Map<String, Object> map = getOtherLogData(logid);
                    if (map != null) {
                        Map<String, Object> newMap = SDUtil.lowerMapKey(map);

                        //相关联系人
                        List<String> contactList = new ArrayList<>();
                        contactList.add(Util.null2String(newMap.get("contacterid")));
                        List<String> listOtherContact = getOtherContact(logid);
                        contactList.addAll(listOtherContact);
                        log.info("all contact list:" + contactList);

                        contactLogOthers.setLxr(StringUtils.join(contactList, CommonCst.COMMA_EN)); //联系人
                        contactLogOthers.setLxfs(Util.null2String(newMap.get("contactway"))); //联系方式
                        contactLogOthers.setLxnr(Util.null2String(newMap.get("description"))); //联系内容
                    }
                    ModuleResult mr = ModuleDataUtil.insertObj(contactLogOthers, TABLE_NAME, moduleId, user.getUID());
                    if (mr.isSuccess()) {
                        result.put("billid", mr.getBillid());
                        //如果是上级，则添加商机的非默认共享，将商机的非默认共享也加入到建模的非默认共享中
                        if (!chanceid.isEmpty()) {
                            log.info("是商机的联系记录，开始执行插入非默认共享");
                            ChaceShare2ModUtil.insertOtherShare(chanceid, mr.getBillid(), moduleId);
                            resetNewRight(mr.getBillid(), moduleId);
                        }
                    } else {
                        error = mr.getErroMsg();
                    }
                }
            } else {
                error = "缺失参数";
            }

        } catch (Exception e) {
            error = "异常" + e.getMessage();
            log.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

    private Map<String, Object> getOtherLogData(int logid) {
        String sql = "select * from workplan where id = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, logid)) {
            return QueryUtil.getMap(rs);
        } else {
            log.error("getOtherLogData sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return null;
    }

    private List<String> getOtherContact(int logid) {
        List<String> list = new ArrayList<>();
        String sql = "select contact_user_id from crm_contact_record where contact_log_id = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, logid)) {
            while (rs.next()) {
                list.add(Util.null2String(rs.getString("contact_user_id")));
            }
        } else {
            log.error("getOtherContact sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return list;
    }

    private Map<String, Object> getChanceData(String changeid) {
        String sql = "select * from crm_sellchance where id = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, changeid)) {
            return QueryUtil.getMap(rs);
        }
        return null;
    }


    private void resetNewRight(int billid, int modeid) {
        //重新计算权限
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.setNewRight(false);
        ModeRightInfo.editModeDataShare(0, modeid, billid);
    }


}
