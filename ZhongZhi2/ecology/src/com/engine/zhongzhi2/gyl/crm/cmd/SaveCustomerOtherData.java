package com.engine.zhongzhi2.gyl.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SaveContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class SaveCustomerOtherData extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(SaveCustomerOtherData.class);


    public SaveCustomerOtherData(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            String crmid = Util.null2String(params.get("crmid")); //客户id
            if (!crmid.isEmpty()) {
                //更新 客户录入日期，自动赋值为当天日期S
                String currentDate = TimeUtil.getCurrentDateString();
                String sql = "update CRM_CustomerInfo set khlrrq = '" + currentDate + "' where id = " + crmid;
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                if (!rs.executeUpdate(sql)) {
                    error = "更新客户的录入日期出错：" + rs.getExceptionMsg() + ";sql:" + sql;
                }
            } else {
                error = "缺失参数";
                log.error(error);
            }
        } catch (Exception e) {
            error = "异常" + e.getMessage();
            log.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }


}
