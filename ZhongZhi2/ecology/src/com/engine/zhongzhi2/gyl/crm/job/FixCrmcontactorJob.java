package com.engine.zhongzhi2.gyl.crm.job;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @FileName FixCrmcontactorJob.java
 * @Description 修复联系人地图，行政管理数据
 * 执行前，备份数据:
 * select * into crm_contactor_dept_bak20241016 from crm_contactor_dept;
 * select * into crm_contactor_map_node_bak20241016 from crm_contactor_map_node;
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/10/16
 */
@Getter
@Setter
public class FixCrmcontactorJob extends BaseCronJob {
    /**
     * 指定的客户id
     */
    private String executeCustomerId;
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(FixCrmcontactorJob.class);
    /**
     * 存储新增的 客户id对应的新增的根节点id
     */
    private Map<String, String> mapRootId;

    private String error;

    private void _init() {
        mapRootId = new HashMap<>();
        error = "";
    }

    /**
     *
     */
    @Override
    public void execute() {
        log.info("---FixCrmcontactorJob START---");
        try {
            //初始化
            _init();
            //删除crm_contactor_dept
            delDept();
            if (error.isEmpty()) {
                //插入crm_contactor_dept
                insertDept();
                //获取对应关系
                getRootMap();
                log.info("mapRootId:" + mapRootId);
                if (error.isEmpty()) {
                    //删除行政关系表数据
                    delMapNode();
                    if (error.isEmpty()) {
                        //插入行政关系根节点的数据
                        insert2RootMap();
                        if (error.isEmpty()) {
                            //将联系人插入到行政关系表中
                            insert2Map();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        log.info("---FixCrmcontactorJob END---");
    }

    /**
     * 将本身没有根节点的客户，插入根节点数据
     */
    private void insert2RootMap() {
        log.info("---insert2RootMap START---");
        String customerid, rootid, insertSql;
        try {
            RecordSet rsUpdate = new RecordSet();
            for (Map.Entry<String, String> entry : mapRootId.entrySet()) {
                customerid = entry.getKey();
                rootid = entry.getValue();
                String uuid = String.valueOf(UUID.randomUUID().getMostSignificantBits());//UUID

                insertSql = "INSERT INTO " +
                        "    crm_contactor_map_node (id, customer_id, entity_id,parent_id,direction,type,is_root) " +
                        "VALUES " +
                        "    (" + uuid + ", " + customerid + ", '" + rootid + "',null,null,1,1) ";
                log.info("根节点insert crm_contactor_map_node sql :" + insertSql);
                if (!rsUpdate.executeUpdate(insertSql)) {
                    error = "customerid:" + customerid + ",rootid:" + rootid + ".uuid:" + uuid + ",根节点插入map失败:" + rsUpdate.getExceptionMsg();
                    log.error(error);
                    break;
                }
            }

        } catch (Exception e) {
            error = "insert2RootMap 异常";
            log.error(error, e);

        }
        log.info("---insert2RootMap END---");


    }

    private void insert2Map() {
        log.info("---insert2Map START---");
        String contactorid, customerid, parentid, insertSql;
        try {
            String sql = "select a.id,a.customerid,b.id as parentid from crm_customercontacter a " +
                    " left join crm_contactor_map_node b on (a.customerid = b.customer_id and b.is_root = 1) " +
                    " where 1=1 " +
                    //排除没有根节点的
                    " and b.entity_id is not null ";
            //指定客户id
            if (StringUtils.isNotBlank(executeCustomerId)) {
                sql += " and a.customerid = " + executeCustomerId;
            }
            log.info("查客户对应根节点 id sql:" + sql);
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (rs.executeQuery(sql)) {
                RecordSet rsUpdate = new RecordSet();
                while (rs.next()) {
                    contactorid = Util.null2String(rs.getString("id")); //联系人id
                    customerid = Util.null2String(rs.getString("customerid")); //客户id
                    parentid = Util.null2String(rs.getString("parentid")); //根节点id
                    String uuid = String.valueOf(UUID.randomUUID().getMostSignificantBits());//UUID

                    insertSql = "INSERT INTO " +
                            "    crm_contactor_map_node (id, customer_id, entity_id,parent_id,direction,type,is_root) " +
                            "VALUES " +
                            "    (" + uuid + ", " + customerid + ", '" + contactorid + "'," + parentid + ",null,0,0) ";
                    log.info("insert crm_contactor_map_node sql :" + insertSql);
                    if (!rsUpdate.execute(insertSql)) {
                        error = "customerid:" + customerid + ",contactorid:" + contactorid + ".uuid:" + uuid + ",插入map失败:" + rsUpdate.getExceptionMsg();
                        log.error(error);
                        break;
                    }

                }

            }
        } catch (Exception e) {
            error = "insert2Map 异常";
            log.error(error, e);

        }
        log.info("---insert2Map END---");


    }

    private void delDept() {
        log.info("---delDept START---");
        try {
            String sql = "delete from crm_contactor_dept where 1=1 ";
            //指定客户id
            if (StringUtils.isNotBlank(executeCustomerId)) {
                sql += " and customer_id = " + executeCustomerId;
            }
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            log.info("删除crm_contactor_dept sql:" + sql);
            if (!rs.executeUpdate(sql)) {
                error = "删除crm_contactor_dept数据错误：" + rs.getExceptionMsg();
            }
        } catch (Exception e) {
            error = "delDept 异常";
            log.error(error, e);
        }
        log.info("---delDept END---");
    }

    private void delMapNode() {
        log.info("---delMapNode START---");
        try {
            String sql = "delete from crm_contactor_map_node where 1=1 ";
            //指定客户id
            if (StringUtils.isNotBlank(executeCustomerId)) {
                sql += " and customer_id = " + executeCustomerId;
            }
            log.info("删除crm_contactor_map_node sql:" + sql);
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (!rs.executeUpdate(sql)) {
                error = "删除行政关系数据错误：" + rs.getExceptionMsg();
            }
        } catch (Exception e) {
            error = "delMapNode 异常";
            log.error(error, e);
        }
        log.info("---delMapNode END---");
    }

    /**
     * 插入根节点crm_contactor_dept数据
     */
    private void insertDept() {
        log.info("---insertDept START---");
        String customerid, name;
        String insertSql;
        try {
            String sql = " select distinct a.customerid,b.name from crm_customercontacter a " +
                    " inner join crm_customerinfo b on (a.customerid = b.id) " +
                    " where 1=1 ";
            //指定客户id
            if (StringUtils.isNotBlank(executeCustomerId)) {
                sql += " and a.customerid = " + executeCustomerId;
            }
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            log.info("query dept sql:" + sql);
            if (rs.executeQuery(sql)) {
                RecordSet rsUpdate = new RecordSet();
                while (rs.next()) {
                    customerid = Util.null2String(rs.getString("customerid"));
                    name = Util.null2String(rs.getString("name"));
                    String uuid = String.valueOf(UUID.randomUUID().getMostSignificantBits());

                    insertSql = "INSERT INTO " +
                            "    crm_contactor_dept (id, name, customer_id) " +
                            "VALUES" +
                            "    (" + uuid + ", '" + name + "', " + customerid + ") ";
                    log.info("insert crm_contactor_dept sql :" + insertSql);
                    if (!rsUpdate.execute(insertSql)) {
                        error = "customerid:" + customerid + ",name:" + name + ".uuid:" + uuid + ",插入dept失败:" + rsUpdate.getExceptionMsg();
                        log.error(error);
                        break;
                    }
                }

            }
        } catch (Exception e) {
            error = "insertDept 异常";
            log.error(error, e);
        }
        log.info("---insertDept END---");
    }

    private void getRootMap() {
        String sql = "select id,customer_id from crm_contactor_dept where 1=1 ";
        //指定客户id
        if (StringUtils.isNotBlank(executeCustomerId)) {
            sql += " and customer_id = " + executeCustomerId;
        }
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                mapRootId.put(rs.getString("customer_id"), rs.getString("id"));
            }
        }
    }


}
