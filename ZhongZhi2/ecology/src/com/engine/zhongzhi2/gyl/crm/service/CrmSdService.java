package com.engine.zhongzhi2.gyl.crm.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmSdService.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/17
 */

public interface CrmSdService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveContactLogOthers(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveContactorOthers(Map<String, Object> params, User user);


    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> editContactorOthers(Map<String, Object> params, User user);


    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> delContactorOthers(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveQixin2Customer(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveCustomerOtherData(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveSellchanceDeptHelperData(Map<String, Object> params, User user);

}
