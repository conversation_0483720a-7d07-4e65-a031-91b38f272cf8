package com.engine.zhongzhi2.gyl.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.bean.QiXinArchive;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;


public class SaveQiXin2Customer extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String ESB_NAME = "QXB_GongShangZhaoMian";

    public SaveQiXin2Customer(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            String customerid = Util.null2String(params.get("customerid")); //客户id
            String type = Util.null2String(params.get("type")); //操作类型 "new,update"
            log.info("customerid:" + customerid + ",type:" + type);
            if (!customerid.isEmpty() && !type.isEmpty()) {
                //获取客户数据
                Map<String, Object> customerData = getCustomerData();
                if (customerData != null) {
                    String customerName = Util.null2String(customerData.get("name"));
                    log.info("customerName:" + customerName);
                    if (!customerName.isEmpty()) {
                        JSONObject dataJson;
                        if ("new".equals(type)) {
                            //新建客户后，根据客户获取启信宝数据(会优先从建模存档的数据获取，防止重复调用)
                            dataJson = QiXinArchive.getQiXinData(customerName, customerid, user.getUID(), "初次新建客户后");
                            log.info("new dataJson:" + dataJson);
                        } else {
                            //编辑客户后，根据客户获取启信宝数据(会强制获取最新的ESB数据)
                            dataJson = QiXinArchive.getLatestQiXinData(customerName, customerid, user.getUID(), "编辑客户保存后");
                            log.info("update dataJson:" + dataJson);
                        }
                        if (dataJson != null && !dataJson.isEmpty()) {
                            String sql = "update CRM_CustomerInfo set ";
                            String setSql = "";
                            //设置城市、省份、国家
                            String districtCode = Util.null2String(dataJson.get("districtCode"));
                            if (!districtCode.isEmpty()) {
                                Map<String, Object> cityData = getCityData(districtCode);
                                if (cityData != null) {
                                    String city = Util.null2String(cityData.get("cs"));
                                    String province = Util.null2String(cityData.get("province"));
                                    String country = Util.null2String(cityData.get("country"));
                                    if (!city.isEmpty()) {
                                        setSql += " city = " + city + ",";
                                    }
                                    if (!province.isEmpty()) {
                                        setSql += " province = " + province + ",";
                                    }
                                    if (!country.isEmpty()) {
                                        setSql += " country = " + country + ",";
                                    }
                                }
                            }
                            //地址
                            String address1 = Util.null2String(dataJson.get("address"));
                            if (!address1.isEmpty()) {
                                setSql += " address1 = '" + address1 + "',";
                            }
                            //公司填信用代码/个人填手机号
                            String tyshxydmsjh = Util.null2String(dataJson.get("creditNo"));
                            if (!tyshxydmsjh.isEmpty()) {
                                setSql += " tyshxydmsjh = '" + tyshxydmsjh + "',";
                            }
                            //实缴资本
                            String sjzb = Util.null2String(dataJson.get("actualCapi"));
                            if (!sjzb.isEmpty()) {
                                setSql += " sjzb = '" + sjzb + "',";
                            }
                            //经营范围
                            String jyfw = Util.null2String(dataJson.get("scope"));
                            if (!jyfw.isEmpty()) {
                                setSql += " jyfw = '" + jyfw + "',";
                            }
                            //注册资本
                            String crmCustomerZczb = Util.null2String(dataJson.get("registCapi"));
                            if (!crmCustomerZczb.isEmpty()) {
                                setSql += " crmCustomerZczb = '" + crmCustomerZczb + "',";
                            }
                            //是否上市
                            //企业标签：1-新三板；6-主板上市公司；40-暂停上市；41-终止上市；9-香港上市；17-高新企业；
                            //转换：如果是1、6、9则为是（0），否则为否（1）
                            String sfss = Util.null2String(dataJson.get("tags"));
                            if (!sfss.isEmpty()) {
                                int sfssValue = 1;
                                if ("1".equals(sfss) || "6".equals(sfss) || "9".equals(sfss)) {
                                    sfssValue = 0;
                                }
                                setSql += " sfss = " + sfssValue + ",";
                            }

                            if (!setSql.isEmpty()) {
                                setSql = setSql.substring(0, setSql.length() - 1);
                                sql += setSql + " where id =" + customerid;
                                log.info("update sql :" + sql);
                                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                                if (!rs.executeUpdate(sql)) {
                                    error = "更新客户的启信宝数据sql出错：" + rs.getExceptionMsg() + ";sql:" + sql;
                                    log.error(error);
                                }
                            }
                        }
                    } else {
                        log.warn("客户名称为空，不执行");
                    }
                } else {
                    error = "未获取到客户数据";
                    log.error(error);
                }
            } else {
                error = "缺失参数";
                log.error(error);
            }
        } catch (Exception e) {
            error = "异常" + e.getMessage();
            log.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

    /**
     * 获取客户数据
     *
     * @return
     */
    private Map<String, Object> getCustomerData() {
        Map<String, Object> result = null;
        String customerid = Util.null2String(params.get("customerid")); //客户id
        String sql = "select * from CRM_CustomerInfo where id = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, customerid)) {
            result = QueryUtil.getMap(rs);
            if (result != null && !result.isEmpty()) {
                return SDUtil.lowerMapKey(result);
            }
        } else {
            log.error("getCustomerData sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return result;
    }

    /**
     * 根据地区编码获取城市、省份、国家
     *
     * @param dqbm
     * @return
     */
    private Map<String, Object> getCityData(String dqbm) {
        Map<String, Object> result = null;
        String sql = "select a.cs,c.id as province,d.id as country from uf_dqcsdzb a " +
                " inner join  hrmcity b on (a.cs = b.id)" +
                " inner join hrmprovince c on (b.provinceid = c.id) " +
                " inner join hrmcountry d on (c.countryid = d.id)" +
                "  where a.dqdm = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, dqbm)) {
            result = QueryUtil.getMap(rs);
            if (result != null && !result.isEmpty()) {
                return SDUtil.lowerMapKey(result);
            }
        } else {
            log.error("getCustomerData sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return result;
    }


}
