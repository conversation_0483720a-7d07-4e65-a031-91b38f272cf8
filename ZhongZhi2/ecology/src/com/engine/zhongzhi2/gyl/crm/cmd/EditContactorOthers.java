package com.engine.zhongzhi2.gyl.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.zhongzhi2.gyl.crm.cmd.bean.ContactorOthers;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName EditContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class EditContactorOthers extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    public EditContactorOthers(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            int contactorId = Util.getIntValue(Util.null2String(params.get("contactorId")), 0); //联系人数据id
            if (contactorId > 0) {
                ContactorOthers data = SaveContactorOthers.buildData(String.valueOf(contactorId), user);
                //更新建模
                log.info("update data:" + data);
                if (data != null) {
                    ModuleResult mr = ModuleDataUtil.updateObj(data, ContactorOthers.TABLE_NAME, contactorId, user.getUID());
                    if (!mr.isSuccess()) {
                        error = mr.getErroMsg();
                        log.error(error);
                    }
                } else {
                    error = "获取到的ContactorOthers为空";
                    log.error(error);
                }
            } else {
                error = "缺失参数";
                log.error(error);
            }
        } catch (Exception e) {
            error = "异常" + e.getMessage();
            log.error("异常：", e);
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

}
