package com.engine.zhongzhi2.gyl.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.gyl.crm.cmd.*;
import com.engine.zhongzhi2.gyl.crm.service.CrmSdService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmSdServiceImpl.java
 * @Description CRM二开接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/17
 */

public class CrmSdServiceImpl extends Service implements CrmSdService {
    /**
     * 阶段保存时，记录P4阶段首次进入时间等信息
     *
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveContactLogOthers(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveContactLogOthersCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveContactorOthers(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveContactorOthers(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> editContactorOthers(Map<String, Object> params, User user) {
        return commandExecutor.execute(new EditContactorOthers(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> delContactorOthers(Map<String, Object> params, User user) {
        return commandExecutor.execute(new DelContactorOthers(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveQixin2Customer(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveQiXin2Customer(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveCustomerOtherData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveCustomerOtherData(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveSellchanceDeptHelperData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveSellchanceDeptHelperDataCmd(params, user));
    }


}
