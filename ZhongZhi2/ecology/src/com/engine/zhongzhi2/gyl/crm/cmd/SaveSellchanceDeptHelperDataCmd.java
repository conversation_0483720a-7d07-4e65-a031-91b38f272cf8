package com.engine.zhongzhi2.gyl.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class SaveSellchanceDeptHelperDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(SaveCustomerOtherData.class);

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public SaveSellchanceDeptHelperDataCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---SaveSellchanceDeptHelperDataCmd---START");
        log.info("params:" + params);
        try {
            //商机id
            String sellchanceId = Util.null2String(params.get("sellchanceId"));
            String sql = "select * from CRM_SellChance where id = ?";
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (rs.executeQuery(sql, sellchanceId)) {
                if (rs.next()) {
                    //跟进部门
                    String gzbm = Util.null2String(rs.getString("gzbm"));
                    log.info("gzbm:" + gzbm);
                    if (!gzbm.isEmpty()) {
                        //更新商机的
                        updateSell(gzbm, sellchanceId);
                    } else {
                        log.warn("无跟进部门");
                    }
                }
            } else {
                log.error("未能查到商机");
            }
        } catch (Exception e) {
            log.error("异常", e);
        }
        result.put("error", error);
        log.info("---SaveSellchanceDeptHelperDataCmd---END");
        return result;
    }

    private void updateSell(String gzbm, String sellchanceId) {
        String sql = "select * from uf_yjhsbmdzgx where sqbm = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, gzbm) && rs.next()) {
            String bmzl = Util.null2String(rs.getString("bmzl")); //部门助理
            String bmfzr = Util.null2String(rs.getString("bmfzr"));//部门负责人
            sql = "update CRM_SellChance set bmzl=?, bmfzr=? where id = " + sellchanceId;
            log.info("商机id" + sellchanceId + "更新sql:" + sql + ",bmzl:" + bmzl + ",bmfzr:" + bmfzr);
            if (!rs.executeUpdate(sql, bmzl, bmfzr)) {
                log.error("更新商机出错:" + rs.getExceptionMsg());
            }
        } else {
            log.error("未查询到该部门对应数据");
        }
    }
}
