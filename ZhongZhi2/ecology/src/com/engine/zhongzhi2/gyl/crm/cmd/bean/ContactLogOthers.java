package com.engine.zhongzhi2.gyl.crm.cmd.bean;

import lombok.Data;

/**
 * @FileName ContactLogOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/15
 */
@Data
public class ContactLogOthers {
    /**
     * 客户id
     */
    private Integer crmid;
    /**
     * 商机id
     */
    private Integer chanceid;
    /**
     * 日志id
     */
    private Integer logid;
    /**
     * 中智方出席人
     */
    private String zhongzhihrm;
    /**
     * 联系人
     */
    private String lxr;
    /**
     * 联系方式
     */
    private String lxfs;
    /**
     * 联系内容
     */
    private String lxnr;
    /**
     * 商机编号
     */
    private String sjbh;
    /**
     * 商机负责人
     * 单人力
     */
    private Integer sjfzr;
    /**
     * 商机接单人
     * 单人力
     */
    private Integer sjjdr;
    /**
     * 商机部门助理
     * 多人力
     */
    private String sjbmzl;
}
