package com.engine.zhongzhi2.gyl.crm.action;


import com.engine.common.util.ServiceUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.zhongzhi2.gyl.crm.service.CrmSdService;
import com.engine.zhongzhi2.gyl.crm.service.impl.CrmSdServiceImpl;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName DemoAction.java
 * @Description demo action
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/5
 */
@Getter
@Setter
public class CustomerUpdateQiXinAction extends BaseBean implements Action {
    //Action参数---START---
    /**
     * 主表字段名-客户
     */
    private String field_customer;
    /**
     * 客户类型
     * 例如配置：1,2
     * 逻辑：判断客户是属于该类型的，则执行获取启信宝数据更新的操作
     */
    private String types;
    //Action参数---END---

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    // 创建一个对象作为锁
    private static final Object lock = new Object();

    private CrmSdService getService(User user) {
        return ServiceUtil.getService(CrmSdServiceImpl.class, user);
    }

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        log.info("---START---");
        ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
        log.info("SD action requestid:" + actionInfo.getRequestId());
        //执行业务逻辑
        execuetMy(actionInfo);
        log.info("---END---");
        return ActionUtil.handleResult("", requestInfo);
    }

    private void execuetMy(ActionInfo actionInfo) {
        try {
            //主表数据
            Map<String, String> mainData = actionInfo.getMainData();
            if (StringUtils.isNotBlank(field_customer)) {
                String customerid = Util.null2String(mainData.get(field_customer));
                log.info("customerid:" + customerid);
                Map<String, Object> params = new HashMap<>();
                params.put("customerid", customerid);
                Map<String, Object> result = getService(actionInfo.getUser()).saveQixin2Customer(params, actionInfo.getUser());
                log.info("saveQixin2Customer customerid:" + customerid + ", result:" + result);
            }

        } catch (Exception e) {
            log.error("执行异常", e);
        }

    }
}
