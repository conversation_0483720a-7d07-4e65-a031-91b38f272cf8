package com.engine.zhongzhi2.gyl.crm.cmd.bean;

import lombok.Data;

/**
 * @FileName ContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
@Data
public class ContactorOthers {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 客户
     */
    private Integer kh;
    /**
     * 商机
     */
    private Integer shangji;
    /**
     * 联系人
     */
    private Integer lxr;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 称呼
     */
    private Integer ch;
    /**
     * 职位
     */
    private String zw;
    /**
     * 手机
     */
    private String sj;
    /**
     * 邮箱
     */
    private String yx;
    /**
     * 座机
     */
    private String zj;
    /**
     * 创建人
     */
    private Integer cjr;
    /**
     * 创建人部门
     */
    private Integer cjrbm;
    /**
     * 删除标记 0正常 1已删除
     */
    private Integer deleted;
    /**
     * 微信号
     */
    private String wxh;
    /**
     * 联系人部门
     */
    private String lxrbm;

    public static final String TABLE_NAME = "uf_khlxr_ext";
}
