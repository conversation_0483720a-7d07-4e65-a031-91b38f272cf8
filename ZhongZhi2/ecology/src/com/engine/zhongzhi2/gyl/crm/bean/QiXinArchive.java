package com.engine.zhongzhi2.gyl.crm.bean;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import lombok.Data;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName QiXinArchive.java
 * @Description 启信宝接口数据存档
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/27
 */
@Data
public class QiXinArchive {

    public static final Logger log = LoggerFactory.getLogger(QiXinArchive.class);

    public static final Object lock = new Object();

    /**
     * 主表id
     */
    private Integer id;
    /**
     * 客户名称
     */
    private String customer_name;
    /**
     * 启信宝接口ESB批次号
     */
    private String esb_serial_num;
    /**
     * 启信宝数据
     * 只存最终的data层
     */
    private String qxin_data;
    /**
     * 操作人
     */
    private Integer operator;
    /**
     * 更新日期
     */
    private String update_date;
    /**
     * 更新时间
     */
    private String update_time;
    /**
     * 备注
     */
    private String remark;

    /**
     * 建模表名
     */
    public static final String TABLE_NAME = "uf_qxin_archive";

    /**
     * @FileName QiXinArchive.Detail1.java
     * @Description 明细1 读取回写客户表的日志
     * <AUTHOR>
     * @Version v1.00
     * @Date 2024/12/27
     */
    @Data
    public static class Detail1 {
        private Integer id;
        private Integer mainid;
        /**
         * 回写的客户id
         */
        private Integer customer;
        /**
         * 读取日期
         */
        private String read_date;
        /**
         * 读取时间
         */
        private String read_time;
        /**
         * 读取数据
         */
        private String read_data;
        /**
         * 启信宝接口ESB批次号
         */
        private String esb_serial_num;
        /**
         * 操作人
         */
        private Integer operator;
        /**
         * 备注
         */
        private String remark;
    }

    /**
     * @FileName QiXinArchive.Detail2.java
     * @Description 明细2 编辑客户保存时，更新启信宝数据的更新日志
     * <AUTHOR>
     * @Version v1.00
     * @Date 2024/12/27
     */
    @Data
    public static class Detail2 {
        private Integer id;
        private Integer mainid;
        /**
         * 客户id
         */
        private Integer customer;
        /**
         * 更新日期
         */
        private String update_date;
        /**
         * 更新时间
         */
        private String update_time;
        /**
         * 更新数据
         */
        private String update_data;
        /**
         * 启信宝接口ESB批次号
         */
        private String esb_serial_num;
        /**
         * 操作人
         */
        private Integer operator;
        /**
         * 备注
         */
        private String remark;
    }


    /**
     * 创建存档数据
     *
     * @param customerName 客户名称
     * @param esbSerialNum 启信宝ESB批次号
     * @param qxinData     启信宝返回的data数据
     * @param operator     操作人id
     * @param remark       备注
     */
    public static void create(String customerName, String esbSerialNum, String qxinData, int operator, String remark) {
        try {
            QiXinArchive qiXinArchive = new QiXinArchive();
            qiXinArchive.setCustomer_name(customerName);
            qiXinArchive.setEsb_serial_num(esbSerialNum);
            qiXinArchive.setQxin_data(qxinData);
            qiXinArchive.setOperator(operator);
            qiXinArchive.setUpdate_date(TimeUtil.getToday());
            qiXinArchive.setUpdate_time(SDTimeUtil.getCurrentTimeMillliString());
            qiXinArchive.setRemark(remark);

            int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            log.info("modid:" + modid);
            ModuleDataUtil.insertObj(qiXinArchive, TABLE_NAME, modid, operator);

        } catch (Exception e) {
            log.error("创建归档数据出错", e);
        }
    }

    /**
     * 更新存档数据
     *
     * @param esbSerialNum 启信宝ESB批次号
     * @param qxinData     启信宝返回的data数据
     * @param operator     操作人id
     */
    public static void update(String customerName, String esbSerialNum, String qxinData, int operator, int billid, String remark) {
        try {
            QiXinArchive qiXinArchive = new QiXinArchive();
            qiXinArchive.setCustomer_name(customerName);
            qiXinArchive.setEsb_serial_num(esbSerialNum);
            qiXinArchive.setQxin_data(qxinData);
            qiXinArchive.setOperator(operator);
            qiXinArchive.setUpdate_date(TimeUtil.getToday());
            qiXinArchive.setUpdate_time(SDTimeUtil.getCurrentTimeMillliString());
            qiXinArchive.setRemark(remark);
            ModuleDataUtil.updateObj(qiXinArchive, TABLE_NAME, billid, operator);
        } catch (Exception e) {
            log.error("更新归档数据出错", e);
        }
    }

    /**
     * 读取启信宝数据,优先从建模存档的启信宝数据获取
     * 并记录读取日志
     *
     * @param customerName
     * @param customerid
     * @param operator
     * @param remarkPrefix 备注前缀，代表由什么事件触发的
     * @return
     */
    public static JSONObject getQiXinData(String customerName, String customerid, int operator, String remarkPrefix) {
        JSONObject result = null;
        try {
            log.info("customerName:" + customerName + ",customerid:" + customerid + ",readQiXinData---START");
            //先查询建模中是否有该客户存档的启信宝数据，有的话，直接取建模的使用
            QiXinArchive qiXinArchive;
            String newRemark = "", esbserialNum = "";
            //这里加锁，读写一致
            synchronized (lock) {
                qiXinArchive = getFromMod(customerName);
                if (qiXinArchive == null) {
                    log.info("建模中没有数据，先从ESB读取");
                    //建模总没有
                    //从ESB调用获取，并存入建模
                    Map<String, String> archiveResult = archiveFromEsb(customerName, customerid, operator, remarkPrefix);
                    newRemark = Util.null2String(archiveResult.get("remark"));
                    esbserialNum = Util.null2String(archiveResult.get("esbNumber"));
                } else {
                    newRemark = "建模中已有该客户存档数据，不走ESB接口，直接读取使用";
                    log.info("建模中有数据，直接读取");
                }
            }
            //再次从建模获取数据
            qiXinArchive = getFromMod(customerName);
            if (qiXinArchive != null) {
                String jsonDataStr = Util.null2String(qiXinArchive.getQxin_data());
                log.info("jsonDataStr:" + jsonDataStr);
                if (!jsonDataStr.isEmpty()) {
                    result = JSONObject.parseObject(jsonDataStr);
                }
                //记录读取回写日志
                Detail1 detail = new Detail1();
                detail.setCustomer(Util.getIntValue(customerid));
                detail.setRead_date(TimeUtil.getToday());
                detail.setRead_time(SDTimeUtil.getCurrentTimeMillliString());
                detail.setRead_data(jsonDataStr);
                detail.setOperator(operator);
                detail.setRemark(newRemark);
                detail.setEsb_serial_num(esbserialNum);
                ModuleDataUtil.insertObjDetailOne(detail, TABLE_NAME + "_dt1", qiXinArchive.getId());
            } else {
                log.error("未获取到建模的启信宝数据");
            }
        } catch (Exception e) {
            log.error("getQiXinData 异常", e);
        }
        log.info("customerName:" + customerName + ",customerid:" + customerid + ",readQiXinData---END");
        return result;
    }

    /**
     * 必定从ESB接口获取最新启信宝数据
     * 编辑客户后，重新读取最新的启信宝数据，后续再更新到客户表中，并更新建模存档数据
     *
     * @param customerName
     * @param customerid
     * @param operator
     * @return
     */
    public static JSONObject getLatestQiXinData(String customerName, String customerid, int operator, String remarkPrefix) {
        JSONObject result = null;
        try {
            log.info("customerName:" + customerName + ",customerid:" + customerid + ",getLatestQiXinData---START");
            //直接从ESB获取最新的启信宝数据
            Map<String, String> archiveResult = archiveFromEsb(customerName, customerid, operator, remarkPrefix);
            //再次从建模获取数据
            QiXinArchive qiXinArchive = getFromMod(customerName);
            if (qiXinArchive != null) {
                String jsonDataStr = Util.null2String(qiXinArchive.getQxin_data());
                log.info("jsonDataStr:" + jsonDataStr);
                if (!jsonDataStr.isEmpty()) {
                    result = JSONObject.parseObject(jsonDataStr);
                }
                //记录读取回写日志
                Detail2 detail = new Detail2();
                detail.setCustomer(Util.getIntValue(customerid));
                detail.setEsb_serial_num(Util.null2String(archiveResult.get("esbNumber")));
                detail.setUpdate_date(TimeUtil.getToday());
                detail.setUpdate_time(SDTimeUtil.getCurrentTimeMillliString());
                detail.setUpdate_data(jsonDataStr);
                detail.setOperator(operator);
                detail.setRemark(Util.null2String(archiveResult.get("remark")));
                ModuleDataUtil.insertObjDetailOne(detail, TABLE_NAME + "_dt2", qiXinArchive.getId());
            } else {
                log.error("未获取到建模的启信宝数据");
            }
        } catch (Exception e) {
            log.error("getQiXinData 异常", e);
        }
        log.info("customerName:" + customerName + ",customerid:" + customerid + ",getLatestQiXinData---END");
        return result;
    }

    /**
     * 从ESB读取启信宝数据，并存档到建模
     * 这里加锁，读写一致
     *
     * @param customerName
     * @param customerid
     * @param operator
     * @return
     */
    private synchronized static Map<String, String> archiveFromEsb(String customerName, String customerid, int operator, String remarkPreFix) {
        Map<String, String> result = new HashMap<>();
        String newRemark = "";
        String ESB_NAME = "QXB_GongShangZhaoMian";
        JSONObject esbParam = new JSONObject();
        esbParam.put("keyword", customerName);//客户名称
        esbParam.put("customerid", customerid);//客户id
        EsbEventResult er = EsbUtil.callEsbEvent(ESB_NAME, esbParam);
        result.put("esbNumber", er.getSerialNumber());
        if (er.isSuccess()) {
            JSONObject data = er.getData();
            if (data != null && data.containsKey("data")) {
                String datastr = Util.null2String(data.get("data"));
                if (!datastr.isEmpty()) {
                    //判断建模中是否有该客户的启信宝数据，有则更新，无则创建插入
                    QiXinArchive qiXinArchive = getFromMod(customerName);
                    if (qiXinArchive == null) {
                        //新建到建模中
                        newRemark = remarkPreFix + "，读取启信宝接口，此时无建模存档，新增存档数据";
                        create(customerName, er.getSerialNumber(), datastr, operator, newRemark);
                    } else {
                        newRemark = remarkPreFix + "，读取启信宝接口，此时已有建模存档，更新存档数据";
                        //更新到建模中
                        update(customerName, er.getSerialNumber(), datastr, operator, qiXinArchive.getId(), newRemark);
                    }

                }
            }
        }
        result.put("remark", newRemark);
        return result;
    }


    /**
     * 从建模读取客户的启信宝数据
     *
     * @param customerName
     */
    public static QiXinArchive getFromMod(String customerName) {
        RecordSet rs = new RecordSet();
        String sql = "select * from " + TABLE_NAME + " where customer_name = '" + customerName + "' ";
        if (rs.executeQuery(sql)) {
            return QueryUtil.getObj(rs, QiXinArchive.class);
        }
        return null;
    }
}
