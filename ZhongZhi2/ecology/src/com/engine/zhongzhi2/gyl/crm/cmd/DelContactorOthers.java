package com.engine.zhongzhi2.gyl.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.cmd.bean.ContactorOthers;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SaveContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class DelContactorOthers extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    public DelContactorOthers(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            int contactorId = Util.getIntValue(Util.null2String(params.get("contactorId")), -1); //联系人数据id
            if (contactorId > 0) {
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                String sql = "update " + ContactorOthers.TABLE_NAME + " set deleted = 1,modedatamodifier=?,modedatamodifydatetime=? where lxr = " + contactorId;
                log.info("update del sql:" + sql);
                if (!rs.executeUpdate(sql, user.getUID(), TimeUtil.getCurrentTimeString())) {
                    error = "更新删除标记失败：" + rs.getExceptionMsg() + ";sql:" + sql;
                    log.error(error);
                }
            } else {
                error = "缺失参数";
                log.error(error);
            }
        } catch (Exception e) {
            error = "异常" + e.getMessage();
            log.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }
}
