package com.engine.zhongzhi2.gyl.crm.job;

import com.engine.common.util.ServiceUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.service.CrmSdService;
import com.engine.zhongzhi2.gyl.crm.service.impl.CrmSdServiceImpl;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName CustomerUpdateQixinJob.java
 * @Description 从启信宝抓取数据，刷新客户的字段数据(序号6功能)
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/6/15
 */
@Getter
@Setter
public class CustomerUpdateQixinJob extends BaseCronJob {
    //----定时任务参数-----//
    /**
     * 客户类型
     */
    private String types;
    /**
     * 二开log类
     */
    private final Logger logSD = LoggerFactory.getLogger(this.getClass());

    private CrmSdService getService(User user) {
        return ServiceUtil.getService(CrmSdServiceImpl.class, user);
    }

    /**
     *
     */
    @Override
    public void execute() {
        logSD.info("CustomerUpdateQixinJob---START");
        String id;
        Map<String, Object> params;
        Map<String, Object> result;
        try {
            if (StringUtils.isNotBlank(types)) {
                User user = new User(1);
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                String sql = "select * from CRM_CustomerInfo where 1=1 " +
                        " and type in (" + types + ")" +
                        " and deleted = 0 " +
                        " and id in (select khmc from uf_dsrwqxbtbqd)";
                if (rs.executeQuery(sql)) {
                    while (rs.next()) {
                        id = Util.null2String(rs.getString("id"));
                        params = new HashMap<>();
                        params.put("customerid", id);
                        result = getService(user).saveQixin2Customer(params, user);
                        logSD.info("saveQixin2Customer customerid:" + id + ", result:" + result);
                    }
                } else {
                    logSD.error("query error :" + rs.getExceptionMsg() + ";sql:" + sql);
                }
            } else {
                logSD.warn("types未配置");
            }
        } catch (Exception e) {
            logSD.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        logSD.info("CustomerUpdateQixinJob---END");
    }
}
