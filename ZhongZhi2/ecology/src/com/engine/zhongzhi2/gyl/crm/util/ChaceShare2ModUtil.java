package com.engine.zhongzhi2.gyl.crm.util;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.job.UpdateContactLogAuthJob;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.List;
import java.util.Map;

/**
 * @FileName ChaceShare2ModUtil.java
 * @Description 商机非默认权限插入到联系人记录建模中
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/10/21
 */
public class ChaceShare2ModUtil {
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(UpdateContactLogAuthJob.class);
    /**
     * 插入建模的非默认共享时候，给的jobleveltext赋值的code
     */
    public static final String SD_SHARE_CODE = "SD_CHANCE_SHARE";

    /**
     * 将商机的非默认共享插入到建模的非默认共享中
     *
     * @param chanceid
     * @param billid
     * @param moduleId
     */
    public static void insertOtherShare(String chanceid, int billid, int moduleId) {
        //获取商机非默认共享
        List<Map<String, Object>> chanceShareList = getChanceShare(chanceid);
        if (chanceShareList != null && !chanceShareList.isEmpty()) {
            log.info("chanceShareList size:" + chanceShareList.size());
            log.info("chanceShareList :" + chanceShareList);
            String tableName = "modeDataShare_" + moduleId + "_set";
            for (Map<String, Object> chanceShare : chanceShareList) {
                String sql = "insert into " + tableName + "(sourceid,righttype,sharetype,relatedid,rolelevel,showlevel,showlevel2,isdefault,requestid,hrmCompanyVirtualType,joblevel,jobleveltext)";
                //1为查看权限，2为编辑权限
                String righttype = Util.null2String(chanceShare.get("sharelevel"));
                //商机权限类型
                String sharetype = Util.null2String(chanceShare.get("sharetype"));
                //转换为建模的权限类型
                sharetype = convertShareType(sharetype);
                //共享对象id	如:共享对象为人员时，是人员的id，共享对象为部门时，是部门的id,共享对象为岗位时，是岗位的id
                String relatedid = Util.null2String(chanceShare.get("relatedshareid"));
                //共享级别(角色)	0:部门;1:分部;2:总部
                String rolelevel = Util.null2String(chanceShare.get("rolelevel"));
                //安全级别下限
                String showlevel = Util.null2String(chanceShare.get("seclevel"));
                //安全级别上限
                String showlevel2 = Util.null2String(chanceShare.get("seclevelmax"));
                //是否默认共享0:非默认共享，为用户手动添加的共享;1:默认共享，根据模块设置的权限生成的权限
                int isdefault = 0;
                //对应的是流程的requestid
                int requestid = 0;
                //虚拟公司类型
                int hrmCompanyVirtualType = 0;
                String joblevel = Util.null2String(chanceShare.get("jobtitlelevel"));
                //岗位级别制定对象id
                String jobleveltext = SD_SHARE_CODE;

                sql += " values (" +
                        "'" + billid + "'," +
                        "'" + righttype + "'," +
                        "'" + sharetype + "'," +
                        "'" + relatedid + "'," +
                        "'" + rolelevel + "'," +
                        "'" + showlevel + "'," +
                        "'" + showlevel2 + "'," +
                        isdefault + "," +
                        requestid + "," +
                        hrmCompanyVirtualType + "," +
                        "'" + joblevel + "'," +
                        "'" + jobleveltext + "'" +
                        ")";
                log.info("插入商机的非默认共享sql:" + sql);
                RecordSet rs = DBUtil.getThreadLocalRecordSet();
                if (!rs.executeUpdate(sql)) {
                    log.error("insertOtherShare sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
                }
            }
        } else {
            log.warn("未查到商机：" + chanceid + "的非默认共享");
        }
    }

    private static List<Map<String, Object>> getChanceShare(String chanceid) {
        String sql = "select * from crm_sellchancecardauth where sellchanceid = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, chanceid)) {
            return SDUtil.lowerMapKey(QueryUtil.getMapList(rs));

        }
        return null;
    }

    /**
     * 转换共享类型，将商机的共享类型，转为建模的共享类型
     * 商机的权限类型：1.人员;2.部门;3.角色;4.所有人;5.分部;6.岗位
     * 建模的权限类型：1,人员;2,分部;3,部门;4,角色;5,所有人;6,岗位;80:创建人本人;81,创建人直接上级;84,创建人分部;85,创建人部门;89,创建人所有上级;90,创建人本岗位
     *
     * @param chanceShareType
     * @return
     */
    private static String convertShareType(String chanceShareType) {
        String result = "";
        switch (chanceShareType) {
            case "1": //人力资源
                result = "1";
                break;
            case "2": //部门
                result = "3";
                break;
            case "3": //角色
                result = "4";
                break;
            case "4": //所有人
                result = "5";
                break;
            case "5": //分部
                result = "2";
                break;
            case "6": //岗位
                result = "6";
                break;
            default:
                break;
        }
        return result;
    }
}
