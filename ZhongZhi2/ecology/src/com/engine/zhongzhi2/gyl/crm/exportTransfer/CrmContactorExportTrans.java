package com.engine.zhongzhi2.gyl.crm.exportTransfer;

import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.formmode.interfaces.ExportFieldTransAction;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmContactorExportTrans.java
 * @Description 客户联系日志导出，字段转换
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/6/18
 */
public class CrmContactorExportTrans implements ExportFieldTransAction {
    private BaseBean bb = new BaseBean();
    @SuppressWarnings("unchecked")
    @Override
    public String getTransValue(Map<String, Object> param, User user) {
        bb.writeLog("进入自定义导出数据操作");
        RecordSet rs = new RecordSet();
        BaseBean bb = new BaseBean();
        // 获取当前登录人员ID
        Integer userId = user.getUID();
        // 获取模块ID
        int modeId = Util.getIntValue(param.get("modeid").toString());
        //表单id
        int formId = Util.getIntValue(param.get("formid").toString());
        //当前字段id
        String fieldid = Util.null2String(param.get("fieldid"));
        //查询列表id
        String customid = Util.null2String(param.get("customid"));
        //当前列名称(明细表会有d_前缀)
        String columnname = Util.null2String(param.get("columnname"));
        bb.writeLog("columnname====>"+columnname);
        //当前列数据
        String value = Util.null2String(param.get("value"));
        //当前行数据
        Map<String, Object> data = (Map<String, Object>) param.get("data");
        if ("关注客户".equals(columnname)) {
            //获取当前客户id
//            String sqlByname = "select * from CRM_CustomerInfo where id = "+Util.null2String(data.get("crmid"));
//            rs.executeQuery(sqlByname);
//            bb.writeLog("进入自定义导出数据操作");
//            if(rs.next()){
//
//            }
            String sql = "select *  from CRM_attention where resourceid = " + userId + " and customerid = " + Util.null2String(data.get("crmid"));
            bb.writeLog("导出"+sql);
            rs.executeQuery(sql);
            if (rs.next()) {//关联客户
                value = "已关注";
            } else {//未关联客户
                value = "未关注";
            }
        }
        if ("gzkh".equals(columnname)) {
            //获取当前客户id
//            String sqlByname = "select * from CRM_CustomerInfo where id = "+Util.null2String(data.get("crmid"));
//            rs.executeQuery(sqlByname);
//            if(rs.next()){
//
//            }
            String sql = "select *  from CRM_attention where resourceid = " + userId + " and customerid = " + Util.null2String(data.get("crmid"));
            bb.writeLog("导出"+sql);
            rs.executeQuery(sql);
            if (rs.next()) {//关联客户
                value = "已关注";
            } else {//未关联客户
                value = "未关注";
            }
        }
        return value;
    }
}
