package com.engine.zhongzhi2.gyl.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.cmd.bean.ContactorOthers;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SaveContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class SaveContactorOthers extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(SaveContactorOthers.class);


    public SaveContactorOthers(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            String contactorId = Util.null2String(params.get("contactorId")); //联系人数据id
            if (!contactorId.isEmpty()) {
                int moduleId = ModuleDataUtil.getModuleIdByName(ContactorOthers.TABLE_NAME);
                if (moduleId != -1) {
                    ContactorOthers data = buildData(contactorId, user);
                    log.info("ContactorOthers data:" + data);
                    if (data != null) {
                        //插入建模
                        ModuleResult mr = ModuleDataUtil.insertObj(data, ContactorOthers.TABLE_NAME, moduleId, user.getUID());
                        if (!mr.isSuccess()) {
                            error = mr.getErroMsg();
                            log.error(error);
                        } else {
                            result.put("billid", mr.getBillid());
                        }
                    } else {
                        error = "获取到的ContactorOthers为空";
                        log.error(error);
                    }
                } else {
                    error = "没有找到对应建模id";
                    log.error(error);
                }
            } else {
                error = "缺失参数";
                log.error(error);
            }
        } catch (Exception e) {
            error = "异常" + e.getMessage();
            log.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

    /**
     * 获取联系人数据
     *
     * @return
     */
    private static Map<String, Object> getContactorData(String contactorId) {

        String sql = "select * from CRM_CustomerContacter where id = ?";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, contactorId)) {
            Map<String, Object> data = QueryUtil.getMap(rs);
            if (data != null) {
                return SDUtil.lowerMapKey(data);
            }
        } else {
            log.error("getContactorData sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return null;
    }

    /**
     * 构造数据
     *
     * @param contactorId
     * @param user
     * @return
     */
    public static ContactorOthers buildData(String contactorId, User user) {
        ContactorOthers data = null;
        try {
            //根据联系人数据id，查询联系人数据
            Map<String, Object> contactorData = getContactorData(contactorId);
            log.info("contactorData:" + contactorData);
            if (contactorData != null) {
                data = new ContactorOthers();
                //客户id
                String crmid = Util.null2String(contactorData.get("customerid"));
                if (!crmid.isEmpty()) {
                    data.setKh(Util.getIntValue(crmid, 0));
                }
                //商机id
                String chanceid = Util.null2String(contactorData.get("sellchanceid"));
                if (!chanceid.isEmpty()) {
                    data.setShangji(Util.getIntValue(chanceid, 0));
                }
                int contactorIdInt = Util.getIntValue(contactorId, -1);
                data.setLxr(contactorIdInt); // 联系人
                data.setXm(Util.null2String(contactorData.get("fullname"))); //姓名
                data.setCh(Util.getIntValue(Util.null2String(contactorData.get("title")), 0)); //称呼
                data.setZw(Util.null2String(contactorData.get("jobtitle"))); //职位
                data.setSj(Util.null2String(contactorData.get("mobilephone"))); //手机
                data.setYx(Util.null2String(contactorData.get("email"))); //邮箱
                data.setZj(Util.null2String(contactorData.get("phoneoffice"))); //座机
                data.setLxrbm(Util.null2String(contactorData.get("bm")));//联系人部门
                data.setCjr(user.getUID()); //创建人
                if (user.getUID() != 1) {
                    data.setCjrbm(user.getUserDepartment()); //创建人部门
                }
                data.setDeleted(0); //删除标记，正常
                data.setWxh(Util.null2String(contactorData.get("imcode"))); //微信号（用标准字段IM号码存储）
            }
        } catch (Exception e) {
            log.error("buildData 异常：", e);
        }
        return data;

    }

}
