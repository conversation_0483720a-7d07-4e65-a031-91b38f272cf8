package com.engine.zhongzhi2.gyl.working.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.gyl.working.cmd.CoverApproveReqCmd;
import com.engine.zhongzhi2.gyl.working.cmd.CreateApproveReqCmd;
import com.engine.zhongzhi2.gyl.working.cmd.ForceOverCmd;
import com.engine.zhongzhi2.gyl.working.service.WorkingService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmSdServiceImpl.java
 * @Description CRM二开接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/17
 */

public class WorkingServiceImpl extends Service implements WorkingService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> createApproveReq(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateApproveReqCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> coverApproveReq(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CoverApproveReqCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> forceOver(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ForceOverCmd(params, user));
    }

}
