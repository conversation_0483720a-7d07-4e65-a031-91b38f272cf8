package com.engine.zhongzhi2.gyl.working.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SaveContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class ForceOverCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 对象锁
     */
    private static final Object lock = new Object();


    public ForceOverCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        if (checkParam()) {
            //加锁，防止并发同时查询到都是审批中流程，单其实有一个已经先归档了，所有会先校验一下流程状态
            synchronized (lock) {
                //校验当前流程状态，防止已经归档
                if (checkReqNodeType()) {
                    error = doExecute();
                } else {
                    error = "当前流程已归档，请刷新页面检查！";
                }
            }

        } else {
            error = "缺失参数";
            log.error(error);
        }
        DBUtil.clearThreadLocalRecordSet();
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

    /**
     * 校验参数
     *
     * @return
     */
    private boolean checkParam() {
        String requestid = Util.null2String(params.get("requestid"));//流程请求id
        String forceOverRemark = Util.null2String(params.get("forceOverRemark")); //强制归档签字意见
        String formtable = Util.null2String(params.get("formtable"));
        if (requestid.isEmpty() ||
                forceOverRemark.isEmpty() ||
                formtable.isEmpty()) {
            return false;
        }
        return true;
    }

    /**
     * 实际执行
     *
     * @return
     */
    private String doExecute() {
        String error = "";
        int requestid = Util.getIntValue(Util.null2String(params.get("requestid")), -1);//流程请求id
        String forceOverRemark = Util.null2String(params.get("forceOverRemark"));//强制归档签字意见
        User overUser = new User(1);
        //使用系统管理员，强制归档
        error = WfUtil.doForceOver(overUser, requestid, forceOverRemark, true);
        return error;
    }

    /**
     * 校验当前流程是否已经归档，如果已归档要报错
     *
     * @return
     */
    private boolean checkReqNodeType() {
        String requestid = Util.null2String(params.get("requestid"));//流程请求id
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        String sql = "SELECT " +
                "  m.requestid," +
                "  rb.currentnodetype " +
                " FROM " + formtable + "  m " +
                " INNER JOIN workflow_requestbase rb ON ( m.requestid = rb.requestid )  " +
                " WHERE 1=1  " +
                " and m.requestid = " + requestid;
        log.info("check checkReqNodeType sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                String currentnodetype = Util.null2String(rs.getString("currentnodetype"));
                return !"3".equals(currentnodetype);
            }
        }
        return false;
    }

}
