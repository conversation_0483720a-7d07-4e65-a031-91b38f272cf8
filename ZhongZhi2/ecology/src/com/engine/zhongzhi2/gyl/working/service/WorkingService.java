package com.engine.zhongzhi2.gyl.working.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName CrmSdService.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/17
 */

public interface WorkingService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createApproveReq(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> coverApproveReq(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> forceOver(Map<String, Object> params, User user);
}
