package com.engine.zhongzhi2.gyl.working.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi2.gyl.working.service.WorkingService;
import com.engine.zhongzhi2.gyl.working.service.impl.WorkingServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName CrmWeb.java
 * @Description CRM模块二开接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/19
 */

public class WorkingWeb {
    private WorkingService getService(User user) {
        return ServiceUtil.getService(WorkingServiceImpl.class, user);
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createApproveReq")
    @Produces(MediaType.TEXT_PLAIN)
    public String createApproveReq(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).createApproveReq(params, user)
        );
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/coverApproveReq")
    @Produces(MediaType.TEXT_PLAIN)
    public String coverApproveReq(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).coverApproveReq(params, user)
        );
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/forceOver")
    @Produces(MediaType.TEXT_PLAIN)
    public String forceOver(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).forceOver(params, user)
        );
    }

}
