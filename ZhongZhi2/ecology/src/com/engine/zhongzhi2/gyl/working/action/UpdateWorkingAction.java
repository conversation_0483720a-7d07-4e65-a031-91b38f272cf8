package com.engine.zhongzhi2.gyl.working.action;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.zhongzhi2.gyl.working.bean.ActualWorking;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName UpdateWorkingAction.java
 * @Description 工时审批流程归档后，更新工时明细台账
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/5
 */
@Getter
@Setter
public class UpdateWorkingAction extends BaseBean implements Action {
    //Action参数---START---
    //Action参数---END---

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    // 创建一个对象作为锁
    private static final Object lock = new Object();


    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        log.info("---START---");
        ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
        String error = execuetMy(actionInfo);
        log.info("---END---");
        return ActionUtil.handleResult(error, requestInfo);
    }

    /**
     * 执行
     *
     * @param actionInfo
     * @return
     */
    private String execuetMy(ActionInfo actionInfo) {
        String date;
        BigDecimal amount, rateNum, gs;
        String error = "";
        int db_id;
        String cy, zj, rate;
        List<ActualWorking> newData = new ArrayList<>();
        List<ActualWorking> updateData = new ArrayList<>();
        ActualWorking actualWorking;
        //明细1数据
        List<Map<String, String>> detailData1;
        try {
            //主表数据
            Map<String, String> mainData = actionInfo.getMainData();
            //明细表数据
            Map<Integer, List<Map<String, String>>> mapDetail = actionInfo.getDetailData();
            if (!mapDetail.isEmpty()) {
                //获取明细1表的数据
                detailData1 = mapDetail.getOrDefault(1, null);
                if (detailData1 != null && !detailData1.isEmpty()) {
                    //获取所有工时
                    String prjid = Util.null2String(mainData.get("xmmc"));
                    JSONArray allWorking = getAllGs(prjid);
                    String proCode = getPrjXmbh(prjid);

                    for (Map<String, String> eachDetail : detailData1) {

                        cy = Util.null2String(eachDetail.get("xm")); //成员
                        zj = Util.null2String(eachDetail.get("zj")); //职级
                        rate = Util.null2String(eachDetail.get("rate"));//rate
                        rateNum = SDUtil.getBigDecimalValue(Util.null2String(eachDetail.get("rate"))); //rate数字
                        date = Util.null2String(eachDetail.get("rq")); //日期
                        gs = SDUtil.getBigDecimalValue(Util.null2String(eachDetail.get("gs"))); //工时
                        int sqr = Util.getIntValue(mainData.get("sqr")); //申请人
                        int sqrbm = Util.getIntValue(mainData.get("sqbm"));//申请部门
                        String sqrq = Util.null2String(eachDetail.get("sqrq")); //申请日期
                        //金额=rate*工时
                        amount = gs.multiply(rateNum).setScale(2, RoundingMode.HALF_UP); //金额

                        //根据 人员+职级+rate+日期，判断当前日期的工时是否有数据，存在就更新，不存在就插入
                        db_id = checkHasData(allWorking, cy, zj, rate, date);
                        actualWorking = new ActualWorking();
                        actualWorking.setTbr(sqr)
                                .setTbbm(sqrbm)
                                .setTbrq(sqrq)
                                .setXgxm(prjid)
                                .setXm(Util.getIntValue(cy, -1))
                                .setZj(zj)
                                .setRate(rate)
                                .setRq(date)
                                .setGs(gs)
                                .setJe(amount)
                                .setXmbh(proCode)
                                .setYf(date.substring(0, 7));
                        //新增
                        if (db_id == -1) {
                            newData.add(actualWorking);
                        } else {
                            //更新
                            actualWorking.setId(db_id);
                            updateData.add(actualWorking);
                        }

                    }
                    log.info("newData:" + newData);
                    log.info("updateData:" + updateData);
                    if (!newData.isEmpty()) {
                        int moduleId = ModuleDataUtil.getModuleIdByName(ActualWorking.TABLE_NAME);
                        ModuleResult mr = ModuleDataUtil.insertObjList(newData, ActualWorking.TABLE_NAME, moduleId, actionInfo.getUser().getUID());
                        if (!mr.isSuccess()) {
                            log.error("插入建模出错：" + mr.getErroMsg());
                            error = mr.getErroMsg();
                        }
                    }
                    if (!updateData.isEmpty() && error.isEmpty()) {
                        ModuleResult mr = ModuleDataUtil.updateObjList(updateData, ActualWorking.TABLE_NAME, actionInfo.getUser().getUID());
                        if (!mr.isSuccess()) {
                            log.error("更新建模出错：" + mr.getErroMsg());
                            error = mr.getErroMsg();
                        }
                    }
                }
            }
        } catch (Exception e) {
            error = "doExecute异常:" + e.getMessage();
            log.error("doExecute异常", e);
        }
        return error;

    }

    /**
     * 获取当前项目所有的明细工时数据
     *
     * @param prjid
     * @return
     */
    private JSONArray getAllGs(String prjid) {
        JSONArray result = new JSONArray();
        String sql = "select * from uf_gsmxtz where xgxm = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            result = QueryUtil.getJSONList(rs);
        }
        return result;
    }

    /**
     * 检查当前日期是否已经有数据，有的话则返回数据id，无则返回-1
     *
     * @param allWork
     * @param cy
     * @param zj
     * @param rate
     * @param rq
     * @return
     */
    private int checkHasData(JSONArray allWork, String cy, String zj, String rate, String rq) {
        JSONObject jo;
        String db_cy, db_zj, db_rate, db_rq, db_prjid;
        int db_id = -1;
        for (int i = 0; i < allWork.size(); i++) {
            jo = allWork.getJSONObject(i);
            db_cy = Util.null2String(jo.get("xm"));
            db_zj = Util.null2String(jo.get("zj"));
            db_rate = Util.null2String(jo.get("rate"));
            db_rq = Util.null2String(jo.get("rq"));
            if (db_cy.equals(cy) &&
                    db_zj.equals(zj) &&
                    db_rate.equals(rate) &&
                    db_rq.equals(rq)) {
                db_id = Integer.parseInt(Util.null2String(jo.get("id")));
                break;
            }
        }
        return db_id;
    }

    /**
     * 获取项目编号
     *
     * @param prjid
     * @return
     */
    private String getPrjXmbh(String prjid) {
        String sql = "select procode from prj_projectinfo where id = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                return Util.null2String(rs.getString("procode"));
            }
        }
        return "";

    }
}
