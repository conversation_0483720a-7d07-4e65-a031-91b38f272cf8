package com.engine.zhongzhi2.gyl.working.job;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName ForceOverApproingWorkJob.java
 * @Description 每月1日凌晨，给没有归档的工时审批流程，强制归档
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/6/16
 */
@Getter
@Setter
public class ForceOverApproingWorkJob extends BaseCronJob {
    //----定时任务参数-----//
    /**
     * 审批流程表单名
     */
    private String formtable;
    /**
     * 强制归档签字意见
     */
    private String forceOverRemark;

    /**
     * 二开log类
     */
    private final Logger logSD = LoggerFactory.getLogger(this.getClass());

    /**
     *
     */
    @Override
    public void execute() {
        logSD.info("ForceOverApproingWorkJob---START");
        String overResult;
        try {
            if (StringUtils.isNotBlank(formtable) && StringUtils.isNotBlank(forceOverRemark)) {
                User user = new User(1);
                List<Integer> listReq = getAllReqList();
                logSD.info("listReq:" + listReq);
                if (!listReq.isEmpty()) {
                    logSD.info("listReq size:" + listReq);
                    for (Integer requestid : listReq) {
                        overResult = WfUtil.doForceOver(user, requestid, forceOverRemark, true);
                        logSD.info("requestid :" + requestid + ",overResult:" + overResult);
                    }
                }
            } else {
                logSD.warn("参数缺失配置");
            }
        } catch (Exception e) {
            logSD.error("异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        logSD.info("ForceOverApproingWorkJob---END");
    }

    /**
     * 获取所有未归档的流程
     *
     * @return
     */
    private List<Integer> getAllReqList() {
        List<Integer> list = new ArrayList<>();
        String sql = "select a.requestid  from " + formtable + " a " +
                " inner join workflow_requestbase b on (a.requestid = b.requestid) " +
                " where b.currentnodetype != 3";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                list.add(rs.getInt("requestid"));
            }
        } else {
            logSD.error("getAllReqList sql error :" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return list;
    }
}
