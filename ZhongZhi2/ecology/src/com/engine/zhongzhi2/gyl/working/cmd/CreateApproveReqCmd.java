package com.engine.zhongzhi2.gyl.working.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.NewRequestDto;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * @FileName SaveContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class CreateApproveReqCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(CreateApproveReqCmd.class);

    private static final Object lock = new Object();

    public CreateApproveReqCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);

        if (checkParam()) {
            //加锁，防止并发同时查询到没有审批流程，创建重复的审批流程
            synchronized (lock) {
                if (checkApprovingWork()) {
                    error = "当前已有当月审批中的流程，请刷新页面检查！";
                } else {
                    error = doExecute();
                }
            }

        } else {
            error = "缺失参数";
            log.error(error);
        }
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

    /**
     * 校验参数
     *
     * @return
     */
    private boolean checkParam() {
        String wfid = Util.null2String(params.get("wfid"));//流程路径id
        String reqName = Util.null2String(params.get("reqName"));//流程请求标题
        String datas = Util.null2String(params.get("datas")); //数据datas
        String prjid = Util.null2String(params.get("prjid"));//项目id

        String tbny = Util.null2String(params.get("tbny")); //填报年月
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        if (wfid.isEmpty() ||
                reqName.isEmpty() ||
                prjid.isEmpty() ||
                datas.isEmpty() ||
                tbny.isEmpty() ||
                formtable.isEmpty()) {
            return false;
        }
        return true;
    }

    /**
     * 实际执行
     *
     * @return
     */
    private String doExecute() {
        String error = "";
        try {
            String prjid = Util.null2String(params.get("prjid"));//项目id
            String datas = Util.null2String(params.get("datas")); //数据datas
            String wfid = Util.null2String(params.get("wfid"));//流程路径id
            String reqName = Util.null2String(params.get("reqName"));//流程请求标题
            JSONArray dataArray = JSONArray.parseArray(datas); //转JSONArray

            //获取项目基本信息
            Map<String, Object> prjInfo = getBaseInfo(prjid);
            if (prjInfo == null) {
                error = "未能正确获取到V_workding_info项目信息";
            } else {
                //明细1数据
                Map<String, Object> detailMap = buidlDetail1Data(dataArray, prjid);
                NewRequestDto.DetailData detailData1 = (NewRequestDto.DetailData) detailMap.get("detailData1");
                //明细1所有的工时
                BigDecimal totalWorkAmount = (BigDecimal) detailMap.get("totalWorkAmount");
                if (detailData1 != null) {
                    //主表数据
                    List<NewRequestDto.MainData> mainDataList = buildMainData(prjInfo, totalWorkAmount);
                    if (mainDataList != null) {

                        NewRequestDto newRequestDto = new NewRequestDto();
                        newRequestDto.setWorkflowId(wfid);//设置路径id
                        newRequestDto.setRequestName(reqName);  //设置标题

                        NewRequestDto.OtherParams otherParams = new NewRequestDto.OtherParams();
                        otherParams.setIsnextflow(1);        //设置自动提交 1自动提交
                        newRequestDto.setOtherParams(otherParams);

                        newRequestDto.setMainData(mainDataList); //设置主表数据
                        //设置明细表数据
                        List<NewRequestDto.DetailData> detailData = new ArrayList<>();
                        detailData.add(detailData1);
                        newRequestDto.setDetailData(detailData);
                        String createResult = WfUtil.createRequest(user, newRequestDto);
                        writeLog("createResult:" + createResult);
                        JSONObject createResultObj = JSONObject.parseObject(createResult);
                        if (createResultObj == null) {
                            error = "未获取到流程创建接口响应";
                        } else {
                            String code = Util.null2String(createResultObj.get("code"));
                            if (!"SUCCESS".equals(code)) {
                                error = "流程创建出错：" + createResult;
                            } else {
                                JSONObject dataObj = createResultObj.getJSONObject("data");
                                if (dataObj == null || !dataObj.containsKey("requestid")) {
                                    error = "未获取到新建流程requestid";
                                } else {
                                    log.info("新建流程requestid：" + Util.null2String(dataObj.get("requestid")));
                                }
                            }
                        }
                    } else {
                        error = "组装流程明细1出错";
                    }
                } else {
                    error = "组装流程主表出错";
                }
            }
        } catch (Exception e) {
            log.error("doExecute 异常", e);
            error = "doExecute 异常:" + SDUtil.getExceptionDetail(e);
        }
        return error;
    }

    /**
     * 构建明细1数据
     *
     * @param data
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> buidlDetail1Data(JSONArray data, String prjid) {
        Map<String, Object> result = new HashMap<>();
        JSONObject jo;
        String date;
        //日期对应的工时，rate
        BigDecimal dateValue, rateNum;
        String workcode, cy, zj, rate;
        NewRequestDto.WorkflowRequestTableFields detailField;
        NewRequestDto.DetailData detailData1;
        BigDecimal totalWorkAmount = BigDecimal.ZERO;
        try {
            //当前年月
            String currentYearMonth = TimeUtil.getCurrentDateString().substring(0, 7);
            //获取项目的开始，结束日期
            Map<String, String> mapPrjDate = getPrjDate(prjid);
            String prjStartDate = mapPrjDate.get("date1");
            String prjEndDate = mapPrjDate.get("date2");
            BigDecimal prjStartDateNum = SDUtil.getBigDecimalValue(prjStartDate.replace("-", ""));
            BigDecimal prjEndDateNum = SDUtil.getBigDecimalValue(prjEndDate.replace("-", ""));
            log.info("prjStartDateNum:" + prjStartDateNum);
            log.info("prjEndDateNum:" + prjEndDateNum);

            String formtable = Util.null2String(params.get("formtable"));//流程表单名
            String detailFormTable = formtable + "_dt1";//明细1
            detailData1 = new NewRequestDto.DetailData();
            detailData1.setTableDBName(detailFormTable);
            //明细数据记录records
            List<NewRequestDto.WorkflowRequestTableRecords> workflowRequestTableRecords = new ArrayList<>();

            //遍历datas组装明细表
            for (int i = 0; i < data.size(); i++) {
                jo = data.getJSONObject(i);
                workcode = Util.null2String(jo.get("workcode")); //工号
                cy = Util.null2String(jo.get("cy")); //成员(人员id)
                zj = Util.null2String(jo.get("zj"));//职级
                rate = Util.null2String(jo.get("rate"));//rate
                rateNum = SDUtil.getBigDecimalValue(rate);

                Set<Map.Entry<String, Object>> entries = jo.entrySet();
                //遍历字段，获取日期的数据
                for (Map.Entry<String, Object> entry : entries) {
                    //获取日期的字段值,一个日期一条数据
                    if (entry.getKey().startsWith("$date$")) {
                        NewRequestDto.WorkflowRequestTableRecords workflowRequestTableRecord = new NewRequestDto.WorkflowRequestTableRecords();
                        workflowRequestTableRecord.setRecordOrder(0);

                        //日期
                        date = entry.getKey().substring(6);
                        //日期变为num
                        BigDecimal dateNum = SDUtil.getBigDecimalValue(date.replace("-", ""));
                        //日期对应的工时
                        dateValue = SDUtil.getBigDecimalValue(entry.getValue());

                        //如果当前日期不可编辑，则不做处理
                        //判断当前日期，是否是当前月的，只能编辑当前月份的日期,非当前月份的，并且要在项目范围日期内的
                        if (date.substring(0, 7).equals(currentYearMonth) && dateNum.compareTo(prjStartDateNum) >= 0 && dateNum.compareTo(prjEndDateNum) <= 0) {
                            //累计所有工时*rate
                            totalWorkAmount = totalWorkAmount.add(dateValue.multiply(rateNum));
                            //根据 人员+职级+rate+日期，

                            //字段list
                            List<NewRequestDto.WorkflowRequestTableFields> workflowRequestTableFieldsList = new ArrayList<>();
                            //明细字段设置
                            //员工编号
                            detailField = new NewRequestDto.WorkflowRequestTableFields();
                            detailField.setFieldName("ygbh");
                            detailField.setFieldValue(workcode);
                            workflowRequestTableFieldsList.add(detailField);

                            //姓名
                            detailField = new NewRequestDto.WorkflowRequestTableFields();
                            detailField.setFieldName("xm");
                            detailField.setFieldValue(cy);
                            workflowRequestTableFieldsList.add(detailField);

                            //职级
                            detailField = new NewRequestDto.WorkflowRequestTableFields();
                            detailField.setFieldName("zj");
                            detailField.setFieldValue(zj);
                            workflowRequestTableFieldsList.add(detailField);

                            //rate
                            detailField = new NewRequestDto.WorkflowRequestTableFields();
                            detailField.setFieldName("rate");
                            detailField.setFieldValue(rateNum);
                            workflowRequestTableFieldsList.add(detailField);

                            //日期
                            detailField = new NewRequestDto.WorkflowRequestTableFields();
                            detailField.setFieldName("rq");
                            detailField.setFieldValue(date);
                            workflowRequestTableFieldsList.add(detailField);

                            //工时
                            detailField = new NewRequestDto.WorkflowRequestTableFields();
                            detailField.setFieldName("gs");
                            detailField.setFieldValue(dateValue);
                            workflowRequestTableFieldsList.add(detailField);

                            //设置到每个明细record上
                            workflowRequestTableRecord.setWorkflowRequestTableFields(workflowRequestTableFieldsList);

                            //再添加到records上
                            workflowRequestTableRecords.add(workflowRequestTableRecord);
                        } else {
                            log.info("date:" + date + "不符合编辑条件，跳过处理");
                        }
                    }
                }
            }
            //明细1设置workflowRequestTableRecords
            detailData1.setWorkflowRequestTableRecords(workflowRequestTableRecords);
        } catch (Exception e) {
            detailData1 = null;
            log.error("buidlDetail1Data异常：", e);
        }
        result.put("detailData1", detailData1);
        result.put("totalWorkAmount", totalWorkAmount);
        return result;

    }


    /**
     * 获取项目的开始结束日期
     *
     * @param prjid
     * @return
     */
    private Map<String, String> getPrjDate(String prjid) {
        Map<String, String> map = new HashMap<>();
        String sql = "select xmsjkssj as date1,xmssjssj as date2 from prj_projectinfo where id = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                map.put("date1", Util.null2String(rs.getString("date1")));
                map.put("date2", Util.null2String(rs.getString("date2")));
            }
        }
        return map;
    }

    /**
     * 组装主表数据
     *
     * @param prjInfo
     * @param totalWorkAmount 当前审批中工时
     * @return
     */
    private List<NewRequestDto.MainData> buildMainData(Map<String, Object> prjInfo, BigDecimal totalWorkAmount) {
        List<NewRequestDto.MainData> newMainDataList;
        try {
            newMainDataList = new ArrayList<>();
            NewRequestDto.MainData newMainData;
            BigDecimal qbkfpgs = SDUtil.getBigDecimalValue(prjInfo.get("qbkfpgs"));//全部可分配工时
            String prjid = Util.null2String(params.get("prjid"));//项目id
            String tbny = Util.null2String(params.get("tbny")); //填报年月

            //申请人，默认当前接口请求人
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("sqr");
            newMainData.setFieldValue(user.getUID());
            newMainDataList.add(newMainData);

            //申请日期
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("sqrq");
            newMainData.setFieldValue(TimeUtil.getCurrentDateString());
            newMainDataList.add(newMainData);

            //申请部门
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("sqbm");
            newMainData.setFieldValue(user.getUserDepartment());
            newMainDataList.add(newMainData);

            //项目名称
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("xmmc");
            newMainData.setFieldValue(prjid);
            newMainDataList.add(newMainData);

            //项目金额
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("xmje");
            newMainData.setFieldValue(Util.null2String(prjInfo.get("xmje")));
            newMainDataList.add(newMainData);

            //开票金额
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("kpje");
            newMainData.setFieldValue(Util.null2String(prjInfo.get("kpje")));
            newMainDataList.add(newMainData);

            //开票金额（百分比）
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("kpjebfb");
            newMainData.setFieldValue(Util.null2String(prjInfo.get("kpbl")));
            newMainDataList.add(newMainData);


            //全部可分配工时
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("qbkfpgs");
            newMainData.setFieldValue(qbkfpgs);
            newMainDataList.add(newMainData);

            //WIP
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("wip");
            newMainData.setFieldValue(Util.null2String(prjInfo.get("cyjewip")));
            newMainDataList.add(newMainData);

            //WIP（百分比）
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("wipbfb");
            newMainData.setFieldValue(Util.null2String(prjInfo.get("cybl")));
            newMainDataList.add(newMainData);

            //审批前=其他月工时+当月已审批工时（其实就是所有已审批的工时）
            newMainData = new NewRequestDto.MainData();
            BigDecimal spq = getAllApproved(prjid);
            log.info("spq:" + spq);
            newMainData.setFieldName("spq");
            newMainData.setFieldValue(spq);
            newMainDataList.add(newMainData);

            //审批前（百分比）
            //审批前/全部可分配工时
            if (spq != null) {
                BigDecimal spqbfb = BigDecimal.ZERO;
                if (qbkfpgs.compareTo(BigDecimal.ZERO) != 0) {
                    spqbfb = spq.divide(qbkfpgs, 4, RoundingMode.HALF_UP);
                }
                newMainData = new NewRequestDto.MainData();
                newMainData.setFieldName("spqbfb");
                newMainData.setFieldValue(spqbfb);
                newMainDataList.add(newMainData);
            }


            //审批后=其他月工时+当月审批中工时
            BigDecimal otherMonthWorking = getOtherMonthWorking(prjid);//其他月工时
            log.info("otherMonthWorking:" + otherMonthWorking);
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("sph");
            if (otherMonthWorking == null) {
                otherMonthWorking = BigDecimal.ZERO;
            }
            log.info("totalWorkAmount:" + totalWorkAmount);
            BigDecimal workingApproveAfter = otherMonthWorking.add(totalWorkAmount);
            log.info("workingApproveAfter:" + workingApproveAfter);
            newMainData.setFieldValue(workingApproveAfter);
            newMainDataList.add(newMainData);


            //审批后（百分比）
            //审批后/全部可分配工时
            BigDecimal sphbfb = BigDecimal.ZERO;
            if (qbkfpgs.compareTo(BigDecimal.ZERO) != 0) {
                sphbfb = workingApproveAfter.divide(qbkfpgs, 4, RoundingMode.HALF_UP);
            }
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("sphbfb");
            newMainData.setFieldValue(sphbfb);
            newMainDataList.add(newMainData);

            //填报年月
            newMainData = new NewRequestDto.MainData();
            newMainData.setFieldName("tbny");
            newMainData.setFieldValue(tbny);
            newMainDataList.add(newMainData);
        } catch (Exception e) {
            newMainDataList = null;
            log.error("buildMainData 异常:", e);
        }
        return newMainDataList;
    }

    /**
     * 获取项目信息
     *
     * @return
     */
    public Map<String, Object> getBaseInfo(String prjid) {
        String sql = "SELECT * from V_workding_info where 1=1 and prjid = ? ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql, prjid)) {
            return QueryUtil.getMap(rs);
        } else {
            log.error("getBaseInfo sql erro:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return null;
    }

    /**
     * 获取当前项目所有的已审批的工时数据
     *
     * @param prjid
     * @return
     */
    private BigDecimal getAllApproved(String prjid) {
        String sql = "select sum(gs*rate) as amount from uf_gsmxtz " +
                " where xgxm = " + prjid;
        log.info("getAllApproved sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return SDUtil.getBigDecimalValue(rs.getString("amount"));
            }
        } else {
            log.error("getAllApproved sql error :" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 获取当前项目其他月的明细工时数据
     *
     * @param prjid
     * @return
     */
    private BigDecimal getOtherMonthWorking(String prjid) {
        String tbny = Util.null2String(params.get("tbny")); //填报年月
        String[] dates = getMonthStartAndEnd(tbny);
        String sql = "select sum(gs*rate) as amount from uf_gsmxtz " +
                " where xgxm = " + prjid +
                " and (rq < '" + dates[0] + "' or rq > '" + dates[1] + "') ";
        log.info("getOtherMonthWorking sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return SDUtil.getBigDecimalValue(rs.getString("amount"));
            }
        } else {
            log.error("getOtherMonthWorking sql error :" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 获取月的开始结束日期
     *
     * @param dateString
     * @return
     * @throws DateTimeParseException
     */
    public static String[] getMonthStartAndEnd(String dateString) throws DateTimeParseException {
        dateString = dateString + "-01";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateString, formatter);

        LocalDate startDate = date.withDayOfMonth(1);
        LocalDate endDate = date.withDayOfMonth(date.lengthOfMonth());

        return new String[]{
                startDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                endDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
        };
    }

    /**
     * 校验是否有本月审批中的流程
     *
     * @return
     */
    private boolean checkApprovingWork() {
        String prjid = Util.null2String(params.get("prjid"));//项目id
        String tbny = Util.null2String(params.get("tbny")); //填报年月
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        String sql = "SELECT " +
                "  m.requestid " +
                " FROM " + formtable + "  m " +
                " INNER JOIN workflow_requestbase rb ON ( m.requestid = rb.requestid )  " +
                "WHERE 1=1  " +
                " and rb.currentnodetype IN (0,1,2) " +
                " and m.xmmc = " + prjid +
                " and m.sqr = " + user.getUID() +
                " and m.tbny = '" + tbny + "' ";
        log.info("check approvingWork sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return rs.next();
        }
        return false;
    }


}
