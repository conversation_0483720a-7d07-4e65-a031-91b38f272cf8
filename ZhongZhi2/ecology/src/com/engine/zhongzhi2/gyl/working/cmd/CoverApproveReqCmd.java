package com.engine.zhongzhi2.gyl.working.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.*;

/**
 * @FileName SaveContactorOthers.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/30
 */
public class CoverApproveReqCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 对象锁
     */
    private static final Object lock = new Object();


    public CoverApproveReqCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @return
     */
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        if (checkParam()) {
            //加锁，防止并发同时查询到都是审批中流程，单其实有一个已经先归档了，所有会先校验一下流程状态
            synchronized (lock) {
                //校验当前流程状态，防止已经归档
                if (checkReqNodeType()) {
                    error = doExecute();
                } else {
                    error = "当前流程已归档，请刷新页面检查！";
                }
            }

        } else {
            error = "缺失参数";
            log.error(error);
        }
        DBUtil.clearThreadLocalRecordSet();
        result.put("status", error.isEmpty());
        result.put("error", error);
        return result;
    }

    /**
     * 校验参数
     *
     * @return
     */
    private boolean checkParam() {
        String requestid = Util.null2String(params.get("requestid"));//流程请求id
        String datas = Util.null2String(params.get("datas")); //数据datas
        String prjid = Util.null2String(params.get("prjid"));//项目id
        String createNode = Util.null2String(params.get("createNode")); //创建节点id
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        if (requestid.isEmpty() ||
                datas.isEmpty() ||
                prjid.isEmpty() ||
                createNode.isEmpty() ||
                formtable.isEmpty()) {
            return false;
        }
        return true;
    }

    /**
     * 实际执行
     *
     * @return
     */
    private String doExecute() {
        String error = "";
        try {
            String datas = Util.null2String(params.get("datas")); //数据datas
            int createNode = Util.getIntValue(Util.null2String(params.get("createNode")), -1);//流程发起节点id
            JSONArray dataArray = JSONArray.parseArray(datas); //转JSONArray
            int requestid = Util.getIntValue(Util.null2String(params.get("requestid")), -1);//流程请求id

            //获取流程主表数据
            Map<String, Object> mainData = getMainData();
            if (mainData != null) {
                int sqr = Util.getIntValue(Util.null2String(mainData.get("sqr")), -1);//申请人
                int mainid = Util.getIntValue(Util.null2String(mainData.get("id")), -1); //主表数据id
                log.info("mainid:" + mainid);
                if (mainid == -1) {
                    error = "获取流程主表id失败";
                }
                if (error.isEmpty()) {
                    //删除明细
                    error = delDetail(mainid);
                    if (error.isEmpty()) {
                        //执行插入明细
                        error = executeDetailData(dataArray, mainid);
                        if (error.isEmpty()) {
                            //流程干预到发起节点
                            Set<String> intervenoridSet = new HashSet<>();
                            intervenoridSet.add(String.valueOf(sqr));
                            //使用系统管理员干预
                            User admin = new User(1);
                            error = WfUtil.doIntervenor(admin, requestid, intervenoridSet, createNode, "");
                            if (error.isEmpty()) {
                                //流程提交,使用流程申请人提交
                                User userApply = new User(sqr);
                                error = WfUtil.doSubmitRequest(userApply, requestid, "重新填报审批");
                            }
                        }
                    }
                }
            } else {
                error = "获取流程主表数据出错";
            }
        } catch (Exception e) {
            log.error("doExecute 异常", e);
            error = "doExecute 异常:" + SDUtil.getExceptionDetail(e);
        }
        return error;
    }

    /**
     * 执行明细数据
     *
     * @param data
     */
    @SuppressWarnings("unchecked")
    private String executeDetailData(JSONArray data, int mainid) {
        JSONObject jo;
        String date;
        //日期对应的工时，rate
        BigDecimal dateValue, rateNum;
        String workcode, cy, zj, rate;
        List<String> listSql = new ArrayList<>();
        String sql;
        String error = "";
        try {
            String prjid = Util.null2String(params.get("prjid"));//项目id
            String formtable = Util.null2String(params.get("formtable"));//流程表单名
            String detailFormTable = formtable + "_dt1";//明细1
            //当前年月
            String currentYearMonth = TimeUtil.getCurrentDateString().substring(0, 7);
            //获取项目的开始，结束日期
            Map<String, String> mapPrjDate = getPrjDate(prjid);
            String prjStartDate = mapPrjDate.get("date1");
            String prjEndDate = mapPrjDate.get("date2");
            BigDecimal prjStartDateNum = SDUtil.getBigDecimalValue(prjStartDate.replace("-", ""));
            BigDecimal prjEndDateNum = SDUtil.getBigDecimalValue(prjEndDate.replace("-", ""));
            log.info("prjStartDateNum:" + prjStartDateNum);
            log.info("prjEndDateNum:" + prjEndDateNum);

            String insertFields = "ygbh,xm,zj,rate,rq,gs,mainid";
            //遍历datas组装明细表
            for (int i = 0; i < data.size(); i++) {
                jo = data.getJSONObject(i);
                workcode = Util.null2String(jo.get("workcode")); //工号
                cy = Util.null2String(jo.get("cy")); //成员(人员id)
                zj = Util.null2String(jo.get("zj"));//职级
                rate = Util.null2String(jo.get("rate"));//rate
                rateNum = SDUtil.getBigDecimalValue(rate);

                Set<Map.Entry<String, Object>> entries = jo.entrySet();
                //遍历字段，获取日期的数据
                for (Map.Entry<String, Object> entry : entries) {
                    //获取日期的字段值,一个日期一条数据
                    if (entry.getKey().startsWith("$date$")) {
                        //日期
                        date = entry.getKey().substring(6);
                        //日期变为num
                        BigDecimal dateNum = SDUtil.getBigDecimalValue(date.replace("-", ""));
                        //日期对应的工时
                        dateValue = SDUtil.getBigDecimalValue(entry.getValue());

                        //如果当前日期不可编辑，则不做处理
                        //判断当前日期，是否是当前月的，只能编辑当前月份的日期,非当前月份的，并且要在项目范围日期内的
                        if (date.substring(0, 7).equals(currentYearMonth) && dateNum.compareTo(prjStartDateNum) >= 0 && dateNum.compareTo(prjEndDateNum) <= 0) {

                            //明细字段设置
                            //员工编号
                            String insertValue = "'" + workcode + "'," +
                                    //姓名
                                    cy + "," +
                                    //职级
                                    zj + "," +
                                    //rate
                                    rateNum + "," +
                                    //日期
                                    "'" + date + "'," +
                                    //工时
                                    dateValue + "," +
                                    //流程主表数据id
                                    mainid;
                            sql = "insert into " + detailFormTable + " (" + insertFields + ") values (" + insertValue + ") ";
                            listSql.add(sql);

                        } else {
                            log.warn("date:" + date + "不符合编辑条件，跳过处理");
                        }
                    }
                }
            }
            if (!listSql.isEmpty()) {
                log.info("listSql:" + listSql);
                RecordSetTrans rst = new RecordSetTrans();
                rst.setAutoCommit(false);
                try {
                    for (String eachSql : listSql) {
                        if (!rst.executeUpdate(eachSql)) {
                            error = "执行插入明细出错";
                            rst.rollback();
                            break;
                        }
                    }
                    if (error.isEmpty()) {
                        rst.commit();
                    }
                } catch (Exception e) {
                    rst.rollback();
                    error = "执行插入明细出错：" + e.getMessage();
                    log.error("执行listSql异常", e);
                }
            }

        } catch (Exception e) {
            error = "executeDetailData异常：" + e.getMessage();
            log.error("buidlDetail1Data异常：", e);
        }
        return error;
    }


    /**
     * 获取项目的开始结束日期
     *
     * @param prjid
     * @return
     */
    private Map<String, String> getPrjDate(String prjid) {
        Map<String, String> map = new HashMap<>();
        String sql = "select xmsjkssj as date1,xmssjssj as date2 from prj_projectinfo where id = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                map.put("date1", Util.null2String(rs.getString("date1")));
                map.put("date2", Util.null2String(rs.getString("date2")));
            }
        }
        return map;
    }


    /**
     * 校验当前流程是否已经归档，如果已归档要报错
     *
     * @return
     */
    private boolean checkReqNodeType() {
        String requestid = Util.null2String(params.get("requestid"));//流程请求id
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        String sql = "SELECT " +
                "  m.requestid," +
                "  rb.currentnodetype " +
                " FROM " + formtable + "  m " +
                " INNER JOIN workflow_requestbase rb ON ( m.requestid = rb.requestid )  " +
                " WHERE 1=1  " +
                " and m.requestid = " + requestid;
        log.info("check checkReqNodeType sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                String currentnodetype = Util.null2String(rs.getString("currentnodetype"));
                return !"3".equals(currentnodetype);
            }
        }
        return false;
    }

    /**
     * 删除明细
     *
     * @return
     */
    private String delDetail(int mainid) {
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        String detailTable = formtable + "_dt1";
        String sql = "delete from  " + detailTable + " where mainid = " + mainid;
        log.info("check delDetail sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (!rs.executeUpdate(sql)) {
            return "删除明细失败:" + rs.getExceptionMsg() + ";sql:" + sql;
        }
        return "";
    }

    /**
     * 获取主表id
     *
     * @return
     */
    private Map<String, Object> getMainData() {
        String requestid = Util.null2String(params.get("requestid"));//流程请求id
        String formtable = Util.null2String(params.get("formtable")); //流程表单
        String sql = "SELECT " +
                "  m.id," +
                "  m.sqr " +
                " FROM " + formtable + "  m " +
                " where m.requestid = " + requestid;
        log.info("check getMainId sql :" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getMap(rs);
        }
        return null;
    }
}
