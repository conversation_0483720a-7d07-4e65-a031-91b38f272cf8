package com.engine.zhongzhi2.tpw.workflow.common.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.tpw.crm.service.SellChanceCardAuthConfigService;
import com.engine.zhongzhi2.tpw.crm.service.impl.SellChanceCardAuthConfigServiceImpl;
import com.weaver.general.Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
    * @FileName SellChanceAuthSaveAction
    * @Description 商机协同确认流程触发更新商机权限
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/6/21
    */
@Getter
@Setter
public class SellChanceAuthSaveAction implements Action {
    /**
     * 商机字段名
     */
    private String sellChanceField;
    /**
     * 商机字段名
     */
    private String functionId;
    /**
     * wfId
     */
    private String wfId;

    private BaseBean bb = new BaseBean();

    private SellChanceCardAuthConfigService getService(User user) {
        return ServiceUtil.getService(SellChanceCardAuthConfigServiceImpl.class, user);
    }
    @Override
    public String execute(RequestInfo requestInfo) {
        bb.writeLog("SellChanceAuthSaveAction----START");
        String errorMsg = "";//出错信息
        String sellChanceId = "";
        try {
            //退回状态
//            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isNotBlank(sellChanceField)) {
                ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
                User user = actionInfo.getUser();
                Map<String, String> mainData = actionInfo.getMainData();
                if (mainData != null) {
                    sellChanceId = Util.null2String(mainData.get(sellChanceField));
                    bb.writeLog("商机id:" + sellChanceId);
                    Map<String, Object> params = new HashMap<>();
                    if(StringUtils.isNotBlank(sellChanceId)){
                        JSONObject objConfigWithoutEnable = WfConfigUtil.getObjConfigWithoutEnable(functionId, wfId);
                        bb.writeLog("objConfigWithoutEnable:" + JSONObject.toJSONString(objConfigWithoutEnable));
                        params.put("fromType", "sellChanceCard");
                        params.put("sellChanceId", sellChanceId);
                        params.put("type", "workflow");
                        bb.writeLog("sellChanceId:" + sellChanceId);
                        bb.writeLog("objConfigWithoutEnable:" + JSONObject.toJSONString(objConfigWithoutEnable));
                        if(objConfigWithoutEnable.getJSONArray("hrmResource")!=null){
                            params.put("hrmResource",objConfigWithoutEnable.getJSONArray("hrmResource").toJSONString());
                            bb.writeLog("hrmResource:" + JSONObject.toJSONString(objConfigWithoutEnable));
                        }
                        if(objConfigWithoutEnable.getJSONArray("matrix")!=null){
                            params.put("matrix",objConfigWithoutEnable.getJSONArray("matrix").toJSONString());
                            bb.writeLog("matrix:" + JSONObject.toJSONString(objConfigWithoutEnable.getJSONArray("matrix")));
                        }
                        Map<String, Object> serviceResult = getService(user).sellChanceCardAuthConfig(params, user);
                        bb.writeLog("商机权限卡片逻辑触发结果："+JSONObject.toJSONString(serviceResult));
                    }
                } else {
                    errorMsg = "主表数据不存在!";
                }
            } else {
                errorMsg = "商机字段名未配置!";
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("SellChanceAuthSaveAction--Exception:" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("SellChanceAuthSaveAction--errInfo:" + errorMsg);
        bb.writeLog("SellChanceAuthSaveAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }
}
