package com.engine.zhongzhi2.tpw.module.fixedConditions;

import com.alibaba.fastjson.JSONObject;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractCustomSqlConditionJavaCode;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Map;

public class CustomerCustomSearch1 extends AbstractCustomSqlConditionJavaCode {

    /**
     * 生成SQL查询限制条件
     * @param param
     *  param包含(但不限于)以下数据
     *  user 当前用户
     *
     * @return
     *  返回的查询限制条件的格式举例为: t1.a = '1' and t1.b = '3' and t1.c like '%22%'
     *  其中t1为表单主表表名的别名
     */
    public String generateSqlCondition(Map<String, Object> param) throws Exception {
        BaseBean bb = new BaseBean();
        String sqlCondition = "";
        bb.writeLog("测试 param:" + JSONObject.toJSONString(param));
        User user = (User)param.get("user");
        String zd1 = Util.null2String(param.get("zd1"));
        bb.writeLog("测试 zd1:" + zd1);
        String zd2 = Util.null2String(param.get("zd2"));
        bb.writeLog("测试 zd2:" + zd2);
        String moth = Util.null2String(param.get("moth"));
        bb.writeLog("测试 moth:" + moth);
        //当月统计
        //客户的过滤条件
        sqlCondition = "t1.rq like '"+moth+"%'";
        bb.writeLog("测试 sqlCondition:" + sqlCondition);
        return sqlCondition;
    }

}

