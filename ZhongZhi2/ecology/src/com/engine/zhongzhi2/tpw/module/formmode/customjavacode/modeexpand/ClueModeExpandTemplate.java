package com.engine.zhongzhi2.tpw.module.formmode.customjavacode.modeexpand;

import com.alibaba.fastjson.JSONObject;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.formmode.log.FormmodeLog;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;


/**
 * 说明
 * 修改时
 * 类名要与文件名保持一致
 * class文件存放位置与路径保持一致。
 * 请把编译后的class文件，放在对应的目录中才能生效
 * 注意 同一路径下java名不能相同。
 *
 * <AUTHOR>
 */
public class ClueModeExpandTemplate extends AbstractModeExpandJavaCodeNew {
    /**
     * 执行模块扩展动作
     *
     * @param param param包含(但不限于)以下数据
     *              user 当前用户
     *              importtype 导入方式(仅在批量导入的接口动作会传输) 1 追加，2覆盖,3更新，获取方式(int)param.get("importtype")
     *              导入链接中拼接的特殊参数(仅在批量导入的接口动作会传输)，比如a=1，可通过param.get("a")获取参数值
     *              页面链接拼接的参数，比如b=2,可以通过param.get("b")来获取参数
     * @return
     */
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<String, String>();
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        try {
            User user = (User) param.get("user");
            int billid = -1;//数据id
            int modeid = -1;//模块id
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");
            FormmodeLog formmodeLog = new FormmodeLog();
            formmodeLog.writeLog("Z自定义接口验证请求参数时刻：" + System.currentTimeMillis() + "  参数：" + JSONObject.toJSONString(requestInfo));
            if (requestInfo != null) {
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());
                String tableName = "";
                if (billid > 0 && modeid > 0) {
                    recordSet.executeQuery("select formid from modeinfo where id = " + modeid);
                    if (recordSet.next()) {
                        String formid = recordSet.getString("formid");
                        if (StringUtils.isNotBlank(formid)) {
                            recordSet.executeQuery("SELECT tablename FROM workflow_bill where id = " + formid);
                            tableName = recordSet.getString("tablename");
                        }
                    }
                    if (StringUtils.isNotBlank(tableName)) {
                        recordSet.executeQuery("select * from " + tableName + " where id = " + billid);
                        if (recordSet.next()) {
                            //城市
                            String cs = recordSet.getString("cs");
                            //线索池
                            String xsc = recordSet.getString("xsc");
                            int uid = user.getUID();
                            String paramSql = "";
                            //跟进人、创建人
//                            paramSql = " fzr = " + uid + ", " +
//                                    " cjr = " + uid + ", ";
                            paramSql = " cjr = " + uid + ", ";
                            if (uid != 1) {//非系统管理员
                                int departmentId = user.getUserDepartment();
                                //跟进部门、创建部门
//                                paramSql = paramSql + " fzbm = " + departmentId + ", " +
//                                        " cjbm = " + departmentId + ", ";
                                paramSql = paramSql + " cjbm = " + departmentId + ", ";
                            }
                            //字段联动
                            //1.城市带出省份
                            recordSet.executeQuery("select * from HrmCity where id = " + cs);
                            if (recordSet.next()) {
                                if (StringUtils.isNotBlank(recordSet.getString("provinceid"))) {
                                    paramSql = paramSql + " dq = " + recordSet.getString("provinceid") + " , ";
                                }

                            }
                            //2.线索池带出线索池管理员
                            recordSet.executeQuery("select * from uf_JCH_xsc where id =  " + xsc);
                            if (recordSet.next()) {
                                if (StringUtils.isNotBlank(recordSet.getString("xscgly"))) {
                                    paramSql = paramSql + " xscfzr = " + recordSet.getString("xscgly") + " , ";
                                }
                            }

                            String sql = "update " + tableName + " set " + paramSql + " gjzt = 0 where id = " + billid;
                            formmodeLog.writeLog("更新的的sql：" + sql);
                            recordSet.executeUpdate(sql);
                        }
                    }
                }

            }
        } catch (Exception e) {
            result.put("errmsg", "自定义出错信息");
            result.put("flag", "false");
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return result;
    }

}


