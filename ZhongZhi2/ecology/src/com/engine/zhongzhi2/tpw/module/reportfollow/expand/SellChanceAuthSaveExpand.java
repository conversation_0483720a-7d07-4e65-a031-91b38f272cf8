package com.engine.zhongzhi2.tpw.module.reportfollow.expand;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.zhongzhi2.tpw.crm.service.SellChanceCardAuthConfigService;
import com.engine.zhongzhi2.tpw.crm.service.impl.SellChanceCardAuthConfigServiceImpl;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName ReportSave2AddShareExpand.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/6/12
 */
public class SellChanceAuthSaveExpand extends AbstractModeExpandJavaCodeNew {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private SellChanceCardAuthConfigService getService(User user) {
        return ServiceUtil.getService(SellChanceCardAuthConfigServiceImpl.class, user);
    }


    /**
     * 执行模块扩展动作
     *
     * @param param param包含(但不限于)以下数据
     *              user 当前用户
     *              importtype 导入方式(仅在批量导入的接口动作会传输) 1 追加，2覆盖,3更新，获取方式(int)param.get("importtype")
     *              导入链接中拼接的特殊参数(仅在批量导入的接口动作会传输)，比如a=1，可通过param.get("a")获取参数值
     *              页面链接拼接的参数，比如b=2,可以通过param.get("b")来获取参数
     * @return
     */
    public Map<String, String> doModeExpand(Map<String, Object> param) {
//        let hrmResource =  JSON.stringify(config.hrmResource)
//        apiParam.hrmResource = hrmResource
//        let matrix =  JSON.stringify(config.matrix)
//        apiParam.matrix = matrix


        Map<String, String> result = new HashMap<>();
        String error = "";
        try {
            User user = (User) param.get("user");
            int billid = -1;//数据id
            int modeid = -1;//模块id
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");
            RecordSet rs = new RecordSet();
            if (requestInfo != null) {
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());
                if (billid > 0 && modeid > 0) {
                    //校验当前数据id，是否有被哪些人谁关注
                    String tableName = ModuleDataUtil.getModuleNameById(String.valueOf(modeid));
                    Map<String, Object> params = new HashMap<>();
                    params.put("billid", billid);
                    JSONObject objConfigWithoutEnable = WfConfigUtil.getObjConfigWithoutEnable("sellchance_auth_config", "1");
                    String sjid = "";
                    String sql = "select * from " + tableName + " where billid = " + billid;
                    rs.executeUpdate(sql);
                    if(rs.next()){
                        sjid = rs.getString("sjid");
                    }
                    if(StringUtils.isNotBlank(sjid)){
                        params.put("fromType", "sellChanceCard");
                        params.put("sellChanceId", sjid);
                        params.put("hrmResource",objConfigWithoutEnable.getJSONArray("hrmResource").toJSONString());
                        params.put("matrix",objConfigWithoutEnable.getJSONArray("matrix").toJSONString());
                        Map<String, Object> serviceResult = getService(user).sellChanceCardAuthConfig(params, user);
                        if (serviceResult != null) {
                            error = Util.null2String(serviceResult.get("error"));
                        }
                    }

                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        }
        if (!error.isEmpty()) {
            result.put("errmsg", error);
        }
        result.put("flag", String.valueOf(error.isEmpty()));
        return result;
    }
}
