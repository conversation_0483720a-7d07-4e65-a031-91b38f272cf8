package com.engine.zhongzhi2.tpw.module.fixedConditions;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractCustomSqlConditionJavaCode;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class CustomerCustomSearch extends AbstractCustomSqlConditionJavaCode {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 生成SQL查询限制条件
     * @param param
     *  param包含(但不限于)以下数据
     *  user 当前用户
     *
     * @return
     *  返回的查询限制条件的格式举例为: t1.a = '1' and t1.b = '3' and t1.c like '%22%'
     *  其中t1为表单主表表名的别名
     */
    public String generateSqlCondition(Map<String, Object> param) throws Exception {
        String sqlCondition = "";
        log.info("CustomerCustomSearch param:" + JSONObject.toJSONString(param));
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        User user = (User)param.get("user");
        String fgldField = Util.null2String(param.get("fgldField"));
        String bmfzrField = Util.null2String(param.get("bmfzrField"));
        String moth = Util.null2String(param.get("moth"));
        //查询当前操作人员的所有下级（包含下级的下级）以及当前操作人为部门矩阵的分管领导和部门负责人时的部门部门人员
        Set hrmids = getSubordinateResourceIds(Util.null2String(user.getUID()));
        log.info("当前操作人员与其下级:" + JSONObject.toJSONString(hrmids));
        //2.系统登录人为部门矩阵中的分管领导或部门负责人时所对应的部门所有人员
        rs.executeQuery("select * from Matrixtable_2");
        Set<String> departs = new HashSet<>();
        while (rs.next()) {
            //分管领导
            String fgld = rs.getString(fgldField);
            //部门负责人
            String bmfzr = rs.getString(bmfzrField);
            if (StringUtils.isNotBlank(fgld)) {
                String[] fglds = fgld.split(",");
                if(Arrays.asList(fglds).contains(user.getUID())){
                    departs.add(rs.getString("id"));
                }
            }
            if (StringUtils.isNotBlank(bmfzr)) {
                String[] bmfzrs = bmfzr.split(",");
                if(Arrays.asList(bmfzrs).contains(user.getUID())){
                    departs.add(rs.getString("id"));
                }
            }
        }
        if (!departs.isEmpty()) {
            String departIds = String.join(",", departs);
            rs.executeQuery("select id  from HrmResource  where departmentid in (" + departIds + ")");
            while (rs.next()) {
                hrmids.add(rs.getString("id"));
            }
        }
        log.info("符合查询条件的人员:" + JSONObject.toJSONString(hrmids));

        //当月统计
        //客户的过滤条件
        sqlCondition = " t1.manager in ( "+String.join(",",hrmids)+" ) and t1.createdate like '"+moth+"%'";
        log.info("CustomerCustomSearch sqlCondition:" + sqlCondition);
        DBUtil.clearThreadLocalRecordSet();
        return sqlCondition;
    }

    /**
     * 获取指定人员ID下所有下级人员的ID列表
     *
     * @param managerid 指定的上级人员ID
     * @return 下级人员ID列表
     */
//    public Set<String> getSubordinateResourceIds(String managerid) {
//        RecordSet rs = DBUtil.getThreadLocalRecordSet();
//        Set<String> subordinateIds = new HashSet<>();
//        // SQL查询语句，获取所有下级人员ID
//        String sql = "SELECT id FROM hrmresource WHERE managerid = '" + managerid+"'";
//        rs.executeQuery(sql);
//        while (rs.next()) {
//            // 递归查询下级人员的下级人员id
//            subordinateIds.addAll(getSubordinateResourceIds(rs.getString("id")));
//        }
//        // 添加当前人员ID
//        subordinateIds.add(managerid);
//        return subordinateIds;
//    }
    public Set<String> getSubordinateResourceIds(String managerid) {
        Set<String> subordinateIds = new HashSet<>();
        try{
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            // SQL查询语句，获取所有下级人员ID
            String sql = "SELECT id FROM hrmresource WHERE managerid = '" + managerid+"'";
            HashSet<String> currLowerHrmIds = new HashSet<>();
            rs.executeQuery(sql);
            while (rs.next()) {
                // 递归查询下级人员的下级人员id
                String hrmId = rs.getString("id");
                currLowerHrmIds.add(hrmId);
            }
            if(!currLowerHrmIds.isEmpty()){
                //遍历currLowerHrmIds
                // 递归获取下级人员的下级人员ID
                for (String subordinateId : currLowerHrmIds) {
                    subordinateIds.addAll(getSubordinateResourceIds(subordinateId));
                }
            }
            // 添加当前人员ID
            subordinateIds.add(managerid);
        }catch (Exception e){
            log.info("getSubordinateResourceIds Exception:" + e.getMessage());
        }finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        return subordinateIds;
    }

}

