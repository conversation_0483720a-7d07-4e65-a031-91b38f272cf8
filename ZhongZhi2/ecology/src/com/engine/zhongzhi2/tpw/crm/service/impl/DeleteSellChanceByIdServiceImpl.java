package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.DeleteSellChanceByIdCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.GenerateSerialNumberCmd;
import com.engine.zhongzhi2.tpw.crm.service.DeleteSellChanceByIdService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class DeleteSellChanceByIdServiceImpl extends Service implements DeleteSellChanceByIdService {
    @Override
    public Map<String, Object> deleteSellChanceById(Map<String, Object> params, User user) {
        return commandExecutor.execute(new DeleteSellChanceByIdCmd(params, user));
    }
}
