package com.engine.zhongzhi2.tpw.crm.service.impl;


import com.engine.zhongzhi2.tpw.crm.cmd.ReferrerDataFilteringEditCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.ReferrerDataFilteringEditEntityCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.ReferrerDataFilteringNewCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.ReferrerDataFilteringNewEntityCmd;
import com.engine.zhongzhi2.tpw.crm.service.ReferrerDataFilteringService;

import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class ReferrerDataFilteringServiceImpl extends Service implements ReferrerDataFilteringService {
    @Override
    public Map<String, Object> processReferrerNewData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ReferrerDataFilteringNewCmd(params, user));
    }

    @Override
    public Map<String, Object> processReferrerNewEntityData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ReferrerDataFilteringNewEntityCmd(params, user));
    }

    @Override
    public Map<String, Object> processReferrerEditData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ReferrerDataFilteringEditCmd(params, user));
    }

    @Override
    public Map<String, Object> processReferrerEditEntityData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ReferrerDataFilteringEditEntityCmd(params, user));
    }
}
