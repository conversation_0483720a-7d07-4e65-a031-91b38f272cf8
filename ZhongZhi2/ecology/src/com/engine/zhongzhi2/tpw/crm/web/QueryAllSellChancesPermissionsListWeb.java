package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi2.tpw.crm.service.QueryAllSellChancesPermissionsListService;
import com.engine.zhongzhi2.tpw.crm.service.impl.QueryAllSellChancesPermissionsListServiceImpl;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class QueryAllSellChancesPermissionsListWeb extends BaseBean {
    private QueryAllSellChancesPermissionsListService getService(User user) {
        return ServiceUtil.getService(QueryAllSellChancesPermissionsListServiceImpl.class, user);
    }
    @POST
    @Path("/queryAllSellChances")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryAllSellChancesPermissionsList(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);

        Map<String, Object> params = ParamUtil.request2Map(request);
        String origin = request.getHeader("origin");
        ArrayList<String> list = new ArrayList<>();
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                String name = cookie.getName();
                String value = cookie.getValue();
                String paramsObj = name+"="+value;
                list.add(paramsObj);
            }
        }
        params.put("cookie", String.join(";", list));
        params.put("origin", origin);
        if (user != null) {
            result = getService(user).queryAllSellChancesPermissionsList(params, user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
