package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi2.tpw.crm.service.CurrentMonthCountsService;
import com.engine.zhongzhi2.tpw.crm.service.MonthlyContractStatisticsService;
import com.engine.zhongzhi2.tpw.crm.service.impl.CurrentMonthCountsServiceImpl;
import com.engine.zhongzhi2.tpw.crm.service.impl.MonthlyContractStatisticsServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

public class CurrentMonthCountsWeb {
    private CurrentMonthCountsService getService(User user) {
        return ServiceUtil.getService(CurrentMonthCountsServiceImpl.class, user);
    }

    /**
     * 月度统计
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/currentMonthCounts")
    @Produces(MediaType.TEXT_PLAIN)
    public String currentMonthCounts(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).currentMonthCounts(params, user)
        );
    }
}
