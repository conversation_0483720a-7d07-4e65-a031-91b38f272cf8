package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class SellchanceWritebackClueCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();
    public SellchanceWritebackClueCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            bb.writeLog("SellchanceWritebackClueCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            //线索id
            String id = (String) params.get("id");
            //跟进状态
            String gjzt = (String) params.get("gjzt");
            //更进结果
            String gjjg = (String) params.get("gjjg");
            if(StringUtils.isNotBlank(id)){
                String sql = "UPDATE uf_JCH_xskp SET "+"gjzt = "+gjzt+", gjjg = "+gjjg+" WHERE id = "+id;
                rs.executeUpdate(sql);
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("商机流水编号生成异常：" + errorMsg);
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;

    }
}
