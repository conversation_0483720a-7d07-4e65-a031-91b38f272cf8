package com.engine.zhongzhi2.tpw.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class ReferrerDataFilteringEditCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public ReferrerDataFilteringEditCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("ReferrerDataFilteringEditCmd params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            String sellchanceId = Util.null2String(params.get("sellchanceId"));
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String querySql = "SELECT tjr1, tjryddsjgly,sjly FROM CRM_SellChance WHERE id = ?";
            recordSet.executeQuery(querySql, sellchanceId);
            if (recordSet.next()) {
                String tjryddsjgly = recordSet.getString("tjryddsjgly");
                String sjly = recordSet.getString("sjly");
                //如果商机来源等于领导介绍 不做操作
                if("4".equals(sjly)) {
                    String updateSql = "UPDATE CRM_SellChance SET tjr1 = ? WHERE id = ?";
                    recordSet.executeUpdate(updateSql, tjryddsjgly, sellchanceId);
                }
            } else {
                result.put("flag", "false");
                result.put("errmsg", "未找到对应的商机记录");
            }
        }catch (Exception e){
            errorMsg = e.getMessage();
        }finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}
