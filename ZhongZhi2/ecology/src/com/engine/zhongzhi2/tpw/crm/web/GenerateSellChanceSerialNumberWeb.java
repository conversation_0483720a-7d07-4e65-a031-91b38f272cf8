package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;

import com.engine.zhongzhi2.tpw.crm.service.GenerateSerialNumberService;
import com.engine.zhongzhi2.tpw.crm.service.impl.GenerateSerialNumberServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class GenerateSellChanceSerialNumberWeb extends BaseBean {
    private GenerateSerialNumberService getService(User user) {
        return ServiceUtil.getService(GenerateSerialNumberServiceImpl.class, user);
    }
    @POST
    @Path("/generateSerialNumber")
    @Produces({MediaType.TEXT_PLAIN})
    public String generateSerialNumber(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            result = getService(user).generateSerialNumber(ParamUtil.request2Map(request), user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
    //将商机编号和商机id回写到商机实体表单中
    @POST
    @Path("/generateSerialNumberIET")
    @Produces({MediaType.TEXT_PLAIN})
    public String generateSerialNumberInEntityTable(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            result = getService(user).generateSerialNumberInEntityTable(ParamUtil.request2Map(request), user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
