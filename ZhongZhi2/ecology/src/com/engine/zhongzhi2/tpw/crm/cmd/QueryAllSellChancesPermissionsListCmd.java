package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.formmode.page.util.Util;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.tpw.crm.bean.SellChancePermissionLog;
import com.engine.zhongzhi2.tpw.job.bean.KpyjbBean;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.*;
import java.util.stream.Collectors;

public class QueryAllSellChancesPermissionsListCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    // todo 请求url要修改
    private final static String List_Url = "/api/crm/sellchance/list";
    private final static String PageSize_Url = "/api/ec/dev/table/pageSize";
    private final static String Data_Url = "/api/ec/dev/table/datas";

    public QueryAllSellChancesPermissionsListCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog("QueryAllSellChancesPermissionsListCmd--start");
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        String cookie = Util.null2String(params.get("cookie"));
        String origin = Util.null2String(params.get("origin"));
        bb.writeLog("QueryAllSellChancesPermissionsListCmd--cookie:" + cookie);
        //获取sessionkey
        String sessionkey = geSessionKey(cookie, origin);
        HashSet<String> sellChances = new HashSet<>();
        if (StringUtils.isNotBlank(sessionkey)) {
            bb.writeLog("sessionkey: " + sessionkey);
            //请求PageSize_Url接口 设置单次请求返回的PageSize数量
            setSellChanceReturnedNumber(sessionkey, cookie, 100000, origin);
            //请求Data_Url接口 获取具有当前操作人查看权限的商机数据集
            errorMsg = getSellChancesPermissionsList(sessionkey, sellChances, cookie, origin);
            setSellChanceReturnedNumber(sessionkey, cookie, 10, origin);
            //插入建模权限记录
            insetModule(sellChances);
        } else {
            //获取sessionkey失败
            errorMsg = "获取sessionkey失败";
        }
        bb.writeLog("sellChances:" + JSON.toJSONString(sellChances));
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        result.put("sellChances", sellChances);
        bb.writeLog("QueryAllSellChancesPermissionsListCmd--end");
        return result;
    }

    //请求List_Url接口 获取sessionkey
    public String geSessionKey(String cookie, String origin) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        String sessionkey = "";
        String url = origin + List_Url + "?labelid=all&dspField170=&dspField193=1&dspField192=&dspField191=&dspField182=&dspField183=&dspField147=&dspField150=&dspField152=&dspField188=&dspField190=&dspField176=&dspField179=&dspField186=&dspField177=&dspField180=&dspField160select=0&dspField160from=&dspField160to=&dspField163select=0&dspField163from=&dspField163to=&__random__" + System.currentTimeMillis();
        //        String url = origin+List_Url+"?labelid=all&dspField147=&dspField150=&dspField152=&dspField175=&includeSubCompany=1&subCompanyId=&includeSubDepartment=1&departmentId=&productId=&__random__="+System.currentTimeMillis();
        RestResult data = HttpUtil.getDataWithHeader(url, headers);
        bb.writeLog("geSessionKey: " + JSON.toJSONString(data));
        String body = data.getResponseInfo().getBody();
        if (data.isSuccess() && StringUtils.isNotBlank(body)) {
            sessionkey = (String) JSON.parseObject(body).get("sessionkey");
        }
        return sessionkey;
    }

    public void setSellChanceReturnedNumber(String sessionkey, String cookie, int pageSize, String origin) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        JSONObject params = new JSONObject();
        params.put("dataKey", sessionkey);
        params.put("pageSize", pageSize);
        try {
            String restResult = HttpUtil.postWeaverDataWithCookie(origin + PageSize_Url, cookie, params);
            bb.writeLog("setSellChanceReturnedNumber: " + restResult);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String getSellChancesPermissionsList(String sessionkey, HashSet<String> sellChances, String cookie, String origin) {
        String errMsg = "";
        JSONObject params = new JSONObject();
        params.put("dataKey", sessionkey);
        params.put("current", 1);
        params.put("sortParams", "[]");
        bb.writeLog("data接口请求params: " + JSON.toJSONString(params));
        try {
            String restResult = HttpUtil.postWeaverDataWithCookie(origin + Data_Url, cookie, params);
            if (StringUtils.isNotBlank(restResult)) {
                // 将 JSON 字符串解析为 JsonObject
                JSONObject jsonObject = JSONObject.parseObject(restResult);
                // 获取 datas 数组
                JSONArray datas = jsonObject.getJSONArray("datas");
                // 遍历 datas 数组，提取每个对象的 id 字段
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject data = datas.getJSONObject(i);
                    String id = data.getString("id");
                    sellChances.add(id);
                }
            }
            bb.writeLog("getSellChancesPermissionsList: " + restResult);
        } catch (Exception e) {
            errMsg = SDUtil.getExceptionDetail(e);
        }
        return errMsg;
    }


    private void insetModule(HashSet<String> sellChances) {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
       try {
           // 清除旧数据（使用参数化查询）
           String userId = String.valueOf(this.user.getUID());
           recordSet.execute("DELETE FROM " + SellChancePermissionLog.TABLE_NAME + " WHERE ryid= " + userId);
           recordSet.execute("DELETE FROM " + SellChancePermissionLog.DETAIL_TABLE_NAME + " WHERE ryid= " + userId);

           // 插入主表
           SellChancePermissionLog mainLog = new SellChancePermissionLog();
           mainLog.setSync_cnt(sellChances.size());
           mainLog.setRyid(userId);

           ModuleResult moduleResult = ModuleDataUtil.insertObj(
                   mainLog,
                   SellChancePermissionLog.TABLE_NAME,
                   ModuleDataUtil.getModuleIdByName(SellChancePermissionLog.TABLE_NAME),
                   1,
                   false
           );
           if(moduleResult.isSuccess()){
               // 插入明细表（批量操作）
               int billid = moduleResult.getBillid();
               List<SellChancePermissionLog.SellChancePermissionLogDetailLog> details = sellChances.stream()
                       .map(chance -> {
                           SellChancePermissionLog.SellChancePermissionLogDetailLog detail =
                                   new SellChancePermissionLog.SellChancePermissionLogDetailLog();
                           detail.setSjid(chance);
                           detail.setRyid(userId);
                           return detail;
                       })
                       .collect(Collectors.toList());

               if (!details.isEmpty()) {
                   ModuleResult detailResult = ModuleDataUtil.insertObjDetail(
                           details,
                           SellChancePermissionLog.DETAIL_TABLE_NAME,
                           billid
                   );
               }
           }
       }catch (Exception e){

       }finally {
           DBUtil.clearThreadLocalRecordSet();
       }
    }
}
