package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.zhongzhi2.tpw.crm.cmd.QueryAllSellChancesPermissionsListCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.SellChanceCardAuthConfigCmd;
import com.engine.zhongzhi2.tpw.crm.service.QueryAllSellChancesPermissionsListService;
import weaver.hrm.User;
import com.engine.core.impl.Service;
import java.util.Collections;
import java.util.Map;

public class QueryAllSellChancesPermissionsListServiceImpl extends Service implements QueryAllSellChancesPermissionsListService {
    @Override
    public Map<String, Object> queryAllSellChancesPermissionsList(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryAllSellChancesPermissionsListCmd(params, user));
    }
}
