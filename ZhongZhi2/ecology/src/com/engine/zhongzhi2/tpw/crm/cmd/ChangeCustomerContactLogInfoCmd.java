package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
    * @FileName ChangeCustomerContactLogInfoCmd
    * @Description 更改客户联系人日志信息
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/2/25
    */
public class ChangeCustomerContactLogInfoCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    public ChangeCustomerContactLogInfoCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            bb.writeLog("ChangeCustomerContactLogInfoCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            //根据系统标准表中的商机 到建模表
            String contactorId = Util.null2String(params.get("contactorId"));
            String billid = Util.null2String(params.get("billid"));
            if (StringUtils.isNotBlank(contactorId) && StringUtils.isNotBlank(billid)) {
                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                boolean b = recordSet.executeUpdate("update uf_khlxr_ext set lxr = " + contactorId + " where id = " + billid);
                if(!b){
                    errorMsg = "更改客户联系人日志信息失败";
                }
            } else {
                errorMsg = "contactorId || billid is null";
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("更改客户联系人日志信息：" + errorMsg);
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}
