package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.zhongzhi2.tpw.crm.service.SellChanceCardAuthConfigService;
import com.engine.zhongzhi2.tpw.crm.service.impl.SellChanceCardAuthConfigServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class SellChanceCardAuthConfigWeb extends BaseBean {
    private SellChanceCardAuthConfigService getService(User user) {
        return ServiceUtil.getService(SellChanceCardAuthConfigServiceImpl.class, user);
    }
    @POST
    @Path("/sellChanceCardAuthConfig")
    @Produces({MediaType.TEXT_PLAIN})
    public String sellChanceCardAuthConfig(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            result = getService(user).sellChanceCardAuthConfig(ParamUtil.request2Map(request), user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
