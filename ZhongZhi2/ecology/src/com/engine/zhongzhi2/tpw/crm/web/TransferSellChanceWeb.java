package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.zhongzhi2.tpw.crm.service.TransferSellChanceService;
import com.engine.zhongzhi2.tpw.crm.service.impl.QueryAllSellChancesPermissionsListServiceImpl;
import com.engine.zhongzhi2.tpw.crm.service.impl.TransferSellChanceServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
    * @FileName TransferSellChanceWeb
    * @Description 商机迁移 。系统标准表的已经商机迁移到建模实体表中
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/2/20
    */
public class TransferSellChanceWeb extends BaseBean {
    private TransferSellChanceService getService(User user) {
        return ServiceUtil.getService(TransferSellChanceServiceImpl.class, user);
    }
    @POST
    @Path("/transferSellChance")
    @Produces(MediaType.TEXT_PLAIN)
    public String transferSellChance(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ParamUtil.request2Map(request);
        if (user != null) {
            result = getService(user).transferSellChance(params, user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
