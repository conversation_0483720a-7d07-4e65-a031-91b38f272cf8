package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.BusinessBrowseCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.BusinessOpportunitiesCmd;
import com.engine.zhongzhi2.tpw.crm.service.BusinessOpportunitiesService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class BusinessOpportunitiesServiceImpl extends Service implements BusinessOpportunitiesService {
    @Override
    public Map<String, Object> businessOpportunities(Map<String, Object> params, User user) {
        return commandExecutor.execute(new BusinessOpportunitiesCmd(params, user));
    }
}
