package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.GenerateSerialNumberCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.GenerateSerialNumberInEntityTableCmd;
import com.engine.zhongzhi2.tpw.crm.service.GenerateSerialNumberService;

import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class GenerateSerialNumberServiceImpl extends Service implements GenerateSerialNumberService {
    @Override
    public Map<String, Object> generateSerialNumber(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GenerateSerialNumberCmd(params, user));
    }

    @Override
    public Map<String, Object> generateSerialNumberInEntityTable(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GenerateSerialNumberInEntityTableCmd(params, user));
    }
}
