package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.CurrentMonthCountsCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.CustomerTaxIdVerifyCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.GenerateSerialNumberCmd;
import com.engine.zhongzhi2.tpw.crm.service.CurrentMonthCountsService;
import com.engine.zhongzhi2.tpw.crm.service.CustomerTaxIdVerifyService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class CustomerTaxIdVerifyServiceImpl extends Service implements CustomerTaxIdVerifyService {

    @Override
    public Map<String, Object> customerTaxIdVerify(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CustomerTaxIdVerifyCmd(params, user));
    }
}
