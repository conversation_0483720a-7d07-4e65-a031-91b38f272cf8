package com.engine.zhongzhi2.tpw.crm.mobile;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import com.engine.common.util.ServiceUtil;
import com.engine.zhongzhi2.tpw.crm.service.impl.SellchanceWritebackClueServiceImpl;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

/**
 * 商机阶段信息回写到对应线索的自定义逻辑类
 */
public class SellchanceWritebackClue extends AbstractModeExpandJavaCodeNew {

    /**
     * 创建阶段配置项
     */
    private static Map<String, String> createStageConfig(String gjzt, String gjjg) {
        Map<String, String> config = new HashMap<>();
        config.put("gjzt", gjzt);
        config.put("gjjg", gjjg);
        return config;
    }

    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<>();
        try {
            // 获取上下文信息
            User user = (User) param.get("user");
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");

            // 参数校验
            if (requestInfo == null) {
                return errorResult(result, "请求信息为空");
            }

            int billid = Util.getIntValue(requestInfo.getRequestManager().getBillid());
            int modeid = Util.getIntValue(requestInfo.getRequestManager().getWorkflowid());

            if (billid <= 0 || modeid <= 0) {
                return errorResult(result, "无效的商机ID或模块ID");
            }

            // 查询商机阶段信息
            Optional<HashMap<String, String>> stageInfoOpt = getStageInfo(billid);
            if (!stageInfoOpt.isPresent()) {
                return errorResult(result, "商机信息为空");
            }

            HashMap<String, String> stageInfo = stageInfoOpt.get();
            String sjjdValue = stageInfo.get("sjjd2");
            if (StringUtils.isBlank(sjjdValue)) {
                return errorResult(result, "商机阶段信息为空");
            }

            // 匹配阶段配置

            Map<String, Map<String, String>> STAGE_CONFIG = new HashMap<>();
            STAGE_CONFIG.put("1", createStageConfig("2", "4"));
            STAGE_CONFIG.put("2", createStageConfig("2", "4"));
            STAGE_CONFIG.put("3", createStageConfig("2", "5"));
            STAGE_CONFIG.put("4", createStageConfig("2", "6"));
            STAGE_CONFIG.put("5", createStageConfig("2", "7"));
            STAGE_CONFIG.put("6", createStageConfig("2", "9"));
            STAGE_CONFIG.put("7", createStageConfig("2", "8"));
            Map<String, String> matchedConfig = STAGE_CONFIG.get(sjjdValue);

            if (matchedConfig == null) {
                return errorResult(result, "未找到阶段[" + sjjdValue + "]对应的配置");
            }

            // 构建服务参数
            Map<String, Object> params = new HashMap<>();
            params.put("id", Util.null2String(stageInfo.get("glxs"))); // 线索id
            params.put("gjzt", matchedConfig.get("gjzt")); // 跟进状态
            params.put("gjjg", matchedConfig.get("gjjg")); // 跟进结果

            // 调用服务执行回写
            Map<String, Object> serviceResult = ServiceUtil.getService(SellchanceWritebackClueServiceImpl.class, user)
                    .sellchanceWritebackClue(params, user);

            // 处理服务返回结果
            result.putIfAbsent("errorMsg", Util.null2String(serviceResult.get("errorMsg")));
            result.putIfAbsent("status", Util.null2String(serviceResult.get("status")));

        } catch (Exception e) {
            errorResult(result, "系统异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * 查询商机阶段信息
     */
    private Optional<HashMap<String, String>> getStageInfo(int billid) {
        HashMap<String, String> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql = "SELECT sjjd2, glxs FROM CRM_SellChance WHERE id = ?";
        rs.executeQuery(sql, billid);

        if (rs.next()) {
            map.put("sjjd2", Util.null2String(rs.getString("sjjd2")));
            map.put("glxs", Util.null2String(rs.getString("glxs")));
            return Optional.of(map);
        }
        return Optional.empty();
    }

    /**
     * 构建错误结果
     */
    private Map<String, String> errorResult(Map<String, String> result, String message) {
        result.put("errorMsg", message);
        result.put("status", "false");
        return result;
    }
}
