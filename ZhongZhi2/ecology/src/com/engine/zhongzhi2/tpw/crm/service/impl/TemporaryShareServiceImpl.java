package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.SellchanceWritebackClueCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.TemporaryShareCmd;
import com.engine.zhongzhi2.tpw.crm.service.TemporaryShareService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class TemporaryShareServiceImpl extends Service implements TemporaryShareService {
    @Override
    public Map<String, Object> temporarySharing(Map<String, Object> params, User user) {
        return commandExecutor.execute(new TemporaryShareCmd(params, user));
    }
}
