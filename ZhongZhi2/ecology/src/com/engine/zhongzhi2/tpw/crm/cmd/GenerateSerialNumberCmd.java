package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class GenerateSerialNumberCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();
    // 创建一个对象作为锁
    private static final Object lock = new Object();
    public GenerateSerialNumberCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        synchronized (lock) {
            bb.writeLog(this.getClass().getName() + "---START");
            bb.writeLog("params:" + params);
            Map<String, Object> result = new HashMap<>();
            String errorMsg = "";
            try {
                bb.writeLog("GenerateSerialNumberCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
                if (params != null && !params.isEmpty()) {
                    String sellChanceId = (String) params.get("sellchanceId");
                    if (StringUtils.isNotBlank(sellChanceId)) {
                        String bh = "";
                        Date date = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                        String ny = sdf.format(date);
                        String sqlparam = "SJ" + ny;
                        String Sql = "select top 1 * from CRM_SellChance where sjbh like '" + sqlparam + "%' ORDER BY id DESC ";
                        bb.writeLog("数据库查询sql====>" + Sql);
                        rs.execute(Sql);
                        if (rs.next()) {//数据库中有当前年月的商机
                            String sjbh = rs.getString("sjbh");
                            if (StringUtils.isNotBlank(sjbh)) {
                                int lsh = Integer.parseInt(sjbh.substring(8)) + 1;
                                if (lsh < 10000) {
                                    bh = sqlparam + String.format("%04d", lsh);
                                } else {
                                    bh = sqlparam + lsh;
                                }
                            }
                        } else {//数据库中没有当前年月的商机
                            bh = sqlparam + "0001";
                        }
                        bb.writeLog("生成的商机编号====>" + bh);
                        //更新数据库中的商机数据
                        if(StringUtils.isNotBlank(bh)){
                            String update = "update CRM_SellChance set sjbh = '" + bh + "' where id = " + sellChanceId;
                            bb.writeLog("数据库更新sql====>" + update);
                            rs.execute(update);
                        }else {
                            errorMsg = "商机编号设置为空";
                        }
                    }else {
                        errorMsg = "商机id为空";
                    }
                }else {
                    errorMsg = "请求参数为空";
                }

            } catch (Exception e) {
                errorMsg = SDUtil.getExceptionDetail(e);
                bb.writeLog("商机流水编号生成异常：" + errorMsg);
            }
            result.put("errorMsg", errorMsg);
            result.put("status", errorMsg.isEmpty());
            bb.writeLog("result:" + result);
            bb.writeLog(this.getClass().getName() + "---END");
            return result;
        }
    }
}
