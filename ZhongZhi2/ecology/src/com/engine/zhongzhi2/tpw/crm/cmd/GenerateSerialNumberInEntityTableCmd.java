package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class GenerateSerialNumberInEntityTableCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public GenerateSerialNumberInEntityTableCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("GenerateSerialNumberInEntityTableCmd params:" + params);
        Map<String, Object> result = new HashMap<>();
        try {
            String sellchanceId = Util.null2String(params.get("sellchanceId"));
            String billid = Util.null2String(params.get("billid"));
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String querySql = "SELECT * FROM CRM_SellChance WHERE id = ?";
            recordSet.executeQuery(querySql, sellchanceId);
            if (recordSet.next()) {
                String sjbh = recordSet.getString("sjbh");
                if (StringUtils.isNotBlank(sellchanceId) && StringUtils.isNotBlank(billid)) {
                    recordSet.executeUpdate("UPDATE uf_yddsjstb SET sjbh = ?,sjid = ? WHERE id = ?", sjbh, sellchanceId, billid);
                }else {
                    result.put("flag", "false");
                    result.put("errmsg", "sellchanceId || billid is null");
                }
            } else {
                result.put("flag", "false");
                result.put("errmsg", "未找到对应的商机记录");
            }
        } catch (Exception e) {
            bb.writeLog(e.getMessage());
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}