package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.zhongzhi2.tpw.crm.service.DeleteSellChanceByIdService;
import com.engine.zhongzhi2.tpw.crm.service.TransferSellChanceService;
import com.engine.zhongzhi2.tpw.crm.service.impl.DeleteSellChanceByIdServiceImpl;
import com.engine.zhongzhi2.tpw.crm.service.impl.TransferSellChanceServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class DeleteSellChanceByIdWeb {
    private DeleteSellChanceByIdService getService(User user) {
        return ServiceUtil.getService(DeleteSellChanceByIdServiceImpl.class, user);
    }
    @POST
    @Path("/deleteSellChanceById")
    @Produces(MediaType.TEXT_PLAIN)
    public String deleteSellChanceById(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ParamUtil.request2Map(request);
        if (user != null) {
            result = getService(user).deleteSellChanceById(params, user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
