package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi2.tpw.crm.service.TemporaryShareService;
import com.engine.zhongzhi2.tpw.crm.service.impl.TemporaryShareServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

public class TemporaryShareWeb {
    private TemporaryShareService getService(User user) {
        return ServiceUtil.getService(TemporaryShareServiceImpl.class, user);
    }
    /**
     * 临时赋权（非共享权限）
     * 添加获取删除
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/temporarySharing")
    @Produces(MediaType.TEXT_PLAIN)
    public String temporarySharing(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).temporarySharing(params, user)
        );
    }
}
