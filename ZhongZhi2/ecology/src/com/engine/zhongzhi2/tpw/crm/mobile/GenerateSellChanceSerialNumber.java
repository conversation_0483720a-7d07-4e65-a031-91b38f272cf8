package com.engine.zhongzhi2.tpw.crm.mobile;

import java.util.HashMap;
import java.util.Map;
import com.engine.common.util.ServiceUtil;
import com.engine.zhongzhi2.tpw.crm.service.impl.GenerateSerialNumberServiceImpl;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;


/**
    * @FileName GenerateSellChanceSerialNumber
    * @Description 生成商机编号的自定义逻辑类
    * <AUTHOR>
    * @Version v1.00 推荐人数据过滤
    * @Date 2025/2/6
    */
public class GenerateSellChanceSerialNumber extends AbstractModeExpandJavaCodeNew {

    /**
     * 执行模块扩展逻辑
     *
     * @param param 包含以下数据：
     *              - user: 当前用户
     *              - RequestInfo: 请求信息，包含请求ID和工作流ID
     *              - 其他页面或导入时传递的参数
     * @return 返回包含执行结果的Map，包含状态和错误信息
     */
    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<>();
        try {
            // 获取当前用户和请求信息
            User user = (User) param.get("user");
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");

            // 校验请求信息是否有效
            if (requestInfo == null) {
                result.put("errorMsg", "请求信息为空，无法生成商机编号");
                result.put("status", "false");
                return result;
            }

            // 获取商机ID和模块ID
            int billid = Util.getIntValue(requestInfo.getRequestid());
            int modeid = Util.getIntValue(requestInfo.getWorkflowid());

            // 校验商机ID和模块ID是否有效
            if (billid <= 0 || modeid <= 0) {
                result.put("errorMsg", "商机ID或模块ID无效");
                result.put("status", "false");
                return result;
            }

            // 查询商机信息
            String sql = "SELECT sjbh FROM CRM_SellChance WHERE id = " + billid;
            RecordSet recordSet = new RecordSet();
            recordSet.executeQuery(sql);

            // 检查是否已存在商机编号
            if (recordSet.next()) {
                String serialNumber = Util.null2String(recordSet.getString("sjbh"));
                if (StringUtils.isBlank(serialNumber)) {
                    // 调用服务生成商机编号
                    Map<String, Object> params = new HashMap<>();
                    params.put("sellchanceId", billid);
                    Map<String, Object> serviceResult = ServiceUtil.getService(GenerateSerialNumberServiceImpl.class, user)
                            .generateSerialNumber(params, user);

                    // 设置返回结果
                    result.put("errorMsg", Util.null2String(serviceResult.get("errorMsg")));
                    result.put("status", Util.null2String(serviceResult.get("status")));
                } else {
                    result.put("errorMsg", "该商机已存在商机编号，无法重复生成");
                    result.put("status", "false");
                }
            } else {
                result.put("errorMsg", "未找到对应的商机信息");
                result.put("status", "false");
            }
        } catch (Exception e) {
            // 捕获异常并返回错误信息
            result.put("errorMsg", "生成商机编号时发生异常：" + e.getMessage());
            result.put("status", "false");
        }
        return result;
    }
}