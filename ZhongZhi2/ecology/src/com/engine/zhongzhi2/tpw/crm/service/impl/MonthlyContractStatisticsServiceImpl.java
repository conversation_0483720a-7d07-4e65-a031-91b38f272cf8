package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.GenerateSerialNumberCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.MonthlyContractStatisticsCmd;
import com.engine.zhongzhi2.tpw.crm.service.MonthlyContractStatisticsService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class MonthlyContractStatisticsServiceImpl extends Service implements MonthlyContractStatisticsService {

    @Override
    public Map<String, Object> monthlyContractStatistics(Map<String, Object> params, User user) {
        return commandExecutor.execute(new MonthlyContractStatisticsCmd(params, user));
    }
}
