package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.wbi.util.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class BusinessOpportunitiesCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();
    // 创建一个对象作为锁
    private static final Object lock = new Object();

    public BusinessOpportunitiesCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            bb.writeLog("BusinessOpportunitiesCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            if (params != null && !params.isEmpty()) {
                String sellChanceId = (String) params.get("sellChanceId");
                String cookie = (String) params.get("cookie");
                String origin = (String) params.get("origin");
                if (StringUtils.isNotBlank(sellChanceId)) {
                    //查询所有的该客户的所有商机信息
                    rs.execute("select * from CRM_SellChance where id =" + sellChanceId);
                    ExecutorService executorService = Executors.newFixedThreadPool(5);
                    CountDownLatch latch = new CountDownLatch(rs.getCounts());
                    while (rs.next()) {
                        String customerId = rs.getString("customerid");
                        bb.writeLog("customerid===>" + customerId);
                        String process = process(customerId, sellChanceId, cookie,origin);
                        result.put("sellChanceIds", process);
                    }
                }
            } else {
                errorMsg = "请求参数为空";
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("商机一览异常：" + errorMsg);
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;

    }
    private String process(String customerId,String sellChanceId,String cookie,String origin) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        String url = origin+"/api/crm/sellchance/form?customerid="+customerId+"&selectedKey=0&sellChanceId="+sellChanceId+"&operation=edit&__random__="+System.currentTimeMillis();;
        bb.writeLog("url===>" + url);
        RestResult result = HttpUtil.getDataWithHeader(url, headers);
        bb.writeLog("调用/api/crm/sellchance/form接口返回结果===>" + JSONObject.toJSONString(result));
        if(result.isSuccess()){
            RestResult.ResponseInfo responseInfo = result.getResponseInfo();
            String body = responseInfo.getBody();
            bb.writeLog("body===>" + body);
            JSONObject jsonObject = JSONObject.parseObject(body);
            bb.writeLog("jsonObject===>" + jsonObject.toJSONString());
            String msgcode = Util.null2String(jsonObject.get("msgcode"));
            if (StringUtils.isNotBlank(msgcode) && msgcode.contains("出现异常情况") && msgcode.contains("请查看日志") ) {
                bb.writeLog("存在权限");
                return "";
            }
        }
        return sellChanceId;
    }

}
