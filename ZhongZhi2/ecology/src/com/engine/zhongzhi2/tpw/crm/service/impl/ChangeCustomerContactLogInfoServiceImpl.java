package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.ChangeCustomerContactLogInfoCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.CurrentMonthCountsCmd;
import com.engine.zhongzhi2.tpw.crm.service.ChangeCustomerContactLogInfoService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class ChangeCustomerContactLogInfoServiceImpl extends Service implements ChangeCustomerContactLogInfoService {
    @Override
    public Map<String, Object> changeCustomerContactLog(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ChangeCustomerContactLogInfoCmd(params, user));
    }
}
