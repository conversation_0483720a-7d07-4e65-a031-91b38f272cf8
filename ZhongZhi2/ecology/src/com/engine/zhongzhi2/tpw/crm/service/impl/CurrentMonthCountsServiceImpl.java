package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.CurrentMonthCountsCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.MonthlyContractStatisticsCmd;
import com.engine.zhongzhi2.tpw.crm.service.CurrentMonthCountsService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class CurrentMonthCountsServiceImpl  extends Service implements CurrentMonthCountsService {
    @Override
    public Map<String, Object> currentMonthCounts(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CurrentMonthCountsCmd(params, user));
    }
}
