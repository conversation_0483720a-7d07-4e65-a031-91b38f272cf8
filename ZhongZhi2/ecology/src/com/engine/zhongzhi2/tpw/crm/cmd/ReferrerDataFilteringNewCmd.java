package com.engine.zhongzhi2.tpw.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class ReferrerDataFilteringNewCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    @Override
    public BizLogContext getLogContext() {
        return null;
    }
    public ReferrerDataFilteringNewCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("ReferrerDataFilteringNewCmd params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            String sellchanceId = Util.null2String(params.get("sellchanceId"));
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String querySql = "SELECT tjr1, tjryddsjgly FROM CRM_SellChance WHERE id = ?";
            recordSet.executeQuery(querySql, sellchanceId);
            if (recordSet.next()) {
                String tjr1 = recordSet.getString("tjr1");
                String tjryddsjgly = recordSet.getString("tjryddsjgly");
                if (StringUtils.isNotBlank(tjr1)) {

                } else if (StringUtils.isNotBlank(tjryddsjgly)) {
                    String updateSql = "UPDATE CRM_SellChance SET tjr1 = ? WHERE id = ?";
                    recordSet.executeUpdate(updateSql, tjryddsjgly, sellchanceId);
                } else {
                    // 两个字段都无值，不进行任何操作
                    result.put("flag", "true");
                    result.put("msg", "tjr1 和 tjryddsjgly 均无值，无需更新");
                }
            } else {
                result.put("flag", "false");
                result.put("errmsg", "未找到对应的商机记录");
            }
        }catch (Exception e){
            errorMsg = e.getMessage();
        }finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}
