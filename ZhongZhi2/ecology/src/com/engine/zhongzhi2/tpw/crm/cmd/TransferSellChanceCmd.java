package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

public class TransferSellChanceCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final User user;
    private final Map<String, Object> params;

    public TransferSellChanceCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        this.bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params: " + JSONObject.toJSONString(params));

        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";

        try {
            String sellChanceId = Util.null2String(params.get("sellChanceId"));
            if (StringUtils.isBlank(sellChanceId)) {
                errorMsg = "sellChanceId is null";
            } else {
                errorMsg = transferSellChance(sellChanceId, result);
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("商机迁移数据异常：" + errorMsg);
        }

        result.put("errorMsg", errorMsg);
        result.put("status", StringUtils.isEmpty(errorMsg));
        bb.writeLog("result: " + JSONObject.toJSONString(result));
        bb.writeLog(this.getClass().getName() + "---END");

        return result;
    }

    private String transferSellChance(String sellChanceId, Map<String, Object> result) {
        String errorMsg = "";

        try {

            List<String> fields = getTableFields();
            List<Object> values = getFieldValues(sellChanceId, fields);

            bb.writeLog("fields size: " + fields.size());
            bb.writeLog("values size: " + values.size());
            bb.writeLog("fields: " + fields);
            bb.writeLog("values: " + values);

            ModuleInsertBean mb = buildModuleInsertBean(fields, values);
            ModuleResult moduleResult = ModuleDataUtil.insertOne(mb);

            if (moduleResult.isSuccess()) {
                result.put("billid", String.valueOf(moduleResult.getBillid()));
            } else {
                errorMsg = moduleResult.getErroMsg();
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("数据迁移异常：" + errorMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        return errorMsg;
    }

    /**
     * 获取表字段列表（排除 id、sjid 和指定字段）
     */
    private List<String> getTableFields() throws Exception {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        List<String> fields = new ArrayList<>();
        recordSet.executeQuery("select a.fieldname,\n" +
                                "\tb.labelname\n" +
                                "\tFROM\n" +
                                "\t\tworkflow_billfield a\n" +
                                "\t\tLEFT JOIN htmllabelinfo b ON ( a.fieldlabel = b.indexid AND b.languageid = 7 )\n" +
                                "\t\tLEFT JOIN workflow_bill c ON ( a.billid = c.id ) \n" +
                                "\tWHERE\n" +
                                "\t\tc.TABLENAME = 'uf_yddsjstb' \n" +
                                "\n" +
                                "ORDER BY\n" +
                                "\ta.dsporder  ");

        // 需要排除的字段列表
        List<String> excludedFields = Arrays.asList("id", "sjid");
        while (recordSet.next()) {
            String fieldname = recordSet.getString("fieldname");
            // 如果字段不在排除列表中，则添加到 fields 中
            if (!excludedFields.contains(fieldname.toLowerCase())) {
                fields.add(fieldname);
            }
        }
        fields.add("sjid"); // 添加 sjid 字段
        return fields;
    }

    /**
     * 获取字段对应的值
     */
    private List<Object> getFieldValues(String sellChanceId, List<String> fields) throws Exception {
        RecordSet recordSet =  DBUtil.getThreadLocalRecordSet();
        List<Object> values = new ArrayList<>();
        recordSet.executeQuery("SELECT * FROM CRM_SellChance WHERE id = ?", sellChanceId);

        if (recordSet.next()) {
            for (String field : fields) {
                if (!"sjid".equalsIgnoreCase(field)) {
                    values.add(recordSet.getString(field));
                }
            }
            values.add(sellChanceId); // 添加 sjid 的值
        } else {
            throw new Exception("未找到对应的销售机会数据");
        }

        return values;
    }

    /**
     * 构建 ModuleInsertBean 对象
     */
    private ModuleInsertBean buildModuleInsertBean(List<String> fields, List<Object> values) {
        ModuleInsertBean mb = new ModuleInsertBean();
        mb.setFields(fields);
        mb.setModuleId(ModuleDataUtil.getModuleIdByName("uf_yddsjstb"));
        mb.setCreatorId(user.getUID());
        mb.setTableName("uf_yddsjstb");
        mb.setValue(values);
        return mb;
    }
}
