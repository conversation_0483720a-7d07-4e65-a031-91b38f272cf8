package com.engine.zhongzhi2.tpw.crm.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class TemporaryShareCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 插入非默认共享的标记值
     */
    private static final String SD_SHARE_CODE = "SD_GUANZHU";

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public TemporaryShareCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;

    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            errorMsg = addShare();
        } catch (Exception e) {
            log.info(this.getClass().getName() + "---EXCEPTION:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", errorMsg);
        result.put("status", errorMsg.isEmpty());
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 添加非默认共享
     */
    private String addShare() {
        String errorMsg = "";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        int billid = Util.getIntValue(Util.null2String(params.get("billid")), 0); //数据id
//        int modeid = Util.getIntValue(Util.null2String(params.get("modeid")), 0); //建模id
        int modeid = ModuleDataUtil.getModuleIdByName("uf_khlxr_ext");//通过表名获取建模id
        log.info("modeid:" + modeid);
        String tableName = "modeDataShare_" + modeid + "_set";
        log.info("tableName:" + tableName);
        rs.execute("select * from " + tableName + " where relatedid = " + user.getUID() + " and sourceid = " + billid);
        log.info("select sql :" + "select * from " + tableName + " where relatedid = " + user.getUID() + " and sourceid = " + billid);
        if (rs.getCounts() == 0) {
            String sql = "insert into " + tableName + "(sourceid,righttype,sharetype,relatedid,rolelevel,showlevel,isdefault,requestid,hrmCompanyVirtualType,jobleveltext)";
            sql += " values ('" + billid + "','1','1','" + user.getUID() + "','0','0',0,0,0,'" + SD_SHARE_CODE + "' )";
            log.info("sql:" + sql);
            if (rs.executeUpdate(sql)) {
                resetNewRight(billid, modeid);
            } else {
                errorMsg = "添加非默认共享失败";
                log.error("addShare sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
            }
        }
        return errorMsg;
    }

    private void resetNewRight(int billid, int modeid) {
        //重新计算权限
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.setNewRight(false);
        ModeRightInfo.editModeDataShare(0, modeid, billid);
    }
}
