package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.BusinessBrowseCmd;
import com.engine.zhongzhi2.tpw.crm.cmd.GenerateSerialNumberCmd;
import com.engine.zhongzhi2.tpw.crm.service.BusinessBrowseService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class BusinessBrowseServiceImpl extends Service implements BusinessBrowseService {
    @Override
    public Map<String, Object> businessBrowse(Map<String, Object> params, User user) {
        return commandExecutor.execute(new BusinessBrowseCmd(params, user));
    }
}
