package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.SellchanceWritebackClueCmd;
import com.engine.zhongzhi2.tpw.crm.service.SellchanceWritebackClueService;

import weaver.hrm.User;

import java.util.Map;

public class SellchanceWritebackClueServiceImpl extends Service implements SellchanceWritebackClueService {
    @Override
    public Map<String, Object> sellchanceWritebackClue(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SellchanceWritebackClueCmd(params, user));
    }
}

