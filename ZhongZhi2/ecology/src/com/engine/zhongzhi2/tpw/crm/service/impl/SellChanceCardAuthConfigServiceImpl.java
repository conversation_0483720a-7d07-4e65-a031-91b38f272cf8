package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.SellChanceCardAuthConfigCmd;
import com.engine.zhongzhi2.tpw.crm.service.SellChanceCardAuthConfigService;
import weaver.hrm.User;

import java.util.Map;

public class SellChanceCardAuthConfigServiceImpl extends Service implements SellChanceCardAuthConfigService {
    @Override
    public Map<String, Object> sellChanceCardAuthConfig(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SellChanceCardAuthConfigCmd(params, user));
    }
}
