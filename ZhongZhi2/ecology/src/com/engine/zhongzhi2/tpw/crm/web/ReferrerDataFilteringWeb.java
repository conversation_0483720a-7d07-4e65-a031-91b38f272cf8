package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongzhi2.tpw.crm.service.QueryAllSellChancesPermissionsListService;
import com.engine.zhongzhi2.tpw.crm.service.ReferrerDataFilteringService;
import com.engine.zhongzhi2.tpw.crm.service.impl.QueryAllSellChancesPermissionsListServiceImpl;
import com.engine.zhongzhi2.tpw.crm.service.impl.ReferrerDataFilteringServiceImpl;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class ReferrerDataFilteringWeb {

    private ReferrerDataFilteringService getService(User user) {
        return ServiceUtil.getService(ReferrerDataFilteringServiceImpl.class, user);
    }

    //商机标准表新建成功触发回调更改推荐人数据
    @POST
    @Path("/processReferrerNewData")
    @Produces(MediaType.TEXT_PLAIN)
    public String processReferrerData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        Map<String, Object> result = new HashMap<>();
        String sellchanceId = Util.null2String(params.get("sellchanceId"));
        if (StringUtils.isNotBlank(sellchanceId)) {
            result = getService(user).processReferrerNewData(ParamUtil.request2Map(request), user);
        } else {
            result.put("flag", "false");
            result.put("errmsg", "商机id为空!");
        }
        return JSONObject.toJSONString(result);
    }

    //商机实体表新建成功触发回调更改推荐人数据
    @POST
    @Path("/processReferrerNewEntityData")
    @Produces(MediaType.TEXT_PLAIN)
    public String processReferrerNewEntityData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        Map<String, Object> result = new HashMap<>();
        String billid = Util.null2String(params.get("billid"));
        if (StringUtils.isNotBlank(billid)) {
            result = getService(user).processReferrerNewEntityData(ParamUtil.request2Map(request), user);
        } else {
            result.put("flag", "false");
            result.put("errmsg", "商机id为空!");
        }
        return JSONObject.toJSONString(result);
    }

    //商机标准表编辑成功触发回调更改推荐人数据
    @POST
    @Path("/processReferrerEditData")
    @Produces(MediaType.TEXT_PLAIN)
    public String processReferrerEditData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        Map<String, Object> result = new HashMap<>();
        String sellchanceId = Util.null2String(params.get("sellchanceId"));
        if (StringUtils.isNotBlank(sellchanceId)) {
            result = getService(user).processReferrerEditData(ParamUtil.request2Map(request), user);
        } else {
            result.put("flag", "false");
            result.put("errmsg", "商机id为空!");
        }
        return JSONObject.toJSONString(result);
    }

    //商机实体表编辑成功触发回调更改推荐人数据
    @POST
    @Path("/processReferrerEditEntityData")
    @Produces(MediaType.TEXT_PLAIN)
    public String processReferrerEditEntityData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        Map<String, Object> result = new HashMap<>();
        String billid = Util.null2String(params.get("billid"));
        if (StringUtils.isNotBlank(billid)) {
            result = getService(user).processReferrerEditEntityData(ParamUtil.request2Map(request), user);
        } else {
            result.put("flag", "false");
            result.put("errmsg", "商机id为空!");
        }
        return JSONObject.toJSONString(result);
    }


}
