package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.hrm.User;

import java.util.*;

/**
    * @FileName CurrentMonthCountsCmd
    * @Description 当月数据统计
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/8/20
    */
public class CurrentMonthCountsCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    RecordSet rs = new RecordSet();

    public CurrentMonthCountsCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        //矩阵
//        String departMatrix = String.valueOf(params.get("departMatrix"));
        //分管领导字段名
        String fgldField = String.valueOf(params.get("fgldField"));
        //部门负责人字段名
        String bmfzrField = String.valueOf(params.get("bmfzrField"));
        //年月
        String moth = TimeUtil.getCurrentDateString().substring(0, 7);
        Map<String, Object> result = new HashMap<>();
        JSONObject data = new JSONObject();
        String errorMsg = "";
        try {
            log.info("MonthlyContractStatisticsCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            if (params != null && !params.isEmpty()) {
                //1.查询当前操作人员与其下级（下级包含所有下级）
                Set<String> hrmids = getSubordinateResourceIds(String.valueOf(user.getUID()));
                log.info("当前操作人员与其下级:" + JSONObject.toJSONString(hrmids));
                //2.系统登录人为部门矩阵中的分管领导或部门负责人时所对应的部门所有人员
                rs.executeQuery("select * from Matrixtable_2");
                Set<String> departs = new HashSet<>();
                while (rs.next()) {
                    //分管领导
                    String fgld = rs.getString(fgldField);
                    //部门负责人
                    String bmfzr = rs.getString(bmfzrField);
                    if (StringUtils.isNotBlank(fgld)) {
                        String[] fglds = fgld.split(",");
                        if(Arrays.asList(fglds).contains(user.getUID())){
                            departs.add(rs.getString("id"));
                        }
                    }
                    if (StringUtils.isNotBlank(bmfzr)) {
                        String[] bmfzrs = bmfzr.split(",");
                        if(Arrays.asList(bmfzrs).contains(user.getUID())){
                            departs.add(rs.getString("id"));
                        }
                    }
                }
                if (!departs.isEmpty()) {
                    String departIds = String.join(",", departs);
                    rs.executeQuery("select id  from HrmResource  where departmentid in (" + departIds + ")");
                    while (rs.next()) {
                        hrmids.add(rs.getString("id"));
                    }
                }
                //3.
                if (!hrmids.isEmpty()) {
                    //本月签订合同的数量
//                    String sql1 = "select count(*) as tots from CRM_SellChance " +
//                            "where 1=1 " +
//                            "and id in (select xgsj from uf_ywhtxx where 1=1 and htsxrq like '%" + moth.substring(0, 7) + "%'" +
//                            "and creater in (" + String.join(",", hrmids) + ")";

                    String sql1 = "select count(*) as tots from uf_ywhtxx " +
                            " where 1=1 " +
                            " and xgsj in (select id from CRM_SellChance where 1=1 and creater in (" +String.join(",", hrmids) + "))"+
                            " and htsxrq = '"+moth+"'";
                    log.info("本月签订合同的数量"+sql1);
                    rs.executeQuery(sql1);
                    if (rs.next()) {
                        data.put("clue", rs.getString("tots"));
                    }
                    //本月签订商机的数量
                    rs.executeQuery("select count(*) as tots from CRM_SellChance where createdate like '" + moth + "%' and creater in (" + String.join(",", hrmids) + ")");
                    if (rs.next()) {
                        data.put("saleChanceNum", rs.getString("tots"));
                    }
                    //本月创建客户的数量
                    rs.executeQuery("select count(*) as tots from CRM_CustomerInfo where createdate like '" + moth + "%' and manager in (" + String.join(",", hrmids) + ")");
                    if (rs.next()) {
                        data.put("customerNum", rs.getString("tots"));
                    }
                    //本年创建客户的数量
                    rs.executeQuery("select count(*) as tots from CRM_CustomerInfo where createdate like '" + moth.substring(0, 4) + "%' and manager in (" + String.join(",", hrmids) + ")");
                    if (rs.next()) {
                        data.put("customerNumYear", rs.getString("tots"));
                    }
                    data.put("hrmids", JSONObject.toJSONString(hrmids));
                }
            } else {
                errorMsg = "请求参数为空";
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            log.info("当月统计异常：" + errorMsg);
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        result.put("data", data.toJSONString());
        log.info("result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 获取指定人员ID下所有下级人员的ID列表
     *
     * @param managerid 指定的上级人员ID
     * @return 下级人员ID列表
     */
/*    public Set<String> getSubordinateResourceIds(String managerid) {
        Set<String> subordinateIds = new HashSet<>();

        // SQL查询语句，获取所有下级人员ID
        String sql = "SELECT id FROM hrmresource WHERE managerid = '" + managerid+"'";
        rs.executeQuery(sql);
        while (rs.next()) {
            // 递归查询下级人员的下级人员id
            subordinateIds.addAll(getSubordinateResourceIds(rs.getString("id")));
        }
        // 添加当前人员ID
        subordinateIds.add(managerid);
        return subordinateIds;
    }*/


    /**
     * 获取指定人员ID下所有下级人员的ID列表
     *
     * @param managerid 指定的上级人员ID
     * @return 下级人员ID列表
     */
    public Set<String> getSubordinateResourceIds(String managerid) {
        Set<String> subordinateIds = new HashSet<>();
        // SQL查询语句，获取所有下级人员ID
        String sql = "SELECT id FROM hrmresource WHERE managerid = '" + managerid+"'";
        HashSet<String> currLowerHrmIds = new HashSet<>();
        rs.executeQuery(sql);
        while (rs.next()) {
            // 递归查询下级人员的下级人员id
            String hrmId = rs.getString("id");
            currLowerHrmIds.add(hrmId);
        }
        if(!currLowerHrmIds.isEmpty()){
            //遍历currLowerHrmIds
            //递归获取下级人员的下级人员ID
            for (String subordinateId : currLowerHrmIds) {
                subordinateIds.addAll(getSubordinateResourceIds(subordinateId));
            }
        }
        // 添加当前人员ID
        subordinateIds.add(managerid);
        return subordinateIds;
    }

}

