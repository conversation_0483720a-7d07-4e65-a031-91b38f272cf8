package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.gyl.crm.bean.QiXinArchive;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

public class CustomerTaxIdVerifyCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final Map<String, Object> params;
    private final User user;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public CustomerTaxIdVerifyCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog("CustomerTaxIdVerifyCmd---START");
        bb.writeLog("CustomerTaxIdVerifyCmd params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            //客户名
            String customerName = Util.null2String(params.get("customerName"));
            if (StringUtils.isNotBlank(customerName)) {
                //获取启信宝数据，优先从建模获取，没有的话会自动从ESB读取并记录相关日志
                JSONObject dataJson = QiXinArchive.getQiXinData(customerName, "", user.getUID(), "初始校验税号");
                if (dataJson != null && !dataJson.isEmpty()) {
                    //判断税号是否再客户表中存在重复。如果存在，返回失败信息；反之不存在，返回成功信息。
                    //税号
                    String creditNo = Util.null2String(dataJson.getString("creditNo"));
                    if (StringUtils.isNotBlank(creditNo)) {
                        //根据税号在客户表中查询是否有相同的税号
                        recordSet.executeQuery("select id from CRM_CustomerInfo where tyshxydmsjh ='" + creditNo + "' and deleted = 0");
                        boolean creditNoflag = false;
                        if (recordSet.next()) {
                            if (StringUtils.isNotBlank(recordSet.getString("id"))) {
                                creditNoflag = true;
                            }
                        }
                        //如果存在 返回失败信息
                        if (creditNoflag) {
                            errorMsg = "该客户的税号在客户表中已存在，不允许新建相同税号的客户！";
                        }
                    } else {
                        errorMsg = "该客户的税号为空，不允许新建!";
                    }
                } else {
                    errorMsg = "未查询到该客户的启信宝数据，不允许新建！";
                }


                //根据客户名查找是否在建模中存在该客户 如果存在 返回失败 不进行后续操作
//                recordSet.executeQuery("select customer_name from " + QiXinArchive.TABLE_NAME + " where customer_name ='" + customerName + "'");
//                boolean flag = false;
//                if (recordSet.next()) {
//                    if (StringUtils.isNotBlank(recordSet.getString("customer_name"))) {
//                        flag = true;
//                        String readData = recordSet.getString("read_data");
//                        if (StringUtils.isNotBlank(readData)) {
//                            JSONObject dataObj = JSONObject.parseObject(readData);
//                            //税号
//                            String creditNo = Util.null2String(dataObj.getString("creditNo"));
//                            if (StringUtils.isNotBlank(creditNo)) {
//                                //根据税号在客户表中查询是否有相同的税号
//                                recordSet.executeQuery("select id from CRM_CustomerInfo where tyshxydmsjh ='" + creditNo + "' and deleted = 0");
//                                boolean creditNoflag = false;
//                                if (recordSet.next()) {
//                                    if (StringUtils.isNotBlank(recordSet.getString("id"))) {
//                                        creditNoflag = true;
//                                    }
//                                }
//                                //如果存在 返回失败信息
//                                if (creditNoflag) {
//                                    errorMsg = "该客户的税号在客户表中已存在，不允许新建相同税号的客户！";
//                                }
//                            } else {
//                                errorMsg = "该客户的税号为空，不允许新建!";
//                            }
//                        }
//
//                    }
//                }

//                if (!flag) {
//                    //调用ESB事件，判断税号是否再客户表中存在重复。如果存在，返回失败信息，插入数据；反之不存在，返回成功信息，插入数据。
//                    JSONObject dataJson = QiXinArchive.getQiXinData(customerName, "", user.getUID(), "初始校验税号");
//                    if (dataJson != null && !dataJson.isEmpty()) {
//                        //税号
//                        String creditNo = Util.null2String(dataJson.getString("creditNo"));
//                        if (StringUtils.isNotBlank(creditNo)) {
//                            //根据税号在客户表中查询是否有相同的税号
//                            recordSet.executeQuery("select id from CRM_CustomerInfo where tyshxydmsjh ='" + creditNo + "' and deleted = 0");
//                            boolean creditNoflag = false;
//                            if (recordSet.next()) {
//                                if (StringUtils.isNotBlank(recordSet.getString("id"))) {
//                                    creditNoflag = true;
//                                }
//                            }
//                            //如果存在 返回失败信息
//                            if (creditNoflag) {
//                                errorMsg = "该客户的税号在客户表中已存在，不允许新建相同税号的客户！";
//                            }
//                        } else {
//                            errorMsg = "该客户的税号为空，不允许新建!";
//                        }
//                    } else {
//                        errorMsg = "启信宝查询ESB失败，不允许新建相同税号的客户！";
//                    }
//                }
            } else {
                errorMsg = "客户名不允许为空！";
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog("CustomerTaxIdVerifyCmd---END");
        return result;
    }
}
