package com.engine.zhongzhi2.tpw.crm.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongzhi2.tpw.crm.cmd.TransferSellChanceCmd;
import com.engine.zhongzhi2.tpw.crm.service.TransferSellChanceService;
import weaver.hrm.User;

import java.util.Map;

public class TransferSellChanceServiceImpl extends Service implements TransferSellChanceService {
    @Override
    public Map<String, Object> transferSellChance(Map<String, Object> params, User user) {
        return commandExecutor.execute(new TransferSellChanceCmd(params, user));
    }
}
