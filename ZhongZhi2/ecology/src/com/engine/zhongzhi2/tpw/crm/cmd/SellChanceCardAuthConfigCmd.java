package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import com.weaver.general.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SellChanceCardAuthConfigCmd extends AbstractCommonCommand<Map<String, Object>> {

    private final BaseBean bb;
    private String type;
    private final RecordSet rs = new RecordSet();
    // 创建一个对象作为锁
    private static final Object lock = new Object();

    public SellChanceCardAuthConfigCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        String fromType = "";
        try {
            bb.writeLog("GenerateSerialNumberCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            if (params != null && !params.isEmpty()) {
                //首先判断进入该方法的是那种方式  1：商机卡片页面保存进入  2：矩阵卡片保存进入
                fromType = (String) params.get("fromType");
                type = Util.null2String((String) params.get("type"));
                if (StringUtils.isBlank(fromType)) {
                    errorMsg = "来源类型FromType未填写!";
                }
                if ("sellChanceCard".equals(fromType)) {
                    bb.writeLog("接口是由商机卡片触发");
                    errorMsg = sellChanceCard();
                } else {
                    bb.writeLog("接口是由矩阵触发");
                    errorMsg = matrix();
                }
            } else {
                errorMsg = "请求参数为空";
            }
        } catch (Exception e) {
            bb.writeLog("商机的权限赋值操作异常：" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    //商机卡片逻辑
    private String sellChanceCard() {
        bb.writeLog("商机卡片逻辑保存=====>START");
        String errorMsg = "";
        //商机id
        String sellChanceId = (String) params.get("sellChanceId");
        bb.writeLog("商机卡片逻辑保存=====>sellChanceId：" + sellChanceId);
        if (StringUtils.isNotBlank(sellChanceId)) {
            Set<String> looks = new HashSet<>();
            Set<String> edits = new HashSet<>();
            Set<String> toRemove = new HashSet<>();
            //人员字段名 数组
            JSONArray hrmResourceArray = reqParamsCover("hrmResource");
            bb.writeLog("cinfig配置的人力资源字段信息" + hrmResourceArray.toJSONString());
            ArrayList<String> hrmLooks = new ArrayList<>();
            ArrayList<String> hrmEdits = new ArrayList<>();
            Set<String> uniquehrmLook = new HashSet<>();
            Set<String> uniquehrmEdit = new HashSet<>();
            // 创建一个临时集合，用于存储需要移除的元素
            Set<String> uniquehrmLookToRemove = new HashSet<>();
            if (!hrmResourceArray.isEmpty()) {
                for (int i = 0; i < hrmResourceArray.size(); i++) {
                    JSONObject hrm = (JSONObject) hrmResourceArray.get(i);
                    String fieldName = hrm.getString("fieldName");
                    String permission = hrm.getString("permission");
                    if (StringUtils.isNotBlank(permission)) {
                        if ("0".equals(permission)) {//查看权限
                            hrmLooks.add(fieldName);
                        } else {
                            hrmEdits.add(fieldName);
                        }
                    }
                }
            }
            bb.writeLog("根据cinfig配置的人力资源字段信息找到的具有查看权限的人力资源字段名：" + JSONObject.toJSONString(hrmLooks));
            bb.writeLog("根据cinfig配置的人力资源字段信息找到的具有编辑权限的人力资源字段名：" + JSONObject.toJSONString(hrmEdits));
            String querySellChanceById = "select * from CRM_SellChance where id = " + sellChanceId;
            rs.execute(querySellChanceById);
            if (rs.next()) {
                if (!hrmLooks.isEmpty()) {
                    for (int i = 0; i < hrmLooks.size(); i++) {
                        String hrmLook = rs.getString(hrmLooks.get(i));
                        if (StringUtils.isNotBlank(hrmLook)) {
                            if (hrmLook.contains(",")) {
                                List<String> list = Arrays.asList(hrmLook.split(","));
                                uniquehrmLook.addAll(list);
                            } else {
                                uniquehrmLook.add(hrmLook);
                            }
                        }
                    }
                }
                bb.writeLog("商机中(人力资源字段)具有查看权限的人员id" + JSONObject.toJSONString(uniquehrmLook));
                if (!hrmEdits.isEmpty()) {
                    for (int i = 0; i < hrmEdits.size(); i++) {
                        String hrmEdit = rs.getString(hrmEdits.get(i));
                        if (StringUtils.isNotBlank(hrmEdit)) {
                            if (hrmEdit.contains(",")) {
                                List<String> list = Arrays.asList(hrmEdit.split(","));
                                uniquehrmEdit.addAll(list);
                            } else {
                                uniquehrmEdit.add(hrmEdit);
                            }
                        }
                    }
                }
                bb.writeLog("商机中(人力资源字段)具有编辑权限的人员id" + JSONObject.toJSONString(uniquehrmEdit));
            }
//            if (!uniquehrmLook.isEmpty() && !uniquehrmEdit.isEmpty()) {
//                for (String hrmEdit : uniquehrmEdit) {
//                    for (String hrmLook : uniquehrmLook) {
//                        if (hrmEdit.equals(hrmLook)) {
//                            uniquehrmLookToRemove.add(hrmLook);
//                            bb.writeLog("商机中(人力资源字段)要去除具有查看权限的人员id" + JSONObject.toJSONString(uniquehrmLookToRemove));
//                        }
//                    }
//                }
//            }
//            if (!uniquehrmLook.isEmpty() && !uniquehrmLookToRemove.isEmpty()) {
//                uniquehrmLook.removeAll(uniquehrmLookToRemove);
//            }
//            bb.writeLog("商机中(人力资源字段)经过去除(重复权限后的)具有查看权限的人员id" + JSONObject.toJSONString(uniquehrmLook));


            //部门字段名 数组
            JSONArray matrixs = reqParamsCover("matrix");
            if (matrixs != null && !matrixs.isEmpty()) {
                for (int i = 0; i < matrixs.size(); i++) {
                    ArrayList<String> departLookNames = new ArrayList<>();
                    ArrayList<String> departEditNames = new ArrayList<>();
                    Set<String> uniqueDepartLookIds = new HashSet<>();
                    Set<String> uniqueDepartEditIds = new HashSet<>();
                    Set<String> uniqueDepartHrmLookIds = new HashSet<>();
                    Set<String> uniqueDepartHrmEditIds = new HashSet<>();
                    Set<String> uniquedepartLookToRemove = new HashSet<>();
                    JSONObject matrix = (JSONObject) matrixs.get(i);
                    String matrixName = (String) matrix.get("matrixName");
                    String fieldJuzhens = (String) matrix.get("field_juzhen");
                    JSONArray departmentArray = (JSONArray) matrix.get("depts");
                    if (departmentArray != null && !departmentArray.isEmpty()) {

                        for (int j = 0; j < departmentArray.size(); j++) {
                            JSONObject depart = (JSONObject) departmentArray.get(j);
                            String fieldName = depart.getString("fieldName");
                            String permission = depart.getString("permission");
                            if (StringUtils.isNotBlank(permission)) {
                                if ("0".equals(permission)) { //查看权限
                                    departLookNames.add(fieldName);
                                } else {
                                    departEditNames.add(fieldName);
                                }
                            }
                        }

                        rs.execute("select * from CRM_SellChance where id = " + sellChanceId);
                        if (rs.next()) {
                            if (!departLookNames.isEmpty()) {
                                for (int j = 0; j < departLookNames.size(); j++) {
                                    String departLook = rs.getString(departLookNames.get(j));
                                    if (StringUtils.isNotBlank(departLook)) {
                                        if (departLook.contains(",")) {
                                            List<String> list = Arrays.asList(departLook.split(","));
                                            uniqueDepartLookIds.addAll(list);
                                        } else {
                                            uniqueDepartLookIds.add(departLook);
                                        }
                                    }
                                }
                            }
                            bb.writeLog("商机中(部门字段)具有查看权限的部门id" + JSONObject.toJSONString(uniqueDepartLookIds));
                            if (!departEditNames.isEmpty()) {
                                for (int j = 0; j < departEditNames.size(); j++) {
                                    String departEdit = rs.getString(departEditNames.get(j));
                                    if (StringUtils.isNotBlank(departEdit)) {
                                        if (departEdit.contains(",")) {
                                            List<String> list = Arrays.asList(departEdit.split(","));
                                            uniqueDepartEditIds.addAll(list);
                                        } else {
                                            uniqueDepartEditIds.add(departEdit);
                                        }
                                    }
                                }
                            }
                            bb.writeLog("商机中(部门字段)具有编辑权限的部门id" + JSONObject.toJSONString(uniqueDepartEditIds));
                        }
                        if (!uniqueDepartLookIds.isEmpty()) {
                            for (String id : uniqueDepartLookIds) {
                                if ("matrixtable_2".equals(matrixName) || "matrixtable_1".equals(matrixName)) {
                                    rs.execute("select * from " + matrixName + " where id = " + id);
                                } else {
                                    rs.execute("select * from " + matrixName + " where bm = " + id);
                                }
                                if (rs.next()) {
                                    if (StringUtils.isNotBlank(fieldJuzhens)) {
                                        if (fieldJuzhens.contains(",")) {
                                            for (String fieldJuzhen : fieldJuzhens.split(",")) {
                                                String bmfzrs = rs.getString(fieldJuzhen);
                                                if (StringUtils.isNotBlank(bmfzrs)) {
                                                    if (bmfzrs.contains(",")) {
                                                        List<String> list = Arrays.asList(bmfzrs.split(","));
                                                        uniqueDepartHrmLookIds.addAll(list);
                                                    } else {
                                                        uniqueDepartHrmLookIds.add(bmfzrs);
                                                    }
                                                }
                                            }
                                        } else {
                                            String bmfzrs = rs.getString(fieldJuzhens);
                                            if (StringUtils.isNotBlank(bmfzrs)) {
                                                if (bmfzrs.contains(",")) {
                                                    List<String> list = Arrays.asList(bmfzrs.split(","));
                                                    uniqueDepartHrmLookIds.addAll(list);
                                                } else {
                                                    uniqueDepartHrmLookIds.add(bmfzrs);
                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        bb.writeLog("商机中(部门字段)通过举证查找后具有查看权限的人员id" + JSONObject.toJSONString(uniqueDepartHrmLookIds));
                        if (!uniqueDepartEditIds.isEmpty()) {
                            for (String id : uniqueDepartEditIds) {
                                if ("matrixtable_2".equals(matrixName) || "matrixtable_1".equals(matrixName)) {
                                    rs.execute("select * from " + matrixName + " where id = " + id);
                                } else {
                                    rs.execute("select * from " + matrixName + " where bm = " + id);
                                }
                                if (rs.next()) {
                                    if (StringUtils.isNotBlank(fieldJuzhens)) {
                                        if (fieldJuzhens.contains(",")) {
                                            for (String fieldJuzhen : fieldJuzhens.split(",")) {
                                                String bmfzrs = rs.getString(fieldJuzhen);
                                                if (StringUtils.isNotBlank(bmfzrs)) {
                                                    if (bmfzrs.contains(",")) {
                                                        List<String> list = Arrays.asList(bmfzrs.split(","));
                                                        uniqueDepartHrmEditIds.addAll(list);
                                                    } else {
                                                        uniqueDepartHrmEditIds.add(bmfzrs);
                                                    }
                                                }
                                            }
                                        } else {
                                            String bmfzrs = rs.getString(fieldJuzhens);
                                            if (StringUtils.isNotBlank(bmfzrs)) {
                                                if (bmfzrs.contains(",")) {
                                                    List<String> list = Arrays.asList(bmfzrs.split(","));
                                                    uniqueDepartHrmEditIds.addAll(list);
                                                } else {
                                                    uniqueDepartHrmEditIds.add(bmfzrs);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        bb.writeLog("商机中(部门字段)通过举证查找后具有编辑权限的人员id" + JSONObject.toJSONString(uniqueDepartHrmEditIds));
                    }
                    looks.addAll(uniqueDepartHrmLookIds);
                    edits.addAll(uniqueDepartHrmEditIds);
                }
            }
            looks.addAll(uniquehrmLook);
            edits.addAll(uniquehrmEdit);
            bb.writeLog("商机中具有查看权限的人员id" + JSONObject.toJSONString(looks));
            bb.writeLog("商机中具有编辑权限的人员id" + JSONObject.toJSONString(edits));
            if (!looks.isEmpty() && !edits.isEmpty()) {
                for (String edit : edits) {
                    for (String look : looks) {
                        if (edit.equals(look)) {
                            toRemove.add(look);
                            bb.writeLog("商机中经过对比将要删除具有查看权限的人员id" + JSONObject.toJSONString(toRemove));
                        }
                    }
                }
            }
            if (!looks.isEmpty() && !toRemove.isEmpty()) {
                looks.removeAll(toRemove);
            }
            bb.writeLog("商机中经过对比删除后的具有查看权限的人员id" + JSONObject.toJSONString(looks));
            updataAuth(looks, edits);
        } else {
            errorMsg = "商机id为空";
        }
        bb.writeLog("商机卡片逻辑保存失败信息：" + errorMsg);
        bb.writeLog("商机卡片逻辑保存=====>END");
        return errorMsg;
    }

    //矩阵逻辑
    private String matrix() {
        String errorMsg = "";
        JSONArray hrmResourceArray = reqParamsCover("hrmResource");
        bb.writeLog("cinfig配置的人力资源字段信息" + hrmResourceArray.toJSONString());
        ArrayList<String> hrmLooks = new ArrayList<>();
        ArrayList<String> hrmEdits = new ArrayList<>();
        if (!hrmResourceArray.isEmpty()) {
            for (int i = 0; i < hrmResourceArray.size(); i++) {
                JSONObject hrm = (JSONObject) hrmResourceArray.get(i);
                String fieldName = hrm.getString("fieldName");
                String permission = hrm.getString("permission");
                if (StringUtils.isNotBlank(permission)) {
                    if ("0".equals(permission)) {//查看权限
                        hrmLooks.add(fieldName);
                    } else {
                        hrmEdits.add(fieldName);
                    }
                }
            }
        }
        rs.execute("select * from CRM_SellChance");
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        CountDownLatch latch = new CountDownLatch(rs.getCounts());
        while (rs.next()) {
            String sellchanceId = rs.getString("id");
            Set<String> uniquehrmLook = new HashSet<>();
            Set<String> uniquehrmEdit = new HashSet<>();
            if (!hrmLooks.isEmpty()) {
                for (int i = 0; i < hrmLooks.size(); i++) {
                    String hrmLook = rs.getString(hrmLooks.get(i));
                    if (StringUtils.isNotBlank(hrmLook)) {
                        if (hrmLook.contains(",")) {
                            List<String> list = Arrays.asList(hrmLook.split(","));
                            uniquehrmLook.addAll(list);
                        } else {
                            uniquehrmLook.add(hrmLook);
                        }
                    }
                }
            }
            if (!hrmEdits.isEmpty()) {
                for (int i = 0; i < hrmEdits.size(); i++) {
                    String hrmEdit = rs.getString(hrmEdits.get(i));
                    if (StringUtils.isNotBlank(hrmEdit)) {
                        if (hrmEdit.contains(",")) {
                            List<String> list = Arrays.asList(hrmEdit.split(","));
                            uniquehrmEdit.addAll(list);
                        } else {
                            uniquehrmEdit.add(hrmEdit);
                        }

                    }
                }
            }
            executorService.submit(() -> {
                try {
                    //step 6:处理每一组数据
//                    String process = process(sellchanceId, uniquehrmLook, uniquehrmEdit, uniqueDepartLookIds, uniqueDepartEditIds);
                    String process = process(sellchanceId, uniquehrmLook, uniquehrmEdit);
                    if (StringUtils.isNotBlank(process)) {
                        bb.writeLog("矩阵同属商机共享权限出错" + process);
                    }
                } finally {
                    latch.countDown(); // 线程完成任务后减少计数器
                    DBUtil.clearThreadLocalRecordSet();
                }
            });
        }
        return errorMsg;
    }

    //请求参数类型转换
    private JSONArray reqParamsCover(String paramsName) {
        String paramObj = (String) params.get(paramsName);
        JSONArray jsonArray = JSON.parseArray(paramObj);
        return jsonArray;
    }

    private String updataAuth(Set<String> looks, Set<String> edits) {

        bb.writeLog("商机中开始进行人员权限的数据库操作=====>START");
        String errorMsg = "";
        String sellchanceId = (String) params.get("sellChanceId");
        String deleteSql = "delete from CRM_SELLCHANCECARDAUTH where SECLEVELMAX = '1122334455' and SELLCHANCEID = " + sellchanceId;
        bb.writeLog("通过商机ID和二开字段删除原有的二开人员权限sql" + deleteSql);
        rs.execute(deleteSql);
        if (!looks.isEmpty()) {
            JSONArray lookJsonArray = assembledUpdataObj("1", looks);
            if (!lookJsonArray.isEmpty()) {
                for (int i = 0; i < lookJsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) lookJsonArray.get(i);
                    String values = " VALUES(" + jo.getString("SELLCHANCEID") + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                            + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                    String sql = "INSERT INTO crm_sellchancecardauth\n" +
                            " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER) " + values;
                    bb.writeLog("c插入具有查看权限的sql" + sql);
                    rs.execute(sql);
                }
            }
        }
        if (!edits.isEmpty()) {
            JSONArray editJsonArray = assembledUpdataObj("2", edits);
            if (!editJsonArray.isEmpty()) {
                for (int i = 0; i < editJsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) editJsonArray.get(i);
                    String values = " VALUES(" + jo.getString("SELLCHANCEID") + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                            + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                    String sql = "INSERT INTO crm_sellchancecardauth\n" +
                            " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                    bb.writeLog("c插入具有编辑权限的sql" + sql);
                    rs.execute(sql);
                }
            }
        }
        bb.writeLog("商机中开始进行人员权限的数据库操作=====>END");
        return errorMsg;
    }

    private JSONArray assembledUpdataObj(String type, Set<String> sets) {
        String sellchanceId = (String) params.get("sellChanceId");
        JSONArray ja = new JSONArray();
        for (String set : sets) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("SELLCHANCEID", sellchanceId);
            jsonObject.put("SHARETYPE", 1);
            jsonObject.put("RELATEDSHAREID", set);
            jsonObject.put("SHARELEVEL", type);
            jsonObject.put("SECLEVELMAX", 1122334455);
            jsonObject.put("CANDELETED", 1);
            jsonObject.put("CANEDIT", 1);
            jsonObject.put("CREATER", user.getUID());
            ja.add(jsonObject);
        }
        return ja;
    }

    private String process(String sellchanceId, Set<String> uniquehrmLook, Set<String> uniquehrmEdit) {


        // 由人力资源字段保存的人员
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        Set<String> uniquehrms = new HashSet<>();
        uniquehrms.addAll(uniquehrmLook);
        uniquehrms.addAll(uniquehrmEdit);
        String ids = String.join(",", uniquehrms);
        Set<String> lookHrmIds = new HashSet<>();
        Set<String> editHrmIds = new HashSet<>();
        Set<String> remove = new HashSet<>();
        String deleteSql = "delete from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID not in(" + ids + ") and SECLEVELMAX = '1122334455'";
        recordSet.execute(deleteSql);


        JSONArray matrixs = reqParamsCover("matrix");
        if (matrixs != null && !matrixs.isEmpty()) {
            for (int i = 0; i < matrixs.size(); i++) {
                ArrayList<String> departLookNames = new ArrayList<>();
                ArrayList<String> departEditNames = new ArrayList<>();
                Set<String> uniqueDepartLookIds = new HashSet<>();
                Set<String> uniqueDepartEditIds = new HashSet<>();
                Set<String> uniqueDepartHrmLookIds = new HashSet<>();
                Set<String> uniqueDepartHrmEditIds = new HashSet<>();
                Set<String> uniquedepartLookToRemove = new HashSet<>();
                JSONObject matrix = (JSONObject) matrixs.get(i);
                String matrixName = (String) matrix.get("matrixName");
                String fieldJuzhens = (String) matrix.get("field_juzhen");
                JSONArray departmentArray = (JSONArray) matrix.get("depts");
                if (departmentArray != null && !departmentArray.isEmpty()) {
                    for (int j = 0; j < departmentArray.size(); j++) {
                        JSONObject depart = (JSONObject) departmentArray.get(j);
                        String fieldName = depart.getString("fieldName");
                        String permission = depart.getString("permission");
                        if (StringUtils.isNotBlank(permission)) {
                            if ("0".equals(permission)) { //查看权限
                                departLookNames.add(fieldName);
                            } else {
                                departEditNames.add(fieldName);
                            }
                        }
                    }

                    rs.execute("select * from CRM_SellChance where id = " + sellchanceId);
                    if (rs.next()) {
                        if (!departLookNames.isEmpty()) {
                            for (int j = 0; j < departLookNames.size(); j++) {
                                String departLook = rs.getString(departLookNames.get(j));
                                if (StringUtils.isNotBlank(departLook)) {
                                    if (departLook.contains(",")) {
                                        List<String> list = Arrays.asList(departLook.split(","));
                                        uniqueDepartLookIds.addAll(list);
                                    } else {
                                        uniqueDepartLookIds.add(departLook);
                                    }
                                }
                            }
                        }
                        bb.writeLog("商机中(部门字段)具有查看权限的部门id" + JSONObject.toJSONString(uniqueDepartLookIds));
                        if (!departEditNames.isEmpty()) {
                            for (int j = 0; j < departEditNames.size(); j++) {
                                String departEdit = rs.getString(departEditNames.get(j));
                                if (StringUtils.isNotBlank(departEdit)) {
                                    if (departEdit.contains(",")) {
                                        List<String> list = Arrays.asList(departEdit.split(","));
                                        uniqueDepartEditIds.addAll(list);
                                    } else {
                                        uniqueDepartEditIds.add(departEdit);
                                    }
                                }
                            }
                        }
                        bb.writeLog("商机中(部门字段)具有编辑权限的部门id" + JSONObject.toJSONString(uniqueDepartEditIds));
                    }
                    if (!uniqueDepartLookIds.isEmpty()) {
                        for (String id : uniqueDepartLookIds) {
                            if ("matrixtable_2".equals(matrixName) || "matrixtable_1".equals(matrixName)) {
                                rs.execute("select * from " + matrixName + " where id = " + id);
                            } else {
                                rs.execute("select * from " + matrixName + " where bm = " + id);
                            }
                            if (rs.next()) {
                                if (StringUtils.isNotBlank(fieldJuzhens)) {
                                    if (fieldJuzhens.contains(",")) {
                                        for (String fieldJuzhen : fieldJuzhens.split(",")) {
                                            String bmfzrs = rs.getString(fieldJuzhen);
                                            if (StringUtils.isNotBlank(bmfzrs)) {
                                                if (bmfzrs.contains(",")) {
                                                    List<String> list = Arrays.asList(bmfzrs.split(","));
                                                    uniqueDepartHrmLookIds.addAll(list);
                                                } else {
                                                    uniqueDepartHrmLookIds.add(bmfzrs);
                                                }
                                            }
                                        }
                                    } else {
                                        String bmfzrs = rs.getString(fieldJuzhens);
                                        if (StringUtils.isNotBlank(bmfzrs)) {
                                            if (bmfzrs.contains(",")) {
                                                List<String> list = Arrays.asList(bmfzrs.split(","));
                                                uniqueDepartHrmLookIds.addAll(list);
                                            } else {
                                                uniqueDepartHrmLookIds.add(bmfzrs);
                                            }
                                        }
                                    }
                                }

                            }
                        }
                    }
                    bb.writeLog("商机中(部门字段)通过举证查找后具有查看权限的人员id" + JSONObject.toJSONString(uniqueDepartHrmLookIds));
                    if (!uniqueDepartEditIds.isEmpty()) {
                        for (String id : uniqueDepartEditIds) {
                            if ("matrixtable_2".equals(matrixName) || "matrixtable_1".equals(matrixName)) {
                                rs.execute("select * from " + matrixName + " where id = " + id);
                            } else {
                                rs.execute("select * from " + matrixName + " where bm = " + id);
                            }
                            if (rs.next()) {
                                if (StringUtils.isNotBlank(fieldJuzhens)) {
                                    if (fieldJuzhens.contains(",")) {
                                        for (String fieldJuzhen : fieldJuzhens.split(",")) {
                                            String bmfzrs = rs.getString(fieldJuzhen);
                                            if (StringUtils.isNotBlank(bmfzrs)) {
                                                if (bmfzrs.contains(",")) {
                                                    List<String> list = Arrays.asList(bmfzrs.split(","));
                                                    uniqueDepartHrmEditIds.addAll(list);
                                                } else {
                                                    uniqueDepartHrmEditIds.add(bmfzrs);
                                                }
                                            }
                                        }
                                    } else {
                                        String bmfzrs = rs.getString(fieldJuzhens);
                                        if (StringUtils.isNotBlank(bmfzrs)) {
                                            if (bmfzrs.contains(",")) {
                                                List<String> list = Arrays.asList(bmfzrs.split(","));
                                                uniqueDepartHrmEditIds.addAll(list);
                                            } else {
                                                uniqueDepartHrmEditIds.add(bmfzrs);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    bb.writeLog("商机中(部门字段)通过举证查找后具有编辑权限的人员id" + JSONObject.toJSONString(uniqueDepartHrmEditIds));
                }
                lookHrmIds.addAll(uniqueDepartHrmLookIds);
                editHrmIds.addAll(uniqueDepartHrmEditIds);
            }
        }
        if (!lookHrmIds.isEmpty() && !editHrmIds.isEmpty()) {
            for (String edit : editHrmIds) {
                for (String look : lookHrmIds) {
                    if (edit.equals(look)) {
                        remove.add(look);
                    }
                }
            }
            lookHrmIds.removeAll(remove);
        }


//        Set<String> editManagerHrmIds = new HashSet<>();
////       编辑权限的人员的上级
//        String editsql = "select managerid from hrmresource where id in ("+String.join(",", editHrmIds)+")";
//        recordSet.executeQuery(editsql);
//        while (recordSet.next()){
//            editManagerHrmIds.add(recordSet.getString("managerid"));
//        }
//        editHrmIds.addAll(editManagerHrmIds);
//
////       查看权限的人员的上级
//        Set<String> lookManagerHrmIds = new HashSet<>();
//        String lookssql = "select managerid from hrmresource where id in ("+String.join(",", lookHrmIds)+")";
//        recordSet.executeQuery(lookssql);
//        while (recordSet.next()){
//            lookManagerHrmIds.add(recordSet.getString("managerid"));
//        }
//
//        lookHrmIds.addAll(lookManagerHrmIds);
//        remove = new HashSet<>();
//        if (!lookHrmIds.isEmpty() && !editHrmIds.isEmpty()) {
//            for (String edit : editHrmIds) {
//                for (String look : lookHrmIds) {
//                    if (edit.equals(look)) {
//                        remove.add(look);
//                    }
//                }
//            }
//            lookHrmIds.removeAll(remove);
//        }


        if (StringUtils.isNotBlank(type) && "workflow".equals(type)) {
            Set<String> editManagerHrmIds = new HashSet<>();
//       编辑权限的人员的上级
            String editsql = "select managerstr from hrmresource where id in (" + String.join(",", editHrmIds) + ")";
            recordSet.executeQuery(editsql);
            while (recordSet.next()) {
                if (StringUtils.isNotBlank(recordSet.getString("managerstr"))) {
                    String managerstr = recordSet.getString("managerstr");
                    if (managerstr.contains(",")) {
                        managerstr = removeFirstAndLastComma(managerstr);
                        // 如果 managerstr 包含逗号，将其按逗号分割并逐一处理
                        String[] managers = managerstr.split(",");
                        for (String managerId : managers) {
                            if (StringUtils.isNotBlank(managerId)) {
                                editManagerHrmIds.add(managerId.trim()); // 去除可能的空格并添加
                            }
                        }
                    } else {
                        // 如果 managerstr 不包含逗号，直接添加
                        editManagerHrmIds.add(managerstr.trim());
                    }
                }
                // 添加 managerid 字段到 editManagerHrmIds
                editManagerHrmIds.add(recordSet.getString("managerid"));
            }
            // 最终将 manager IDs 添加到 editHrmIds 中
            editHrmIds.addAll(editManagerHrmIds);


//      查看权限的人员的上级
            Set<String> lookManagerHrmIds = new HashSet<>();
            String lookssql = "select managerstr from hrmresource where id in (" + String.join(",", lookHrmIds) + ")";
            recordSet.executeQuery(lookssql);
            while (recordSet.next()) {
                if (StringUtils.isNotBlank(recordSet.getString("managerstr"))) {
                    String managerstr = recordSet.getString("managerstr");
                    if (managerstr.contains(",")) {
                        managerstr = removeFirstAndLastComma(managerstr);
                        // 如果 managerstr 包含逗号，将其按逗号分割并逐一处理
                        String[] managers = managerstr.split(",");
                        for (String managerId : managers) {
                            if (StringUtils.isNotBlank(managerId)) {
                                lookManagerHrmIds.add(managerId.trim()); // 去除可能的空格并添加
                            }
                        }
                    } else {
                        // 如果 managerstr 不包含逗号，直接添加
                        lookManagerHrmIds.add(managerstr.trim());
                    }
                }

            }
            // 最终将 manager IDs 添加到 editHrmIds 中
            lookHrmIds.addAll(lookManagerHrmIds);
            remove = new HashSet<>();
            if (!lookHrmIds.isEmpty() && !editHrmIds.isEmpty()) {
                for (String edit : editHrmIds) {
                    for (String look : lookHrmIds) {
                        if (edit.equals(look)) {
                            remove.add(look);
                        }
                    }
                }
                lookHrmIds.removeAll(remove);
            }
        }


        for (String id : editHrmIds) {
            String sql = "select * from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID =" + id;
            recordSet.execute(sql);
            if (recordSet.next()) {
                String shareLevel = recordSet.getString("SHARELEVEL");
                String SECLEVELMAX = recordSet.getString("SECLEVELMAX");
                //查看权限
                if (StringUtils.isNotBlank(shareLevel) && "1".equals(shareLevel)) {
                    //删除 再插入
                    String deleteByRelatedshareID = "delete from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID =" + id;
                    recordSet.execute(deleteByRelatedshareID);
                    HashSet<String> sets = new HashSet<>();
                    sets.add(id);
                    JSONArray editJsonArray = assembledUpdataObj("2", sets);
                    for (int i = 0; i < editJsonArray.size(); i++) {
                        JSONObject jo = (JSONObject) editJsonArray.get(i);
                        String values = " VALUES(" + sellchanceId + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                                + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                        String insertSsql = "INSERT INTO crm_sellchancecardauth\n" +
                                " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                        recordSet.execute(insertSsql);
                    }
                }
            } else {
                //插入
                HashSet<String> sets = new HashSet<>();
                sets.add(id);
                JSONArray editJsonArray = assembledUpdataObj("2", sets);
                for (int i = 0; i < editJsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) editJsonArray.get(i);
                    String values = " VALUES(" + sellchanceId + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                            + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                    String insertSsql = "INSERT INTO crm_sellchancecardauth\n" +
                            " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                    rs.execute(insertSsql);
                }
            }
        }
        for (String id : lookHrmIds) {
            String sql = "select * from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID =" + id;
            recordSet.execute(sql);
            if (recordSet.next()) {

            } else {
                //插入
                HashSet<String> sets = new HashSet<>();
                sets.add(id);
                JSONArray editJsonArray = assembledUpdataObj("1", sets);
                for (int i = 0; i < editJsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) editJsonArray.get(i);
                    String values = " VALUES(" + sellchanceId + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                            + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                    String insertSsql = "INSERT INTO crm_sellchancecardauth\n" +
                            " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                    recordSet.execute(insertSsql);
                }
            }
        }
        return null;
    }

/*
    private String process(String sellchanceId, Set<String> uniquehrmLook, Set<String> uniquehrmEdit, Set<String> uniqueDepartLookIds, Set<String> uniqueDepartEditIds) {

        // 由人力资源字段保存的人员
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        Set<String> uniquehrms = new HashSet<>();
        uniquehrms.addAll(uniquehrmLook);
        uniquehrms.addAll(uniquehrmEdit);
        String ids = String.join(",", uniquehrms);
        Set<String> lookHrmIds = new HashSet<>();
        Set<String> editHrmIds = new HashSet<>();
        Set<String> remove = new HashSet<>();
        String deleteSql = "delete from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID not in(" + ids + ") and SECLEVELMAX = '1122334455'";
        recordSet.execute(deleteSql);

        //查看权限的部门id
        String lookIds = String.join(",", uniqueDepartLookIds);
        String queryLookSql = "select * from matrixtable_2 where id in(" + lookIds + ")";
        recordSet.execute(queryLookSql);
        while (recordSet.next()) {
            String bmfzr = recordSet.getString(jzqzzd);
            if (StringUtils.isNotBlank(bmfzr)) {
                if (bmfzr.contains(",")) {
                    List<String> list = Arrays.asList(bmfzr.split(","));
                    lookHrmIds.addAll(list);
                } else {
                    lookHrmIds.add(bmfzr);
                }
            }
        }
        //编辑权限的人
        String editIds = String.join(",", uniqueDepartEditIds);
        String queryEditSql = "select * from matrixtable_2 where id in(" + editIds + ")";
        recordSet.execute(queryEditSql);
        while (recordSet.next()) {
            String bmfzr = recordSet.getString(jzqzzd);
            if (StringUtils.isNotBlank(bmfzr)) {
                if (bmfzr.contains(",")) {
                    List<String> list = Arrays.asList(bmfzr.split(","));
                    editHrmIds.addAll(list);
                } else {
                    editHrmIds.add(bmfzr);
                }
            }
        }
        if (!lookHrmIds.isEmpty() && !editHrmIds.isEmpty()) {
            for (String edit : editHrmIds) {
                for (String look : lookHrmIds) {
                    if (edit.equals(look)) {
                        remove.add(look);
                    }
                }
            }
            lookHrmIds.removeAll(remove);
        }
        for (String id : editHrmIds) {
            String sql = "select * from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID =" + id;
            recordSet.execute(sql);
            if (recordSet.next()) {
                String shareLevel = recordSet.getString("SHARELEVEL");
                String SECLEVELMAX = recordSet.getString("SECLEVELMAX");
                //查看权限
                if (StringUtils.isNotBlank(shareLevel) && "1".equals(shareLevel)) {
                    //删除 再插入
                    String deleteByRelatedshareID = "delete from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID =" + id;
                    recordSet.execute(deleteByRelatedshareID);
                    HashSet<String> sets = new HashSet<>();
                    sets.add(id);
                    JSONArray editJsonArray = assembledUpdataObj("2", sets);
                    for (int i = 0; i < editJsonArray.size(); i++) {
                        JSONObject jo = (JSONObject) editJsonArray.get(i);
                        String values = " VALUES(" + sellchanceId + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                                + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                        String insertSsql = "INSERT INTO crm_sellchancecardauth\n" +
                                " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                        recordSet.execute(insertSsql);
                    }
                }
            } else {
                //插入
                HashSet<String> sets = new HashSet<>();
                sets.add(id);
                JSONArray editJsonArray = assembledUpdataObj("2", sets);
                for (int i = 0; i < editJsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) editJsonArray.get(i);
                    String values = " VALUES(" + sellchanceId + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                            + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                    String insertSsql = "INSERT INTO crm_sellchancecardauth\n" +
                            " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                    rs.execute(insertSsql);
                }
            }
        }
        for (String id : lookHrmIds) {
            String sql = "select * from CRM_SELLCHANCECARDAUTH where SELLCHANCEID =" + sellchanceId + " and RELATEDSHAREID =" + id;
            recordSet.execute(sql);
            if (recordSet.next()) {

            } else {
                //插入
                HashSet<String> sets = new HashSet<>();
                sets.add(id);
                JSONArray editJsonArray = assembledUpdataObj("1", sets);
                for (int i = 0; i < editJsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) editJsonArray.get(i);
                    String values = " VALUES(" + sellchanceId + "," + jo.getString("SHARETYPE") + "," + jo.getString("RELATEDSHAREID") + "," + jo.getString("SHARELEVEL")
                            + "," + jo.getString("SECLEVELMAX") + "," + jo.getString("CANDELETED") + "," + jo.getString("CANEDIT") + "," + jo.getString("CREATER") + ")";
                    String insertSsql = "INSERT INTO crm_sellchancecardauth\n" +
                            " (SELLCHANCEID, SHARETYPE, RELATEDSHAREID, SHARELEVEL,SECLEVELMAX, CANDELETED, CANEDIT, CREATER)" + values;
                    recordSet.execute(insertSsql);
                }
            }
        }
        return null;
    }
*/

    public static String removeFirstAndLastComma(String s) {
        // 如果字符串以逗号开头，则去除第一个逗号
        if (s.startsWith(",")) {
            s = s.substring(1);
        }
        // 如果字符串以逗号结尾，则去除最后一个逗号
        if (s.endsWith(",")) {
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }
}

