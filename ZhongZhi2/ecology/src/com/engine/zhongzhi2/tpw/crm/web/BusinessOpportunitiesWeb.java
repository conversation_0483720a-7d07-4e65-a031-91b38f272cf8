package com.engine.zhongzhi2.tpw.crm.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.zhongzhi2.tpw.crm.service.BusinessBrowseService;
import com.engine.zhongzhi2.tpw.crm.service.BusinessOpportunitiesService;
import com.engine.zhongzhi2.tpw.crm.service.impl.BusinessBrowseServiceImpl;
import com.engine.zhongzhi2.tpw.crm.service.impl.BusinessOpportunitiesServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class BusinessOpportunitiesWeb extends BaseBean {
    private BusinessOpportunitiesService getService(User user) {
        return ServiceUtil.getService(BusinessOpportunitiesServiceImpl.class, user);
    }
    @POST
    @Path("/businessOpportunities")
    @Produces({MediaType.TEXT_PLAIN})
    public String businessOpportunities(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ParamUtil.request2Map(request);
        ArrayList<String> list = new ArrayList<>();
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                String name = cookie.getName();
                String value = cookie.getValue();
                String paramsObj = name+"="+value;
                list.add(paramsObj);
            }
        }
        params.put("cookie", String.join(";", list));
        if (user != null) {
            result = getService(user).businessOpportunities(params, user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}