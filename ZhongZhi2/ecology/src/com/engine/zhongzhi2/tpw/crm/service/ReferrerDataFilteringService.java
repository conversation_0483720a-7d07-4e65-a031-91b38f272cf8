package com.engine.zhongzhi2.tpw.crm.service;

import weaver.hrm.User;

import java.util.Map;

public interface ReferrerDataFilteringService   {
    Map<String, Object> processReferrerNewData(Map<String, Object> params, User user);

    Map<String, Object> processReferrerNewEntityData(Map<String, Object> params, User user);

    Map<String, Object> processReferrerEditData(Map<String, Object> params, User user);

    Map<String, Object> processReferrerEditEntityData(Map<String, Object> params, User user);
}
