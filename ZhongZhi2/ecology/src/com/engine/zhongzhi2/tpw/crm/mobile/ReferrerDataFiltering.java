package com.engine.zhongzhi2.tpw.crm.mobile;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 说明
 * 修改时
 * 类名要与文件名保持一致
 * class文件存放位置与路径保持一致。
 * 请把编译后的class文件，放在对应的目录中才能生效
 * 注意 同一路径下java名不能相同。
 */
public class ReferrerDataFiltering extends AbstractModeExpandJavaCodeNew {
    private static final Log logger = LogFactory.getLog(ReferrerDataFiltering.class);

    /**
     * 执行模块扩展动作
     * @param param 包含用户、请求信息等参数
     * @return 返回操作结果
     */
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<>();
        try {
            User user = (User) param.get("user");
            int billid = -1; // 数据id
            int modeid = -1; // 模块id
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");

            if (requestInfo != null) {
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());

                if (billid > 0 && modeid > 0) {
                    // 处理业务逻辑
                    processReferrerData(billid, result);
                }
            }
        } catch (Exception e) {
            logger.error("执行模块扩展逻辑时出错", e);
            result.put("errmsg", "自定义出错信息");
            result.put("flag", "false");
        }
        return result;
    }

    /**
     * 处理推荐人数据逻辑
     * @param billid 数据ID
     * @param result 返回结果
     */
    private void processReferrerData(int billid, Map<String, String> result) {
        RecordSet recordSet = new RecordSet();
        String querySql = "SELECT tjr1, tjryddsjgly FROM CRM_SellChance WHERE id = ?";
        recordSet.executeQuery(querySql, billid);

        if (recordSet.next()) {
            String tjr1 = recordSet.getString("tjr1");
            String tjryddsjgly = recordSet.getString("tjryddsjgly");

            if (StringUtils.isNotBlank(tjr1)) {
                // tjr1 有值，不进行任何操作
//                result.put("flag", "true");
//                result.put("msg", "tjr1 已有值，无需更新");
            } else if (StringUtils.isNotBlank(tjryddsjgly)) {
                // tjr1 无值，且 tjryddsjgly 有值，更新 tjr1
                String updateSql = "UPDATE CRM_SellChance SET tjr1 = ? WHERE id = ?";
                recordSet.executeUpdate(updateSql, tjryddsjgly, billid);
//                result.put("flag", "true");
//                result.put("msg", "已更新 tjr1 的值为 " + tjryddsjgly);
            } else {
                // 两个字段都无值，不进行任何操作
                result.put("flag", "true");
                result.put("msg", "tjr1 和 tjryddsjgly 均无值，无需更新");
            }
        } else {
            result.put("flag", "false");
            result.put("errmsg", "未找到对应的商机记录");
        }
    }
}