package com.engine.zhongzhi2.tpw.crm.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.wbi.util.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class BusinessBrowseCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();
    // 创建一个对象作为锁
    private static final Object lock = new Object();
    public BusinessBrowseCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
            bb.writeLog(this.getClass().getName() + "---START");
            bb.writeLog("params:" + params);
            Map<String, Object> result = new HashMap<>();
            ArrayList<String> sellChanceIds = new ArrayList<>();
            String errorMsg = "";
            try {
                bb.writeLog("BusinessBrowseCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
                if (params != null && !params.isEmpty()) {
                    String customerId = (String) params.get("customerId");
                    String  cookie = (String) params.get("cookie");
                    String  origin = (String) params.get("origin");
                    if(StringUtils.isNotBlank(customerId)&& StringUtils.isNotBlank(origin)){
                        //查询所有的该客户的所有商机信息
                        rs.executeQuery("select * from CRM_SellChance where customerid ="+customerId);
                        ExecutorService executorService = Executors.newFixedThreadPool(5);
                        CountDownLatch latch = new CountDownLatch(rs.getCounts());
                        bb.writeLog("根据客户id查询商机中的记录数===>"+rs.getCounts());
                        while(rs.next()){
                            String sellChanceId = rs.getString("id");
                            bb.writeLog("根据客户id查询商机中的sellChanceId===>"+sellChanceId);
                            //调用接口 查看是否有查看权限
                            executorService.submit(() -> {
                                try {
                                    //处理每一组数据
                                    String process = process(customerId,sellChanceId,cookie,origin);
                                    if(StringUtils.isNotBlank(process)){
                                        sellChanceIds.add(process);
                                    }
                                } finally {
                                    latch.countDown();// 线程完成任务后减少计数器
                                }
                            });
                        }
                        try {
                            // 等待所有线程完成任务，超时10分钟
                            if (!latch.await(10, TimeUnit.MINUTES)) {
                                writeLog("超时未执行完所有线程任务，请检查!");
                            }
                        } catch (InterruptedException e) {
                            writeLog("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
                        } finally {
                            executorService.shutdown();
                        }
                    }
                }else {
                    errorMsg = "请求参数为空";
                }

            } catch (Exception e) {
                errorMsg = SDUtil.getExceptionDetail(e);
                bb.writeLog("商机一览异常：" + errorMsg);
            }
            result.put("errorMsg", errorMsg);
            result.put("status", errorMsg.isEmpty());
            result.put("sellChanceIds", sellChanceIds);
            bb.writeLog("result:" + result);
            bb.writeLog(this.getClass().getName() + "---END");
            return result;

    }

    private String process(String customerId,String sellChanceId,String cookie,String origin) {
        bb.writeLog("进入线程");
        String id = sellChanceId;
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        String url = origin+"/api/crm/sellchance/form?customerid="+customerId+"&selectedKey=0&sellChanceId="+sellChanceId+"&operation=edit&__random__="+System.currentTimeMillis();;
        RestResult result = HttpUtil.getDataWithHeader(url, headers);
        bb.writeLog("接口发送响应=====>" + JSONObject.toJSONString(result));
        if(result.isSuccess()){
            RestResult.ResponseInfo responseInfo = result.getResponseInfo();
            String body = responseInfo.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            String msgcode = Util.null2String(jsonObject.get("msgcode"));
            if (StringUtils.isNotBlank(msgcode) && msgcode.contains("出现异常情况") && msgcode.contains("请查看日志") ) {
                return "";
            }
        }
        return sellChanceId;
    }
}
