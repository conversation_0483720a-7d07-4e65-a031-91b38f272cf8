package com.engine.zhongzhi2.tpw.job;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.tpw.job.bean.ProjectManagerLog;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @FileName CustomeContractfieldUpdateJob
 * @Description 客户合同清单字段更新定时任务
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/7/25
 */
@Getter
@Setter
public class CustomeContractfieldUpdateJob extends BaseCronJob {
    private BaseBean bb = new BaseBean();
    /**
     * 是否已全额开票
     */
    private String fullInvoicing;
    /**
     * 是否已完成确认收入
     */
    private String recognizeRevenue;
    /**
     * 合同未开票金额字段名
     */
    private String wkpjeFieldName;
    /**
     * 累计确认收入金额字段名
     */
    private String srjeFieldName;
    /**
     * 合同金额字段名
     */
    private String htjeFieldName;

    @Override
    public void execute() {
        try {
            bb.writeLog("CustomeContractfieldUpdateJob----START");
            //更新项目经理同步日志
            ProjectManagerLog projectManagerLog = new ProjectManagerLog();
            projectManagerLog.setSync_start_date(TimeUtil.getCurrentTimeString());
            List<ProjectManagerLog.ProjectManagerDetailLog> detailLogList = new ArrayList<>();
            ProjectManagerLog.ProjectManagerDetailLog projectManagerDetailLog1 = new ProjectManagerLog.ProjectManagerDetailLog();
            ProjectManagerLog.ProjectManagerDetailLog projectManagerDetailLog2 = new ProjectManagerLog.ProjectManagerDetailLog();
            projectManagerDetailLog1.setRemark("已全额开票的合同");
            projectManagerDetailLog2.setRemark("确认收入的合同");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            if (StringUtils.isNotBlank(fullInvoicing) && StringUtils.isNotBlank(recognizeRevenue)) {
                Set<String> set1 = new HashSet<>();
                Set<String> set3 = new HashSet<>();
                Set<String> set2 = new HashSet<>();
                Set<String> set4 = new HashSet<>();
                //未开票金额字段名 = 0
                recordSet.executeQuery("select htbh from uf_ywhtxx where 1=1 and " + wkpjeFieldName + " = 0");
                while (recordSet.next()) {
                    set1.add(recordSet.getString("htbh"));
                }
                projectManagerDetailLog1.setUpdateBeforeData(JSONObject.toJSONString(set1));
                if (!set1.isEmpty()) {
                    List<String> setList = new ArrayList<>(set1);
                    int batchSize = 500;

                    for (int i = 0; i < setList.size(); i += batchSize) {
                        List<String> batch = setList.subList(i, Math.min(i + batchSize, setList.size()));
                        String result = batch.stream()
                                .map(s -> "'" + s + "'")
                                .collect(Collectors.joining(","));
                        recordSet.executeUpdate("UPDATE uf_algh SET " + fullInvoicing + " = 0 WHERE htbh IN (" + result + ")");
                    }
                }

                //未全额开票的合同 非0
                recordSet.executeQuery("select htbh from uf_ywhtxx where 1=1 and (" + wkpjeFieldName + " <> 0 or "+wkpjeFieldName+" is null)");
                while (recordSet.next()) {
                    set3.add(recordSet.getString("htbh"));
                }
                bb.writeLog("未全额开票的合同: " + JSONObject.toJSONString(set3));

                if (!set3.isEmpty()) {
                    List<String> setList = new ArrayList<>(set3);
                    int batchSize = 500;
                    for (int i = 0; i < setList.size(); i += batchSize) {
                        List<String> batch = setList.subList(i, Math.min(i + batchSize, setList.size()));
                        String result = batch.stream()
                                .map(s -> "'" + s + "'")
                                .collect(Collectors.joining(","));
                        recordSet.executeUpdate("UPDATE uf_algh SET " + fullInvoicing + " = 1 WHERE htbh IN (" + result + ")");
                    }
                }

                //已完成确认收入
                recordSet.executeQuery("select htbh from uf_ywhtxx  where 1=1 and " + srjeFieldName + " = " + htjeFieldName);
                //已完成确认收入的合同
                while (recordSet.next()) {
                    set2.add(recordSet.getString("htbh"));
                }
                projectManagerDetailLog2.setUpdateBeforeData(JSONObject.toJSONString(set2));

                if(!set2.isEmpty()){
                    List<String> setList = new ArrayList<>(set2);
                    int batchSize = 500;
                    for (int i = 0; i < setList.size(); i += batchSize) {
                        List<String> batch = setList.subList(i, Math.min(i + batchSize, setList.size()));
                        String result = batch.stream()
                                .map(s -> "'" + s + "'")
                                .collect(Collectors.joining(","));
                        recordSet.executeUpdate(" UPDATE uf_algh set " + recognizeRevenue + " = 0  where htbh in (" + result+ ")");
                    }
                }


                //未完成确认收入
                recordSet.executeQuery("select htbh from uf_ywhtxx  where "+srjeFieldName+" is null or "+htjeFieldName+" is null or "+ srjeFieldName + " <> " + htjeFieldName);
                //已完成确认收入的合同
                while (recordSet.next()) {
                    set4.add(recordSet.getString("htbh"));
                }
                bb.writeLog("未完成确认收入: " + JSONObject.toJSONString(set4));
                if(!set4.isEmpty()){

                    List<String> setList = new ArrayList<>(set4);
                    int batchSize = 500;
                    for (int i = 0; i < setList.size(); i += batchSize) {
                        List<String> batch = setList.subList(i, Math.min(i + batchSize, setList.size()));
                        String result = batch.stream()
                                .map(s -> "'" + s + "'")
                                .collect(Collectors.joining(","));
                        recordSet.executeUpdate(" UPDATE uf_algh set " + recognizeRevenue + " = 1  where htbh in (" + result+ ")");
                    }

                }
                projectManagerLog.setSync_end_date(TimeUtil.getCurrentTimeString());
                detailLogList.add(projectManagerDetailLog1);
                detailLogList.add(projectManagerDetailLog2);
                int moduleId = ModuleDataUtil.getModuleIdByName(ProjectManagerLog.TABLE_NAME);
                ModuleResult mr = ModuleDataUtil.insertObj(projectManagerLog, ProjectManagerLog.TABLE_NAME, moduleId, 1);
                if (mr.isSuccess()) {
                    int mainid = mr.getBillid();
                    //异步插入明细日志
                    if (!detailLogList.isEmpty()) {
                        ModuleDataUtil.insertObjDetailAC(detailLogList, ProjectManagerLog.DETAIL_TABLE_NAME, mainid);
                    }
                }
            }
        } catch (Exception e) {
            bb.writeLog("客户合同清单字段更新定时任务异常信息：" + SDUtil.getExceptionDetail(e));
        }
    }
}
