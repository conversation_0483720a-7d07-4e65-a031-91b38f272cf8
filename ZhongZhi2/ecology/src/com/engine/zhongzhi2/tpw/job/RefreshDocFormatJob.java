package com.engine.zhongzhi2.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName RefreshDocFormatJob
 * @Description 将历史的项目资料库台账里的文档，刷上对应的格式
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/4/17
 */
@Getter
@Setter
public class RefreshDocFormatJob extends BaseCronJob {
    private BaseBean bb = new BaseBean();
    /*
     * 表单名
     */
    private String formName;
    /*
     * 保存文档的字段名
     */
    private String docField;
    /*
     * 文档格式字段名
     */
    private String docTypeField;
    /*
     * 数据id 多个数据id之间用英文逗号连接
     */
    private String dataid;
    /*
     * 项目资料库的数据集
     */
    private JSONArray projectDataList;

    /*
     * 文档格式map 附件后置名->id
     */
    private Map<String, String> docBrowseMap;

    @Override
    public void execute() {
        bb.writeLog("RefreshDocFormatJob Start ");
        String errMsg = "";
        try {
            errMsg = _init();
            resetParams();
            //如果errMsg非空 不执行后续逻辑
            if (StringUtils.isNotBlank(errMsg)) {
                bb.writeLog("RefreshDocFormatJob errMsg: " + errMsg);
                return;
            }
            //更新docField为空的数据
            processNoDocIds();
            processDocuments();
        } catch (Exception e) {
            bb.writeLog("RefreshDocFormatJob exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("RefreshDocFormatJob End ");
    }


    private void processNoDocIds() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            ArrayList<String> noDocIdList = new ArrayList<>();
            // 1. 过滤出无docId的项目
            Iterator<Object> iterator = projectDataList.iterator();
            while (iterator.hasNext()) {
                JSONObject project = (JSONObject) iterator.next();
                String id = Util.null2String(project.get("id"));
                String docIds = Util.null2String(project.get(docField));
                if (StringUtils.isBlank(docIds)) {
                    noDocIdList.add(id);
                    iterator.remove();
                }
            }
            bb.writeLog("过滤出无docId的项目ids: " + noDocIdList);
            // 2. 分批更新数据库（每批最多100条）
//            if (!noDocIdList.isEmpty()) {
//                int batchSize = 100;
//                for (int i = 0; i < noDocIdList.size(); i += batchSize) {
//                    int end = Math.min(i + batchSize, noDocIdList.size());
//                    List<String> childNoDocIdList = noDocIdList.subList(i, end);
//                    //构建安全的IN条件参数
//                    String placeholders = String.join(",", childNoDocIdList);
//                    String sql = String.format("UPDATE %s SET %s = 0 WHERE id IN (%s)", formName, docTypeField, placeholders);
//                    bb.writeLog("要更新的文档格式为空: " + sql);
//                    bb.writeLog("processNoDocIds childNoDocIdList SQL : " + i + " : " + sql);
//                    recordSet.executeUpdate(sql);
//                }
//            }
        } catch (Exception e) {
            bb.writeLog("processNoDocIds exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void resetParams() {
        try {
            String sqlWhere = "";
            if (StringUtils.isNotBlank(dataid)) {
                sqlWhere = " where id in (" + dataid + ")";
            }
            projectDataList = getProjectDataList("select * from " + formName + sqlWhere);
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from uf_knowledge_format");
            while (recordSet.next()) {
                String gs = recordSet.getString("gs");
                String id = recordSet.getString("id");
                docBrowseMap.put(gs, id);
            }
            bb.writeLog("通过表单名找到项目资料库的数据集 : " + JSONObject.toJSONString(projectDataList));
        } catch (Exception e) {
            bb.writeLog("resetParams exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void processDocuments() {
        try {
            bb.writeLog(" 过滤出无docId后的项目: " + JSONObject.toJSONString(projectDataList));
            CountDownLatch latch = new CountDownLatch(projectDataList.size());
            ExecutorService executorService = Executors.newFixedThreadPool(5);
            //遍历项目资料库的数据集,找到保存文档,根据其文件类型,做一定处理后，赋值给文档格式字段
            for (int i = 0; i < projectDataList.size(); i++) {
                JSONObject project = (JSONObject) projectDataList.get(i);
                //开启多线程
                executorService.submit(() -> {
                    try {
                        //处理每一组数据
                        _process(project);
                    } finally {
                        latch.countDown(); // 线程完成任务后减少计数器
                    }
                });
            }
            try {
                // 等待所有线程完成任务，超时3小时
                if (!latch.await(3, TimeUnit.HOURS)) {
                    bb.writeLog("对历史的项目资料库台账里的文档，刷上对应的格式超时");
                }
            } catch (InterruptedException e) {
                bb.writeLog("对历史的项目资料库台账里的文档，刷上对应的格式 InterruptedException" + e.getMessage());
            } finally {
                executorService.shutdown();
            }
        } catch (Exception e) {
            bb.writeLog("RefreshDocFormatJob exception: " + SDUtil.getExceptionDetail(e));
        }
    }

    private void _process(JSONObject project) {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            //id
            String id = Util.null2String(project.get("id"));
            //文档id
            String docIds = Util.null2String(project.get(docField));
            List<String> fileExtensions = new ArrayList<>();
            //1.通过文档id获取对应附件名
            recordSet.executeQuery("SELECT filerealpath, imagefilename FROM imagefile WHERE imagefileid in (SELECT imagefileid FROM docimagefile WHERE docid in (" + String.join(",", docIds) + ") )");
            while (recordSet.next()) {
                String imagefilename = recordSet.getString("imagefilename");
                //2.根据附件名获取附件后缀 这里三种选项 1：附件没有后缀且唯一 2.附件有后g缀，且所有的附件后缀都一样 3.非前两种情况
                fileExtensions.add(extractFileExtension(imagefilename));
            }
            // 3. 智能判断文档格式
            String docFormat = determineDocFormat(fileExtensions);
            String sql = String.format("UPDATE %s SET %s = %s WHERE id = %s",
                    formName, docTypeField, docFormat, id);
            bb.writeLog("要更新的文档格式: " + sql);
            recordSet.executeUpdate(sql);
        } catch (Exception e) {
            bb.writeLog("_process exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private String _init() {
        String errMsg = "";
        if (StringUtils.isBlank(formName)) {
            errMsg = "必填参数<表单名>为空，请先补全参数。";
        } else if (StringUtils.isBlank(docField)) {
            errMsg = "必填参数<保存文档的字段名>为空，请先补全参数。";
        } else if (StringUtils.isBlank(docTypeField)) {
            errMsg = "必填参数<文档格式字段名>为空，请先补全参数。";
        }
        projectDataList = null;
        docBrowseMap = new HashMap<>();
        return errMsg;
    }

    public JSONArray getProjectDataList(String sql) {
        JSONArray JA = new JSONArray();
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            String[] columnNames = null;
            rs.execute(sql);
            while (rs.next()) {
                JSONObject jsonObject = new JSONObject();
                if (columnNames == null || columnNames.length <= 0) {
                    columnNames = rs.getColumnName();
                }
                if (columnNames != null && columnNames.length > 0) {
                    for (String columnName : columnNames) {
                        jsonObject.put(columnName, rs.getString(columnName));
                    }
                    JA.add(jsonObject);
                }
            }
        } catch (Exception e) {
            bb.writeLog("getDetailBaseInfo出错:" + sql + "错误信息:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return JA;
    }

    /**
     * 提取文件后缀（小写，无点）
     */
    private String extractFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) return "";
        int dotIndex = filename.lastIndexOf('.');
        return (dotIndex > 0) ?
                filename.substring(dotIndex + 1) :
                "";
    }

    /*
     * 智能判断文档格式类型
     */
    private String determineDocFormat(List<String> extensions) {
        Set<String> uniqueExtensions = new HashSet<>(extensions);
        // 情况1：所有文件后缀都相等
        if (uniqueExtensions.size() == 1 && docBrowseMap.containsKey(extensions.get(0))) {
            return Util.null2String(docBrowseMap.get(extensions.get(0)));
        } else if (uniqueExtensions.size() > 1) {
            //情况2：出现多附件且格式不一致，则默认写入“多格式”
            return Util.null2String(docBrowseMap.get("多格式"));
        } else if (uniqueExtensions.size() >= 1) {
            for (String ext : uniqueExtensions) {
                if (!docBrowseMap.containsKey(ext)) {
                    return Util.null2String(docBrowseMap.get(""));
                }
            }
        }
        return "";
    }
}
