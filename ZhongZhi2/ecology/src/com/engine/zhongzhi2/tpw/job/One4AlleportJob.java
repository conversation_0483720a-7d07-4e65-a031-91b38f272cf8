package com.engine.zhongzhi2.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.tpw.job.bean.KpyjbBean;
import com.engine.zhongzhi2.tpw.job.bean.SyncKPYJLog;
import com.weaver.general.BaseBean;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.List;

/**
 * 开票计划
 */

@Getter
@Setter
public class One4AlleportJob extends BaseCronJob {
    //视图名
    private String view;
    //开票计划表单名
    private String table;
    //开票计划定时任务执行状态表名
    private String statusTableName;
    //开票计划定时任务日志表名
    private String logTableName;

    private BaseBean bb = new BaseBean();

    private RecordSet recordSet = DBUtil.getThreadLocalRecordSet();

    @Override
    public void execute() {
        try {
            String errMsg = "";
            bb.writeLog("One4AlleportJob----START");
            bb.writeLog("requestParams:view===>" + view);
            bb.writeLog("requestParams:table===>" + table);
            bb.writeLog("requestParams:statusTableName===>" + statusTableName);
            bb.writeLog("requestParams:logTableName===>" + logTableName);
            long startTime = System.currentTimeMillis();
            SyncKPYJLog syncKPYJLog = new SyncKPYJLog();
            syncKPYJLog.setSync_start_date(TimeUtil.getCurrentTimeString());
            JSONArray datajson = new JSONArray();
            if (StringUtils.isNotBlank(view) && StringUtils.isNotBlank(table) && StringUtils.isNotBlank(statusTableName) && StringUtils.isNotBlank(logTableName)) {
                //更新状态表中，使之处于正在查询的状态
                recordSet.executeUpdate("UPDATE " + statusTableName + " set status = 0 ,startTime  = '" + TimeUtil.getCurrentTimeString() + "'");
                //获取开票计划的所有数据
                long s = System.currentTimeMillis();
                datajson = executeView(view);
//                bb.writeLog("datajson===>" + datajson.toJSONString());
                long e = System.currentTimeMillis();
                long d = (e - s) / 1000;
                bb.writeLog("查询数据耗时：" + d);
                syncKPYJLog.setSync_cnt(datajson.size());
                //向建模中插入datajson中的所有数据
                errMsg = insetModule(ModuleDataUtil.getModuleIdByName(table), table, datajson, syncKPYJLog);
                long endTime = System.currentTimeMillis();
                long operate_during = (endTime - startTime) / 1000;
                syncKPYJLog.setOperate_during(String.valueOf(operate_during));
                syncKPYJLog.setSync_end_date(TimeUtil.getCurrentTimeString());
            } else {
                bb.writeLog("必填参数不完整!");
            }

            syncKPYJLog.setError(errMsg);

            ModuleResult mr = ModuleDataUtil.insertObj(syncKPYJLog, logTableName, ModuleDataUtil.getModuleIdByName(logTableName), 1);
            if (mr.isSuccess()) {
                bb.writeLog("同步日志错误信息：" + mr.getErroMsg());
            }
            //更新状态表中，使之处于完成查询的状态
            recordSet.executeUpdate("UPDATE " + statusTableName + " set status = 1 ,endTime  = '" + TimeUtil.getCurrentTimeString() + "'");
        } catch (Exception e) {
//            bb.writeLog("定时调用交易流水信息查询接口更新数据请求异常：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("One4AlleportJob----end");
    }

    private JSONArray executeView(String view) {
        String sql = "select * from " + view;
        recordSet.executeQuery(SDUtil.toDBC(sql));
        return QueryResultUtil.getJSONArrayList(recordSet);
    }

    private String insetModule(int moduleid, String table, JSONArray datajson, SyncKPYJLog syncKPYJLog) {
        String errMsg = "";
        recordSet.execute("TRUNCATE table " + table);
        List listBean = datajson.toJavaList(KpyjbBean.class);
//        bb.writeLog("组装好的对象信息：" + JSONObject.toJSONString(listBean));
        if (!listBean.isEmpty()) {
            ModuleResult mr = ModuleDataUtil.insertObjList(listBean, table, moduleid, 1, true);
//            bb.writeLog("ModuleResult ：" + JSONObject.toJSONString(mr));
            if (!mr.isSuccess()) {
                errMsg = "插入数据失败：" + mr.getErroMsg();
                syncKPYJLog.setSync_success_cnt(0);
            } else {
                syncKPYJLog.setSync_success_cnt(listBean.size());
            }
        }
        return errMsg;
    }

}
