package com.engine.zhongzhi2.tpw.job;


import com.alibaba.fastjson.JSONObject;
import com.api.crm.service.ContacterService;
import com.engine.crm.constant.LogSmallTypeEnum;
import com.engine.crm.constant.OperateTypeDetailEnum;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongzhi2.tpw.job.bean.CustomerContactLogBean;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.StringUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.wechat.util.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @FileName ContractContactDatasMigratedCustomerContactsJob
 * @Description 合同联系人数据迁移至CRM客户联系人
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/9/25
 */
@Getter
@Setter
public class ContractContactDatasMigratedCustomerContactsJob extends BaseCronJob {
    private static Logger log = LoggerFactory.getLogger(ContractContactDatasMigratedCustomerContactsJob.class);
    private RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
    private List<Map<String, Object>> contractContactList = new ArrayList<>();
    private ContacterService contacterService = new ContacterService();
    // 数据标记
    private ArrayList<String> dataLabels = new ArrayList<>();
    private HashMap<String, String> hrmMap = new HashMap<>();
    private HashMap<String, String> customercontacterMap = new HashMap<>();


    @Override
    public void execute() {
        try {
            log.info("ContractContactDatasMigratedCustomerContactsJob----START");
            // 查询数据库，获取合同联系人信息
            if (recordSet.executeQuery("select * from uf_khlxrxx")) {
                contractContactList = QueryUtil.getMapList(recordSet);
            }
            if (recordSet.executeQuery("select * from hrmresource")) {
                while (recordSet.next()) {
                    hrmMap.put(recordSet.getString("id"), recordSet.getString("departmentid"));
                }
            }
//            log.info("dataLabels===>" + JSONObject.toJSONString(dataLabels));
            if (recordSet.executeQuery("select sjbj from uf_khlxr_ext")) {
                while (recordSet.next()) {
                    if (StringUtils.isNotBlank(recordSet.getString("sjbj"))) {
                        dataLabels.add(recordSet.getString("sjbj"));
                    }
                }
            }
            log.info("dataLabels===>" + JSONObject.toJSONString(dataLabels));
            int moduleId = ModuleDataUtil.getModuleIdByName(CustomerContactLogBean.TABLE_NAME);
            log.info("合同联系人数据===>" + JSONObject.toJSONString(contractContactList));
            if (!contractContactList.isEmpty()) {
                //合同联系人数据，迁移至CRM客户联系人(crm_customercontacter)
                for (Map<String, Object> contractContact : contractContactList) {
                    if (dataLabels.contains(Utils.null2String(contractContact.get("id")))) {

                    } else {
                        User user = null;
                        Map<String, Object> params = new HashMap<>();
                        params.put("OperateTypeDetail", OperateTypeDetailEnum.GENERAL.getOperateTypeDetail());
                        params.put("LogSmallType", LogSmallTypeEnum.CONTACTER.getLogSmallType());
                        params.put("customerId", Util.null2String(contractContact.get("khmc")));
                        params.put("saveType", "addContacter");
                        params.put("main", "0");
                        params.put("firstname", Util.null2String(contractContact.get("xm")));
                        params.put("jobtitle", Util.null2String(contractContact.get("zw")));
                        params.put("bm", Util.null2String(contractContact.get("bm")));
                        params.put("mobilephone", Util.null2String(contractContact.get("yddh")));
                        params.put("title", Util.null2String(contractContact.get("ch")));
                        params.put("email", Util.null2String(contractContact.get("dzyj")));
                        params.put("phoneoffice", Util.null2String(contractContact.get("bgsdh")));
                        params.put("bgdz", Util.null2String(contractContact.get("bgdz")));
                        params.put("createdate", Util.null2String(contractContact.get("lrrq")));
                        params.put("createrid", Util.null2String(contractContact.get("lrr")));
                        if(StringUtils.isNotBlank(Util.null2String(contractContact.get("lrr")))){
                            user = new User(Integer.parseInt(Util.null2String(contractContact.get("lrr"))));
                        }
                        Map<String, Object> stringObjectMap = contacterService.create(user, params);
                        log.info("响应数据===>" + JSONObject.toJSONString(stringObjectMap));
                    }
                }
                if (recordSet.executeQuery("select id,firstname,mobilephone from crm_customercontacter")) {
                    while (recordSet.next()) {
                        customercontacterMap.put(recordSet.getString("firstname")+recordSet.getString("mobilephone"),Utils.null2String(recordSet.getString("id")));
                    }
                }
                // 将合同联系人转换为客户联系人
                List<CustomerContactLogBean> customerContactList = contractContactList.stream()
                        .filter(contractContact -> {
                            if (dataLabels.contains(Utils.null2String(contractContact.get("id")))) {
                                return false;
                            } else {
                                return true;
                            }
                        })
                        .map(contractContact -> {
                            CustomerContactLogBean customerContact = new CustomerContactLogBean();
                            // 数据转换，将合同联系人字段转换为客户联系人字段
                            // 客户名称
                            String kh = Utils.null2String(contractContact.get("khmc"));
                            customerContact.setKh(kh);
                            //获取系统 标准客户联系人/crm_customercontacter
                            if(customercontacterMap.containsKey(Utils.null2String(contractContact.get("xm")+Utils.null2String(contractContact.get("yddh"))))){
                                String contactId = customercontacterMap.get(Utils.null2String(contractContact.get("xm") + Utils.null2String(contractContact.get("yddh"))));
                                customerContact.setLxr(contactId);
                            }
                            // 姓名
                            String xm = Utils.null2String(contractContact.get("xm"));
                            customerContact.setXm(xm);
                            // 称呼
                            String ch = Utils.null2String(contractContact.get("ch"));
                            customerContact.setCh(ch);
                            // 职位
                            String zw = Utils.null2String(contractContact.get("zw"));
                            customerContact.setZw(zw);
                            // 手机
                            String yddh = Utils.null2String(contractContact.get("yddh"));
                            customerContact.setSj(yddh);
                            // 邮箱
                            String dzyj = Utils.null2String(contractContact.get("dzyj"));
                            customerContact.setYx(dzyj);
                            // 座机
                            String bgsdh = Utils.null2String(contractContact.get("bgsdh"));
                            customerContact.setZj(bgsdh);
                            //创建人
                            String lrr = Utils.null2String(contractContact.get("lrr"));
                            customerContact.setCjr(lrr);
                            //创建人部门
                            if(hrmMap.containsKey(Utils.null2String(contractContact.get("modedatacreater")))){
                                customerContact.setCjrbm(hrmMap.get(Utils.null2String(contractContact.get("modedatacreater"))));
                            }
                            //删除标记
                            customerContact.setDeleted("0");
                            //数据标记
                            customerContact.setSjbj(Utils.null2String(contractContact.get("id")));
                            return customerContact;
                        }).collect(Collectors.toList());
                // 合同联系人数据，迁移至客户联系人日志(uf_khlxr_ext )
                if (!customerContactList.isEmpty()) {
                    ModuleResult mr = ModuleDataUtil.insertObjList(customerContactList, CustomerContactLogBean.TABLE_NAME, moduleId, 1);
                }

            } else {
                log.info("uf_khlxrxx表中不存在数据");
            }
            log.info("ContractContactDatasMigratedCustomerContactsJob----END");
        } catch (Exception e) {
            log.info("合同联系人数据迁移至客户联系人异常信息：" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

}
