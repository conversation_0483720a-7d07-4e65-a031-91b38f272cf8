package com.engine.zhongzhi2.tpw.job.bean;

import lombok.Data;
import weaver.general.TimeUtil;


/**
 * @FileName YSVendorLog
 * @Description 更新项目经理同步日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/7/22
 */
@Data
public class ProjectManagerLog {
    private Integer id;
    private String sync_start_date;
    private String sync_end_date;
    private String remark;

    @Data
    public static class ProjectManagerDetailLog {
        private Integer id;
        private Integer mainid;
        //符合条件的更新前的数据
        private String updateBeforeData;
        //更新sql语句
        private String sql;
        private String remark;
    }

    public static final String TABLE_NAME = "uf_UPM_log";
    public static final String DETAIL_TABLE_NAME = "uf_UPM_log_dt1";

    public ProjectManagerLog() {

    }

    /**
     * 是否需要初始化赋值
     *
     * @param useDefaults
     */
    public ProjectManagerLog(boolean useDefaults) {
        if (useDefaults) {
            this.sync_start_date = TimeUtil.getCurrentDateString();
        }
    }
}
