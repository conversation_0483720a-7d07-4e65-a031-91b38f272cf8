package com.engine.zhongzhi2.tpw.job.bean;

import lombok.Data;
/**
    * @FileName KpyjbBean
    * @Description 开票
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/8/2
    */
@Data
public class KpyjbBean {
    private String id;
    private String htbh;         // 合同编号
    private String khmc;         // 客户名称
    private String htsxrq;       // 合同生效
    private String htmc;         // 合同名称
    private String htly;         // 合同来源
    private String htfwlx;       // 合同服务类型
    private String htzyyjgsbm;   // 合同主要业绩归属部门
    private String htzyyjhsbm;   // 合同主要业绩核算部门
    private String hte;          // 合同额
    private String gjq;          // 共几期
    private String ykjq;         // 已开几期
    private String zfjd;         // 支付阶段
    private String zfsjdtj;      // 支付时间点/条件
    private String zfbl;         // 支付比例
    private String fpje;         // 发票金额
    private String kpzt;         // 开票状态
    private String yjkprq;       // 预计开票
    private String sjkprq;       // 实际开票
    private String kpjhhkzt;     // 开票计划回款状态
    private String fpbh;         // 发票编号
    private String ykpxj;        // 合同已开票小计
    private String yhkxj;        // 合同已回款小计
    private String yszk;         // 合同应收账款
    private String hzcq;         // 合同含中长期
    private String cwgdrq;       // 财务勾兑
    private String khsjhkrq;     // 客户实际回款
    private String zxhk;         // 最新回款
    private String yszkzq;       // 应收账款账期
    private String yjgzbm;       // 开票业绩归属部门
    private String yjgzfb;       // 开票业绩归属分部
    private String yjhsbm;       // 开票业绩核算部门
    private String kpyjje;       // 开票业绩金额
    private String yhkxj1;       // 开票已回款小计
    private String yszk1;        // 开票应收账款
    private String hzcq1;        // 开票含中长期
    private String fptt;         // 发票抬头
    private String xmjl;         // 项目经理
    private String htcjr;        // 合同创建人
    private String htgxry;       // 合同共享人员
    private String fpbhsje;      // 发票不含税金额
    private String fpszgs;       // 发票所属公司
    private String nd;           // 年度
    private String htsupdepid;   // 合同上级部门1
    private String kpsupdepid;   // 开票上级部门1
    private String ny;           // 年月
    private String htsupdepid2;  // 合同上级部门2
    private String kpsupdepid2;  // 开票上级部门2
    private String kpny;         // 开票年月
    private String hkny;         // 回款年月
    private String sfzf;         // 是否作废
    private String kpnd;         // 开票年度
    private String hknd;         // 回款年度
    private String zffpbh;       // 红冲作废发票编号
    private String htgx;         // 合同关系

}
