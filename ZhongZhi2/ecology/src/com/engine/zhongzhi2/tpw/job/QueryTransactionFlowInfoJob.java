package com.engine.zhongzhi2.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DateUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @FileName QueryTransactionFlowInfoJob
 * @Description 定时调用交易流水信息查询接口更新数据
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/22
 */
@Getter
@Setter
public class QueryTransactionFlowInfoJob extends BaseCronJob {
    private BaseBean bb = new BaseBean();
    /**
     * 事件标识名
     */
    private String eventName;
    /**
     * 开始日期
     */
    private String startTransDate;
    /**
     * 结束日期
     */
    private String endTransDate;
    /**
     * 要插入的表名
     */
    private String tableName;

    @Override
    public void execute() {
        try {
            String errMsg = "";
            bb.writeLog("QueryTransactionFlowInfoJob----START");
            bb.writeLog("requestParams:eventName===>" + eventName);
            bb.writeLog("requestParams:startTransDate===>" + startTransDate);
            bb.writeLog("requestParams:endTransDate===>" + endTransDate);
            JSONObject params = new JSONObject();
            if (StringUtils.isNotBlank(tableName) && StringUtils.isNotBlank(eventName)) {
                long currentTimeMillis = System.currentTimeMillis();
                if (StringUtils.isNotBlank(startTransDate) && StringUtils.isNotBlank(endTransDate)) {
                    params.put("startTransDate", startTransDate);
                    params.put("endTransDate", endTransDate);
                    params.put("currentTimeMillis", currentTimeMillis);
                } else {
                    params.put("startTransDate", TimeUtil.dateAdd(DateUtil.today(), -1));
                    params.put("endTransDate", DateUtil.today());
                    params.put("currentTimeMillis", currentTimeMillis);
                }
                errMsg = updateData(params);
            }

            if (StringUtils.isNotBlank(errMsg)) {
                bb.writeLog("插入-uf_jhdrmb数据失败，失败信息" + errMsg);
            } else {
                bb.writeLog("插入-uf_jhdrmb数据成功");
            }

        } catch (Exception e) {
            bb.writeLog("定时调用交易流水信息查询接口更新数据请求异常：" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("QueryTransactionFlowInfoJob----end");
    }

    private String updateData(JSONObject params) {
        String errMsg = "";
        String NextTag = "1";
        bb.writeLog("插入-uf_jhdrmb数据---start");
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        //此处是查询存在uf_jhdrmb中的交易流水号的唯一标识，后续有待确定，这里先默认取得是"TransInfoNo": "流水号唯一标识",
        recordSet.execute("select wbid from " + tableName);
        ArrayList<String> wbids = new ArrayList<>();
        while (recordSet.next()) {
            wbids.add(recordSet.getString("wbid"));
        }
        bb.writeLog("uf_jhdrmb表中查询的唯一标识：:" + JSONObject.toJSONString(wbids));
        //查看公司内部账号
        recordSet.execute("select nbzh from uf_gsnbzh");
        ArrayList<String> nbzhs = new ArrayList<>();
        while (recordSet.next()) {
            nbzhs.add(recordSet.getString("nbzh"));
        }
        bb.writeLog("公司内部账号信息:" + JSONObject.toJSONString(nbzhs));

        while (!"0".equals(NextTag) && StringUtils.isBlank(errMsg)) {
            params.put("NextTag", NextTag);
            EsbEventResult esbEventResult = EsbUtil.callEsbEvent(eventName, JSONObject.toJSONString(params));
            bb.writeLog("接口返回的esbEventResult数据:" + JSONArray.toJSONString(esbEventResult));
            JSONArray resultSet = null;
            //提取数据
            if (esbEventResult.isSuccess()) {
                JSONObject result = esbEventResult.getData();
                bb.writeLog("接口返回的result数据:" + JSONArray.toJSONString(result));
                if (result != null) {
                    String cmscloudStr = result.getString("Cmscloud");
                    JSONObject cmscloud = (JSONObject) JSONObject.parse(cmscloudStr);
                    if (cmscloud != null) {
                        JSONObject body = (JSONObject) cmscloud.get("Body");
                        bb.writeLog("接口返回的body数据:" + JSONArray.toJSONString(body));
                        if (body != null) {
                            JSONObject data = (JSONObject) body.get("Data");
                            bb.writeLog("接口返回的data数据:" + JSONArray.toJSONString(data));
                            if (data != null && "0000".equals(data.getString("ResultCode"))) {
                                resultSet = (JSONArray) data.get("ResultSet");
                                NextTag = Util.null2String(data.get("NextTag"));
                            }
                        }
                    }
                }
                bb.writeLog("接口返回的resultSet数据:" + JSONArray.toJSONString(resultSet));
                //做数据的过滤
                if (!resultSet.isEmpty()) {
                    for (int i = 0; i < resultSet.size(); i++) {
                        JSONObject flowInfo = (JSONObject) resultSet.get(i);
                        if ("1".equals(flowInfo.getString("TransDirection"))) {
                            resultSet.remove(i);
                            i--;
                        } else {
                            //本方账户号
                            String accountNo = flowInfo.getString("AccountNo");
                            //对方账户号
                            String oppAccountNo = flowInfo.getString("OppAccountNo");
                            if (!nbzhs.isEmpty() && nbzhs.contains(accountNo) && nbzhs.contains(oppAccountNo)) {
                                resultSet.remove(i);
                                i--;
                            } else if (StringUtils.isBlank(accountNo) || StringUtils.isBlank(oppAccountNo)) {
                                resultSet.remove(i);
                                i--;

                            } else {
                                if (!wbids.isEmpty() && wbids.contains(flowInfo.getString("TransInfoNo"))) {
                                    resultSet.remove(i);
                                    i--;
                                }
                            }
                        }
                    }
                }
                bb.writeLog("resultSet过滤后的数据:" + JSONArray.toJSONString(resultSet));

                if (!resultSet.isEmpty()) {
                    //拼接要插入到uf_jhdrmb中的数据对象
                    ModuleInsertBean moduleInsertBean = new ModuleInsertBean();
                    List<String> fields = createFields();
                    List<List<Object>> values = createValues(resultSet);
                    moduleInsertBean.setTableName(tableName);
                    moduleInsertBean.setCreatorId(1);
                    moduleInsertBean.setFields(fields);
                    moduleInsertBean.setValues(values);
                    int moduleId = ModuleDataUtil.getModuleIdByName(tableName);
                    moduleInsertBean.setModuleId(moduleId);
                    bb.writeLog("ModuleInsertBean:" + JSONObject.toJSONString(moduleInsertBean));
                    //数据插入
                    ModuleResult insert = ModuleDataUtil.insert(moduleInsertBean);
                    if (!insert.isSuccess()) {
                        errMsg = insert.getErroMsg();
                    }
                }
            } else {
                errMsg = esbEventResult.getErroMsg();
            }
        }
        bb.writeLog("更新-uf_jhdrmb数据----end");
        return errMsg;
    }

    private List<String> createFields() {
        ArrayList<String> fields = new ArrayList<>();
        //记账日期
        fields.add("wbid");
        fields.add("jyrq");
        fields.add("jysj");
        fields.add("pzzl");
        fields.add("pzh");
//        fields.add("jeout");
        fields.add("je");
        fields.add("ye");
        fields.add("chbz");
        fields.add("fkrmc");
        fields.add("fkrzh");
        fields.add("zy");
        fields.add("bz");
        fields.add("jylsh");
        fields.add("qylsh");
        fields.add("bfzh");
        fields.add("bfzhmc");
        fields.add("bfzhkhjg");

//        fields.add("kpsq");
//        fields.add("kpkhmc");
//        fields.add("fptt");
        fields.add("zt");
//        fields.add("ywht");
//        fields.add("hkje");
//        fields.add("sxf");
//        fields.add("yhkje");
//        fields.add("whkje");
//        fields.add("sqr");
        fields.add("gdzt");
        fields.add("szfx");
        return fields;
    }

    private List<List<Object>> createValues(JSONArray resultSet) {
        List<List<Object>> values = new ArrayList<>();
        for (int i = 0; i < resultSet.size(); i++) {
            ArrayList<Object> value = new ArrayList<>();
            JSONObject flowInfo = (JSONObject) resultSet.get(i);
            //唯一标识
            String transInfoNo = flowInfo.getString("TransInfoNo");
            value.add(transInfoNo);
            //交易日期
            String transTime = flowInfo.getString("TransTime");

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime dateTime = LocalDateTime.parse(transTime, formatter);

            String date = dateTime.toLocalDate().toString();
            String time = dateTime.toLocalTime().toString();
            value.add(date);
            if (StringUtils.isNotBlank(time)) {
                value.add(time.substring(0, 5));
            }
            //交易类型
            String transType = flowInfo.getString("TransType");
            value.add(transType);
            //记账流水号
            String accSerialNo = flowInfo.getString("AccSerialNo");
            value.add(accSerialNo);
            //交易金额
            String transAmount = flowInfo.getString("TransAmount");
            if (StringUtils.isNotBlank(transAmount)) {
                transAmount = transAmount.replace(",", "");
            }
            value.add(transAmount);
            //余额
            String transBalance = flowInfo.getString("TransBalance");
            if (StringUtils.isNotBlank(transBalance)) {
                transBalance = transBalance.replace(",", "");
            }
            value.add(transBalance);
            value.add("钞");
            //对方账户名
            String oppAccountName = flowInfo.getString("OppAccountName");
            value.add(oppAccountName);
            //对方账号
            String oppAccountNo = flowInfo.getString("OppAccountNo");
            value.add(oppAccountNo);
            //摘要
            String memo = flowInfo.getString("Memo");
            value.add(memo);
            //附言
            String remark = flowInfo.getString("Remark");
            value.add(remark);
            //银行流水号
            String bankTransNo = flowInfo.getString("BankTransNo");
            value.add(bankTransNo);
            value.add(accSerialNo);
            //本方账户号码
            String accountNo = flowInfo.getString("AccountNo");
            value.add(accountNo);
            //本方账户名称
            String accountName = flowInfo.getString("AccountName");
            value.add(accountName);
            //开户行名称
            String branchName = flowInfo.getString("BranchName");
            value.add(branchName);
            value.add(0);
            value.add(0);
            value.add(2);
            values.add(value);
        }
        return values;
    }

}
