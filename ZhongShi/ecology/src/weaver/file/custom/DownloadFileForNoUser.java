package weaver.file.custom;

import com.api.doc.detail.util.DocDownloadCheckUtil;
import com.engine.doc.util.WaterMarkUtil;
import weaver.conn.RecordSet;
import weaver.email.service.MailFilePreviewService;
import weaver.file.ImageFileManager;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.mobile.plugin.ecology.service.HrmResourceService;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Map;

/**
 * 下载文件无需人员校验
 * 需要配置weaver_session_filter.properties 的rest api地址，这里为 /api/doc/custom/getDownloadPath?docid=?，该接口为了返回下面的接口地址并直接调用
 * 接口地址：ip://weaver/weaver.file.custom.DownloadFileForNoUser?download=1&fileid={fileid}
 * fileid 为 sql:select IMAGEFILEID,IMAGEFILENAME,docid from docimagefile where docid  = ? 结果的IMAGEFILEID
 * 注意：有些安全包会拦截，需要在以下文件添加白名单：
 * 在WEB-INF下的weaver_security_rules.xml中在excepts和skip-any-check-list 节点下添加url配置： 改完需要重启OA服务
 * /weaver/weaver.file.custom.DownloadFileForNoUser
 */
public class DownloadFileForNoUser extends HttpServlet {

    private static final int BUFFER_SIZE = 1 * 1024 * 1024;

    public void doGet(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException {
        BaseBean baseBean = new BaseBean();
        User user = new User(1);
        String uuid = Util.null2String(req.getParameter("uuid"));
        baseBean.writeLog("weaver.file.FileDownloadFornews,uuid:" + uuid);
        String currentTime = TimeUtil.getCurrentTimeString();
        String tempTime = TimeUtil.timeAdd(currentTime, -5 * 60);
        RecordSet rs = new RecordSet();
        if (user == null && uuid.length() > 0) {
            rs.executeQuery("select userid from odoc_usercheck where uuid=? and dateTime>? ", uuid, tempTime);
            if (rs.next()) {
                int userid = rs.getInt("userid");
                baseBean.writeLog("weaver.file.FileDownloadFornews,userid:" + userid);
                user = new HrmResourceService().getUserById(userid);
            }
        }
        if (uuid.length() > 0) {
            rs.executeUpdate("delete from odoc_usercheck where uuid=?", uuid);
            rs.executeUpdate("delete from odoc_usercheck where dateTime<?", tempTime);
        }
        if (user == null) {
            res.sendRedirect(weaver.general.GCONST.getContextPath() + "/login/Login.jsp");
            baseBean.writeLog("weaver.file.FileDownloadFornews,FileDownloadFornews 没有获取到用户信息");
            return;
        }
        String type = Util.null2String(req.getParameter("type"));
        if ("showMould".equals(type) || "editMould".equals(type) || "printMould".equals(type)) {
            downloadMould(req, res, type);
        } else if ("document".equals(type)) {
            downloadForDocument(req, res, user);
        } else if ("ofd".equals(type)) {
            downloadOfd(req, res);
        } else if ("Email".equals(type)) {
            downloadForEmaill(req, res);
        } else {
            downloadForDocument(req, res, user);
        }
    }

    public void doPost(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException {
        this.doGet(req, res);
    }


    /**
     * 文档下载
     *
     * @param req
     * @param res
     */
    private void downloadForDocument(HttpServletRequest req, HttpServletResponse res, User user) {
        //int fileid = Util.getIntValue(req.getParameter("fileid"));

        int fileid = Util.getIntValue(DocDownloadCheckUtil.getDownloadfileid(req), -1);
        int isofficeview = Util.getIntValue(Util.null2String(req.getParameter("isofficeview")), -1);
        new BaseBean().writeLog("---------- FileDownloadForNews ---------fileid=" + fileid);
        if (fileid <= 0) return;
        InputStream imagefile = null;

        try {
            String agent = req.getHeader("user-agent");
            ImageFileManager ifm = new ImageFileManager();
            ifm.getImageFileInfoById(fileid);
            imagefile = ifm.getInputStream();
            String filename = ifm.getImageFileName();
            String contenttype = "application/x-download";
            if (filename.toLowerCase().endsWith(".gif")) {
                contenttype = "image/gif";
                res.addHeader("Cache-Control", "private, max-age=8640000");
            } else if (filename.toLowerCase().endsWith(".png")) {
                contenttype = "image/png";
                res.addHeader("Cache-Control", "private, max-age=8640000");
            } else if (filename.toLowerCase().endsWith(".jpg") || filename.toLowerCase().endsWith(".jpeg")) {
                contenttype = "image/jpg";
                res.addHeader("Cache-Control", "private, max-age=8640000");
            } else if (filename.toLowerCase().endsWith(".bmp")) {
                contenttype = "image/bmp";
                res.addHeader("Cache-Control", "private, max-age=8640000");
            }
            if (isofficeview == 1) {
                String extName = filename.contains(".") ? filename.substring(filename.lastIndexOf(".") + 1) : "";
                Map<String, Object> secWmSetMap = WaterMarkUtil.getCategoryWmSet(fileid + "");
                if ("1".equals(secWmSetMap.get(WaterMarkUtil.SECCATEGORYVIEW)) && "1".equals(secWmSetMap.get(WaterMarkUtil.SECCATEGORYWMISOPEN)) && "0".equals(secWmSetMap.get(WaterMarkUtil.WATERCONTENTISNULL))) {
                    imagefile = WaterMarkUtil.takefileWater(imagefile, user, filename, fileid, extName, WaterMarkUtil.MOULDDOC);
                }
            }
            ServletOutputStream out = res.getOutputStream();
            res.setContentType(contenttype);
            if ((agent.contains("Firefox") || agent.contains(" Chrome") || agent.contains("Safari")) && !agent.contains("Edge")) {
                res.setHeader("content-disposition", "attachment; filename*=UTF-8''" + URLEncoder.encode(filename.replaceAll("<", "").replaceAll(">", "").replaceAll("&lt;", "").replaceAll("&gt;", ""), "UTF-8").replaceAll("\\+", "%20").replaceAll("%28", "(").replaceAll("%29", ")"));
            } else {
                res.setHeader("content-disposition", "attachment; filename=\"" +
                        URLEncoder.encode(filename.replaceAll("<", "").replaceAll(">", "").replaceAll("&lt;", "").replaceAll("&gt;", ""), "UTF-8").replaceAll("\\+", "%20").replaceAll("%28", "(").replaceAll("%29", ")") + "\"");
            }

            int byteread;
            byte data[] = new byte[1024];
            while ((byteread = imagefile.read(data)) != -1) {
                out.write(data, 0, byteread);
                out.flush();
            }
            out.flush();
            out.close();
        } catch (Exception e) {
            new BaseBean().writeLog(e);
        } finally {
            if (imagefile != null) {
                try {
                    imagefile.close();
                } catch (Exception e) {
                    new BaseBean().writeLog(e);
                }
            }
        }
    }

    //模板下载
    private void downloadMould(HttpServletRequest req, HttpServletResponse res, String type) {
        //int mouldid = Util.getIntValue(req.getParameter("fileid"));
        int mouldid = Util.getIntValue(DocDownloadCheckUtil.getDownloadfileid(req), -1);
        if (mouldid <= 0) return;
        String mouldName = "";
        String mouldPath = "";
        String tableName = "";
        String mouldtype = "";
        int imagefileid = 0;
        if ("showMould".equals(type)) {
            tableName = "docMould";
        } else if ("printMould".equals(type)) {
            tableName = "OdocPrintMould";
        } else {
            tableName = "DocMouldFile";
        }
        RecordSet rs = new RecordSet();
        rs.executeQuery("select mouldName,mouldPath,imagefileid,mouldtype from " + tableName + " where id=?", mouldid);
        if (rs.next()) {
            mouldName = rs.getString("mouldName");
            mouldPath = rs.getString("mouldPath");
            imagefileid = rs.getInt("imagefileid");
            mouldtype = rs.getString("mouldtype");
        } else {
            return;
        }

        if ("printMould".equals(type) && "2".equals(mouldtype)) {
            mouldName += ".docx";
        } else if ("2".equals(mouldtype)) {
            mouldName += ".doc";
        } else if ("3".equals(mouldtype)) {
            mouldName += ".xls";
        } else if ("4".equals(mouldtype)) {
            mouldName += ".wps";
        }

        InputStream is = null;
        try {
            if (imagefileid > 0) {
                is = ImageFileManager.getInputStreamById(imagefileid);
            } else {
                is = new BufferedInputStream(new FileInputStream(mouldPath));
            }
            ServletOutputStream out = res.getOutputStream();
            res.setContentType("");
            res.setHeader("content-disposition", "attachment; filename=" + new String(mouldName.replaceAll("<", "").replaceAll(">", "").replaceAll("&lt;", "").replaceAll("&gt;", "").getBytes("UTF-8"), "ISO-8859-1"));
            int byteread;
            byte data[] = new byte[1024];
            while ((byteread = is.read(data)) != -1) {
                out.write(data, 0, byteread);
                out.flush();
            }
            out.flush();
            out.close();
        } catch (Exception e) {
            new BaseBean().writeLog(e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception e) {
                    new BaseBean().writeLog(e);
                }
            }
        }
    }

    /**
     * ofd下载逻辑
     *
     * @param req
     * @param res
     */
    private void downloadOfd(HttpServletRequest req, HttpServletResponse res) {
        //本例中传入的url是类似  http://ip:port/DownloadServlet?docId=2&imageFileId=5
        int docId = Util.getIntValue(req.getParameter("docId"), -1);
        int imageFileId = Util.getIntValue(req.getParameter("imageFileId"), -1);
        new BaseBean().writeLog("DownloadServlet docId=" + docId + "###imageFileId=" + imageFileId);
        InputStream is = null;
        BufferedOutputStream bos = null;
        try {
            ImageFileManager imageFileManager = new ImageFileManager();
            imageFileManager.getImageFileInfoById(imageFileId);
            res.setStatus(200);
            res.setContentType("APPLICATION/OCTET-STREAM; charset=UTF-8");
            res.setHeader("Content-Disposition", "attachment; filename=" + new String(imageFileManager.getImageFileName().replaceAll("<", "").replaceAll(">", "").replaceAll("&lt;", "").replaceAll("&gt;", "").getBytes("UTF-8"), "ISO-8859-1") + "");
            res.setContentType("application/ofd");
            is = imageFileManager.getInputStream();
            bos = new BufferedOutputStream(res.getOutputStream());
            byte[] buf = new byte[BUFFER_SIZE];
            int len = -1;
            while ((len = is.read(buf)) != -1) {
                bos.write(buf, 0, len);
            }
            bos.flush();
        } catch (IOException exp) {
            exp.printStackTrace();
        } finally {
            if (is != null)
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            if (bos != null)
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
        }
    }


    private void downloadForEmaill(HttpServletRequest req, HttpServletResponse res) {
        //int fileid = Util.getIntValue(req.getParameter("fileid"));
        int fileid = Util.getIntValue(DocDownloadCheckUtil.getDownloadfileid(req), -1);
        MailFilePreviewService mfps = new MailFilePreviewService();
        InputStream imagefile = mfps.getInputStreamByMailFileId(fileid + "");

        String filename = Util.null2String(mfps.getFileNameOnly(fileid + ""));
        ServletOutputStream out = null;
        if (imagefile != null) {
            try {
                out = res.getOutputStream();
                res.setContentType("application/octet-stream");
                res.setHeader("content-disposition", "attachment; filename=\"" +
                        URLEncoder.encode(filename.replaceAll("<", "").replaceAll(">", "").replaceAll("&lt;", "").replaceAll("&gt;", ""), "UTF-8").replaceAll("\\+", "%20").replaceAll("%28", "(").replaceAll("%29", ")") + "\"");
                int byteread;
                byte data[] = new byte[1024];
                while ((byteread = imagefile.read(data)) != -1) {
                    out.write(data, 0, byteread);
                    out.flush();
                }
            } catch (Exception e) {
                new BaseBean().writeLog(e);
            } finally {
                if (out != null) {
                    try {
                        out.close();
                    } catch (Exception e) {
                        new BaseBean().writeLog(e);
                    }
                }
                if (imagefile != null) {
                    try {
                        imagefile.close();
                    } catch (Exception e) {
                        new BaseBean().writeLog(e);
                    }
                }
            }
        }
    }

}
