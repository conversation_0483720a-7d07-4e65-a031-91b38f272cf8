package com.engine.interfaces.gyl.workflow.projectTask.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.module.util.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.util.*;

/**
 * @FileName ArchiveTaskAction.java
 * @Description 归档流程，写入任务归档阶段明细数据
 * <AUTHOR>
 * @Version v1.00
 * @Date 2022/10/31 10:30
 */
public class ArchiveTaskAction extends BaseBean implements Action {

    /**
     * 插入建模的字段名,英文逗号隔开
     */
    private static final String insertFields = "wjmc,scr,scrq,lc,ah,gdwj,mainid";
    //Action配置参数--开始
    /**
     * 建模主表名
     */
    private String moduleMainTable;
    /**
     * 建模明细表名
     */
    private String moduleDetailTable;
    /**
     * 建模包件信息表名
     */
    private String moduleBaoJianTable;
    /**
     * 【建模-主表】暗号字段名
     */
    private String moduleAnHaoField;
    /**
     * 【建模-项目】主表合同流程字段名
     */
    private String moduleContractField;

    /**
     * 【表单-主表】暗号字段名
     */
    private String workflowAnHaoField;

    /**
     * 归档流程的文档字段，多个英文逗号隔开
     */
    private String archiveWorkDocFields;
    /**
     * 合同流程的文档字段，多个英文逗号隔开
     */
    private String contactWorkDocFields;
    /**
     * 文件流转流程的文档字段，多个英文逗号隔开
     */
    private String fileworkDocFields;
    /**
     * 合同流程表名
     */
    private String contactWorkTable;

    /**
     * 文件流转流程表名
     */
    private String fileWorkTable;

    //Action配置参数--结束


    @Override
    public String execute(RequestInfo requestInfo) {

        writeLog(this.getClass().getName() + "---START");
        String erroMsg;
        JSONObject joTask;
        //流程id
        String workflowId = requestInfo.getWorkflowid();
        //请求id
        String requestId = requestInfo.getRequestid();
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();
        //是否为单据(1为是)
        int isBill = rm.getIsbill();
        //获取数据库主表名(如果 不为1，流程数据是存在"workflow_form"表中)
        String tableName = isBill == 1 ? rm.getBillTableName() : "workflow_form";
        //获取主表信息
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        JSONObject joMainData = new JSONObject();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            joMainData.put(str, value);
        }
        String docIds, wfReqId;
        Map<String, String> mapWork;
        try {
            writeLog("requestId:" + requestId);
            writeLog("workflowId:" + workflowId);
            writeLog("tableName:" + tableName);
            //step 1: 校验参数必填
            erroMsg = checkParams(new String[]{"moduleMainTable",
                    "moduleDetailTable",
                    "moduleAnHaoField",
                    "workflowAnHaoField",
                    "insertFields"});
            if (erroMsg.isEmpty()) {
                String workflowAnHao = Util.null2String(joMainData.get(workflowAnHaoField));
                //step 2 : 根据案号查询任务建模的数据id
                joTask = getModuleInfoByAnhao(Util.null2String(joMainData.get(workflowAnHaoField)));
                writeLog("joTask :" + joTask);
                if (joTask.getIntValue("id") > 0) {
                    // step 3 : 将流程数据写入建模
                    // step 3-1: 获取合同流程的文档数据
                    mapWork = getContractWorkDoc(joTask);
                    writeLog("合同mapWork：" + mapWork);
                    docIds = mapWork.getOrDefault("docids", "");
                    wfReqId = mapWork.getOrDefault("requestid", "");
                    if (!docIds.isEmpty()) {
                        //插入明细
                        insertTaskDetail(docIds, wfReqId, workflowAnHao, joTask.getIntValue("id"));
                    }
                    //step 3-2: 获取文件流转流程的文档数据
                    mapWork = getFileWorkDoc(workflowAnHao);
                    writeLog("文件流程mapWork：" + mapWork);
                    docIds = mapWork.getOrDefault("docids", "");
                    wfReqId = mapWork.getOrDefault("requestid", "");
                    if (!docIds.isEmpty()) {
                        //插入明细
                        insertTaskDetail(docIds, wfReqId, workflowAnHao, joTask.getIntValue("id"));
                    }

                    //step 3-3: 获取归档流程本身的文档数据
                    mapWork = getArchiveWorkDoc(joMainData, requestId);
                    writeLog("归档流程mapWork：" + mapWork);
                    docIds = mapWork.getOrDefault("docids", "");
                    wfReqId = mapWork.getOrDefault("requestid", "");
                    if (!docIds.isEmpty()) {
                        //插入明细
                        insertTaskDetail(docIds, wfReqId, workflowAnHao, joTask.getIntValue("id"));
                    }
                    //step 3-4: 获取任务台账包件信息
                    mapWork = getBjDoc(joTask.getIntValue("id"));
                    writeLog("台账包件信息mapWork：" + mapWork);
                    docIds = mapWork.getOrDefault("docids", "");
                    wfReqId = mapWork.getOrDefault("requestid", "");
                    if (!docIds.isEmpty()) {
                        //插入明细
                        insertTaskDetail(docIds, wfReqId, workflowAnHao, joTask.getIntValue("id"));
                    }

                } else {
                    erroMsg = "未查询到案号对应的任务建模数据，请检查！";
                }
            }

            //根据docid获取文件名称
            //插入建模明细表
        } catch (Exception e) {
            e.printStackTrace();
            erroMsg = Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            return Action.SUCCESS;
        } else {
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent(erroMsg);
            return Action.FAILURE_AND_CONTINUE;
        }

    }

    /**
     * 获取合同流程的文档docid
     *
     * @return
     */
    private Map<String, String> getContractWorkDoc(JSONObject joTask) {
        RecordSet rs;
        String sql;

        Map<String, String> result = new HashMap<>(2);
        StringBuilder sb = new StringBuilder();
        String contractReq = Util.null2String(joTask.get("contract"));
        sql = "select " + contactWorkDocFields + " from " + contactWorkTable + " where requestid = ? ";
        writeLog("getContractDoc sql:" + sql);
        rs = new RecordSet();
        rs.executeQuery(sql, contractReq);
        if (rs.next()) {
            //将所有文档字段的docid拼接
            for (String str : contactWorkDocFields.split(CommonCst.COMMA_EN)) {
                if (!Util.null2String(rs.getString(str)).isEmpty()) {
                    sb.append(Util.null2String(rs.getString(str))).append(CommonCst.COMMA_EN);
                }
            }
        }
        if (!sb.toString().isEmpty()) {
            result.put("docids", sb.substring(0, sb.length() - 1));
            result.put("requestid", contractReq);
        }
        return result;
    }

    /**
     * 获取文件流程的文档docid
     *
     * @return
     */
    private Map<String, String> getFileWorkDoc(String workflowAnHao) {
        RecordSet rs;
        String sql;
        Map<String, String> result = new HashMap<>(2);
        StringBuilder sb = new StringBuilder();
        String requestid = "";
        sql = "select " + fileworkDocFields + ",requestid from " + fileWorkTable + " where " + workflowAnHaoField + " = ? ";
        writeLog("getFileWorkDoc sql:" + sql);
        rs = new RecordSet();
        rs.executeQuery(sql, workflowAnHao);
        while (rs.next()) {
            //将所有文档字段的docid拼接
            for (String str : fileworkDocFields.split(CommonCst.COMMA_EN)) {
                if (!Util.null2String(rs.getString(str)).isEmpty()) {
                    sb.append(Util.null2String(rs.getString(str))).append(CommonCst.COMMA_EN);
                }
            }
            requestid = Util.null2String(rs.getString("requestid"));
        }
        if (!sb.toString().isEmpty()) {
            result.put("docids", sb.substring(0, sb.length() - 1));
            result.put("requestid", requestid);
        }
        return result;
    }

    /**
     * 获取归档流程的文档docid
     *
     * @return
     */
    private Map<String, String> getArchiveWorkDoc(JSONObject joMainData, String requestId) {
        Map<String, String> result = new HashMap<>(2);
        StringBuilder sb = new StringBuilder();
        for (String str : archiveWorkDocFields.split(CommonCst.COMMA_EN)) {
            if (!Util.null2String(joMainData.get(str)).isEmpty()) {
                sb.append(Util.null2String(joMainData.get(str))).append(CommonCst.COMMA_EN);
            }
        }
        if (!sb.toString().isEmpty()) {
            result.put("docids", sb.substring(0, sb.length() - 1));
            result.put("requestid", requestId);
        }
        return result;
    }


    /**
     * 获取任务包件台账的文档docid
     *
     * @return
     */
    private Map<String, String> getBjDoc(int mainid) {
        RecordSet rs;
        String sql;
        Map<String, String> result = new HashMap<>(2);
        StringBuilder sb = new StringBuilder();
        sql = "select bsm from " + moduleBaoJianTable + " where mainid = ? ";
        writeLog("getBjDoc sql:" + sql);
        rs = new RecordSet();
        rs.executeQuery(sql, mainid);
        while (rs.next()) {
            //将所有文档字段的docid拼接
            sb.append(Util.null2String(rs.getString("bsm"))).append(CommonCst.COMMA_EN);
        }
        if (!sb.toString().isEmpty()) {
            result.put("docids", sb.substring(0, sb.length() - 1));
            result.put("requestid", "");
        }
        return result;
    }


    /**
     * 获取文档
     *
     * @param docids
     * @return
     */
    private Map<String, String> getAllDocFileName(String docids) {
        String result = "";
        RecordSet rs;
        String sql;
        Map<String, String> map = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        sql = "select IMAGEFILEID,IMAGEFILENAME,OPERATEDATE,OPERATEUSERID from docimagefile where docid in (" + docids + ")";
        rs = new RecordSet();
        rs.executeQuery(sql);
        while (rs.next()) {
            sb.append(Util.null2String(rs.getString("IMAGEFILENAME"))).append(CommonCst.COMMA_EN);
            map.put("userId", Util.null2String(rs.getString("OPERATEUSERID")));
            map.put("uploadDate", Util.null2String(rs.getString("OPERATEDATE")));
        }
        if (!sb.toString().isEmpty()) {
            result = sb.substring(0, sb.length() - 1);
            map.put("fileNames", result);
        }
        return map;
    }

    /**
     * 根据案号查询建模任务
     *
     * @param anHao
     * @return
     */
    private JSONObject getModuleInfoByAnhao(String anHao) {
        RecordSet rs;
        String sql;
        JSONObject joTask;

        joTask = new JSONObject();
        sql = "select a.id,b." + moduleContractField + " as contract from " + moduleMainTable + " a " +
                " left join uf_xmxx b on (a." + moduleAnHaoField + " = b.id)" +
                "  where a." + moduleAnHaoField + " = ?";
        rs = new RecordSet();
        writeLog("getModuleInfoByAnhao sql:" + sql);
        rs.executeQuery(sql, anHao);
        if (rs.next()) {
            //任务主表id
            joTask.put("id", rs.getInt("id"));
            //合同流程id
            joTask.put("contract", Util.null2String(rs.getString("contract")));
        }
        return joTask;
    }

    /**
     * 校验参数必填
     *
     * @param params
     */
    private String checkParams(String[] params) {
        String erroMsg = "";
        for (String str : params) {
            if (str == null || Util.null2String(str).isEmpty()) {
                erroMsg = str + "参数未配置，请检查！";
                break;
            }
        }
        return erroMsg;

    }

    /**
     * 插入任务明细数据
     *
     * @param docIds
     * @param requestid
     * @param anHaoValue
     * @param mainid
     */
    private void insertTaskDetail(String docIds, String requestid, String anHaoValue, int mainid) {

        List<Object> values = new ArrayList<>();
        Object value = null;
        Map<String, String> mapDoc = getAllDocFileName(docIds);
        for (String field : insertFields.split(CommonCst.COMMA_EN)) {
            //文件名
            if ("wjmc".equals(field)) {
                value = mapDoc.getOrDefault("fileNames", "");
            }
            //上传人
            if ("scr".equals(field)) {
                value = mapDoc.getOrDefault("userId", "");
            }
            //上传日期
            if ("scrq".equals(field)) {
                value = mapDoc.getOrDefault("uploadDate", "");
            }
            //流程
            if ("lc".equals(field)) {
                value = requestid;
            }
            //案号
            if ("ah".equals(field)) {
                value = anHaoValue;
            }
            //归档文件
            if ("gdwj".equals(field)) {
                value = docIds;
            }
            //主表id
            if ("mainid".equals(field)) {
                value = mainid;
            }
            values.add(value);
        }
        writeLog("insertTaskDetail insertFields:" + insertFields);
        writeLog("insertTaskDetail values:" + values);
        InsertModuleUtil.DetailModuleInsert(moduleDetailTable, insertFields.split(CommonCst.COMMA_EN), values);

    }

    public String getModuleMainTable() {
        return moduleMainTable;
    }

    public void setModuleMainTable(String moduleMainTable) {
        this.moduleMainTable = moduleMainTable;
    }

    public String getModuleDetailTable() {
        return moduleDetailTable;
    }

    public void setModuleDetailTable(String moduleDetailTable) {
        this.moduleDetailTable = moduleDetailTable;
    }

    public String getModuleBaoJianTable() {
        return moduleBaoJianTable;
    }

    public void setModuleBaoJianTable(String moduleBaoJianTable) {
        this.moduleBaoJianTable = moduleBaoJianTable;
    }

    public String getModuleAnHaoField() {
        return moduleAnHaoField;
    }

    public void setModuleAnHaoField(String moduleAnHaoField) {
        this.moduleAnHaoField = moduleAnHaoField;
    }

    public String getModuleContractField() {
        return moduleContractField;
    }

    public void setModuleContractField(String moduleContractField) {
        this.moduleContractField = moduleContractField;
    }

    public String getWorkflowAnHaoField() {
        return workflowAnHaoField;
    }

    public void setWorkflowAnHaoField(String workflowAnHaoField) {
        this.workflowAnHaoField = workflowAnHaoField;
    }

    public String getArchiveWorkDocFields() {
        return archiveWorkDocFields;
    }

    public void setArchiveWorkDocFields(String archiveWorkDocFields) {
        this.archiveWorkDocFields = archiveWorkDocFields;
    }

    public String getContactWorkDocFields() {
        return contactWorkDocFields;
    }

    public void setContactWorkDocFields(String contactWorkDocFields) {
        this.contactWorkDocFields = contactWorkDocFields;
    }

    public String getFileworkDocFields() {
        return fileworkDocFields;
    }

    public void setFileworkDocFields(String fileworkDocFields) {
        this.fileworkDocFields = fileworkDocFields;
    }

    public String getContactWorkTable() {
        return contactWorkTable;
    }

    public void setContactWorkTable(String contactWorkTable) {
        this.contactWorkTable = contactWorkTable;
    }

    public String getFileWorkTable() {
        return fileWorkTable;
    }

    public void setFileWorkTable(String fileWorkTable) {
        this.fileWorkTable = fileWorkTable;
    }
}
