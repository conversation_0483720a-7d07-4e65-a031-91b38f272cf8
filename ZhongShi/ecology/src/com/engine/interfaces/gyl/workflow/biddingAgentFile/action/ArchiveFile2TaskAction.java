package com.engine.interfaces.gyl.workflow.biddingAgentFile.action;

import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @FileName ArchiveFile2TaskAction.java
 * @Description 写入文件到项目任务的归档阶段
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/8
 */
@Getter
@Setter
public class ArchiveFile2TaskAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 建模表名-项目任务
     */
    private String tableName_task;
    /**
     * 项目任务的归档阶段表的序列号，1,2,3...
     */
    private String tableIndex_archive;
    /**
     * 流程主表字段名-案号
     */
    private String wfFieldName_ah;
    /**
     * 建模项目任务主表字段名-案号
     */
    private String modFieldName_ah;
    /**
     * 流程主表字段名-归档文件
     */
    private String wfFieldName_file;
    /**
     * 流程主表字段名-流转类型
     */
    private String wfFieldName_lzlx;
    /**
     * 需要归档的流转类型配置，多个逗号隔开
     */
    private String valid_lzlx;

    //---Action参数---
    /**
     * 项目-归档阶段明细表表字段
     * 文件名称，上传人，上传日期，流程，案号，归档文件,流转类型,主表id
     */
    private static final String[] ARCHIVE_TABLE_FIELDS = new String[]{"wjmc", "scr", "scrq", "lc", "ah", "gdwj", "lzlx", "mainid"};

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg = "";
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //主表数据
            Map<String, String> mainData = actionInfo.getMainData();
            if (StringUtils.isNotBlank(valid_lzlx)) {
                List<String> validLzlxArray = Arrays.asList(valid_lzlx.split(CommonCst.COMMA_EN));
                String lzlx = Util.null2String(mainData.get(wfFieldName_lzlx));
                if (!validLzlxArray.contains(lzlx)) {
                    writeLog("配置的可用流程类型为：" + valid_lzlx + ",当前流程流转类型为：" + lzlx + ",跳过写入归档文件");
                } else {
                    //案号对应的项目任务主表id
                    int taskId = getModuleIdByAh(mainData.get(wfFieldName_ah));
                    writeLog("查询到案号对一个的任务台账id：" + taskId);
                    if (taskId > 0) {
                        //任务归档明细表名
                        String archiveTableName = tableName_task + "_dt" + tableIndex_archive;
                        writeLog("archiveTableName：" + archiveTableName);
                        //获取所有的文件信息
                        String docIds = mainData.get(wfFieldName_file);
                        List<DocFileInfo> docFileInfoList = DocUtil.getDocFileInfoByDocIds(docIds);
                        writeLog("docFileInfoList：" + docFileInfoList);
                        String allFileName = getAllFileNames(docFileInfoList);

                        //组装插入bean
                        List<List<Object>> values = new ArrayList<>();
                        List<Object> value = new ArrayList<>();
                        // 文件名称，上传人，上传日期，流程，案号，归档文件,流转类型,主表id
                        value.add(allFileName);
                        value.add(docFileInfoList.get(0).getOperateUserId());
                        value.add(docFileInfoList.get(0).getOperateDate());
                        value.add(actionInfo.getRequestId());
                        value.add(mainData.get(wfFieldName_ah));
                        value.add(docIds);
                        value.add(mainData.get(wfFieldName_lzlx));
                        value.add(taskId);
                        //添加到values
                        values.add(value);

                        //组装插入的数据
                        ModuleInsertBean mb = new ModuleInsertBean();
                        
                        mb.setTableName(archiveTableName)
                                .setFields(Arrays.asList(ARCHIVE_TABLE_FIELDS))
                                .setValues(values);
                        writeLog("mb detail:" + mb);
                        ModuleResult mr = ModuleDataUtil.insertDetail(mb);
                        if (mr.isSuccess()) {
                            errorMsg = mr.getErroMsg();
                        }
                    } else {
                        errorMsg = "根据案号未查询到对应的项目任务台账！";
                    }
                }
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        writeLog("errorMsg：" + errorMsg);
        writeLog(this.getClass().getName() + "---END");
        //默认无错误信息
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }


    /**
     * 根据案号查询建模任务id
     *
     * @param ah
     * @return
     */
    private int getModuleIdByAh(String ah) {
        RecordSet rs = new RecordSet();
        int id = -1;
        String sql = "select id from " + tableName_task + "  where " + modFieldName_ah + " = '" + ah + "'";
        writeLog("getModuleIdByAh sql:" + sql);
        rs.executeQuery(sql);
        if (rs.next()) {
            id = rs.getInt("id");
        }
        return id;
    }

    /**
     * 获取文件名称
     *
     * @param docFileInfoList
     * @return
     */
    private String getAllFileNames(List<DocFileInfo> docFileInfoList) {
        String result = "";
        StringBuilder sb = new StringBuilder();
        for (DocFileInfo info : docFileInfoList) {
            sb.append(info.getFileName()).append(",");
        }
        result = sb.toString();
        if (!result.isEmpty()) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }
}
