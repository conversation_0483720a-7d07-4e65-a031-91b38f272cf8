package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;


/**
 * 复制项目任务
 *
 * <AUTHOR>
 */
public class CopyProjectCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CopyProjectCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        erroMsg = "";
        projectColumns = new String[0];
        taskColumns = new String[0];
        taskDetailColumns = new String[0];
        projectMain = new JSONObject();
        taskMain = new JSONObject();
        taskDetail = new JSONArray();
        projectId = -1;
        taskId = -1;
        copyTimes = -1;
        projectModuleId = -1;
        taskModuleId = -1;
        newProjectId = -1;
        newTaskId = 1;

    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 数据库操作类（不带事务）
     */
    private RecordSet rs;
    /**
     * RecordSet返回成功失败值
     */
    private boolean rsFlag;
    /**
     * 错误信息
     */
    private String erroMsg;

    private String[] projectColumns;
    private String[] taskColumns;
    private String[] taskDetailColumns;

    private JSONObject projectMain;
    private JSONObject taskMain;
    private JSONArray taskDetail;
    private int projectId, taskId, copyTimes, projectModuleId, taskModuleId, newProjectId, newTaskId;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            bb.writeLog("params：" + params.toString());
            //校验参数
            //项目id
            if (Util.null2String(params.get("projectId")).isEmpty()) {
                erroMsg = "projectId为空";
            }
            //任务id
            if (Util.null2String(params.get("taskId")).isEmpty()) {
                erroMsg = "taskId为空";
            }
            //项目复制的次数
            if (Util.null2String(params.get("copyTimes")).isEmpty()) {
                erroMsg = "copyTimes为空";
            }
            //项目建模id
            if (Util.null2String(params.get("projectModuleId")).isEmpty()) {
                erroMsg = "projectoduleId为空";
            }
            //任务建模id
            if (Util.null2String(params.get("taskModuleId")).isEmpty()) {
                erroMsg = "taskModuleId为空";
            }

            if (erroMsg.isEmpty()) {
                projectId = Integer.parseInt(Util.null2String(params.get("projectId")));
                taskId = Integer.parseInt(Util.null2String(params.get("taskId")));
                copyTimes = Integer.parseInt(Util.null2String(params.get("copyTimes")));
                projectModuleId = Integer.parseInt(Util.null2String(params.get("projectModuleId")));
                taskModuleId = Integer.parseInt(Util.null2String(params.get("taskModuleId")));
                //可能同时两个复制请求，会导致前台显示的次数不一致，后台做一次比对校验
                //查询当前已复制的次数，比对copyTimes，一致继续，不一致返回错误
                int queryCopyTimes = queryCopyTimes();
                if (copyTimes != queryCopyTimes) {
                    erroMsg = "当前项目已有" + queryCopyTimes + "次复制记录，请重新点击复制";
                }
                if (erroMsg.isEmpty()) {
                    //获取表信息
                    getProjectInfo();
                    getTaskMainInfo();
                    getTaskDetailInfo();
                    //新建项目主表信息
                    if (projectMain.isEmpty()) {
                        erroMsg = "查询项目主信息为为空";
                    }
                    if (taskMain.isEmpty()) {
                        erroMsg = "查询任务主信息为为空";
                    }
                    if (erroMsg.isEmpty()) {
                        //复制新的项目主信息
                        creatNewProject();
                        if (newProjectId == -1) {
                            erroMsg = "复制项目信息数据出错";
                        } else {
                            //复制新的任务主信息
                            creatNewTaskMain();
                            if (newTaskId == -1) {
                                erroMsg = "复制任务信息数据出错";
                            } else {
                                //复制新的任务明细
                                creatNewTaskDetail();
                                //更新新项目的任务数据id字段
                                updateProjectTaskId();

                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
            result.put("newProjectId", newProjectId);
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 获取项目的复制次数
     *
     * @return
     */
    private int queryCopyTimes() {
        String sql = "select count(1) as cnt from uf_xmxx where ysxm = ?";
        RecordSet rs = new RecordSet();
        rsFlag = rs.executeQuery(sql, projectId);
        if (!rsFlag) {
            return -1;
        } else {
            if (rs.next()) {
                return rs.getInt("cnt");
            } else {
                return -1;
            }
        }
    }

    /**
     * 获取项目主表信息
     */
    private void getProjectInfo() {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from uf_xmxx where id = ? ";
        rs.executeQuery(sql, projectId);
        RecordSetData rd = rs.getData();
        projectColumns = rd.getColumnName();
        bb.writeLog("projectColumns:" + Arrays.toString(projectColumns));
        ja = QueryResultUtil.getJSONArrayList(rs);
        if (!ja.isEmpty()) {
            projectMain = ja.getJSONObject(0);
            bb.writeLog("projectMain:" + projectMain);
        }
    }

    /**
     * 获取任务主表信息
     */
    private void getTaskMainInfo() {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw where id = ? ";
        rs.executeQuery(sql, taskId);
        RecordSetData rd = rs.getData();
        taskColumns = rd.getColumnName();
        bb.writeLog("taskColumns:" + Arrays.toString(taskColumns));
        ja = QueryResultUtil.getJSONArrayList(rs);
        if (!ja.isEmpty()) {
            taskMain = ja.getJSONObject(0);
            bb.writeLog("taskMain:" + taskMain);
        }
    }

    /**
     * 获取任务明细1信息
     */
    private void getTaskDetailInfo() {
        rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw_dt1 where mainid = ? ";
        rs.executeQuery(sql, taskId);
        RecordSetData rd = rs.getData();
        taskDetailColumns = rd.getColumnName();
        bb.writeLog("taskDetailColumns:" + Arrays.toString(taskDetailColumns));
        taskDetail = QueryResultUtil.getJSONArrayList(rs);
        bb.writeLog("taskDetail:" + taskDetail);

    }

    /**
     * 创建新复制的项目信息
     *
     * @throws Exception
     */
    private void creatNewProject() throws Exception {
        List<String> insertFields = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        String value;
        for (String str : projectColumns) {
            //id字段跳过
            if ("id".equals(str)) {
                continue;
            }
            //添加字段
            insertFields.add(str);
            //添加字段对应值
            value = Util.null2String(projectMain.get(str));
            //项目名称，案号，需要加上复制的次数
            if ("xmmc".equals(str) || "ah".equals(str)) {
                int newTimes = copyTimes + 2;
                value += "(第" + newTimes + "次)";
            }
            //原始项目字段复制
            if ("ysxm".equals(str)) {
                value = String.valueOf(projectId);
            }

            if (value.isEmpty()) {
                values.add(null);
            } else {
                values.add(value);
            }
        }
        bb.writeLog("creatNewProject insertFields:" + insertFields);
        bb.writeLog("creatNewProject values:" + values);
        //使用当前操作人
        String tableName = "uf_xmxx";
        newProjectId = InsertModuleUtil.ModuleInsert(tableName, insertFields, values, user.getUID(), projectModuleId, null);
        bb.writeLog("creatNewProject newProjectId:" + newProjectId);

    }

    /**
     * 创建新复制的任务主信息
     *
     * @throws Exception
     */
    private void creatNewTaskMain() throws Exception {
        List<String> insertFields = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        String value;
        for (String str : taskColumns) {
            //id字段跳过
            if ("id".equals(str)) {
                continue;
            }
            //添加字段
            insertFields.add(str);
            //添加字段对应值
            value = Util.null2String(taskMain.get(str));
            //项目名称,需要加上复制的次数
            if ("xmmc".equals(str)) {
                int newTimes = copyTimes + 2;
                value += "(第" + newTimes + "次)";
            }
            //案号文本,需要加上复制的次数
            if ("ahwb".equals(str)) {
                int newTimes = copyTimes + 2;
                value += "(第" + newTimes + "次)";
            }

            //案号，使用新复制出来的项目数据id，这里暗号是浏览框
            if ("ah".equals(str)) {
                value = String.valueOf(newProjectId);
            }
            //原始案号
            if ("ysah".equals(str)) {
                value = String.valueOf(projectId);
            }
            //复制日期
            if ("fzrq".equals(str)) {
                value = TimeUtil.getCurrentDateString();
            }
            //复制时间
            if ("fzsj".equals(str)) {
                value = TimeUtil.getCurrentTimeString();
            }
            //复制人
            if ("fzr".equals(str)) {
                value = Util.null2String(user.getUID());
            }
            if (value.isEmpty()) {
                values.add(null);
            } else {
                values.add(value);
            }
        }
        bb.writeLog("creatNewTaskMain insertFields:" + insertFields);
        bb.writeLog("creatNewTaskMain values:" + values);
        //使用当前操作人
        String tableName = "uf_cwcc_xmrw";
        newTaskId = InsertModuleUtil.ModuleInsert(tableName, insertFields, values, user.getUID(), taskModuleId, null);
        bb.writeLog("creatNewTaskMain newTaskId:" + newTaskId);
    }

    /**
     * 创建新复制的任务明细1信息
     *
     * @throws Exception
     */
    private void creatNewTaskDetail() {
        List<String> insertFields;
        List<Object> values;
        String value;
        JSONObject jo;
        String tableName = "uf_cwcc_xmrw_dt1";
        for (int i = 0; i < taskDetail.size(); i++) {
            jo = taskDetail.getJSONObject(i);
            insertFields = new ArrayList<>();
            values = new ArrayList<>();
            for (String str : taskDetailColumns) {
                //id字段跳过
                if ("id".equals(str)) {
                    continue;
                }
                //添加字段
                insertFields.add(str);
                //添加字段对应值
                value = Util.null2String(jo.get(str));
                //mainid,等于新建出来的任务id
                if ("mainid".equals(str)) {
                    value = String.valueOf(newTaskId);
                }
                if (value.isEmpty()) {
                    values.add(null);
                } else {
                    values.add(value);
                }
            }
            bb.writeLog("creatNewTaskDetail insertFields:" + insertFields);
            bb.writeLog("creatNewTaskDetail values:" + values);
            InsertModuleUtil.DetailModuleInsert(tableName, insertFields, values);
        }
    }

    /**
     * 更新新项目的任务数据id字段，将新建出来的任务id赋值上去
     */
    private void updateProjectTaskId() {
        rs = new RecordSet();
        String sql = "update uf_xmxx set rwsjid = " + newTaskId + " where id = " + newProjectId;
        bb.writeLog("updateProjectTaskId sql:" + sql);
        if (rs.executeUpdate(sql)) {
            bb.writeLog("updateProjectTaskId 成功");
        } else {
            bb.writeLog("updateProjectTaskId 失败:" + rs.getExceptionMsg());
        }
    }

}
