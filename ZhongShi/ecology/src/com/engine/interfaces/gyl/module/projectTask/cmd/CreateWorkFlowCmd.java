package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.interfaces.gyl.esb.EsbEventUtil;
import com.engine.interfaces.gyl.esb.vo.EsbEventResult;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * 创建流程
 *
 * <AUTHOR>
 */
public class CreateWorkFlowCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CreateWorkFlowCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        erroMsg = "";
        mainData = new JSONObject();
        detailData = new JSONArray();
        projectMainData = new JSONObject();
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 数据库操作类（不带事务）
     */
    private RecordSet rs;
    /**
     * RecordSet返回成功失败值
     */
    private boolean rsFlag;
    /**
     * 错误信息
     */
    private volatile String erroMsg;
    //任务主表信息
    private volatile JSONObject mainData;
    //任务明细表信息
    private volatile JSONArray detailData;
    //项目信息主表信息
    private volatile JSONObject projectMainData;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());

        String moduleDetailTableName, workflowDetailTableName, moduleDetailIndex,
                detailIds, requestName, workflowId, appid, esbEvent;
        JSONArray mainField, detailField = null;
        int billid;
        try {
            //校验参数
            //任务主表id
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid为空";
            }
            //主表字段配置
            if (Util.null2String(params.get("mainField")).isEmpty()) {
                erroMsg = "mainField为空";
            }
            //新建流程标题
            if (Util.null2String(params.get("requestName")).isEmpty()) {
                erroMsg = "requestName为空";
            }
            //新建流程workflowId
            if (Util.null2String(params.get("workflowId")).isEmpty()) {
                erroMsg = "workflowId为空";
            }
            //appid
            if (Util.null2String(params.get("appid")).isEmpty()) {
                erroMsg = "appid为空";
            }
            if (Util.null2String(params.get("esbEvent")).isEmpty()) {
                erroMsg = "esbEvent为空";
            }

            if (erroMsg.isEmpty()) {
                //数据id
                billid = Integer.parseInt(Util.null2String(params.get("billid")));
                //建模明细id
                detailIds = Util.null2String(params.get("detailIds"));
                //建模明细index
                moduleDetailIndex = Util.null2String(params.get("moduleDetailIndex"));
                moduleDetailTableName = "uf_cwcc_xmrw_dt" + moduleDetailIndex;
                bb.writeLog("detailTableName:" + moduleDetailTableName);
                //流程的明细表名
                workflowDetailTableName = Util.null2String(params.get("workflowDetailTableName"));
                bb.writeLog("workflowDetailTableName:" + workflowDetailTableName);

                //主表字段配置
                mainField = JSONArray.parseArray(Util.null2String(params.get("mainField")));
                if (!Util.null2String(params.get("detailField")).isEmpty()) {
                    //明细表字段配置
                    detailField = JSONArray.parseArray(Util.null2String(params.get("detailField")));
                }

                //流程标题
                requestName = Util.null2String(params.get("requestName"));
                //流程id
                workflowId = Util.null2String(params.get("workflowId"));
                //appid
                appid = Util.null2String(params.get("appid"));
                //ESB事件
                esbEvent = Util.null2String(params.get("esbEvent"));
                bb.writeLog("mainField:" + mainField);
                bb.writeLog("detailField:" + detailField);
                //查看主子表数据
                getMainData(billid);
                if (!detailIds.isEmpty()) {
                    getDetailData(moduleDetailTableName, detailIds);
                }
                //查询项目主表信息
                getProjectMainData();


                //组装ESB主表数据
                JSONArray esbMainData = packageESBMainData(mainField);
                JSONArray esbDetailData = new JSONArray();
                //组装ESB明细数据
                if (!workflowDetailTableName.isEmpty()) {
                    esbDetailData = packageESBDetailData(workflowDetailTableName, detailField);
                }

                //构造ESB事件参数
                JSONObject esbParams = new JSONObject();
                JSONObject otherParams = new JSONObject();
                //默认不流转
                otherParams.put("isnextflow", "0");
                esbParams.put("appid", appid);
                esbParams.put("userid", user.getUID());


                JSONObject bodyParams = new JSONObject();
                bodyParams.put("mainData", esbMainData);
                bodyParams.put("detailData", esbDetailData);

                bodyParams.put("requestName", requestName);
                bodyParams.put("workflowId", workflowId);
                bodyParams.put("otherParams", otherParams);
                esbParams.put("data", bodyParams.toJSONString());

                writeLog("esbParams:" + esbParams);
                //调用ESB事件
                EsbEventResult esbResult = EsbEventUtil.callEsbEvent(esbEvent, esbParams.toJSONString());
                writeLog("esbResult:" + esbResult);


                if (!esbResult.isSuccess()) {
                    erroMsg = esbResult.getErroMsg();
                } else {
                    JSONObject joData = esbResult.getData();
                    if ("SUCCESS".equals(Util.null2String(joData.get("code")))) {
                        //新建的请求id
                        String newRequestId = Util.null2String(joData.get("requestid"));
                        result.put("newRequestId", newRequestId);
                    } else {
                        erroMsg = "事件批次号：" + esbResult.getSerialNumber() + "，" + joData.toJSONString();
                    }
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 组装ESB 主表数据结构
     * 无法转文件
     *
     * @param mainField
     */
    private JSONArray packageESBMainData(JSONArray mainField) {
        JSONArray esbMainData = new JSONArray();
        JSONObject eachCfg, jo;
        String targetField, fromField;
        Object fieldValue;
        for (int i = 0; i < mainField.size(); i++) {
            eachCfg = mainField.getJSONObject(i);
            //目标字段 流程
            targetField = Util.null2String(eachCfg.get("wfField"));
            //来源字段 建模
            fromField = Util.null2String(eachCfg.get("modField"));
            fieldValue = getMainFieldValue(fromField);
            if (!targetField.isEmpty() && fieldValue != null) {
                jo = new JSONObject();
                jo.put("fieldName", targetField);
                jo.put("fieldValue", fieldValue);
                esbMainData.add(jo);
            }
        }
        return esbMainData;
    }

    private Object getMainFieldValue(String fromField) {
        Object fieldValue;
        if (fromField.startsWith("$$")) {
            //$$开头的是固定值
            fieldValue = fromField.substring(2);
        } else if (fromField.startsWith("$xm$")) {
            //$xm$取项目主表的信息
            fieldValue = projectMainData.get(fromField.substring(4));
        } else if ("$user$".equals(fromField)) {
            //当前操作人
            fieldValue = user.getUID();
        } else if ("$dept$".equals(fromField)) {
            //当前操作人对应的部门
            fieldValue = user.getUserDepartment();
        } else if ("$subcompany$".equals(fromField)) {
            //当前操作人对应的所属分部
            fieldValue = user.getUserSubCompany1();
        } else if ("$date$".equals(fromField)) {
            //当前日期
            fieldValue = TimeUtil.getCurrentDateString();
        } else {
            //任务主表数据
            fieldValue = mainData.get(fromField);
        }
        return fieldValue;
    }

    private Object getDetailFieldValue(String fromField, JSONObject joEachDetail) {
        Object fieldValue;
        if (fromField.startsWith("$$")) {
            //$$开头的是固定值
            fieldValue = fromField.substring(2);
        } else if (fromField.startsWith("$xm$")) {
            //$xm$取项目主表的信息
            fieldValue = projectMainData.get(fromField.substring(4));
        } else if ("$user$".equals(fromField)) {
            //当前操作人
            fieldValue = user.getUID();
        } else if ("$dept$".equals(fromField)) {
            //当前操作人对应的部门
            fieldValue = user.getUserDepartment();
        } else if ("$subcompany$".equals(fromField)) {
            //当前操作人对应的所属分部
            fieldValue = user.getUserSubCompany1();
        } else if ("$date$".equals(fromField)) {
            //当前日期
            fieldValue = TimeUtil.getCurrentDateString();
        } else {
            //任务主表数据
            fieldValue = joEachDetail.get(fromField);
        }
        return fieldValue;
    }


    /**
     * 组装ESB 明细表数据结构
     */
    private JSONArray packageESBDetailData(String detailTableName, JSONArray detailField) {
        JSONArray result = new JSONArray();
        JSONArray workflowRequestTableRecords, workflowRequestTableFields;
        JSONObject eachResult, joEachDetail, eachTableRecord, eachTableFields;
        Object fieldValue;
        String targetField, fromField;
        eachResult = new JSONObject();
        eachResult.put("tableDBName", detailTableName);
        workflowRequestTableRecords = new JSONArray();
        //遍历明细数据
        for (int i = 0; i < detailData.size(); i++) {
            joEachDetail = detailData.getJSONObject(i);
            eachTableRecord = new JSONObject();
            eachTableRecord.put("recordOrder", 0);
            workflowRequestTableFields = new JSONArray();
            //遍历明细字段数据
            for (int j = 0; j < detailField.size(); j++) {
                //目标字段
                targetField = Util.null2String(detailField.getJSONObject(j).get("wfField"));
                //来源字段
                fromField = Util.null2String(detailField.getJSONObject(j).get("modField"));

                fieldValue = getDetailFieldValue(fromField, joEachDetail);
                if (!targetField.isEmpty() && fieldValue != null) {
                    eachTableFields = new JSONObject();
                    eachTableFields.put("fieldName", targetField);
                    //判断字段值是否为JSONArray,是的话为附件信息
                    try {
                        JSONArray valueJson = JSONArray.parseArray(Util.null2String(fieldValue));
                        eachTableFields.put("fieldValue", valueJson);
                    } catch (Exception e) {
                        //如果parse异常，则为普通的字符串
                        eachTableFields.put("fieldValue", fieldValue);
                    }
                    workflowRequestTableFields.add(eachTableFields);
                }
            }
            eachTableRecord.put("workflowRequestTableFields", workflowRequestTableFields);
            workflowRequestTableRecords.add(eachTableRecord);

        }
        eachResult.put("workflowRequestTableRecords", workflowRequestTableRecords);
        result.add(eachResult);

        return result;
    }

    private void getMainData(int billid) {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw where id = ?";
        if (rs.executeQuery(sql, billid)) {
            ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                mainData = ja.getJSONObject(0);
            }
        } else {
            bb.writeLog("getMainData sql erro:" + rs.getExceptionMsg());
        }
    }

    private void getProjectMainData() {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from uf_xmxx where id = ?";
        if (rs.executeQuery(sql, mainData.get("ah"))) {
            ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                projectMainData = ja.getJSONObject(0);
            }
        } else {
            bb.writeLog("getProjectMainData sql erro:" + rs.getExceptionMsg());
        }

    }


    private void getDetailData(String detailTableName, String detailIds) {
        rs = new RecordSet();
        String sql = "select * from " + detailTableName + " where id in (" + detailIds + ")";
        rs.executeQuery(sql);
        detailData = QueryResultUtil.getJSONArrayList(rs);

    }


}
