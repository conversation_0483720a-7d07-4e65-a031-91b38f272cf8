package com.engine.interfaces.gyl.module.projectTask.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR>
 */
public interface ModHandleService {
    /**
     * 保存或更新建模数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveOrUpdate(Map<String, Object> params, User user);


    /**
     * 复制项目
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> copyProject(Map<String, Object> params, User user);

    /**
     * 查询复制的项目
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> queryCopyProject(Map<String, Object> params, User user);

    /**
     * 退保证金
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> retreatBail(Map<String, Object> params, User user);

    /**
     * 创建流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createWorkFlow(Map<String, Object> params, User user);

    /**
     * 复制标书附件
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> copyBsm(Map<String, Object> params, User user);

    /**
     * 创建开票服务费流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createInvoiceWork(Map<String, Object> params, User user);

    /**
     * 从任务信息更新到项目信息
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> updateProjectInfo(Map<String, Object> params, User user);

    /**
     * 创建保证金流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createBzjWork(Map<String, Object> params, User user);

    /**
     * 创建开票流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createKaipiaoWork(Map<String, Object> params, User user);

    /**
     * 创建分配流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createFenpeiWork(Map<String, Object> params, User user);

    /**
     * 创建总公司归档流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createGuiDangWork(Map<String, Object> params, User user);

    /**
     * 更新发标阶段的保证金
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> updateFabiaoBzj(Map<String, Object> params, User user);
}
