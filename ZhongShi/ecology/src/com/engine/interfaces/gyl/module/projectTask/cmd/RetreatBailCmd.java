package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.workflow.util.WorkFlowUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;


/**
 * 退保证金
 *
 * <AUTHOR>
 */
public class RetreatBailCmd extends AbstractCommonCommand<Map<String, Object>> {

    public RetreatBailCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        erroMsg = "";
    }

    //基类
    private final BaseBean bb;

    //错误信息
    private volatile String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());

        String submitResult;
        int creator;
        String reqTableName;
        try {
            //校验参数
            //明细行id
            if (Util.null2String(params.get("detailIds")).isEmpty()) {
                erroMsg = "detailIds为空";
            }
            //保证金流程可退的节点id
            if (Util.null2String(params.get("validNodeId")).isEmpty()) {
                erroMsg = "validNodeId为空";
            }

            if (erroMsg.isEmpty()) {
                //案号
                String anhao = Util.null2String(params.get("anhao"));
                //明细id
                String detailIds = Util.null2String(params.get("detailIds"));
                //可以执行退回的节点id
                String validNodeId = Util.null2String(params.get("validNodeId"));
                //手动发起保证金退回流程的formtable名称
                String bzjFormTableName = Util.null2String(params.get("bzjFormTableName"));
                //获取明细行数据的requestid
                String reqIds = getDetailRequest(detailIds, bzjFormTableName, anhao);
                bb.writeLog("reqIds:" + reqIds);
                if (erroMsg.isEmpty()) {
                    //校验流程是否匹配可退的节点id
                    Map<String, String> mapReqTable = new HashMap<>();
                    Map<String, String> mapSubmitor = checkReqNode(reqIds, validNodeId, mapReqTable);
                    bb.writeLog("mapCreator:" + mapSubmitor);
                    bb.writeLog("mapReqTable:" + mapReqTable);
                    String[] reqIdsArray = reqIds.split(CommonCst.COMMA_EN);
                    if (mapSubmitor.size() != reqIdsArray.length) {
                        erroMsg = "流程数量和流程创建人数量不匹配，请检查流程请求id是否存在，reqIds:" + reqIds + ";mapSubmitor:" + mapSubmitor;
                    }
                    if (erroMsg.isEmpty()) {
                        //实际退保证金操作（提交流程）
                        for (String str : reqIdsArray) {
                            creator = Integer.parseInt(mapSubmitor.get(str));
                            submitResult = WorkFlowUtil.submitWorkflowRequest(Integer.parseInt(str), creator, "submit");
                            if (!"success".equals(submitResult)) {
                                erroMsg = "流程提交出错：" + submitResult + "，提交人id为：" + creator + ",流程请求id为：" + str + "，请确认该人是否有当前流程节点的提交权限！";
                                break;
                            } else {
                                //退成功之后，更新流程中退还金额=保证金金额
                                RecordSet rs = new RecordSet();
                                //先拿到requestid对应的表单
                                reqTableName = mapReqTable.get(str);
                                writeLog("reqId:" + str);
                                writeLog("reqTableName:" + reqTableName);
                                //更新
                                rs.executeUpdate("update " + reqTableName + " set thje = bzjje where requestid = ?", str);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 获取流程明细的requestid合集
     *
     * @param detailIds        勾选的明细行id
     * @param bzjFormTableName 手动发起保证金的流程formtable
     * @param anhao            案号
     * @return
     */
    private String getDetailRequest(String detailIds, String bzjFormTableName, String anhao) {
        String result = "", sql, bjh, dwmc;
        StringBuilder reqIds = new StringBuilder();
        RecordSet rs;
        boolean rsFlag;
        //分两步查询

        //1.查询有BZJrequestid的数据
        rs = new RecordSet();
        sql = "select BZJrequestid from uf_cwcc_xmrw_dt2 where id in (" + detailIds + ") and BZJrequestid is not null and BZJrequestid != ''";
        rsFlag = rs.executeQuery(sql);
        if (rsFlag) {
            while (rs.next()) {
                reqIds.append(Util.null2String(rs.getString("BZJrequestid"))).append(",");
            }
        } else {
            erroMsg = "查询发标阶段明细自动触发的保证金流程数据出错：" + rs.getExceptionMsg() + ";" + rs.getMsg();
        }
//        if (erroMsg.isEmpty()) {
//            //2.查询无BZJrequestid的数据，这些数据为手动建的，根据案号+供应商名称+包件号 查询对应的流程
//            rs = new RecordSet();
//            sql = "select distinct bjh,dwmc from uf_cwcc_xmrw_dt2 where id in (" + detailIds + ") and (BZJrequestid is null or BZJrequestid = '') ";
//            rsFlag = rs.executeQuery(sql);
//            if (rsFlag) {
//                while (rs.next()) {
//                    //包件号
//                    bjh = Util.null2String(rs.getString("bjh"));
//                    //供应商名称
//                    dwmc = Util.null2String(rs.getString("dwmc"));
//                    //添加对应的手动发起的流程请求id
//                    addCustomWfRequest(bjh, dwmc, anhao, bzjFormTableName, reqIds);
//                }
//            } else {
//                erroMsg = "查询发标阶段明细手动发起保证金流程数据出错：" + rs.getExceptionMsg() + ";" + rs.getMsg();
//            }
//        }
        if (!reqIds.toString().isEmpty()) {
            result = reqIds.substring(0, reqIds.length() - 1);
        }
        return result;
    }

    /**
     * 添加手动发起的保证金流程的requestid
     *
     * @param bjh       包件号
     * @param dwmc      单位名称
     * @param anhao     案号
     * @param formtable 流程表单名
     */
    private void addCustomWfRequest(String bjh, String dwmc, String anhao, String formtable, StringBuilder reqIds) {
        RecordSet rs;
        boolean rsFlag;
        //流程请求id
        String requestid;
        rs = new RecordSet();
        String sql = "select requestid from " + formtable + " where ah = ? and bjh = ? and gysmc = ? ";
        rsFlag = rs.executeQuery(sql, anhao, bjh, dwmc);
        if (rsFlag) {
            while (rs.next()) {
                requestid = Util.null2String(rs.getString("requestid"));
                reqIds.append(requestid).append(",");
            }
        } else {
            erroMsg = "查询发标阶段明细手动发起保证金流程数据请求id出错：" + rs.getExceptionMsg() + ";" + rs.getMsg();
        }
    }

    /**
     * 校验流程节点
     *
     * @param reqIds
     * @param validNodeId
     * @return
     */
    private Map<String, String> checkReqNode(String reqIds, String validNodeId, Map<String, String> mapReqTable) {
        Map<String, String> mapSubmitor = new HashMap<>();
        String currentnodeid, requestid, requestname, gongchengshi, tableName;
        String[] validNodeArray = validNodeId.split(CommonCst.COMMA_EN);
        List<String> listValidNode = new ArrayList<>(Arrays.asList(validNodeArray));
        RecordSet rs;
        boolean rsFlag;
        String sql = "select " +
                " a.requestid, " +
                " a.requestname, " +
                " a.creater, " +
                " a.currentnodeid, " +
                " a.lastoperator, " +
                " a.workflowid,  " +
                " c.tablename,  " +
                " b.workflowname  " +
                " from " +
                " workflow_requestbase a " +
                " left join workflow_base b on a.workflowid = b.id " +
                " left join workflow_bill c on c.id = b.formid  " +
                " where " +
                " a.requestid  in (" + reqIds + ") ";
        rs = new RecordSet();
        rsFlag = rs.executeQuery(sql);
        if (!rsFlag) {
            erroMsg = "查询流程对应的节点数据出错：" + rs.getExceptionMsg();
        } else {
            while (rs.next()) {
                currentnodeid = Util.null2String(rs.getString("currentnodeid"));
                requestid = Util.null2String(rs.getString("requestid"));
                requestname = Util.null2String(rs.getString("requestname"));

                tableName = Util.null2String(rs.getString("tablename"));
                gongchengshi = getGongChengShi(tableName, requestid);
                if (gongchengshi.isEmpty()) {
                    erroMsg = "流程标题为：【" + requestname + "】，请求Id为：【" + requestid + "】的流程未找到有效的工程师人员，请检查流程和节点配置！";
                    break;
                }
                mapReqTable.put(requestid, tableName);
                if (!listValidNode.contains(currentnodeid)) {
                    erroMsg = "流程标题为：【" + requestname + "】，请求Id为：【" + requestid + "】的流程当前节点不可进行退保证金操作，请检查流程和节点配置！";
                    break;
                } else {
                    mapSubmitor.put(requestid, gongchengshi);
                }
            }
        }
        return mapSubmitor;
    }


    /**
     * 获取流程的工程师即项目组成员字段，使用工程师中任意一个人进行流程的提交
     *
     * @param tableName
     * @param requestid
     * @return
     */
    private String getGongChengShi(String tableName, String requestid) {
        RecordSet rs = new RecordSet();
        String users, user = "";
        rs.executeQuery("select xmzcy from " + tableName + " where requestid = ?", requestid);
        if (rs.next()) {
            users = Util.null2String(rs.getString("xmzcy"));
            if (!users.isEmpty()) {
                user = users.split(CommonCst.COMMA_EN)[0];
            }
        }
        return user;
    }

}
