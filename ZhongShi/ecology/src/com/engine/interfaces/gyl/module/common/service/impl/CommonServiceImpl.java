package com.engine.interfaces.gyl.module.common.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.gyl.module.common.cmd.CreateWorkFlowCmd;
import com.engine.interfaces.gyl.module.common.service.CommonService;
import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR> Mason
 */
public class CommonServiceImpl extends Service implements CommonService {

    @Override
    public Map<String, Object> createWorkFlow(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateWorkFlowCmd(params, user));
    }

    @Override
    public Map<String, Object> createWorkFlowById(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateWorkFlowCmd(params, user));
    }

}
  