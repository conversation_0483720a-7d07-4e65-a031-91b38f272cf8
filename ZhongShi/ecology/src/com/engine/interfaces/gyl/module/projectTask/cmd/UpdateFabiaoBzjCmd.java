package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;


/**
 * 更新发标阶段保证金金额
 *
 * <AUTHOR>
 */
public class UpdateFabiaoBzjCmd extends AbstractCommonCommand<Map<String, Object>> {

    public UpdateFabiaoBzjCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    /**
     * 基类
     */
    private final BaseBean bb;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());
        String errorMsg = "";
        try {
            //校验参数
            //任务主表的数据id
            if (Util.null2String(params.get("billid")).isEmpty()) {
                errorMsg = "billid参数为空";
            }
            if (errorMsg.isEmpty()) {
                String billid = Util.null2String(params.get("billid"));
                //明细id
                RecordSet rs = new RecordSet();
                String sql = "UPDATE uf_cwcc_xmrw_dt2 " +
                        " SET bzjje = a.bzj " +
                        " FROM uf_cwcc_xmrw_dt2 " +
                        " INNER JOIN ( " +
                        "    SELECT bzj, ah, bjh  " +
                        "    FROM uf_cwcc_xmrw_dt1  " +
                        "    WHERE mainid = " + billid +
                        " ) a ON uf_cwcc_xmrw_dt2.ah = a.ah AND uf_cwcc_xmrw_dt2.bjh = a.bjh " +
                        " WHERE uf_cwcc_xmrw_dt2.mainid = " + billid;
                bb.writeLog("update sql :" + sql);
                if (!rs.executeUpdate(sql)) {
                    errorMsg = "更新发标阶段的保证金金额出错：" + rs.getExceptionMsg();
                }
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        result.put("status", errorMsg.isEmpty());
        result.put("errorMsg", errorMsg);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }
}
