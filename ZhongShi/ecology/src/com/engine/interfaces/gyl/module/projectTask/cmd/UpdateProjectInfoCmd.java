package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * 退保证金
 *
 * <AUTHOR>
 */
public class UpdateProjectInfoCmd extends AbstractCommonCommand<Map<String, Object>> {

    public UpdateProjectInfoCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        erroMsg = "";
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 数据库操作类（不带事务）
     */
    private RecordSet rs;
    /**
     * RecordSet返回成功失败值
     */
    private boolean rsFlag;
    /**
     * 错误信息
     */
    private String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());


        try {
            //校验参数
            //任务主表的数据id
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid参数为空";
            }
            if (Util.null2String(params.get("xmmc")).isEmpty()) {
                erroMsg = "xmmc参数为空";
            }
            if (erroMsg.isEmpty()) {
                String xmmc = Util.null2String(params.get("xmmc"));
                String billid = Util.null2String(params.get("billid"));
                //明细id
                rs = new RecordSet();
                rsFlag = rs.executeUpdate(" update uf_xmxx set xmmc = '" + xmmc + "' where rwsjid = ? ", billid);
                if (!rsFlag) {
                    erroMsg = "更新项目名称至项目信息出错：" + rs.getExceptionMsg();
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


}
