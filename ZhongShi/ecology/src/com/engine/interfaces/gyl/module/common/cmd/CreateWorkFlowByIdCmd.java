package com.engine.interfaces.gyl.module.common.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.interfaces.gyl.esb.EsbEventUtil;
import com.engine.interfaces.gyl.esb.vo.EsbEventResult;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * 创建流程（暂时无用）
 *
 * <AUTHOR>
 */
public class CreateWorkFlowByIdCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CreateWorkFlowByIdCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        erroMsg = "";
        mainData = new JSONObject();
        sql = "";
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 数据库操作类（不带事务）
     */
    private RecordSet rs;
    /**
     * RecordSet返回成功失败值
     */
    private boolean rsFlag;
    private String sql;
    /**
     * 错误信息
     */
    private String erroMsg;
    private JSONObject mainData;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());

        String workflowId, appid, esbEvent, fromMainTableName, targetMainTableName, targetRequestName, modIndex, wfIndex;

        JSONArray mainFields = null, detailFields = null, fields;
        JSONObject joData, jo, eachEsbDetailData;
        int billid, creatUser;
        try {
            //校验参数
            //配置数据
            if (Util.null2String(params.get("data")).isEmpty()) {
                erroMsg = "configId参数为空";
            }


            if (erroMsg.isEmpty()) {
                //根据billid查询参数配置


                //data配置数据
                joData = JSONObject.parseObject(Util.null2String(params.get("data")));
                //数据id
                billid = Integer.parseInt(Util.null2String(params.get("billid")));
                //主表字段配置
                if (!Util.null2String(joData.get("mainFields")).isEmpty()) {
                    //主表表字段配置
                    mainFields = JSONArray.parseArray(Util.null2String(joData.get("mainFields")));
                }

                if (!Util.null2String(joData.get("detailFields")).isEmpty()) {
                    //明细表字段配置
                    detailFields = JSONArray.parseArray(Util.null2String(joData.get("detailFields")));
                }

                if (!Util.null2String(joData.get("creatUser")).isEmpty()) {
                    //指定流程创建人
                    creatUser = Integer.parseInt(Util.null2String(joData.get("creatUser")));
                } else {
                    creatUser = user.getUID();
                }

                //来源主表名
                fromMainTableName = Util.null2String(joData.get("fromMainTableName"));
                //目标流程主表名
                targetMainTableName = Util.null2String(joData.get("targetMainTableName"));
                //目标流程标题名称
                targetRequestName = Util.null2String(joData.get("targetRequestName"));


                //流程id
                workflowId = Util.null2String(joData.get("workflowId"));
                //appid
                appid = Util.null2String(joData.get("appid"));
                //ESB事件
                esbEvent = Util.null2String(joData.get("esbEvent"));

                bb.writeLog("mainFields:" + mainFields);
                bb.writeLog("detailFields:" + detailFields);
                //获取来源主表信息
                getMainData(billid, fromMainTableName);
                //组装ESB主表数据
                JSONArray esbMainData = packageESBMainData(mainFields);
                bb.writeLog("esbMainData:" + esbMainData);
                JSONArray esbDetailData = new JSONArray();
                //遍历明细表配置
                if (detailFields != null && detailFields.size() > 0) {
                    for (int i = 0; i < detailFields.size(); i++) {
                        jo = detailFields.getJSONObject(i);
                        modIndex = Util.null2String(jo.get("modIndex"));
                        wfIndex = Util.null2String(jo.get("wfIndex"));
                        String modDetailTable = fromMainTableName + "_dt" + modIndex;
                        String wfDetailTable = targetMainTableName + "_dt" + wfIndex;
                        fields = JSONArray.parseArray(Util.null2String(jo.get("fields")));
                        //获取来源明细表信息
                        JSONArray detailData = getDetailData(modDetailTable, billid);
                        //组装一套明细数据
                        eachEsbDetailData = packageESBDetailData(wfDetailTable, fields, detailData);
                        //添加到总的明细esb参数中
                        esbDetailData.add(eachEsbDetailData);
                    }
                }

                bb.writeLog("esbDetailData:" + esbDetailData);
                //构造ESB事件参数
                JSONObject esbParams = new JSONObject();
                JSONObject otherParams = new JSONObject();
                //默认不流转
                otherParams.put("isnextflow", "0");
                esbParams.put("appid", appid);
                esbParams.put("userid", creatUser);


                JSONObject bodyParams = new JSONObject();
                bodyParams.put("mainData", esbMainData);
                if (!esbDetailData.isEmpty()) {
                    bodyParams.put("detailData", esbDetailData);
                }

                bodyParams.put("requestName", targetRequestName);
                bodyParams.put("workflowId", workflowId);
                bodyParams.put("otherParams", otherParams);
                esbParams.put("data", bodyParams.toJSONString());

                writeLog("esbParams:" + esbParams);
                //调用ESB事件
                EsbEventResult esbResult = EsbEventUtil.callEsbEvent(esbEvent, esbParams.toJSONString());
                writeLog("esbResult:" + esbResult);


                if (!esbResult.isSuccess()) {
                    erroMsg = esbResult.getErroMsg();
                } else {
                    JSONObject esbResultData = esbResult.getData();
                    if ("SUCCESS".equals(Util.null2String(esbResultData.get("code")))) {
                        //新建的请求id
                        String newRequestId = Util.null2String(esbResultData.get("requestid"));
                        result.put("newRequestId", newRequestId);
                    } else {
                        erroMsg = "事件批次号：" + esbResult.getSerialNumber() + "，" + esbResultData.toJSONString();
                    }
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 组装ESB 主表数据结构
     * 无法转文件
     *
     * @param mainField
     */
    private JSONArray packageESBMainData(JSONArray mainField) {

        JSONArray esbMainData = new JSONArray();
        JSONObject eachCfg, jo;
        String targetField, fromField;
        Object fieldValue;
        if (mainField != null) {
            for (int i = 0; i < mainField.size(); i++) {
                eachCfg = mainField.getJSONObject(i);
                //目标字段 流程
                targetField = Util.null2String(eachCfg.get("wfField"));
                //来源字段 建模
                fromField = Util.null2String(eachCfg.get("modField"));
                if (fromField.startsWith("$$")) {
                    fieldValue = fromField.substring(2);
                } else {
                    fieldValue = mainData.get(fromField);
                }
                if (!targetField.isEmpty() && fieldValue != null) {
                    jo = new JSONObject();
                    jo.put("fieldName", targetField);
                    jo.put("fieldValue", fieldValue);
                    esbMainData.add(jo);
                }
            }
        }
        return esbMainData;
    }

    /**
     * 组装ESB 明细表数据结构
     *
     * @param detailTableName
     * @param detailField
     * @param detailData
     * @return
     */
    private JSONObject packageESBDetailData(String detailTableName, JSONArray detailField, JSONArray detailData) {
        JSONArray workflowRequestTableRecords, workflowRequestTableFields;
        JSONObject eachResult, joEachDetail, eachTableRecord, eachTableFields;
        String fieldValue;
        String targetField, fromField;
        eachResult = new JSONObject();
        eachResult.put("tableDBName", detailTableName);
        workflowRequestTableRecords = new JSONArray();
        //遍历明细数据
        for (int i = 0; i < detailData.size(); i++) {
            joEachDetail = detailData.getJSONObject(i);
            eachTableRecord = new JSONObject();
            eachTableRecord.put("recordOrder", 0);
            workflowRequestTableFields = new JSONArray();
            //遍历明细字段数据
            for (int j = 0; j < detailField.size(); j++) {
                //目标字段
                targetField = Util.null2String(detailField.getJSONObject(j).get("wfField"));
                //来源字段
                fromField = Util.null2String(detailField.getJSONObject(j).get("modField"));
                if (fromField.startsWith("$$")) {
                    fieldValue = fromField.substring(2);
                } else {
                    fieldValue = Util.null2String(joEachDetail.get(fromField));
                }
                if (!targetField.isEmpty()) {
                    eachTableFields = new JSONObject();
                    eachTableFields.put("fieldName", targetField);

                    if (fieldValue.isEmpty()) {
                        eachTableFields.put("fieldValue", "");
                    } else {
                        //判断字段值是否为JSONArray,是的话为附件信息
                        try {
                            JSONArray valueJson = JSONArray.parseArray(fieldValue);
                            eachTableFields.put("fieldValue", valueJson);
                        } catch (Exception e) {
                            //如果parse异常，则为普通的字符串
                            eachTableFields.put("fieldValue", fieldValue);
                        }
                    }
                    workflowRequestTableFields.add(eachTableFields);
                }
            }
            eachTableRecord.put("workflowRequestTableFields", workflowRequestTableFields);
            workflowRequestTableRecords.add(eachTableRecord);

        }
        eachResult.put("workflowRequestTableRecords", workflowRequestTableRecords);
        return eachResult;
    }

    /**
     * 获取来源主表数据
     *
     * @param billid
     * @param tableName
     */
    private void getMainData(int billid, String tableName) {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from " + tableName + " where id = ?";
        rsFlag = rs.executeQuery(sql, billid);
        if (rsFlag) {
            ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                mainData = ja.getJSONObject(0);
            }
        } else {
            erroMsg = "查询来源主表数据出错：" + rs.getExceptionMsg();
        }

    }

    /**
     * 获取来源明细表数据
     *
     * @param detailTableName
     * @param mainid
     * @return
     */
    private JSONArray getDetailData(String detailTableName, int mainid) {
        JSONArray detailData = new JSONArray();
        rs = new RecordSet();
        String sql = "select * from " + detailTableName + " where mainid = ?";
        rsFlag = rs.executeQuery(sql, mainid);
        if (rsFlag) {
            detailData = QueryResultUtil.getJSONArrayList(rs);
        } else {
            erroMsg = "查询来源明细表数据出错：" + rs.getExceptionMsg();
        }
        return detailData;
    }


    private void queryConfig(int billid) {
        JSONArray ja;
        rs = new RecordSet();
        sql = "select * from uf_sd_modelcard where id = ?";
        rsFlag = rs.executeQuery(sql, billid);
        if (rsFlag) {
            ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                mainData = ja.getJSONObject(0);
            }
        } else {
            erroMsg = "查询来源主表数据出错：" + rs.getExceptionMsg();
        }
    }

}
