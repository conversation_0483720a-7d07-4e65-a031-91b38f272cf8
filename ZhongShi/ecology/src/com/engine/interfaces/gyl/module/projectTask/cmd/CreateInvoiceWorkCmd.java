package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.interfaces.gyl.esb.EsbEventUtil;
import com.engine.interfaces.gyl.esb.vo.EsbEventResult;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * 中标阶段-创建开票服务费流程
 *
 * <AUTHOR>
 */
public class CreateInvoiceWorkCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CreateInvoiceWorkCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        erroMsg = "";
        mainData = new JSONObject();
        prjMainData = new JSONObject();
        detailData = new JSONArray();
    }

    //基类
    private final BaseBean bb;
    //数据库操作类（不带事务）
    private volatile RecordSet rs;
    //错误信息
    private volatile String erroMsg;
    private volatile JSONObject mainData;
    private volatile JSONObject prjMainData;
    private volatile JSONArray detailData;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());

        String moduleDetailTableName, workflowDetailTableName, moduleDetailIndex,
                detailIds, requestName, workflowId, appid, esbEvent;
        StringBuilder allNewReqIds = new StringBuilder();
        JSONArray mainField, detailField;
        JSONObject eachDetail, eachVendor, eachDetail2Vendor;
        int billid;
        try {
            //校验参数
            //任务主表id
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid为空";
            }
            //勾选的明细ids
            if (Util.null2String(params.get("detailIds")).isEmpty()) {
                erroMsg = "detailIds为空";
            }
            //建模明细index
            if (Util.null2String(params.get("moduleDetailIndex")).isEmpty()) {
                erroMsg = "moduleDetailIndex为空";
            }
            //新建流程明细表名
            if (Util.null2String(params.get("workflowDetailTableName")).isEmpty()) {
                erroMsg = "workflowDetailTableName为空";
            }
            //新建流程标题
            if (Util.null2String(params.get("requestName")).isEmpty()) {
                erroMsg = "requestName为空";
            }
            //新建流程workflowId
            if (Util.null2String(params.get("workflowId")).isEmpty()) {
                erroMsg = "workflowId为空";
            }
            //appid
            if (Util.null2String(params.get("appid")).isEmpty()) {
                erroMsg = "appid为空";
            }
            if (Util.null2String(params.get("esbEvent")).isEmpty()) {
                erroMsg = "esbEvent为空";
            }
            if (Util.null2String(params.get("mainField")).isEmpty()) {
                erroMsg = "mainField为空";
            }

            if (erroMsg.isEmpty()) {
                //数据id
                billid = Integer.parseInt(Util.null2String(params.get("billid")));
                //建模明细id
                detailIds = Util.null2String(params.get("detailIds"));
                //建模明细index
                moduleDetailIndex = Util.null2String(params.get("moduleDetailIndex"));
                moduleDetailTableName = "uf_cwcc_xmrw_dt" + moduleDetailIndex;
                bb.writeLog("detailTableName:" + moduleDetailTableName);
                //流程的明细表名
                workflowDetailTableName = Util.null2String(params.get("workflowDetailTableName"));
                bb.writeLog("workflowDetailTableName:" + workflowDetailTableName);
                //流程标题
                requestName = Util.null2String(params.get("requestName"));
                //流程id
                workflowId = Util.null2String(params.get("workflowId"));
                //appid
                appid = Util.null2String(params.get("appid"));
                //ESB事件
                esbEvent = Util.null2String(params.get("esbEvent"));
                //主表字段配置
                mainField = JSONArray.parseArray(Util.null2String(params.get("mainField")));
                //明细表字段配置
                detailField = JSONArray.parseArray(Util.null2String(params.get("detailField")));

                //查看主表数据
                getMainData(billid);
                //查询项目主表信息
                getPrjMainData();
                //查看子表数据
                getDetailData(moduleDetailTableName, detailIds);
                //根据明细行，创建流程，一行一条流程
                for (int i = 0; i < detailData.size(); i++) {
                    eachDetail = detailData.getJSONObject(i);
                    //中标单位
                    String zbdw = Util.null2String(eachDetail.get("zbdw"));
                    //获取中标单位对应的供应商台账信息，中标单位浏览的是明细7的供应商名称，供应商名称浏览的是明细2的单位名称
                    eachVendor = getVendor(zbdw);
                    //获取中标单位对应任务明细2信息，中标单位浏览的是明细7的供应商名称，供应商名称浏览的是明细2的单位名称
                    eachDetail2Vendor = getDetail2Vendor(zbdw);
                    //组装ESB主表数据
                    JSONArray esbMainData = packageESBMainData(mainField, eachDetail, eachVendor, eachDetail2Vendor);
                    JSONArray esbDetailData = new JSONArray();
                    //组装ESB明细数据
                    if (!workflowDetailTableName.isEmpty()) {
                        //只添加一条明细
                        esbDetailData = packageESBDetailData(workflowDetailTableName, detailField, eachDetail, eachVendor, eachDetail2Vendor);
                    }

                    //构造ESB事件参数
                    JSONObject esbParams = new JSONObject();
                    JSONObject otherParams = new JSONObject();
                    //默认不流转
                    otherParams.put("isnextflow", "0");
                    esbParams.put("appid", appid);
                    esbParams.put("userid", user.getUID());


                    JSONObject bodyParams = new JSONObject();
                    bodyParams.put("mainData", esbMainData);
                    if (!esbDetailData.isEmpty()) {
                        bodyParams.put("detailData", esbDetailData);
                    }

                    bodyParams.put("requestName", requestName);
                    bodyParams.put("workflowId", workflowId);
                    bodyParams.put("otherParams", otherParams);
                    esbParams.put("data", bodyParams.toJSONString());

                    writeLog("esbParams:" + esbParams);
                    //调用ESB事件
                    EsbEventResult esbResult = EsbEventUtil.callEsbEvent(esbEvent, esbParams.toJSONString());
                    writeLog("esbResult:" + esbResult);


                    if (!esbResult.isSuccess()) {
                        erroMsg = esbResult.getErroMsg();
                    } else {
                        JSONObject joData = esbResult.getData();
                        if ("SUCCESS".equals(Util.null2String(joData.get("code")))) {
                            //新建的请求id
                            String newRequestId = Util.null2String(joData.get("requestid"));
                            allNewReqIds.append(newRequestId).append(",");
                        } else {
                            erroMsg = "事件批次号：" + esbResult.getSerialNumber() + "，" + joData.toJSONString();
                        }
                    }
                    if (!erroMsg.isEmpty()) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (!allNewReqIds.toString().isEmpty()) {
            result.put("allNewReqIds", allNewReqIds.substring(0, allNewReqIds.length() - 1));
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 组装ESB 主表数据结构
     * 无法转文件
     *
     * @param mainField
     */
    private JSONArray packageESBMainData(JSONArray mainField, JSONObject eachDetail, JSONObject vendor, JSONObject vendor2) {
        JSONArray esbMainData = new JSONArray();
        JSONObject eachCfg, jo;
        String targetField, fromField, fromIndex;
        Object fieldValue;
        for (int i = 0; i < mainField.size(); i++) {
            eachCfg = mainField.getJSONObject(i);
            //目标字段 流程
            targetField = Util.null2String(eachCfg.get("wfField"));
            //来源字段 建模
            fromField = Util.null2String(eachCfg.get("modField"));
            //来源位置 0主表 1明细表
            fromIndex = Util.null2String(eachCfg.get("modFieldPosition"));
            fieldValue = getMainFieldValue(fromField, fromIndex, eachDetail, vendor, vendor2);
            if (!targetField.isEmpty() && fieldValue != null) {
                jo = new JSONObject();
                jo.put("fieldName", targetField);
                jo.put("fieldValue", fieldValue);
                esbMainData.add(jo);
            }
        }
        return esbMainData;
    }

    private Object getMainFieldValue(String fromField, String fromIndex, JSONObject eachDetail, JSONObject vendor, JSONObject vendor2) {
        Object fieldValue;
        if (fromField.startsWith("$$")) {
            //$$开头的是固定值
            fieldValue = fromField.substring(2);
        } else if (fromField.startsWith("$xm$")) {
            //$xm$取项目主表的信息
            fieldValue = prjMainData.get(fromField.substring(4));
        } else if ("$user$".equals(fromField)) {
            //当前操作人
            fieldValue = user.getUID();
        } else if ("$dept$".equals(fromField)) {
            //当前操作人对应的部门
            fieldValue = user.getUserDepartment();
        } else if ("$subcompany$".equals(fromField)) {
            //当前操作人对应的所属分部
            fieldValue = user.getUserSubCompany1();
        } else if ("$date$".equals(fromField)) {
            //当前日期
            fieldValue = TimeUtil.getCurrentDateString();
        } else {
            if ("0".equals(fromIndex)) {
                //任务主表数据
                fieldValue = mainData.get(fromField);
            } else if ("1".equals(fromIndex)) {
                //任务明细表数据
                fieldValue = eachDetail.get(fromField);
            } else if ("2".equals(fromIndex)) {
                //供应商台账信息，获取对应供应商台账里的字段值信息
                fieldValue = vendor.get(fromField);
            } else if ("3".equals(fromIndex)) {
                //供应商台账信息，获取对应任务台账2的字段值信息
                fieldValue = vendor2.get(fromField);
            } else {
                fieldValue = "";
            }

        }
        return fieldValue;
    }

    /**
     * 组装ESB 明细表数据结构
     *
     * @param detailTableName
     * @param detailField
     * @return
     */
    private JSONArray packageESBDetailData(String detailTableName, JSONArray detailField, JSONObject joEachDetail, JSONObject vendor, JSONObject vendor2) {
        JSONArray result = new JSONArray();
        JSONArray workflowRequestTableRecords, workflowRequestTableFields;
        JSONObject eachResult, eachTableRecord, eachTableFields;
        Object fieldValue;
        String targetField, fromField, fromIndex;
        eachResult = new JSONObject();
        eachResult.put("tableDBName", detailTableName);
        workflowRequestTableRecords = new JSONArray();
        //遍历明细数据


        eachTableRecord = new JSONObject();
        eachTableRecord.put("recordOrder", 0);
        workflowRequestTableFields = new JSONArray();
        //遍历明细字段数据
        for (int j = 0; j < detailField.size(); j++) {
            //目标字段
            targetField = Util.null2String(detailField.getJSONObject(j).get("wfField"));
            //来源字段
            fromField = Util.null2String(detailField.getJSONObject(j).get("modField"));
            //来源字段位置
            fromIndex = Util.null2String(detailField.getJSONObject(j).get("modFieldPosition"));
            //目标值
            fieldValue = getDetailFieldValue(fromField, fromIndex, joEachDetail, vendor, vendor2);
            if (!targetField.isEmpty() && fieldValue != null) {
                eachTableFields = new JSONObject();
                eachTableFields.put("fieldName", targetField);

                //判断字段值是否为JSONArray,是的话为附件信息
                try {
                    JSONArray valueJson = JSONArray.parseArray(Util.null2String(fieldValue));
                    eachTableFields.put("fieldValue", valueJson);
                } catch (Exception e) {
                    //如果parse异常，则为普通的字符串
                    eachTableFields.put("fieldValue", fieldValue);
                }
                workflowRequestTableFields.add(eachTableFields);
            }
        }
        eachTableRecord.put("workflowRequestTableFields", workflowRequestTableFields);
        workflowRequestTableRecords.add(eachTableRecord);


        eachResult.put("workflowRequestTableRecords", workflowRequestTableRecords);
        result.add(eachResult);

        return result;
    }

    private Object getDetailFieldValue(String fromField, String fromIndex, JSONObject joEachDetail, JSONObject vendor, JSONObject vendor2) {
        Object fieldValue;
        if (fromField.startsWith("$$")) {
            //$$开头的是固定值
            fieldValue = fromField.substring(2);
        } else if (fromField.startsWith("$xm$")) {
            //$xm$取项目主表的信息
            fieldValue = prjMainData.get(fromField.substring(4));
        } else if ("$user$".equals(fromField)) {
            //当前操作人
            fieldValue = user.getUID();
        } else if ("$dept$".equals(fromField)) {
            //当前操作人对应的部门
            fieldValue = user.getUserDepartment();
        } else if ("$subcompany$".equals(fromField)) {
            //当前操作人对应的所属分部
            fieldValue = user.getUserSubCompany1();
        } else if ("$date$".equals(fromField)) {
            //当前日期
            fieldValue = TimeUtil.getCurrentDateString();
        } else {
            if ("0".equals(fromIndex)) {
                //任务主表数据
                fieldValue = mainData.get(fromField);
            } else if ("1".equals(fromIndex)) {
                //任务明细表数据
                fieldValue = joEachDetail.get(fromField);
            } else if ("2".equals(fromIndex)) {
                //供应商台账信息，获取对应供应商台账里的字段值信息
                fieldValue = vendor.get(fromField);
            } else if ("3".equals(fromIndex)) {
                //供应商台账信息，获取对应任务台账2的字段值信息
                fieldValue = vendor2.get(fromField);
            } else {
                fieldValue = "";
            }

        }
        return fieldValue;
    }

    /**
     * 获取任务主表信息
     *
     * @param billid
     */
    private void getMainData(int billid) {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw where id = ?";
        rs.executeQuery(sql, billid);
        ja = QueryResultUtil.getJSONArrayList(rs);
        if (!ja.isEmpty()) {
            mainData = ja.getJSONObject(0);
            getPrjMainData();
        }
    }

    /**
     * 获取项目主表信息
     */
    private void getPrjMainData() {
        JSONArray ja;
        rs = new RecordSet();
        String anhao = Util.null2String(mainData.get("ah"));
        String sql = "select * from uf_xmxx where id = ? ";
        rs.executeQuery(sql, anhao);
        ja = QueryResultUtil.getJSONArrayList(rs);
        if (!ja.isEmpty()) {
            prjMainData = ja.getJSONObject(0);
        }
    }


    /**
     * 获取勾选的明细数据
     *
     * @param detailTableName
     * @param detailIds
     */
    private void getDetailData(String detailTableName, String detailIds) {
        rs = new RecordSet();
        String sql = "select * from " + detailTableName + " where id in (" + detailIds + ")";
        rs.executeQuery(sql);
        detailData = QueryResultUtil.getJSONArrayList(rs);
    }

    private JSONObject getVendor(String zbdw) {
        JSONObject vendor = new JSONObject();
        rs = new RecordSet();
        if (rs.executeQuery("select top 1 * from uf_gystz where InvoiceName = (" +
                " select dwmc from uf_cwcc_xmrw_dt2 where id = (" +
                " select gysmc from uf_cwcc_xmrw_dt7 where id = ?))", zbdw)) {
            JSONArray ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                vendor = ja.getJSONObject(0);
            }
        }
        return vendor;
    }

    private JSONObject getDetail2Vendor(String zbdw) {
        JSONObject vendor = new JSONObject();
        rs = new RecordSet();
        if (rs.executeQuery(" select * from uf_cwcc_xmrw_dt2 where id = (" +
                " select gysmc from uf_cwcc_xmrw_dt7 where id = ?)", zbdw)) {
            JSONArray ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                vendor = ja.getJSONObject(0);
            }
        }
        return vendor;
    }

}
