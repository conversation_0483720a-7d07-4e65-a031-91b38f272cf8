package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.module.util.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;


/**
 * 保存或更新建模数据
 *
 * <AUTHOR>
 */
public class SaveOrUpdateCmd extends AbstractCommonCommand<Map<String, Object>> {

    public SaveOrUpdateCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        erroMsg = "";
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 数据库操作类（不带事务）
     */
    private RecordSet rs;
    /**
     * RecordSet返回成功失败值
     */
    private boolean rsFlag;
    /**
     * 错误信息
     */
    private String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());

        JSONObject jo, mainData;
        JSONArray detailData;
        String index, data;
        try {
            //校验参数
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid为空";
            }
            if (Util.null2String(params.get("allData")).isEmpty()) {
                erroMsg = "allData为空";
            }
            if (Util.null2String(params.get("mainTableName")).isEmpty()) {
                erroMsg = "mainTableName为空";
            }
            if (Util.null2String(params.get("moduleId")).isEmpty()) {
                erroMsg = "moduleId为空";
            }
            if (Util.null2String(params.get("stage")).isEmpty()) {
                erroMsg = "stage为空";
            }
            if (erroMsg.isEmpty()) {
                //建模数据id
                int billid = Integer.parseInt(Util.null2String(params.get("billid")));
                //所有的数据集
                String allData = Util.null2String(params.get("allData"));
                //建模主表名
                String mainTableName = Util.null2String(params.get("mainTableName"));
                //建模id
                int moduleId = Integer.parseInt(Util.null2String(params.get("moduleId")));
                //阶段
                String stage = Util.null2String(params.get("stage"));
                if (!allData.isEmpty()) {
                    JSONArray jsonArray = JSONArray.parseArray(allData);
                    if (!jsonArray.isEmpty()) {
                        //全新的数据，一定是先储存主表数据，再存储明细数据
                        for (int i = 0; i < jsonArray.size(); i++) {
                            jo = jsonArray.getJSONObject(i);
                            index = Util.null2String(jo.get("index"));
                            data = Util.null2String(jo.get("data"));
                            if ("0".equals(index)) {
                                mainData = JSONObject.parseObject(data);
                                billid = handleMainData(mainData, billid, mainTableName, moduleId);
                                result.put("newBillId", billid);
                            } else {
                                detailData = JSONArray.parseArray(data);
                                bb.writeLog("detailData : " + detailData);
                                handleDetailData(detailData, billid, index, mainTableName);
                                //更新主表的阶段信息
                                updateStage(mainTableName, stage, billid);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 处理主表数据
     *
     * @param jo
     * @param billid
     * @return
     */
    private int handleMainData(JSONObject jo, int billid, String mainTableName, int moduleId) throws Exception {
        //默认newBillid 等于billid
        int newBillid = billid;
        String insertFields = "";
        StringBuilder sbInsertFields = new StringBuilder();
        StringBuilder sbSetFields = new StringBuilder();
        List<Object> listValue = new ArrayList<>();
        String[] fieldsArray = new String[0];

        for (Map.Entry<String, Object> entry : jo.entrySet()) {
            sbInsertFields.append(entry.getKey()).append(",");
            if (Util.null2String(entry.getValue()).isEmpty()) {
                listValue.add(null);
            } else {
                listValue.add(entry.getValue());
            }

        }
        if (!sbInsertFields.toString().isEmpty()) {
            insertFields = sbInsertFields.substring(0, sbInsertFields.length() - 1);
            fieldsArray = insertFields.split(CommonCst.COMMA_EN);
        }
        bb.writeLog("insertFields:" + insertFields);
        if (billid == -1) {
            //新增数据
            if (!insertFields.isEmpty()) {
                //插入建模
                //新增的数据newBillid赋值
                newBillid = InsertModuleUtil.ModuleInsert(mainTableName, insertFields.split(","), listValue, user.getUID(), moduleId, null);
            }
        } else {
            //更新数据
            rs = new RecordSet();
            for (int i = 0; i < fieldsArray.length; i++) {
                sbSetFields.append(fieldsArray[i]).append("=").append("?");
                if (i != fieldsArray.length - 1) {
                    sbSetFields.append(", ");
                }
            }
            String sql = "update " + mainTableName + " set " + sbSetFields + " where id=" + billid;
            bb.writeLog("update mainData sql :" + sql);
            rsFlag = rs.executeUpdate(sql, listValue);
            if (!rsFlag) {
                erroMsg = rs.getMsg() + ";" + rs.getExceptionMsg();
            }
        }
        return newBillid;
    }

    /**
     * 处理明细数据
     *
     * @param ja
     * @param billid
     * @param mainTableName
     * @return
     * @throws Exception
     */
    private void handleDetailData(JSONArray ja, int billid, String detailIndex, String mainTableName) {
        JSONObject jo;
        String detailId;
        String insertFields;
        String[] fieldsArray = new String[0];
        JSONArray jaNew, jaUpdate;
        jaNew = new JSONArray();
        jaUpdate = new JSONArray();
        StringBuilder sbInsertFields = new StringBuilder();
        StringBuilder sbSetFields = new StringBuilder();
        List<Object> listValue;
        StringBuilder allUpdateId = new StringBuilder();
        //遍历获取，新增的数据，更新的数据
        for (int i = 0; i < ja.size(); i++) {
            jo = ja.getJSONObject(i);
            detailId = Util.null2String(jo.get("id"));
            //判断是否为正整数，正整数为已有的数据id
            if (checkDetailIsNew(detailId)) {
                jaNew.add(jo);
            } else {
                jaUpdate.add(jo);
                allUpdateId.append(detailId).append(CommonCst.COMMA_EN);
            }


            if (i == 0) {
                //拼接插入字段
                for (Map.Entry<String, Object> entry : jo.entrySet()) {
                    //不处理id字段
                    if (!"id".equals(entry.getKey())) {
                        sbInsertFields.append(entry.getKey()).append(",");
                    }
                }
                //默认在结尾加上mainid字段
                sbInsertFields.append("mainid");
            }
        }
        if (!sbInsertFields.toString().isEmpty()) {
            fieldsArray = sbInsertFields.toString().split(CommonCst.COMMA_EN);
        }
        bb.writeLog("fieldsArray: " + Arrays.toString(fieldsArray));
        //先删除明细数据（排除掉需要更新的数据id）
        rs = new RecordSet();
        String sql = "delete from " + mainTableName + "_dt" + detailIndex + " where mainid = ? ";
        if (!allUpdateId.toString().isEmpty()) {
            String allUpdateIdStr = allUpdateId.substring(0, allUpdateId.length() - 1);
            sql += " and id not in (" + allUpdateIdStr + ") ";

        }
        bb.writeLog("delete sql:" + sql);
        rs.executeUpdate(sql, billid);

        //新增的明细数组
        for (int i = 0; i < jaNew.size(); i++) {
            jo = jaNew.getJSONObject(i);
            listValue = new ArrayList<>();
            for (String fieldName : fieldsArray) {
                //新增时排除掉mainid字段获取值
                if (!"mainid".equals(fieldName)) {
                    if (Util.null2String(jo.get(fieldName)).isEmpty()) {
                        listValue.add(null);
                    } else {
                        listValue.add(jo.get(fieldName));
                    }
                }

            }
            //默认在末尾为mainid赋值主表数据id
            listValue.add(billid);
            bb.writeLog("新增的明细字段：" + Arrays.toString(fieldsArray));
            bb.writeLog("新增的明细数据：" + listValue);
            //插入建模明细
            InsertModuleUtil.DetailModuleInsert(mainTableName + "_dt" + detailIndex, fieldsArray, listValue);
        }

        //更新的明细数组
        for (String s : fieldsArray) {
            //排除掉mainid不需要更新
            if (!"mainid".equals(s)) {
                sbSetFields.append(s).append("=").append("?").append(", ");
            }
        }
        if (!sbSetFields.toString().isEmpty()) {
            sbSetFields = new StringBuilder(sbSetFields.substring(0, sbSetFields.length() - 2));
        }
        for (int i = 0; i < jaUpdate.size(); i++) {
            jo = jaUpdate.getJSONObject(i);
            detailId = Util.null2String(jo.get("id"));
            listValue = new ArrayList<>();
            for (String fieldName : fieldsArray) {
                //排除掉mainid不需要更新
                if (!"mainid".equals(fieldName)) {
                    if (Util.null2String(jo.get(fieldName)).isEmpty()) {
                        listValue.add(null);
                    } else {
                        listValue.add(jo.get(fieldName));
                    }
                }
            }
            rs = new RecordSet();
            bb.writeLog("更新的明细字段：" + sbSetFields);
            bb.writeLog("更新的明细数据：" + listValue);
            //更新建模明细
            sql = "update " + mainTableName + "_dt" + detailIndex + " set " + sbSetFields + " where id=" + detailId;
            bb.writeLog("更新明细sql:" + sql);
            rsFlag = rs.executeUpdate(sql, listValue);
            if (!rsFlag) {
                erroMsg = rs.getMsg() + ";" + rs.getExceptionMsg();
            }
        }
    }

    /**
     * 校验明细id是否为新增数据
     *
     * @param detailId
     * @return true 为新增数据
     */
    private boolean checkDetailIsNew(String detailId) {
        try {
            bb.writeLog("detailId:" + detailId);
            int detailInt = Integer.parseInt(detailId);
            bb.writeLog("detailInt:" + detailInt);
            if (detailInt > 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            bb.writeLog("Exception detailId");
            return true;
        }
    }

    private void updateStage(String mainTableName, String stage, int billId) {
        rs = new RecordSet();
        rs.executeUpdate("update " + mainTableName + " set rwjd = ? where id = ?", stage, billId);
    }

}
