package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.interfaces.gyl.esb.EsbEventUtil;
import com.engine.interfaces.gyl.esb.vo.EsbEventResult;
import com.engine.parent.query.util.QueryResultUtil;
import com.engine.parent.query.util.QueryUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * @FileName CreateBzjWorkCmd
 * @Description 创建保证金流程
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/2
 */
public class CreateBzjWorkCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CreateBzjWorkCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        erroMsg = "";
        mainData = new JSONObject();
        prjMainData = new JSONObject();
        detailData = new JSONArray();
    }

    //基类
    private final BaseBean bb;
    //数据库操作类（不带事务）
    private volatile RecordSet rs;
    //错误信息
    private volatile String erroMsg;
    private volatile JSONObject mainData;
    private volatile JSONObject prjMainData;
    private volatile JSONArray detailData;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());

        String moduleDetailTableName, workflowDetailTableName, moduleDetailIndex,
                detailIds, requestName, workflowId, appid, esbEvent;
        StringBuilder allNewReqIds = new StringBuilder();
        JSONObject eachDetail;
        JSONArray mainField;
        int billid;
        try {
            //校验参数
            //任务主表id
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid为空";
            }
            //勾选的明细ids
            if (Util.null2String(params.get("detailIds")).isEmpty()) {
                erroMsg = "detailIds为空";
            }
            //建模明细index
            if (Util.null2String(params.get("moduleDetailIndex")).isEmpty()) {
                erroMsg = "moduleDetailIndex为空";
            }
            //新建流程标题
            if (Util.null2String(params.get("requestName")).isEmpty()) {
                erroMsg = "requestName为空";
            }
            //新建流程workflowId
            if (Util.null2String(params.get("workflowId")).isEmpty()) {
                erroMsg = "workflowId为空";
            }
            //appid
            if (Util.null2String(params.get("appid")).isEmpty()) {
                erroMsg = "appid为空";
            }
            if (Util.null2String(params.get("esbEvent")).isEmpty()) {
                erroMsg = "esbEvent为空";
            }
            if (Util.null2String(params.get("mainField")).isEmpty()) {
                erroMsg = "mainField为空";
            }

            if (erroMsg.isEmpty()) {
                //数据id
                billid = Integer.parseInt(Util.null2String(params.get("billid")));
                //建模明细id
                detailIds = Util.null2String(params.get("detailIds"));
                //建模明细index
                moduleDetailIndex = Util.null2String(params.get("moduleDetailIndex"));
                moduleDetailTableName = "uf_cwcc_xmrw_dt" + moduleDetailIndex;
                bb.writeLog("detailTableName:" + moduleDetailTableName);
                //流程的明细表名
                workflowDetailTableName = Util.null2String(params.get("workflowDetailTableName"));
                bb.writeLog("workflowDetailTableName:" + workflowDetailTableName);
                //流程标题
                requestName = Util.null2String(params.get("requestName"));
                //流程id
                workflowId = Util.null2String(params.get("workflowId"));
                //appid
                appid = Util.null2String(params.get("appid"));
                //ESB事件
                esbEvent = Util.null2String(params.get("esbEvent"));
                //主表字段配置
                mainField = JSONArray.parseArray(Util.null2String(params.get("mainField")));
                //查看主表数据
                getMainData(billid);
                //查看子表数据
                getDetailData(moduleDetailTableName, detailIds);
                //查询项目主表信息
                getPrjMainData();
                //根据明细行，创建流程
                for (int i = 0; i < detailData.size(); i++) {

                    eachDetail = detailData.getJSONObject(i);
                    //明细行id
                    String detailId = Util.null2String(eachDetail.get("id"));
                    //组装ESB主表数据
                    JSONArray esbMainData = packageESBMainData(mainField, eachDetail);

                    //构造ESB事件参数
                    JSONObject esbParams = new JSONObject();
                    JSONObject otherParams = new JSONObject();
                    //默认不流转
                    otherParams.put("isnextflow", "0");
                    esbParams.put("appid", appid);
                    esbParams.put("userid", user.getUID());


                    JSONObject bodyParams = new JSONObject();
                    bodyParams.put("mainData", esbMainData);

                    bodyParams.put("requestName", requestName);
                    bodyParams.put("workflowId", workflowId);
                    bodyParams.put("otherParams", otherParams);
                    esbParams.put("data", bodyParams.toJSONString());

                    writeLog("esbParams:" + esbParams);
                    //调用ESB事件
                    EsbEventResult esbResult = EsbEventUtil.callEsbEvent(esbEvent, esbParams.toJSONString());
                    writeLog("esbResult:" + esbResult);

                    if (!esbResult.isSuccess()) {
                        erroMsg = esbResult.getErroMsg();
                    } else {
                        JSONObject joData = esbResult.getData();
                        if ("SUCCESS".equals(Util.null2String(joData.get("code")))) {
                            //新建的请求id
                            String newRequestId = Util.null2String(joData.get("requestid"));
                            allNewReqIds.append(newRequestId).append(",");
                            //更新当前明细行的保证金流程requestid，将requestid更新上
                            updateBzjReq(detailId, newRequestId, moduleDetailTableName);
                        } else {
                            erroMsg = "事件批次号：" + esbResult.getSerialNumber() + "，" + joData.toJSONString();
                        }
                    }
                    if (!erroMsg.isEmpty()) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (!allNewReqIds.toString().isEmpty()) {
            result.put("allNewReqIds", allNewReqIds.substring(0, allNewReqIds.length() - 1));
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 组装ESB 主表数据结构
     * 无法转文件
     *
     * @param mainField
     */
    private JSONArray packageESBMainData(JSONArray mainField, JSONObject eachDetail) {
        JSONArray esbMainData = new JSONArray();
        JSONObject eachCfg, jo;
        String targetField, fromField, fromIndex;
        Object fieldValue;
        for (int i = 0; i < mainField.size(); i++) {
            eachCfg = mainField.getJSONObject(i);
            //目标字段 流程
            targetField = Util.null2String(eachCfg.get("wfField"));
            //来源字段 建模
            fromField = Util.null2String(eachCfg.get("modField"));
            //来源位置 0主表 1明细表
            fromIndex = Util.null2String(eachCfg.get("modFieldPosition"));
            fieldValue = getMainFieldValue(fromField, fromIndex, eachDetail);
            if (!targetField.isEmpty() && fieldValue != null) {

                jo = new JSONObject();
                jo.put("fieldName", targetField);
                jo.put("fieldValue", fieldValue);
                esbMainData.add(jo);
            }
        }
        return esbMainData;
    }

    private Object getMainFieldValue(String fromField, String fromIndex, JSONObject eachDetail) {
        Object fieldValue;
        if (fromField.startsWith("$$")) {
            //$$开头的是固定值
            fieldValue = fromField.substring(2);
        } else if (fromField.startsWith("$xm$")) {
            //$xm$取项目主表的信息
            fieldValue = prjMainData.get(fromField.substring(4));
        } else if ("$user$".equals(fromField)) {
            //当前操作人
            fieldValue = user.getUID();
        } else if ("$dept$".equals(fromField)) {
            //当前操作人对应的部门
            fieldValue = user.getUserDepartment();
        } else if ("$subcompany$".equals(fromField)) {
            //当前操作人对应的所属分部
            fieldValue = user.getUserSubCompany1();
        } else if ("$date$".equals(fromField)) {
            //当前日期
            fieldValue = TimeUtil.getCurrentDateString();
        } else {
            if ("0".equals(fromIndex)) {
                //任务主表数据
                fieldValue = mainData.get(fromField);
            } else {
                //任务明细表数据
                fieldValue = eachDetail.get(fromField);
            }

        }
        return fieldValue;
    }


    /**
     * 获取任务主表信息
     *
     * @param billid
     */
    private void getMainData(int billid) {
        JSONArray ja;
        rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw where id = ?";
        if (rs.executeQuery(sql, billid)) {
            ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                mainData = ja.getJSONObject(0);
            }
        }
    }

    /**
     * 获取项目主表信息
     */
    private void getPrjMainData() {
        JSONArray ja;
        rs = new RecordSet();
        String anhao = Util.null2String(mainData.get("ah"));
        String sql = "select * from uf_xmxx where id = ? ";
        if (rs.executeQuery(sql, anhao)) {
            ja = QueryUtil.getJSONList(rs);
            if (!ja.isEmpty()) {
                prjMainData = ja.getJSONObject(0);
            }
        }
    }


    /**
     * 获取勾选的明细数据
     *
     * @param detailTableName
     * @param detailIds
     */
    private void getDetailData(String detailTableName, String detailIds) {
        rs = new RecordSet();
        String sql = "select * from " + detailTableName + " where id in (" + detailIds + ")";
        if (rs.executeQuery(sql)) {

            detailData = QueryUtil.getJSONList(rs);
        }
    }

    private void updateBzjReq(String detailId, String requestid, String moduleDetailTableName) {
        rs = new RecordSet();
        rs.executeUpdate("update " + moduleDetailTableName + " set bzjrequestid = ? where id = ?", requestid, detailId);
    }
}
