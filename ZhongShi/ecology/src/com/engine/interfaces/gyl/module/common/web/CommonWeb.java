package com.engine.interfaces.gyl.module.common.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.gyl.module.common.service.CommonService;
import com.engine.interfaces.gyl.module.common.service.impl.CommonServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 建模处理
 */
public class CommonWeb {

    private CommonService getService(User user) {
        return ServiceUtil.getService(CommonServiceImpl.class, user);
    }

    /**
     * 创建流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createWorkFlow")
    @Produces(MediaType.TEXT_PLAIN)
    public String createWorkFlow(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createWorkFlow---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createWorkFlow(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createWorkFlow，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 创建流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createWorkFlowById")
    @Produces(MediaType.TEXT_PLAIN)
    public String createWorkFlowById(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createWorkFlow---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createWorkFlowById(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createWorkFlow，result为：" + result);
        return JSONObject.toJSONString(result);
    }

}
