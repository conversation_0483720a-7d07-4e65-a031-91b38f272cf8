package com.engine.interfaces.gyl.module.common.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR>
 */
public interface CommonService {
    /**
     * 创建流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createWorkFlow(Map<String, Object> params, User user);

    /**
     * 创建流程
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> createWorkFlowById(Map<String, Object> params, User user);
}
