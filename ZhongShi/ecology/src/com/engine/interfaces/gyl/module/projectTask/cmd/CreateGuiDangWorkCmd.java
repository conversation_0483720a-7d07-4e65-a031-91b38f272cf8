package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.*;


/**
 * @FileName CreateGuiDangWorkCmd.java
 * @Description 创建总公司归档流程
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/9
 */
public class CreateGuiDangWorkCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CreateGuiDangWorkCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        erroMsg = "";
    }

    //基类
    private final BaseBean bb;
    private String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());
        String requestName, workflowId, appid, esbEvent;
        int billid;
        try {
            //校验参数
            //任务主表id
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid为空";
            }
            //新建流程标题
            if (Util.null2String(params.get("requestName")).isEmpty()) {
                erroMsg = "requestName为空";
            }
            //新建流程workflowId
            if (Util.null2String(params.get("workflowId")).isEmpty()) {
                erroMsg = "workflowId为空";
            }
            //appid
            if (Util.null2String(params.get("appid")).isEmpty()) {
                erroMsg = "appid为空";
            }
            if (Util.null2String(params.get("esbEvent")).isEmpty()) {
                erroMsg = "esbEvent为空";
            }

            if (erroMsg.isEmpty()) {
                //数据id
                billid = Integer.parseInt(Util.null2String(params.get("billid")));
                //流程标题
                requestName = Util.null2String(params.get("requestName"));
                //流程id
                workflowId = Util.null2String(params.get("workflowId"));
                //appid
                appid = Util.null2String(params.get("appid"));
                //ESB事件
                esbEvent = Util.null2String(params.get("esbEvent"));

                //查看主表数据
                Map<String, Object> mainData = getMainData(billid);
                if (mainData == null) {
                    erroMsg = "未查询到任务主表数据，billid:" + billid;
                } else {
                    //获取当前任务的案号id
                    String anHao = Util.null2String(mainData.get("ah"));
                    //查询项目主表信息
                    Map<String, Object> prjData = getPrjMainData(anHao);
                    if (prjData == null) {
                        erroMsg = "未查询到项目主表数据，案号id:" + anHao;
                    } else {
                        //组装ESB主表数据
                        JSONArray esbMainData = packageESBMainData(mainData, prjData, billid);
                        //构造ESB事件参数
                        JSONObject esbParams = new JSONObject();
                        JSONObject otherParams = new JSONObject();

                        //默认不流转
                        otherParams.put("isnextflow", "0");
                        esbParams.put("appid", appid);
                        esbParams.put("userid", user.getUID());
                        JSONObject bodyParams = new JSONObject();
                        bodyParams.put("mainData", esbMainData);


                        bodyParams.put("requestName", requestName);
                        bodyParams.put("workflowId", workflowId);
                        bodyParams.put("otherParams", otherParams);
                        esbParams.put("data", bodyParams.toJSONString());

                        writeLog("esbParams:" + esbParams);
                        //调用ESB事件
                        EsbEventResult esbResult = EsbUtil.callEsbEvent(esbEvent, esbParams.toJSONString());
                        if (!esbResult.isSuccess()) {
                            erroMsg = esbResult.getErroMsg();
                        } else {
                            JSONObject joData = esbResult.getData();
                            if ("SUCCESS".equals(Util.null2String(joData.get("code")))) {
                                //新建的请求id
                                String newRequestId = Util.null2String(joData.get("requestid"));
                                result.put("newReqId", newRequestId);
                            } else {
                                erroMsg = "事件批次号：" + esbResult.getSerialNumber() + "，" + joData.toJSONString();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {

            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }

        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    /**
     * 组装ESB 主表数据结构
     *
     * @param mainData
     * @param prjData
     * @param billid
     * @return
     */
    private JSONArray packageESBMainData(Map<String, Object> mainData, Map<String, Object> prjData, int billid) {
        JSONArray result = new JSONArray();
        JSONObject jo;

        Map<String, Object> zhongBiaoInfo = getZhongBiaoInfo(billid);

        //案号
        jo = new JSONObject();
        jo.put("fieldName", "ah");
        jo.put("fieldValue", mainData.get("ah"));
        result.add(jo);

        //项目名称
        jo = new JSONObject();
        jo.put("fieldName", "xmmc");
        jo.put("fieldValue", mainData.get("xmmc"));
        result.add(jo);

        //归档文件
        jo = new JSONObject();
        jo.put("fieldName", "gdwj");
        jo.put("fieldValue", getAllFile(billid));
        result.add(jo);

        //服务类别，对应项目信息里的-项目类型
        jo = new JSONObject();
        jo.put("fieldName", "fwlb");
        jo.put("fieldValue", convertFwlb(Util.null2String(prjData.get("xmlx"))));
        result.add(jo);

        //服务内容，对应项目信息里的-项目子类
        jo = new JSONObject();
        jo.put("fieldName", "fwnr");
        jo.put("fieldValue", convertFwnr(Util.null2String(prjData.get("xmzl"))));
        result.add(jo);

        //中标金额,项目任务-明细4-中标金金额合计
        jo = new JSONObject();
        jo.put("fieldName", "zbje");
        jo.put("fieldValue", zhongBiaoInfo.get("total_zbjje"));
        result.add(jo);

        //代理服务费,️项目任务-明细4-中标服务费合计
        jo = new JSONObject();
        jo.put("fieldName", "dlfwfy");
        jo.put("fieldValue", zhongBiaoInfo.get("total_zbfwf"));
        result.add(jo);

        //中标单位,项目任务-明细4-单位名称（文本）平铺式，逗号隔开
        jo = new JSONObject();
        jo.put("fieldName", "zbdw");
        jo.put("fieldValue", zhongBiaoInfo.get("dwmcwb"));
        result.add(jo);

        //归档类别 - 默认值1
        jo = new JSONObject();
        jo.put("fieldName", "gdlb");
        jo.put("fieldValue", "1");
        result.add(jo);

        //是否纸质归档 - 默认值0
        jo = new JSONObject();
        jo.put("fieldName", "sfzzgd");
        jo.put("fieldValue", "0");
        result.add(jo);


        //满意度情况 - 默认值0
        jo = new JSONObject();
        jo.put("fieldName", "mydqk");
        jo.put("fieldValue", "0");
        result.add(jo);

        return result;
    }

    private Map<String, Object> getZhongBiaoInfo(int billid) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        // 中标金金额, 中标服务费
        BigDecimal zbjje, zbfwf;
        BigDecimal total_zbjje = BigDecimal.ZERO;
        BigDecimal total_zbfwf = BigDecimal.ZERO;
        // 单位名称（文本）
        String dwmcwb = "";
        StringBuilder sb = new StringBuilder();
        String sql = "select zbjje,zbfwf,dwmcwb from uf_cwcc_xmrw_dt4 where mainid = " + billid;
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                zbjje = SDUtil.getBigDecimalValue(rs.getString("zbjje"));
                zbfwf = SDUtil.getBigDecimalValue(rs.getString("zbfwf"));
                dwmcwb = Util.null2String(rs.getString("dwmcwb"));
                total_zbjje = total_zbjje.add(zbjje);
                total_zbfwf = total_zbfwf.add(zbfwf);
                if (!dwmcwb.isEmpty()) {
                    sb.append(dwmcwb).append(",");
                }
            }
        }
        if (!sb.toString().isEmpty()) {
            dwmcwb = sb.substring(0, sb.length() - 1);
        }
        result.put("total_zbjje", total_zbjje);
        result.put("total_zbfwf", total_zbfwf);
        result.put("dwmcwb", dwmcwb);
        return result;
    }


    private String getAllFile(int billid) {
        RecordSet rs = new RecordSet();
        String gdwj;
        Set<String> allDoc = new HashSet<>();
        String sql = "select gdwj from uf_cwcc_xmrw_dt5 where mainid = " + billid;
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                gdwj = Util.null2String(rs.getString("gdwj"));
                if (!gdwj.isEmpty()) {
                    add2FileSet(gdwj, allDoc);
                }
            }
        }
        String[] allDocArray = allDoc.stream().map(String::toString).toArray(String[]::new);
        return Arrays.toString(allDocArray);
    }

    private void add2FileSet(String docids, Set<String> allDoc) {
        String[] array = docids.split(CommonCst.COMMA_EN);
        allDoc.addAll(Arrays.asList(array));
    }


    /**
     * 转换项目子类-服务内容
     *
     * @param xmzl
     * @return
     */
    private String convertFwnr(String xmzl) {
        //招标代理-货物
        if ("28".equals(xmzl)) {
            return "4";
        }
        //招标代理-施工
        if ("29".equals(xmzl)) {
            return "3";
        }
        //招标代理-监理
        if ("30".equals(xmzl)) {
            return "2";
        }
        //招标代理-勘察
        if ("31".equals(xmzl)) {
            return "1";
        }
        //招标代理-设计
        if ("32".equals(xmzl)) {
            return "0";
        }
        //政府采购-工程
        if ("33".equals(xmzl)) {
            return "7";
        }
        //政府采购-服务
        if ("34".equals(xmzl)) {
            return "6";
        }
        //政府采购-货物
        if ("35".equals(xmzl)) {
            return "8";
        }
        //招标代理-服务
        if ("23".equals(xmzl)) {
            return "5";
        }
        return "";
    }

    /**
     * 转换项目类型-服务类别
     *
     * @param xmzl
     * @return
     */
    private String convertFwlb(String xmzl) {
        //项目类型
        if ("1".equals(xmzl)) {
            return "0";
        }
        return "";
    }

    /**
     * 获取任务主表信息
     *
     * @param billid
     */
    private Map<String, Object> getMainData(int billid) {
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw where id = ?";
        if (rs.executeQuery(sql, billid)) {
            return QueryUtil.getMap(rs);
        }
        return null;
    }

    /**
     * 获取项目主表信息
     */
    private Map<String, Object> getPrjMainData(String anHao) {
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_xmxx where id = ? ";
        if (rs.executeQuery(sql, anHao)) {
            return QueryUtil.getMap(rs);
        }
        return null;
    }


}
