package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * 保存或更新建模数据
 *
 * <AUTHOR>
 */
public class QueryCopyProjectCmd extends AbstractCommonCommand<Map<String, Object>> {

    public QueryCopyProjectCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        erroMsg = "";
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 错误信息
     */
    private String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());
        String projectId;
        boolean rsFlag;
        try {
            projectId = Util.null2String(params.get("projectId"));
            //校验参数
            if (projectId.isEmpty()) {
                erroMsg = "projectId为空";
            }
            if (erroMsg.isEmpty()) {
                String sql = "select count(1) as cnt from uf_xmxx where ysxm = ?";
                RecordSet rs = new RecordSet();
                rsFlag = rs.executeQuery(sql, projectId);
                if (!rsFlag) {
                    erroMsg = rs.getMsg() + ";" + rs.getExceptionMsg();
                } else {
                    if (rs.next()) {
                        result.put("cnt", Util.null2String(rs.getString("cnt")));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


}
