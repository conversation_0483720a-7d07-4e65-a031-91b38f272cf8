package com.engine.interfaces.gyl.module.projectTask.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.gyl.module.projectTask.cmd.*;
import com.engine.interfaces.gyl.module.projectTask.service.ModHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR> Mason
 */
public class ModHandleServiceImpl extends Service implements ModHandleService {
    @Override
    public Map<String, Object> saveOrUpdate(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveOrUpdateCmd(params, user));
    }

    @Override
    public Map<String, Object> copyProject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CopyProjectCmd(params, user));
    }

    @Override
    public Map<String, Object> queryCopyProject(Map<String, Object> params, User user) {
        return commandExecutor.execute(new QueryCopyProjectCmd(params, user));
    }

    @Override
    public Map<String, Object> retreatBail(Map<String, Object> params, User user) {
        return commandExecutor.execute(new RetreatBailCmd(params, user));
    }

    @Override
    public Map<String, Object> createWorkFlow(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateWorkFlowCmd(params, user));
    }

    @Override
    public Map<String, Object> copyBsm(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CopyBsmCmd(params, user));
    }

    @Override
    public Map<String, Object> createInvoiceWork(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateInvoiceWorkCmd(params, user));
    }

    @Override
    public Map<String, Object> updateProjectInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UpdateProjectInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> createBzjWork(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateBzjWorkCmd(params, user));
    }

    @Override
    public Map<String, Object> createKaipiaoWork(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateKaipiaoWorkCmd(params, user));
    }

    @Override
    public Map<String, Object> createFenpeiWork(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateFenpeiWorkCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> createGuiDangWork(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreateGuiDangWorkCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> updateFabiaoBzj(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UpdateFabiaoBzjCmd(params, user));
    }

}
  