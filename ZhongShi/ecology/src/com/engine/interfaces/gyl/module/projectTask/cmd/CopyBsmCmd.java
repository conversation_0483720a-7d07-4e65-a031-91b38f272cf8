package com.engine.interfaces.gyl.module.projectTask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;


/**
 * 退保证金
 *
 * <AUTHOR>
 */
public class CopyBsmCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CopyBsmCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        erroMsg = "";
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 数据库操作类（不带事务）
     */
    private RecordSet rs;
    /**
     * RecordSet返回成功失败值
     */
    private boolean rsFlag;
    /**
     * 错误信息
     */
    private String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());


        try {
            //校验参数
            if (Util.null2String(params.get("billid")).isEmpty()) {
                erroMsg = "billid参数为空";
            }
            //明细行id
            if (Util.null2String(params.get("checkedkey")).isEmpty()) {
                erroMsg = "checkedkey参数为空";
            }

            if (erroMsg.isEmpty()) {
                //明细id

                String checkedkey = Util.null2String(params.get("checkedkey"));
                JSONObject jo = getDetailData(checkedkey);
                if (!jo.isEmpty()) {
                    int newBjh = getNewBjh(Util.null2String(params.get("billid")));
                    String newBjhStr = "包件" + newBjh;
                    bb.writeLog("newBjhStr:" + newBjhStr);
                    List<String> insertFields = new ArrayList<>();
                    insertFields.add("mainid");
                    insertFields.add("bjh");
                    insertFields.add("bjsm");
                    insertFields.add("bzj");
                    insertFields.add("zbgsrq");
                    insertFields.add("kbrq");
                    insertFields.add("pbrq");
                    insertFields.add("bsf");
                    insertFields.add("bsm");
                    insertFields.add("scsj");
                    insertFields.add("wjlj");
                    List<Object> detailValues = new ArrayList<>();
                    detailValues.add(jo.get("mainid"));
                    detailValues.add(newBjhStr);
                    detailValues.add(jo.get("bjsm"));
                    detailValues.add(jo.get("bzj"));
                    detailValues.add(jo.get("zbgsrq"));
                    detailValues.add(jo.get("kbrq"));
                    detailValues.add(jo.get("pbrq"));
                    detailValues.add(jo.get("bsf"));
                    detailValues.add(jo.get("bsm"));
                    detailValues.add(jo.get("scsj"));
                    detailValues.add(jo.get("wjlj"));

                    bb.writeLog("insertDetailFields:" + insertFields);
                    bb.writeLog("detailValues:" + detailValues);
                    //明细表暂时没做失败信息判断
                    InsertModuleUtil.DetailModuleInsert("uf_cwcc_xmrw_dt1", insertFields, detailValues);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + Arrays.toString(e.getStackTrace()));
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
        }
        if (erroMsg.isEmpty()) {
            result.put("status", "1");
        } else {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    private JSONObject getDetailData(String id) {
        JSONArray ja;
        JSONObject jo = new JSONObject();
        rs = new RecordSet();
        String sql = "select * from uf_cwcc_xmrw_dt1 where id = ?";
        rsFlag = rs.executeQuery(sql, id);
        if (rsFlag) {
            ja = QueryResultUtil.getJSONArrayList(rs);
            if (!ja.isEmpty()) {
                jo = ja.getJSONObject(0);
            }
        }
        return jo;
    }

    private int getNewBjh(String mainid) {
        int newBjh = 1;
        rs = new RecordSet();
        String sql = "select count(*) as cnt from uf_cwcc_xmrw_dt1 where mainid = ?";
        rs.executeQuery(sql, mainid);
        if (rs.next()) {
            int cnt = rs.getInt("cnt");
            newBjh = newBjh + cnt;
        }
        return newBjh;
    }

}
