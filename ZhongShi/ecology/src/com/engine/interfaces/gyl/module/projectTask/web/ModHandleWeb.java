package com.engine.interfaces.gyl.module.projectTask.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.gyl.module.projectTask.service.ModHandleService;
import com.engine.interfaces.gyl.module.projectTask.service.impl.ModHandleServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 建模处理
 */
public class ModHandleWeb {

    private ModHandleService getService(User user) {
        return ServiceUtil.getService(ModHandleServiceImpl.class, user);
    }

    /**
     * 保存或更新建模数据
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveOrUpdate")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveOrUpdate(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "saveOrUpdate---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).saveOrUpdate(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "saveOrUpdate，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 复制项目
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/copyProject")
    @Produces(MediaType.TEXT_PLAIN)
    public String copyProject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "copyProject---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).copyProject(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "copyProject，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 查询复制过的项目
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/queryCopyProject")
    @Produces(MediaType.TEXT_PLAIN)
    public String queryCopyProject(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "queryCopyProject---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).queryCopyProject(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "queryCopyProject，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 退保证金
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/retreatBail")
    @Produces(MediaType.TEXT_PLAIN)
    public String retreatBail(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "retreatBail---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).retreatBail(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "retreatBail，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 创建流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createWorkFlow")
    @Produces(MediaType.TEXT_PLAIN)
    public String createWorkFlow(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createWorkFlow---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createWorkFlow(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createWorkFlow，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 复制标书名信息并保存
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/copyBsm")
    @Produces(MediaType.TEXT_PLAIN)
    public String copyBsm(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "copyBsm---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).copyBsm(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "copyBsm，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 创建开票服务费流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createInvoiceWork")
    @Produces(MediaType.TEXT_PLAIN)
    public String createInvoiceWork(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createInvoiceWork---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createInvoiceWork(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createInvoiceWork，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 从任务信息更新到项目信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/updateProjectInfo")
    @Produces(MediaType.TEXT_PLAIN)
    public String updateProjectInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "updateProjectInfo---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).updateProjectInfo(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "updateProjectInfo，result为：" + result);
        return JSONObject.toJSONString(result);
    }


    /**
     * 创建保证金流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createBzjWork")
    @Produces(MediaType.TEXT_PLAIN)
    public String createBzjWork(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createBzjWork---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createBzjWork(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createBzjWork，result为：" + result);
        return JSONObject.toJSONString(result);
    }


    /**
     * 创建开票流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createKpWork")
    @Produces(MediaType.TEXT_PLAIN)
    public String createKaipiaoWork(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createKaipiaoWork---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createKaipiaoWork(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createKaipiaoWork，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 创建分配流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createFenpeiWork")
    @Produces(MediaType.TEXT_PLAIN)
    public String createFenpeiWork(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createFenpeiWork---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createFenpeiWork(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createFenpeiWork，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 创建总公司归档流程
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/createGuiDang")
    @Produces(MediaType.TEXT_PLAIN)
    public String createGuiDangWork(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "createFenpeiWork---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createGuiDangWork(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "人员信息有误");
            }
        } catch (Exception ex) {
            result.put("status", "0");
            result.put("errMsg", "接口异常： " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "createFenpeiWork，result为：" + result);
        return JSONObject.toJSONString(result);
    }

    /**
     * 更新发标阶段的保证金=包件信息的保证金金额
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/updateFabiaoBzj")
    @Produces(MediaType.TEXT_PLAIN)
    public String updateFabiaoBzj(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).updateFabiaoBzj(params, user);
        } else {
            result.put("status", false);
            result.put("erroMsg", "人员信息有误");
        }
        return JSONObject.toJSONString(result);
    }

}
