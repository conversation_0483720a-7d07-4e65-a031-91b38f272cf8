package com.engine.interfaces.gyl.doc.web;

import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.gyl.doc.service.DownloadDocFileService;
import com.engine.interfaces.gyl.doc.service.impl.DownloadDocFileServiceImpl;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 下载文档文件
 */
public class DownloadDocFileWeb {
    private DownloadDocFileService getService(User user) {
        return ServiceUtil.getService(DownloadDocFileServiceImpl.class, user);
    }


    /**
     * 获取下载链接
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getDownloadPath")
    @Produces(MediaType.TEXT_PLAIN)
    public void getDownloadPath(@Context HttpServletRequest request, @Context HttpServletResponse response) throws IOException {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "getDownloadPath---START");
        Map<String, Object> result;
        //获取请求链接的域名
        String host = request.getHeader("host");
        bb.writeLog("host", host);
        //默认使用系统管理员
        User user = new User(1);
        Map<String, Object> params = ParamUtil.request2Map(request);
        params.put("host", host);
        result = getService(user).getDownloadPath(params, user);
        String retCode = Util.null2String(result.get("code"));
        if ("SUCCESS".equals(retCode)) {
            String sendUrl = "http://" + Util.null2String(result.get("data"));
            bb.writeLog("跳转页面URL:{}", sendUrl);
            response.sendRedirect(sendUrl);
        }
    }
}
