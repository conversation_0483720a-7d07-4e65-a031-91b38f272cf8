package com.engine.interfaces.gyl.doc.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 保存或更新建模数据
 *
 * <AUTHOR>
 */
public class GetDownloadFilePathCmd extends AbstractCommonCommand<Map<String, Object>> {

    public GetDownloadFilePathCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();

    }

    /**
     * 基类
     */
    private final BaseBean bb;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());
        String downloadPath = "";
        String erroMsg = "";
        try {
            //多个用英文逗号隔开
            String docid = Util.null2String(params.get("docid"));
            String host = Util.null2String(params.get("host"));
            if (!docid.isEmpty()) {
                List<DocFileInfo> listDoc = DocUtil.getDocFileInfoByDocId(docid);
                if (!listDoc.isEmpty()) {
                    DocFileInfo firstFile = listDoc.get(0);
                    String fileId = firstFile.getFileid();
                    downloadPath = host + "/weaver/weaver.file.custom.DownloadFileForNoUser?download=1&fileid=" + fileId;
                } else {
                    erroMsg = "未查询到文档id对应的附件信息";
                }
            } else {
                erroMsg = "参数docid为空！";
            }

        } catch (Exception e) {
            erroMsg = "获取文档下载链接异常：" + SDUtil.getExceptionDetail(e);
        }
        if (erroMsg.isEmpty()) {
            result.put("code", "SUCCESS");
            result.put("data", downloadPath);

        } else {
            result.put("code", "ERRO");
            result.put("erroMsg", erroMsg);
        }
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


}
