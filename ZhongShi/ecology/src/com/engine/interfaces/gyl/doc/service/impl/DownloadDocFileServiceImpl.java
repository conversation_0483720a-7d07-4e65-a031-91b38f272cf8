package com.engine.interfaces.gyl.doc.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.gyl.doc.cmd.GetDownloadFilePathCmd;
import com.engine.interfaces.gyl.doc.service.DownloadDocFileService;
import weaver.hrm.User;

import java.util.Map;

/**
 * 下载文档附件
 *
 * <AUTHOR> Mason
 */
public class DownloadDocFileServiceImpl extends Service implements DownloadDocFileService {


    @Override
    public Map<String, Object> getDownloadPath(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetDownloadFilePathCmd(params, user));
    }
}
  