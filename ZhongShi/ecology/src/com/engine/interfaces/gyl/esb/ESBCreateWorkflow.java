package com.engine.interfaces.gyl.esb;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.http.dto.WeaverApiResult;
import com.engine.parent.http.util.HttpUtil;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * 交换中心创建流程接口
 */
public class ESBCreateWorkflow {

    /**
     * 默认执行方法
     *
     * @param params
     * @return
     */
    public Map<String, Object> execute(Map<String, Object> params) {
        Map<String, Object> ret = new HashMap<>();
        ret.put("code", "PARAM_ERRO");
        try {
            String data = Util.null2String(params.get("data"));
            String appid = Util.null2String(params.get("appid"));
            String userid = Util.null2String(params.get("userid"));
            String token = Util.null2String(params.get("token"));
            String url = Util.null2String(params.get("url"));
            if (!data.isEmpty()) {
                // 示例：data：定义的请求数据，code:定义的响应数据
                JSONObject jo = JSONObject.parseObject(data);
                if (jo != null) {
                    Map<String, Object> bodyParam = new HashMap<>(6);
                    bodyParam.putAll(jo);
                    WeaverApiResult ar = HttpUtil.postWeaverApi(url, appid, userid, token, bodyParam);
                    ret.put("code", ar.getCode());
                    if ("SUCCESS".equals(ar.getCode())) {
                        JSONObject resultData = JSONObject.parseObject(ar.getData());
                        ret.put("requestid", Util.null2String(resultData.get("requestid")));
                    } else {
                        ret.put("errMsg", ar.getErrMsg());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret.put("code", "SYSTEM_INNER_ERROR");
            ret.put("errMsg", e.getMessage());
        }
        return ret;

    }
}
