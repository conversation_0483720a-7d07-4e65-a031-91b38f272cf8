package com.engine.interfaces.gyl.esb;

import com.alibaba.fastjson.JSONObject;
import com.engine.interfaces.gyl.esb.vo.EsbEventResult;
import com.weaver.esb.client.EsbClient;
import com.weaver.esb.spi.EsbService;
import weaver.general.BaseBean;
import weaver.general.Util;

public class EsbEventUtil {

    /**
     * 调用ESB事件
     *
     * @param eventName
     * @param esbParam
     * @return
     */
    public static EsbEventResult callEsbEvent(String eventName, String esbParam) {
        EsbEventResult result = new EsbEventResult();
        BaseBean bb = new BaseBean();
        result.setSuccess(true);
        String erroMsg = "";
        try {
            String esbResult;
            //设置ESB服务
            EsbService esbService = EsbClient.getService();
            bb.writeLog("EsbEventUtil --esbParam: " + esbParam);
            esbResult = esbService.execute(eventName, esbParam);
            bb.writeLog("EsbEventUtil --esbResult: " + esbResult);
            if (esbResult.isEmpty()) {
                erroMsg = "执行" + eventName + "ESB事件出错，未获取到ESB事件返回值";
            } else {
                JSONObject joEsbResult = JSONObject.parseObject(esbResult);
                result.setSerialNumber(Util.null2String(joEsbResult.get("batchKey")));
                if (!"100".equals(Util.null2String(joEsbResult.get("code")))) {
                    erroMsg = "执行" + eventName + "ESB事件出错，事件批次号：" + Util.null2String(joEsbResult.get("batchKey")) + "," + esbResult;
                } else {
                    JSONObject joData = joEsbResult.getJSONObject("data");
                    result.setData(joData);
                }
            }
            if (!erroMsg.isEmpty()) {
                result.setSuccess(false);
                result.setErroMsg(erroMsg);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setErroMsg("执行调用" + eventName + "ESB事件异常：" + e.getMessage());
        }

        return result;
    }

}
