package com.engine.interfaces.gyl.esb.vo;

import com.alibaba.fastjson.JSONObject;

/**
 * ESB事件结果
 */
public class EsbEventResult {
    /**
     * 是否成功
     */
    private boolean isSuccess;
    /**
     * 返回信息
     */
    private JSONObject data;
    /**
     * 失败信息
     */
    private String erroMsg;
    /**
     * 事件批次号
     */
    private String serialNumber;

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public JSONObject getData() {
        return data;
    }

    public void setData(JSONObject data) {
        this.data = data;
    }

    public String getErroMsg() {
        return erroMsg;
    }

    public void setErroMsg(String erroMsg) {
        this.erroMsg = erroMsg;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }
}
