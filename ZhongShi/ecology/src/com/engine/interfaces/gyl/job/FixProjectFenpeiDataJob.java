package com.engine.interfaces.gyl.job;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.List;
import java.util.Map;

/**
 * @FileName FixProjectFenpeiDataJob.java
 * @Description 修复-项目任务-分配阶段数据
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/12/11
 */
@Getter
@Setter
public class FixProjectFenpeiDataJob extends BaseCronJob {
    //---Job 参数---START
    /**
     * 分配流程workflowtable表单名
     */
    private String fenpeiWorkTable;
    /**
     * 指定的案号id
     */
    private String anhaoId;
    //---Job 参数---END

    private BaseBean bb;

    private void _init() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _init();
        try {
            bb.writeLog(this.getClass().getName() + "---START");
            RecordSet rs = new RecordSet();
            //step 1: 查询出，一个案号对应多条流程的错误数据，这些数据不做处理
            checkErrorData(rs);
            //step 2: 查询出需要更新的所有案号数据
            updateData(rs);
        } catch (Exception e) {
            bb.writeLog("FixProjectFenpeiDataJob 异常：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

    private void checkErrorData(RecordSet rs) {
        String sql = "SELECT " +
                " m.ah, " +
                " count( m.requestid ) AS cnt_wf  " +
                "FROM " + fenpeiWorkTable +
                "  m " +
                " LEFT JOIN workflow_requestbase r ON ( m.requestid = r.requestid )  " +
                "WHERE " +
                " m.ah IS NOT NULL  " +
                " AND m.ah != ''  " +
                " AND r.currentnodetype = 3  " +
                "GROUP BY " +
                " m.ah  " +
                "HAVING " +
                " count( m.requestid ) > 1";
        if (rs.executeQuery(sql)) {
            List<Map<String, Object>> mapList = QueryUtil.getMapList(rs);
            if (!mapList.isEmpty()) {
                bb.writeLog("以下案号存在多个归档的流程使用：" + mapList);
            }
        }
    }

    private void updateData(RecordSet rs) {
        String fenpeiWorkTableDetail = fenpeiWorkTable + "_dt3";
        //查询字段：案号id，流程id，任务台账的数量，流程的数量，案号名称
        String sql = "SELECT " +
                " tbc.*, " +
                " xm.ah AS anhao  " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  tba.ah, " +
                "  tbb.id as rwid, " +
                "  tba.requestid, " +
                "  isnull( tbb.cnt_rw, 0 ) AS cnt_rw, " +
                "  isnull( tba.cnt_wf, 0 ) AS cnt_wf  " +
                " FROM " +
                //查询已归档分配流程
                "  ( " +
                "  SELECT " +
                "   m.ah, " +
                "   m.requestid, " +
                "   count( a.id ) AS cnt_wf  " +
                "  FROM " + fenpeiWorkTableDetail +
                "    a " +
                "   LEFT JOIN " + fenpeiWorkTable + " m ON ( a.mainid = m.id ) " +
                "   LEFT JOIN workflow_requestbase r ON ( m.requestid = r.requestid )  " +
                "  WHERE " +
                "   m.ah IS NOT NULL  " +
                "   AND m.ah != ''  " +
                "   AND r.currentnodetype = 3  ";
        //如果指定的案号id，则查询指定的案号
        if (StringUtils.isNotBlank(anhaoId)) {
            sql += " AND m.ah = " + anhaoId;
        }
        //排除一个案号有多个流程的流程数据
        sql += "   AND m.ah NOT IN ( " +
                "   SELECT " +
                "    m.ah  " +
                "   FROM " + fenpeiWorkTable +
                "     m " +
                "    LEFT JOIN workflow_requestbase r ON ( m.requestid = r.requestid )  " +
                "   WHERE " +
                "    m.ah IS NOT NULL  " +
                "    AND m.ah != ''  " +
                "    AND r.currentnodetype = 3  " +
                "   GROUP BY " +
                "    m.ah  " +
                "   HAVING " +
                "    count( m.requestid ) > 1  " +
                "   )  " +
                "  GROUP BY " +
                "   m.ah, " +
                "   m.requestid  " +
                "  ) tba " +
                //连接查询项目任务的分配明细
                "  INNER JOIN ( " +
                //第一部分：明细行6没数据的
                "  SELECT " +
                "   m.ah, " +
                "   m.id, " +
                "   count( a.id ) AS cnt_rw  " +
                "  FROM " +
                "   uf_cwcc_xmrw m " +
                "   LEFT JOIN uf_cwcc_xmrw_dt6 a ON ( a.mainid = m.id )  " +
                "  WHERE " +
                "   m.ah IS NOT NULL  " +
                "   AND m.ah != ''  " +
                "  GROUP BY " +
                "   m.ah,m.id  " +
                "  HAVING " +
                "   count( a.id ) = 0 UNION ALL " +
                //第二部分：明细有数据，筛选有分配流程的（排除掉手工导入的）
                "  SELECT " +
                "   m.ah, " +
                "   m.id, " +
                "   count( a.id ) AS cnt_rw  " +
                "  FROM " +
                "   uf_cwcc_xmrw m " +
                "   LEFT JOIN uf_cwcc_xmrw_dt6 a ON ( a.mainid = m.id )  " +
                "  WHERE " +
                "   m.ah IS NOT NULL  " +
                "   AND m.ah != ''  " +
                "   AND a.fplc IS NOT NULL  " +
                "   AND a.fplc != ''  " +
                "  GROUP BY " +
                "   m.ah,m.id  " +
                "  HAVING " +
                "   count( a.id ) > 0  " +
                "  ) tbb ON ( tba.ah = tbb.ah )  " +
                " ) tbc " +
                " LEFT JOIN uf_xmxx xm ON ( xm.id = tbc.ah )  " +
                " WHERE " +
                //筛选出流程和建模的明细不相等的
                " tbc.cnt_rw != tbc.cnt_wf";
        bb.writeLog("update query sql :" + sql);
        //查出来的字段名：ah,requestid,cnt_rw,cnt_wf,anhao
        //案号id，流程id，任务台账的数量，流程的数量，案号名称
        if (rs.executeQuery(sql)) {
            List<Map<String, Object>> list = QueryUtil.getMapList(rs);
            if (!list.isEmpty()) {
                bb.writeLog("待处理的数量：" + list.size());
                bb.writeLog("待处理的数据：" + list);
                String currentDateTime = TimeUtil.getCurrentTimeString();

                for (Map<String, Object> map : list) {
                    //step: 处理每一组数据
                    process(map, rs, currentDateTime);
                }

//                //使用多线程执行每个ESB任务
//                ExecutorService executorService = Executors.newFixedThreadPool(5);
//                CountDownLatch latch = new CountDownLatch(list.size());
//                for (Map<String, Object> map : list) {
//                    executorService.submit(() -> {
//                        try {
//                            //step: 处理每一组数据
//                            process(map, rs, currentDateTime);
//                        } finally {
//                            //线程完成任务后减少计数器
//                            latch.countDown();
//                            try {
//                                // 让线程等待2秒钟
//                                Thread.sleep(2000);
//                            } catch (InterruptedException e) {
//                                bb.writeLog("Thread.sleep() 出错：" + SDUtil.getExceptionDetail(e));
//                            }
//
//                        }
//                    });
//                }
//                try {
//                    // 等待所有线程完成任务，超时限制
//                    if (!latch.await(60, TimeUnit.MINUTES)) {
//                        bb.writeLog("超时60分钟处理完");
//                    }
//                } catch (InterruptedException e) {
//                    bb.writeLog("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
//                } finally {
//                    executorService.shutdown();
//                }
            }
        }
    }

    /**
     * 异步执行每一条数据
     *
     * @param map
     * @param rs
     */
    private void process(Map<String, Object> map, RecordSet rs, String currentDateTime) {
        //案号id，任务主表id，流程id，案号名称，任务明细数量，流程明细数量
        String ah, rwid, requestid, anhao, cnt_rw, cnt_wf;

        String fenpeiWorkTableDetail = fenpeiWorkTable + "_dt3";
        ah = Util.null2String(map.get("ah"));
        rwid = Util.null2String(map.get("rwid"));
        requestid = Util.null2String(map.get("requestid"));
        anhao = Util.null2String(map.get("anhao"));
        cnt_rw = Util.null2String(map.get("cnt_rw"));
        cnt_wf = Util.null2String(map.get("cnt_wf"));
        String prefix = "案号id:" + ah + ",案号名称:" + anhao + ",流程id:" + requestid + ",任务id:" + rwid + ",任务明细数量：" + cnt_rw + ",流程明细数量：" + cnt_wf;
        try {
            bb.writeLog(prefix + "process---START");
            bb.writeLog("处理的案号:" + anhao + "," + ah);
            //先删除明细6的数据
            String delSql = "delete from uf_cwcc_xmrw_dt6 where mainid = " + rwid;
            String insertSql = " INSERT INTO uf_cwcc_xmrw_dt6 (mainid,fplc,ry,fplb,fpqrcz,tchfpbl,dgtsds,dggjdj,fpje,sfrk,sjxfsj,ah) " +
                    " SELECT " + rwid + "," + requestid + ",d.ry,d.fplb,d.fpqrcz,d.tchfpbl,d.dgtsds,d.dggjdj,d.fpje,sfrk,'" + currentDateTime + "'," + ah +
                    " FROM " + fenpeiWorkTableDetail + " d " +
                    " inner join " + fenpeiWorkTable + " m on (d.mainid = m.id)" +
                    " WHERE m.requestid = " + requestid;
            bb.writeLog(prefix + " delSql sql :" + delSql);
            bb.writeLog(prefix + " insert sql :" + insertSql);
            if (rs.executeUpdate(delSql)) {
                if (rs.executeUpdate(insertSql)) {
                    bb.writeLog(prefix + "处理成功");
                } else {
                    bb.writeLog(prefix + "插入任务明细6出错" + rs.getExceptionMsg());
                }
            } else {
                bb.writeLog(prefix + "删除任务明细6数据出错：" + rs.getExceptionMsg());
            }
        } catch (Exception e) {
            bb.writeLog(prefix + "process异常：" + SDUtil.getExceptionDetail(e));
        }
    }
}
