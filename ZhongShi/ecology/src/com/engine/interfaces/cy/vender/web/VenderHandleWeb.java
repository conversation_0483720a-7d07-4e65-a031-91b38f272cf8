package com.engine.interfaces.cy.vender.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.cy.vender.service.VenderHandleService;
import com.engine.interfaces.cy.vender.service.impl.VenderHandleServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : ��ģ����
 */
public class VenderHandleWeb {

    private VenderHandleService getService(User user) {
        return ServiceUtil.getService(VenderHandleServiceImpl.class, user);
    }

    /**
     * ���½�ģ����
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/expertUpdate")
    @Produces(MediaType.TEXT_PLAIN)
    public String expertUpdate(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "saveOrUpdate---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).expertUpdate(params, user);
            } else {
                result.put("status", "0");
                result.put("errMsg", "��Ա��Ϣ����");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "0");
            result.put("errMsg", "�ӿ��쳣�� " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "saveOrUpdate��resultΪ��" + result);
        return JSONObject.toJSONString(result);
    }

}
