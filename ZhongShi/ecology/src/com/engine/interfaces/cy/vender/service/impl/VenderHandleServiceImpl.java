package com.engine.interfaces.cy.vender.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.cy.vender.cmd.VenderExecuteCmd;
import com.engine.interfaces.cy.vender.service.VenderHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR> Mason
 */
public class VenderHandleServiceImpl extends Service implements VenderHandleService {

    @Override
    public Map<String, Object> expertUpdate(Map<String, Object> params, User user) {
        return commandExecutor.execute(new VenderExecuteCmd(params, user));
    }
}
  