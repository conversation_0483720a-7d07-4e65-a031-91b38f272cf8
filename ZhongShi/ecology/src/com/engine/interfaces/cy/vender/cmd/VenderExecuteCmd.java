package com.engine.interfaces.cy.vender.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;


/**
 * 保存或更新建模数据
 *
 * <AUTHOR>
 */
public class VenderExecuteCmd extends AbstractCommonCommand<Map<String, Object>> {

    public VenderExecuteCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    /*主键字段*/
    public static final String key = "ryid";

    public static final String mainkey = "bm";

    /*对照关系表*/
    public static final Map<String, String> INIT_MAP = new HashMap<>();

    static {
        INIT_MAP.put("dwmc", "InvoiceName");
        INIT_MAP.put("sqr", "ContactName");
        INIT_MAP.put("lxfs", "ContactPhone");
        INIT_MAP.put("yx", "ContactEmail");
    }

    /**
     * 基类
     */
    private final BaseBean bb;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        result.put("status", "1");
        String billid = Util.null2String(params.get("bilid2expert"));

        /* 校验合法性 */
        int billidint = check(billid, result);
        if (!"1".equals(result.get("status"))) {
            return result;
        }

        /*查询建模表供应商字段*/
        UpdateVender(billidint, result);
        if (!"1".equals(result.get("status"))) {
            return result;
        }

        result.put("erroMsg", "处理成功");
        return result;

    }


    /**
     * 入参校验
     *
     * @param billid
     * @param result
     * @return
     */
    public int check(String billid, Map<String, Object> result) {
        if ("".equals(billid)) {
            result.put("status", "-1");
            result.put("erroMsg", "bilid2expert为空");
            return Integer.MIN_VALUE;
        }
        try {
            return Integer.parseInt(billid);
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("erroMsg", "bilid2expert参数格式出错");
        }
        return Integer.MIN_VALUE;
    }

    /**
     * 更新供应商
     *
     * @param billid
     * @param result
     */
    public void UpdateVender(int billid, Map<String, Object> result) {
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT detail.* from uf_cwcc_xmrw main right join uf_cwcc_xmrw_dt2 detail on detail.mainid = main.id where main.id = " + billid;
        boolean flag = recordSet.execute(sql);
        if (!flag) {
            result.put("status", "-1");
            result.put("erroMsg", "查询sql出错" + sql + "-" + recordSet.getExceptionMsg());
            return;
        }
        while (recordSet.next()) {
            Set<Map.Entry<String, String>> entrySet = INIT_MAP.entrySet();
            //foreach遍历
            HashMap<String, String> valmap = new HashMap<>();
            for (Map.Entry<String, String> entry : entrySet) {
                valmap.put(entry.getValue(), Util.null2String(recordSet.getString(entry.getKey())));
            }
            String colkey = Util.null2String(recordSet.getString(key));
            if (valmap.isEmpty()) {
                result.put("status", "-1");
                result.put("erroMsg", "值map为全空");
                return;
            }
            if (!"".equals(colkey)) {
                UpdateOrInsert(colkey, valmap, result);
            }
        }
    }

    /**
     * 插入还是更新
     *
     * @param keyval
     * @param result
     */
    public void UpdateOrInsert(String keyval, HashMap<String, String> valmap, Map<String, Object> result) {
        RecordSet recordSet = new RecordSet();
        RecordSet executesql = new RecordSet();
        String selectsql = "SELECT * FROM uf_gystz where " + mainkey + " <> '' and " + mainkey + " is not null and " + mainkey + " = '" + keyval + "'";
        boolean selectflag = recordSet.execute(selectsql);
        if (!selectflag) {
            result.put("status", "-1");
            result.put("erroMsg", "查询sql出错" + selectsql + "-" + recordSet.getExceptionMsg());
            return;
        }
        if (recordSet.next()) {
            boolean updateflag = executesql.execute(getUpdate(keyval, valmap));
            bb.writeLog("执行更新--" + getUpdate(keyval, valmap));
            if (!updateflag) {
                result.put("status", "-1");
                result.put("erroMsg", "更新sql出错" + getUpdate(keyval, valmap) + "-" + executesql.getExceptionMsg());
            }
        }
//        else{
//            if(!mainkey.equalsIgnoreCase("ID")){
//                valmap.put(mainkey,keyval);
//            }
//            valmap.put(mainkey,keyval);
//            int billid =  InsertModuleUtil4samtable.insertmodule("uf_gystz",valmap,1,12);
//            if(billid<0){
//                result.put("status", "-1");
//                result.put("erroMsg", "插入sql出错-"+valmap);
//            }
//        }
    }

    /**
     * 获取更新语句
     *
     * @return
     */
    public String getUpdate(String keyal, HashMap<String, String> valmap) {
        StringBuilder stringBuilder = new StringBuilder("UPDATE uf_gystz SET ");
        Set<Map.Entry<String, String>> entrySet = valmap.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            stringBuilder.append(entry.getKey()).append(" = ");
            if (!"".equals(entry.getValue())) {
                stringBuilder.append("'").append(entry.getValue()).append("',");
            } else {
                stringBuilder.append(" null ").append(",");
            }
        }
        String substring = stringBuilder.substring(0, stringBuilder.length() - 1);
        return substring + " WHERE " + mainkey + " = '" + keyal + "'";
    }
}
