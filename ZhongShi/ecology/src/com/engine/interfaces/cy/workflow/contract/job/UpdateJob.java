package com.engine.interfaces.cy.workflow.contract.job;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

public class UpdateJob extends BaseCronJob {

    public String moduletable;
    public String todetail;
    public String copymap;


    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        JSONObject map;
        try{
            map = JSONObject.parseObject(copymap);
        }catch (Exception e){
            baseBean.writeLog("JSON转换出错");
            return;
        }
        RecordSet recordSet = new RecordSet();
        boolean flag = recordSet.execute("SELECT * FROM "+ moduletable);
        if (flag){
            while (recordSet.next()){
                Set<Map.Entry<String, Object>> sets = map.entrySet();
                StringBuilder stringBuilder = new StringBuilder("INSERT INTO "+todetail);
                ArrayList<String> keys = new ArrayList<>();
                ArrayList<String> values = new ArrayList<>();
                for(Map.Entry<String, Object> set : sets){
                    String key = set.getKey();
                    String value = (String)set.getValue();
                    String valvalue = Util.null2String(recordSet.getString(key));
                    if(!"".equals(valvalue)){
                        keys.add(value);
                        values.add("'"+valvalue+"'");
                    }
                }
                String keystr = StringUtils.join(keys,",");
                String valuestr = StringUtils.join(values,",");
                stringBuilder.append(" (").append(keystr).append(") ").append("VALUES (").append(valuestr).append(")");
                String insertsql = stringBuilder.toString();
                RecordSet insert = new RecordSet();
                boolean ans =  insert.execute(insertsql);
                if(!ans){
                    baseBean.writeLog("INSERT SQL IS"+insertsql);
                    baseBean.writeLog(insert.getExceptionMsg());
                }
            }
        }else{
            baseBean.writeLog("查询表出错");
        }
    }

    public String getModuletable() {
        return moduletable;
    }

    public void setModuletable(String moduletable) {
        this.moduletable = moduletable;
    }

    public String getTodetail() {
        return todetail;
    }

    public void setTodetail(String todetail) {
        this.todetail = todetail;
    }

    public String getCopymap() {
        return copymap;
    }

    public void setCopymap(String copymap) {
        this.copymap = copymap;
    }
}
