package com.engine.interfaces.cy.workflow.contract.action;

import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

public class UpdateSql extends BaseBean implements Action {


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet rs = new RecordSet();
        rs.execute("\n" +
                "select xmrw.ah,xmxx.rwsjid,xmxx.lylc from uf_xmxx xmxx left join uf_cwcc_xmrw xmrw on xmrw.lylc = xmxx.lylc where xmxx.lylc = '"+requestid+"'");
        writeLog(" SQL -----------"+ QueryResultUtil.getJSONArrayList(rs).toJSONString());
        RecordSet recordSet = new RecordSet();
        recordSet.execute("update uf_cwcc_xmrw \n" +
                "set ah = uf_xmxx.id\n" +
                "from uf_xmxx \n" +
                "left join uf_cwcc_xmrw \n" +
                "on uf_cwcc_xmrw.lylc = uf_xmxx.lylc\n" +
                "where uf_xmxx.lylc = '"+requestid+"'");
        recordSet.execute("update uf_xmxx \n" +
                "set rwsjid = uf_cwcc_xmrw.id\n" +
                "from uf_xmxx \n" +
                "left join uf_cwcc_xmrw \n" +
                "on uf_cwcc_xmrw.lylc = uf_xmxx.lylc\n" +
                "where uf_xmxx.lylc = '"+requestid+"'");
            return Action.SUCCESS;
    }
}
