package com.engine.interfaces.cy.expert.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.cy.expert.cmd.ExpertExecuteCmd;
import com.engine.interfaces.cy.expert.service.ExpertHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR> Mason
 */
public class ExpertHandleServiceImpl extends Service implements ExpertHandleService {

    @Override
    public Map<String, Object> expertUpdate(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ExpertExecuteCmd(params, user));
    }
}
  