package com.engine.interfaces.cy.expert.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.interfaces.cy.tempuri.WebServiceOALocator;
import com.engine.interfaces.cy.tempuri.WebServiceOASoap;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.net.URL;
import java.util.*;


/**
 * 保存或更新建模数据
 *
 * <AUTHOR>
 */
public class   ExpertExecuteCmd extends AbstractCommonCommand<Map<String, Object>> {

    public ExpertExecuteCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    private final String[] cols = {"addr","zjxm","dh","sfzh","gzdw","zy","zc"};

    /**
     * 基类
     */
    private final BaseBean bb;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        result.put("status", "1");
        String billid = Util.null2String(params.get("bilid2expert"));

        /*校验*/
        int billidint = check(billid,result);
        if(!"1".equals(result.get("status"))){
            return result;
        }

        /*查询明细专家信息list*/
        List<HashMap<String,String>> list = getexpert(billidint,result);
        if(!"1".equals(result.get("status"))){
            return result;
        }

        /*调用接口*/
        postInterFace(list,result);
        if(!"1".equals(result.get("status"))){
            return result;
        }

        result.put("erroMsg","处理成功");
        return result;
    }

    /**
     * 入参校验
     * @param billid
     * @param result
     * @return
     */
    public int check(String billid,Map<String, Object> result){
        if("".equals(billid)){
            result.put("status", "-1");
            result.put("erroMsg", "bilid2expert为空");
            return Integer.MIN_VALUE;
        }
        try{
            return Integer.parseInt(billid);
        }catch (Exception e){
            result.put("status", "-1");
            result.put("erroMsg", "bilid2expert参数格式出错");
        }
        return Integer.MIN_VALUE;
    }

    /**
     * 专家查询
     * @param billid
     * @param result
     * @return
     */
    public List<HashMap<String,String>> getexpert(int billid, Map<String, Object> result){
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT detail.* ,addr.address as addr from uf_cwcc_xmrw main right join uf_cwcc_xmrw_dt3 detail on detail.mainid = main.id left join uf_xmxx xm on xm.id = main.ah left join \n" +
                "uf_areasAdresstable addr on addr.area = xm.qy where main.id = '"+billid+"'";
        recordSet.execute(sql);
        List<HashMap<String,String>> list = new ArrayList<>();
        while (recordSet.next()){
            HashMap<String,String> map = new HashMap<>();
            for(String col : cols){
                String colvalue =  Util.null2String(recordSet.getString(col));
                map.put(col,colvalue);
            }
            list.add(map);
            if("".equals(map.get("zjxm"))){
                result.put("status", "-1");
                result.put("erroMsg", "明细表专家名不可空");
                return list;
            }
            if("".equals(map.get("dh"))){
                result.put("status", "-1");
                result.put("erroMsg", "明细表专家电话不可空");
                return list;
            }
            if("".equals(map.get("addr"))){
                result.put("status", "-1");
                result.put("erroMsg", "地址配置webservice链接为空");
                return list;
            }
        }
        return list;
    }

    
    /**
     * 调用查询更新接口 先查询全部 存在查询接口报错返回不执行更新插入
     * @param result
     */
    public void postInterFace(List<HashMap<String,String>> list,Map<String, Object> result){

        List<Mappair> maps = new ArrayList<>();
        for (HashMap<String,String> map : list) {
            JSONArray ans = dopost(map.get("addr"),
                    map.get("zjxm"),
                    map.get("dh"),
                    result);
            if (!"1".equals(result.get("status"))) {
                return;
            }
            maps.add(new Mappair(map, ans));
        }

        for(Mappair mappair:maps){
            JSONArray ans = mappair.getArr();
            HashMap<String,String> map = mappair.getTheMap();
             if(ans.size()==0){
                 JSONObject res = insert(map.get("addr"),map,result);
                 if(!"1".equals(result.get("status"))){
                     return;
                 }
                 if(!"1".equals(res.getString("result"))){
                     result.put("status", "-1");
                     result.put("erroMsg", res.getString("result"));
                 }
             }else{
                 for (int i = 0; i < ans.size(); i++) {
                     JSONObject ans4one = ans.getJSONObject(i);
                     Integer id =  ans4one.getInteger("id");
                     JSONObject res = doUpdate(map.get("addr"),id,map,result);
                     if(!"1".equals(result.get("status"))){
                         return;
                     }
                     if(!"1".equals(res.getString("result"))){
                         result.put("status", "-1");
                         result.put("erroMsg", res.getString("result"));
                     }
                 }
             }
        }
    }


    /**
     * 查询post
     * @param urls
     * @param name
     * @param mobile
     * @param result
     * @return
     */
    public JSONArray dopost(String urls,String name,String mobile,Map<String, Object> result){
        try {
            URL url = new URL(urls);
            WebServiceOALocator webServiceOALocator = new WebServiceOALocator();
            WebServiceOASoap webServiceOASoap = webServiceOALocator.getWebServiceOASoap12(url);
            String str = webServiceOASoap.expertSearch(name,mobile);
            return JSONObject.parseArray(str);
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("erroMsg","接口查询报错"+e.getMessage());
            return new JSONArray();
        }
    }


    /**
     * 更新专家
     * @param urls
     * @param params
     * @param result
     * @return
     */
    public JSONObject doUpdate(String urls,int id,HashMap<String,String> params,Map<String, Object> result){
        try {
            URL url = new URL(urls);
            WebServiceOALocator webServiceOALocator = new WebServiceOALocator();
            WebServiceOASoap webServiceOASoap = webServiceOALocator.getWebServiceOASoap12(url);
            String str = webServiceOASoap.updateExpert(id,
                                                        params.get("sfzh"),
                                                        params.get("gzdw"),
                                                        params.get("zc"));
            return JSONObject.parseObject(str);
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("erroMsg","接口查询报错"+e.getMessage());
            return new JSONObject();
        }
    }


    /**
     * 插入专家
     * @param urls
     * @param params
     * @param result
     * @return
     */
    public JSONObject insert(String urls,HashMap<String,String> params,Map<String, Object> result){
        try {
            URL url = new URL(urls);
            WebServiceOALocator webServiceOALocator = new WebServiceOALocator();
            WebServiceOASoap webServiceOASoap = webServiceOALocator.getWebServiceOASoap12(url);
            String str = webServiceOASoap.addExpert(params.get("zjxm"),
                    params.get("dh"),
                    params.get("sfzh"),
                    params.get("gzdw"),
                    params.get("zy"),
                    params.get("zc")
                    );
            return JSONObject.parseObject(str);
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("erroMsg","接口插入"+e.getMessage());
            return new JSONObject();
        }
    }
}
