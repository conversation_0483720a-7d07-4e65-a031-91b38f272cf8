package com.engine.interfaces.cy.customer;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.crm.service.ContacterService;
import com.engine.crm.service.impl.ContacterServiceImpl;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

public class SynContractorJob  extends BaseCronJob {


    @Override
    public void execute() {
        User user = new User(1);
        ContacterService service = ServiceUtil.getService(ContacterServiceImpl.class, user);
        //查找本条customerid
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT * FROM uf_khlx where sfcr = 1";
        boolean flag =  recordSet.execute(sql);
        new BaseBean().writeLog("sql-----"+sql);
        while (recordSet.next()){
            String bilid = Util.null2String(recordSet.getString("id"));
            String name = Util.null2String(recordSet.getString("khmc"));
            String xm = Util.null2String(recordSet.getString("xm"));
            String dzyj = Util.null2String(recordSet.getString("dzyj"));
            String bgsdh = Util.null2String(recordSet.getString("bgsdh"));
            String zzdh = Util.null2String(recordSet.getString("zzdh"));
            String yddh = Util.null2String(recordSet.getString("yddh"));
            String bmlb = Util.null2String(recordSet.getString("bmlb"));
            String gw = Util.null2String(recordSet.getString("gw"));
            String ch = Util.null2String(recordSet.getString("ch"));
            String sfzlxr = Util.null2String(recordSet.getString("sfzlxr"));
            RecordSet cusselect = new RecordSet();
            if(!"".equals(name)&&!"".equals(bmlb)){
                bmlb = "0".equals(bmlb)?"_招标":"_造价";
                String realname = name+bmlb;
                new BaseBean().writeLog("realname"+realname);
                String searchcussql = " SELECT id as cusid FROM crm_customerinfo where name = '"+realname+"'";
                boolean ans =  cusselect.execute(searchcussql);
                if(ans){
                    if (cusselect.next()){
                        String id = Util.null2String(cusselect.getString("cusid"));
                        new BaseBean().writeLog("id--"+id);
                        if(!"".equals(id)){
                            if(!"".equals(xm)&&!"".equals(dzyj)){
                                Map<String, Object> params = new HashMap<>();
                                params.put("customerId",id);
                                params.put("firstname",xm);
                                params.put("title",ch);
                                params.put("jobtitle",gw);
                                params.put("contacteremail",dzyj);
                                params.put("phoneoffice",bgsdh);
                                params.put("phonehome",zzdh);
                                params.put("mobilephone",yddh);
                                params.put("main",sfzlxr);
                                Map<String,Object> map =  service.create(params);
                                String theans = JSONObject.toJSONString(map, true);
                                new BaseBean().writeLog("ans-----"+theans);
                                RecordSet update = new RecordSet();
                                String updatesql = "UPDATE uf_khlx set ans = '"+theans+"',sfcr = 0 WHERE id = '"+bilid+"'";
                                update.execute(updatesql);
                            }

                        }
                    }
                }
            }

        }

    }


}
