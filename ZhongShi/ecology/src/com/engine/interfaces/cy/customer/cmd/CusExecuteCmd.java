package com.engine.interfaces.cy.customer.cmd;

import com.api.crm.util.CrmConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.crm.cmd.customer.CustomerEditCmd;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;

import java.util.*;


/**
 * <AUTHOR>
 */
public class CusExecuteCmd extends AbstractCommonCommand<Map<String, Object>> {

    public CusExecuteCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        execute = new CustomerEditCmd(params,user);
    }

    private final BaseBean bb;

    private CustomerEditCmd execute;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> hashMap = new HashMap<>();
        String customerId = Util.null2String(this.params.get("customerId"));
        String newValue = Util.convertInput2DB(Util.null2String(this.params.get("newValue")));
        if(!"".equals(newValue)){
            String col = "1".equals(newValue)?"_招标":"_造价";
            String fieldName = Util.fromScreen3(Util.null2String(this.params.get("fieldName")), this.user.getLanguage()).toLowerCase();
            bb.writeLog("fieldName--------",fieldName);
            if("bmlx".equals(fieldName)){
                RecordSet recordSet = new RecordSet();
                recordSet.executeQuery("select bmlx,name from crm_customerinfo where id = ?", customerId);
                while (recordSet.next()){
                    String name = Util.null2String(recordSet.getString("name"));
                    String bmlx = Util.null2String(recordSet.getString("bmlx"));
                    if(newValue.equals(bmlx)){
                        return execute.execute(commandContext);
                    }
                    bb.writeLog("name--------"+name);
                    if(!"".equals(name)){
                        String namereal = name.length()>3?name.substring(0,name.length()-3)+col:name;
                        bb.writeLog("namereal--------"+name);
                        String sql = "select bmlx,name from crm_customerinfo where name = ? ";
                        bb.writeLog("sql--------",sql);
                        RecordSet check = new RecordSet();
                        boolean flag = check.executeQuery(sql,namereal);
                        bb.writeLog(flag);
                        bb.writeLog(check.getData());
                        if(check.next()){
                            hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                            hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"此类型存在重复名称，请重选择部门类型");
                            return hashMap;
                        }else{
                            new RecordSet().execute("update crm_customerinfo set name = '"+namereal+"' where id = '"+customerId+"'");
                        }
                    }
                }
            }
            else if ("status".equals(fieldName)){
                RecordSet recordSet = new RecordSet();
                recordSet.executeQuery("select manager from crm_customerinfo where id = ?", customerId);
                String manager ="";
                if(recordSet.next()){
                    manager=Util.null2String(recordSet.getString("manager"));
                }
                if("".equals(manager)){
                    hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                    hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"获取项目经理出错");
                    return hashMap;
                }
                RecordSet rs = new RecordSet();
                rs.execute("select\n" +
                        "(case when isnull(tabb.number,0)<isnull(tabc.yxkh ,0) then 'true' else 'false' end) as  yxkhstatus ,\n" +
                        "(case when isnull(tabd.number,0)<isnull(tabc.jckh ,0)  then 'true' else 'false' end) as  jckhstatus ,\n" +
                        "(case when isnull(tabe.number,0)<isnull(tabc.cjkh,0)  then 'true' else 'false' end) as  cjkhstatus \n" +
                        "from\n" +
                        "(\n" +
                        "SELECT field1,id FROM cus_fielddata  WHERE scope = 'HrmCustomFieldByInfoType' \n" +
                        ") taba left join \n" +
                        "(\n" +
                        "select count(*)number ,manager from crm_customerinfo where deleted =0 and status = 5 and isnull(seasFlag,0)= 0  group by manager\n" +
                        ") tabd on taba.id = tabd.manager  left join \n" +
                        "(\n" +
                        "select count(*)number ,manager from crm_customerinfo where deleted =0 and status = 3 and isnull(seasFlag,0)= 0  group by manager\n" +
                        ") tabe on taba.id = tabe.manager  left join \n" +
                        "(\n" +
                        "select count(*)number ,manager from crm_customerinfo where deleted =0 and status = 2 and isnull(seasFlag,0)= 0  group by manager\n" +
                        ") tabb on taba.id = tabb.manager\n" +
                        "left join uf_howmuchhave tabc on tabc.type = taba.field1 where taba.id  = '"+manager+"'");

                if(rs.next()){
                    String aimcol = "";
                    switch (newValue) {
                        case "2":
                            aimcol = "yxkhstatus";
                            break;
                        case "3":
                            aimcol = "cjkhstatus";
                            break;
                        case "5":
                            aimcol = "jckhstatus";
                            break;
                        default:
                            hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                            hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"客户状态出错");
                            return hashMap;
                    }
                    if( "false".equals(Util.null2String(rs.getString(aimcol)))){
                        hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                        hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"创建者客户数量已达到上限，请重新选择");
                        return hashMap;
                    }else if( "".equals(Util.null2String(rs.getString(aimcol)))){
                        hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                        hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"查询出错请联系管理人员");
                        return hashMap;
                    }
                }else{
                    hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                    hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"人力资源类型未配置");
                    return hashMap;
                }
            }
            else if ("manager".equals(fieldName)){
                RecordSet recordSet = new RecordSet();
                recordSet.executeQuery("select status from crm_customerinfo where id = ?", customerId);
                String manastatus ="";
                if(recordSet.next()){
                    manastatus=Util.null2String(recordSet.getString("status"));
                }
                if("".equals(manastatus)){
                    hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                    hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"获取人员状态出错");
                    return hashMap;
                }
                RecordSet rs = new RecordSet();
                rs.execute("select\n" +
                        "(case when isnull(tabb.number,0)<isnull(tabc.yxkh ,0) then 'true' else 'false' end) as  yxkhstatus ,\n" +
                        "(case when isnull(tabd.number,0)<isnull(tabc.jckh ,0)  then 'true' else 'false' end) as  jckhstatus ,\n" +
                        "(case when isnull(tabe.number,0)<isnull(tabc.cjkh,0)  then 'true' else 'false' end) as  cjkhstatus \n" +
                        "from\n" +
                        "(\n" +
                        "SELECT field1,id FROM cus_fielddata  WHERE scope = 'HrmCustomFieldByInfoType' \n" +
                        ") taba left join \n" +
                        "(\n" +
                        "select count(*)number ,manager from crm_customerinfo where deleted =0 and status = 5 and isnull(seasFlag,0)= 0  group by manager\n" +
                        ") tabd on taba.id = tabd.manager  left join \n" +
                        "(\n" +
                        "select count(*)number ,manager from crm_customerinfo where deleted =0 and status = 3 and isnull(seasFlag,0)= 0  group by manager\n" +
                        ") tabe on taba.id = tabe.manager  left join \n" +
                        "(\n" +
                        "select count(*)number ,manager from crm_customerinfo where deleted =0 and status = 2 and isnull(seasFlag,0)= 0  group by manager\n" +
                        ") tabb on taba.id = tabb.manager\n" +
                        "left join uf_howmuchhave tabc on tabc.type = taba.field1 where taba.id  = '"+newValue+"'");

                if(rs.next()){
                    String aimcol = "";
                    switch (manastatus) {
                        case "2":
                            aimcol = "yxkhstatus";
                            break;
                        case "3":
                            aimcol = "cjkhstatus";
                            break;
                        case "5":
                            aimcol = "jckhstatus";
                            break;
                        default:
                            hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                            hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"客户状态出错");
                            return hashMap;
                    }
                    if( "false".equals(Util.null2String(rs.getString(aimcol)))){
                        hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                        hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"创建者客户数量已达到上限，请重新选择");
                        return hashMap;
                    }else if( "".equals(Util.null2String(rs.getString(aimcol)))){
                        hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                        hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"查询出错请联系管理人员");
                        return hashMap;
                    }
                }else{
                    hashMap.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                    hashMap.put(CrmConstant.CRM_RESULT_MESSAGECODE,"人力资源类型未配置");
                    return hashMap;
                }
            }
        }
        return execute.execute(commandContext);
    }
}