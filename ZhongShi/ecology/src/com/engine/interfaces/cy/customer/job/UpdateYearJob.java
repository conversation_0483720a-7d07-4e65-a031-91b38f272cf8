package com.engine.interfaces.cy.customer.job;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName UpdateYearJob
 * @Description 更新客户日期job
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/13
 */
public class UpdateYear<PERSON>ob extends BaseCronJob {

    @Override
    public void execute() {
        new BaseBean().writeLog("start----");
        RecordSet recordSet = new RecordSet();
        String sql = "update CRM_CustomerInfo set khnx = DATEDIFF(YYYY,createdate,GETDATE())";
        recordSet.execute(sql);

    }
}
