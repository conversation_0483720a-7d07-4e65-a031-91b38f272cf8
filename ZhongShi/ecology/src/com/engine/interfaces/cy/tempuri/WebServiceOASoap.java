/**
 * WebServiceOASoap.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.engine.interfaces.cy.tempuri;

public interface WebServiceOASoap extends java.rmi.Remote {
    public String expertSearch(String key, String mobile) throws java.rmi.RemoteException;
    public String expertCheck(String key, String mobile, String mojorCode) throws java.rmi.RemoteException;
    public String experteEvaluate(String projectName, String pbDate, int expertID, String expertName, int starAvg, String evaluate, String createUserName) throws java.rmi.RemoteException;
    public String updateExpert(int id, String certificateNum, String job, String title) throws java.rmi.RemoteException;
    public String addExpert(String expertName, String mobileNum, String certificateNum, String job, String mojorCode, String title) throws java.rmi.RemoteException;
    public String checkExpertMojor(String expertId, String mojorCode) throws java.rmi.RemoteException;
    public String mobileChooseExpert(String workAddress, int chooseCount, String mojorCodes, String filterInfos) throws java.rmi.RemoteException;
}
