/**
 * WebServiceOA.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.engine.interfaces.cy.tempuri;

public interface WebServiceOA extends javax.xml.rpc.Service {
    public String getWebServiceOASoapAddress();

    public WebServiceOASoap getWebServiceOASoap() throws javax.xml.rpc.ServiceException;

    public WebServiceOASoap getWebServiceOASoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
    public String getWebServiceOASoap12Address();

    public WebServiceOASoap getWebServiceOASoap12() throws javax.xml.rpc.ServiceException;

    public WebServiceOASoap getWebServiceOASoap12(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
