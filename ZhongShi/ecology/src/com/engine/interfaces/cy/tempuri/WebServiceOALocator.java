/**
 * WebServiceOALocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.engine.interfaces.cy.tempuri;

public class WebServiceOALocator extends org.apache.axis.client.Service implements WebServiceOA {

    public WebServiceOALocator() {
    }


    public WebServiceOALocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public WebServiceOALocator(String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for WebServiceOASoap
    private String WebServiceOASoap_address = "http://bid.cwcc.sh.cn:9961/WebService/WebServiceOA.asmx";

    public String getWebServiceOASoapAddress() {
        return WebServiceOASoap_address;
    }

    // The WSDD service name defaults to the port name.
    private String WebServiceOASoapWSDDServiceName = "WebServiceOASoap";

    public String getWebServiceOASoapWSDDServiceName() {
        return WebServiceOASoapWSDDServiceName;
    }

    public void setWebServiceOASoapWSDDServiceName(String name) {
        WebServiceOASoapWSDDServiceName = name;
    }

    public WebServiceOASoap getWebServiceOASoap() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WebServiceOASoap_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWebServiceOASoap(endpoint);
    }

    public WebServiceOASoap getWebServiceOASoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            WebServiceOASoapStub _stub = new WebServiceOASoapStub(portAddress, this);
            _stub.setPortName(getWebServiceOASoapWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWebServiceOASoapEndpointAddress(String address) {
        WebServiceOASoap_address = address;
    }


    // Use to get a proxy class for WebServiceOASoap12
    private String WebServiceOASoap12_address = "http://bid.cwcc.sh.cn:9961/WebService/WebServiceOA.asmx";

    public String getWebServiceOASoap12Address() {
        return WebServiceOASoap12_address;
    }

    // The WSDD service name defaults to the port name.
    private String WebServiceOASoap12WSDDServiceName = "WebServiceOASoap12";

    public String getWebServiceOASoap12WSDDServiceName() {
        return WebServiceOASoap12WSDDServiceName;
    }

    public void setWebServiceOASoap12WSDDServiceName(String name) {
        WebServiceOASoap12WSDDServiceName = name;
    }

    public WebServiceOASoap getWebServiceOASoap12() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WebServiceOASoap12_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWebServiceOASoap12(endpoint);
    }

    public WebServiceOASoap getWebServiceOASoap12(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            WebServiceOASoap12Stub _stub = new WebServiceOASoap12Stub(portAddress, this);
            _stub.setPortName(getWebServiceOASoap12WSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWebServiceOASoap12EndpointAddress(String address) {
        WebServiceOASoap12_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     * This service has multiple ports for a given interface;
     * the proxy implementation returned may be indeterminate.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (WebServiceOASoap.class.isAssignableFrom(serviceEndpointInterface)) {
                WebServiceOASoapStub _stub = new WebServiceOASoapStub(new java.net.URL(WebServiceOASoap_address), this);
                _stub.setPortName(getWebServiceOASoapWSDDServiceName());
                return _stub;
            }
            if (WebServiceOASoap.class.isAssignableFrom(serviceEndpointInterface)) {
                WebServiceOASoap12Stub _stub = new WebServiceOASoap12Stub(new java.net.URL(WebServiceOASoap12_address), this);
                _stub.setPortName(getWebServiceOASoap12WSDDServiceName());
                return _stub;
            }
        }
        catch (Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        String inputPortName = portName.getLocalPart();
        if ("WebServiceOASoap".equals(inputPortName)) {
            return getWebServiceOASoap();
        }
        else if ("WebServiceOASoap12".equals(inputPortName)) {
            return getWebServiceOASoap12();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://tempuri.org/", "WebServiceOA");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://tempuri.org/", "WebServiceOASoap"));
            ports.add(new javax.xml.namespace.QName("http://tempuri.org/", "WebServiceOASoap12"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(String portName, String address) throws javax.xml.rpc.ServiceException {
        
if ("WebServiceOASoap".equals(portName)) {
            setWebServiceOASoapEndpointAddress(address);
        }
        else 
if ("WebServiceOASoap12".equals(portName)) {
            setWebServiceOASoap12EndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
