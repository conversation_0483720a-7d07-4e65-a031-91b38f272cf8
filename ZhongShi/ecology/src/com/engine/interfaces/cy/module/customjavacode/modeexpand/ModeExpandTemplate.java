package com.engine.interfaces.cy.module.customjavacode.modeexpand;

import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

public class ModeExpandTemplate extends AbstractModeExpandJavaCodeNew {
    /**
     * 执行模块扩展动作
     * @param param
     *  param包含(但不限于)以下数据
     *  user 当前用户
     *  importtype 导入方式(仅在批量导入的接口动作会传输) 1 追加，2覆盖,3更新，获取方式(int)param.get("importtype")
     *  导入链接中拼接的特殊参数(仅在批量导入的接口动作会传输)，比如a=1，可通过param.get("a")获取参数值
     *  页面链接拼接的参数，比如b=2,可以通过param.get("b")来获取参数
     * @return
     */
    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<String, String>();
        result.put("flag", "true");
        try {
            User user = (User)param.get("user");
            int billid = -1;//数据id
            int modeid = -1;//模块id
            RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
            if(requestInfo!=null){
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());
                if(billid>0&&modeid>0){
                    //------请在下面编写业务逻辑代码------
                    RecordSet recordSet = new RecordSet();
                    String sql = "SELECT * FROM htxxreport where id = '"+billid+"'";
                    new BaseBean().writeLog("sql"+sql);
                    boolean ans =  recordSet.execute(sql);
                    if(ans){
                        while (recordSet.next()){
                            String zj = Util.null2String(recordSet.getString("theid"));
                            int zdbzw = Integer.parseInt(Util.null2o(recordSet.getString("zdbzw")));
                            new BaseBean().writeLog(+zdbzw+"-"+zj);
                            if("".equals(zj)){
                                new BaseBean().writeLog("主键获取为空"+sql+"-"+zj);
                                result.put("errmsg","主键获取为空"+sql+"-"+zj);
                                result.put("flag", "false");
                                return result;
                            }
                            if(zdbzw==0){
                                String sqlselect = "SELECT max(zdbzw) num FROM uf_reportutiltable where zdpx = '"+user.getUID()+"'";
                                new BaseBean().writeLog("sqlselect"+sqlselect);
                                RecordSet set = new RecordSet();
                                boolean selectans= set.execute(sqlselect);
                                if(selectans){
                                    int number;
                                    if(set.next()){
                                        number =Integer.parseInt(Util.null2o(set.getString("num")));
                                        number++;
                                    }else{
                                        number=1;
                                    }
                                    String insertsql = "insert into uf_reportutiltable (zdpx,zdbzw,zj) values("+user.getUID()+","+number+","+zj+")";
                                    String select = "select * from uf_reportutiltable where zdpx ='"+user.getUID()+"' and zj = '"+zj+"'";
                                    RecordSet rs = new RecordSet();
                                    rs.execute(select);
                                    if(!rs.next()){
                                        boolean ins =  rs.execute(insertsql);
                                        if(!ins){
                                            new BaseBean().writeLog("insertsql"+insertsql+rs.getExceptionMsg());
                                        }
                                    }
                                }
                            }
                        }
                    }else{
                        result.put("errmsg","sql 执行出错"+sql+ recordSet.getExceptionMsg());
                        result.put("flag", "false");
                    }
                }
            }
        } catch (Exception e) {
            new BaseBean().writeLog("errmsg",e);
            result.put("errmsg","自定义出错信息");
            result.put("flag", "false");
        }
        return result;
    }
}
