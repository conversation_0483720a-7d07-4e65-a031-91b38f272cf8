package com.engine.interfaces.cy.module.projectTask.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.cy.module.projectTask.cmd.UpdateModuleCmd;
import com.engine.interfaces.cy.module.projectTask.service.ModHandleService;
import weaver.hrm.User;

import java.util.Map;

/**
 * 建模处理
 *
 * <AUTHOR> Mason
 */
public class ModHandleServiceImpl extends Service implements ModHandleService {

    @Override
    public Map<String, Object> ModuleUpdate(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UpdateModuleCmd(params, user));
    }
}
  