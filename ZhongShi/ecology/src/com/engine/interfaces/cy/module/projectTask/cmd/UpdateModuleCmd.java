package com.engine.interfaces.cy.module.projectTask.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * 保存或更新建模数据
 *
 * <AUTHOR>
 */
public class UpdateModuleCmd extends AbstractCommonCommand<Map<String, Object>> {

    public UpdateModuleCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        erroMsg = "";
    }

    /**
     * 基类
     */
    private final BaseBean bb;
    /**
     * 错误信息
     */
    private String erroMsg;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("params：" + params.toString());
        String conditioncol,conditionvalue,table,updatecol,updatevalue;
        try {
            conditioncol = Util.null2String(params.get("conditioncol"));
            conditionvalue = Util.null2String(params.get("conditionvalue"));
            table = Util.null2String(params.get("table"));
            updatecol = Util.null2String(params.get("updatecol"));
            updatevalue = Util.null2String(params.get("updatevalue"));
            if("".equals(conditioncol)||"".equals(table)){
                result.put("status", "-1");
                result.put("erroMsg", "未获取到该行数据");
                return result;
            }
            RecordSet recordSet = new RecordSet();
            String sql = " SELECT * FROM "+table+" WHERE "+conditioncol+" = '"+conditionvalue+"'";
            String insert = " insert into "+table+" ("+conditioncol+","+updatecol+") VALUES ('"+conditionvalue+"','"+updatevalue+"') ";
            String updatesql = " UPDATE "+table+" SET "+updatecol+" = '"+updatevalue+"' WHERE "+conditioncol+" = '"+conditionvalue+"'";
            recordSet.execute(sql);
            RecordSet updateInset = new RecordSet();
            if(recordSet.next()){
                updateInset.execute(updatesql);
            }else{
                updateInset.execute(insert);
            }
        } catch (Exception e) {
            erroMsg = "catch exception：" + Arrays.toString(e.getStackTrace());
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
            return result;
        }
        result.put("status", "1");
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


}
