package com.engine.dfmz4.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.dfmz4.tpw.job.util.DAOUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.NewRequestDto;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.time.YearMonth;
import java.util.*;

/**
 * @FileName TriggerProvisionForExpendituresWorkflowJob
 * @Description 支出合同财务数据台账自动触发支出计提流程
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/14
 */


public class TriggerProvisionForExpendituresWorkflowJob extends BaseCronJob {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private SDLogUtil sdLogUtil;
    private SDLog sdLog;
    private String errMsg;
    private String fkdws;
    //创建流程的明细表下标
    private String glmx;
    private String workflowId;
    private JSONArray workflowConfigs;
    private List<Map<String, Object>> mainDatas;
    private HashMap<String, List<Object>> jtDetailMap;
    private HashMap<String, List<Object>> yfjzDetailMap;

    @Override
    public void execute() {
        try {
            _init();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }
            appendLog("TriggerProvisionForExpendituresWorkflowJob----Start");
            executeMy();
        } catch (Exception e) {
            appendLog("execute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("TriggerProvisionForExpendituresWorkflowJob----END");
            afterExecute();
        }
    }

    private void executeMy() {
        try {
            appendLog("executeMy---start");
            for (int i = 0; i < mainDatas.size(); i++) {
                Map<String, Object> mainData = mainDatas.get(i);
                appendLog("executeMy---mainData " + JSONObject.toJSONString(mainData));
                String requestid1 = assembleNewRequestDto1(mainData);
                String requestid2 = assembleNewRequestDto2(mainData);
                sdLog.setExtend1("创建成功的流程requestid1 : " + requestid1 + " requestid2 : " + requestid2);
            }
        } catch (Exception e) {
            errMsg = "executeMy Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
            sdLog.setError(errMsg);
        } finally {
            appendLog("executeMy---end");
        }
    }

    private void _init() {
        try {
            errMsg = "";
            fkdws = "";
            sdLogUtil = new SDLogUtil();
            workflowConfigs = new JSONArray();
            mainDatas = new ArrayList<>();
            jtDetailMap = new HashMap<>();
            yfjzDetailMap = new HashMap<>();
            //初始化日志bean
            sdLog = new SDLog(1,
                    this.getClass().getSimpleName(),
                    this.getClass().getName(),
                    SDLog.TYPE_JOB,
                    "支出合同财务数据台账自动触发支出计提流程");
            sdLog.setRelate_module("定时任务");
            sdLog.setRelate_table("");
            sdLog.setRelate_dataid("");
            appendLog("TriggerProvisionForExpendituresWorkflowJob_innt_start");
            //获取自动触发流程配置表信息
            getConfigInfo();
            if (StringUtils.isBlank(workflowId) || StringUtils.isBlank(fkdws)) {
                errMsg = "获取自动触发流程配置表信息失败 workflowId is blank or fkdws(付款单位) is blank";
                return;
            }
            //获取流程字段配置表信息
            getWorkflowFieldConfig();
            if (workflowConfigs.isEmpty()) {
                errMsg = "获取流程字段配置表信息失败";
                return;
            }
            //获取满足条件的收入合同财务数据档案的明细4（uf_zchtcwsjda_dt4）的数据
            getContractFinancialData();
            if (mainDatas.isEmpty()) {
                errMsg = "没有符合创建流程条件的mainDatas";
            } else if (jtDetailMap.isEmpty()) {
                errMsg = "没有符合创建流程条件的jtDetailMap";
            }
            appendLog("TriggerProvisionForExpendituresWorkflowJob_innt_end");
        } catch (Exception e) {
            errMsg = "TriggerProvisionForExpendituresWorkflowJob----_init:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        }
    }

    private void getWorkflowFieldConfig() {
        try {
            appendLog("getWorkflowFieldConfig--start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String sql = "select * from uf_zdcflczdpz where lc = ? ";
            recordSet.executeQuery(sql, workflowId);
            if (recordSet.next()) {
                glmx = recordSet.getString("glmx");
            }
            appendLog("getWorkflowFieldConfig--glmx : " + glmx);
            String sql1 = "select * from uf_zdcflczdpz_dt1 a inner join uf_zdcflczdpz b on a.mainid = b.id where b.lc = ? ";
            recordSet.executeQuery(sql1, workflowId);
            workflowConfigs = QueryUtil.getJSONList(recordSet);

            appendLog("getWorkflowFieldConfig--workflowConfigs : " + workflowConfigs.toJSONString());
        } catch (Exception e) {
            errMsg = "getWorkflowFieldConfig Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("getWorkflowFieldConfig--end");
        }
    }

    private void getContractFinancialData() {
        try {
            appendLog("getContractFinancialData--start");
            RecordSet rs1 = new RecordSet();
            String sql = "select t1.* from uf_zchtcwsjda t1 " +
                    "inner join uf_htxx t2 on t1.htmc = t2.id " +
                    "where t1.htjelx = 1 " + //合同金额类型=固定金额
                    "and (t2.sjbj <> '' AND t2.sjbj IS NOT NULL)" + // 数据标记为新合同
                    "and t1.wfhtzt in (" + fkdws + ") " + // 我方合同主题
                    "and t2.sfshht = 1 ";  //主表相关合同查合同台账，是否事后合同=否
            appendLog("查询收入合同财务数据档案的主表数据sql: " + sql);
            rs1.executeQuery(sql);

            mainDatas = QueryUtil.getMapList(rs1);

            appendLog("getContractFinancialData--mainDatas: " + JSONObject.toJSONString(mainDatas));
            RecordSet rs2 = new RecordSet();
            String sql1 = "SELECT t1.* \n" +
                    " FROM uf_zchtcwsjda_dt4 t1 \n" +
                    " INNER JOIN uf_zchtcwsjda t2 ON t1.mainid = t2.id \n" +
                    " INNER JOIN uf_zchtcwsjda_dt3 t3 ON t3.mainid = t2.id \n" +
                    " WHERE (t1.sfycjlc = '' OR t1.sfycjlc IS NULL) \n" + // 是否已经创建过流程 不为空说明创建过
                    " AND t1.srqrlx = 0 " +
                    " AND YEAR(t1.cwszsj) = YEAR(GETDATE()) " +
                    " AND MONTH(t1.cwszsj) = MONTH(GETDATE()) \n" +
                    " AND t1.khmc = t3.khmc and t1.glfylx = t3.fylx and t3.sfayqrsrsc = 0 " +
                    " AND (t1.zfsj = '' OR t1.zfsj IS NULL) ";
            appendLog("查询收入合同财务数据档案的明细4数据sql1: " + sql1);
            rs2.executeQuery(sql1);
            List<Map<String, Object>> mapList = QueryUtil.getMapList(rs2);
            for (Map<String, Object> jo : mapList) {
                String mainid = Util.null2String(jo.get("mainid"));
                // 如果jtDetailMap中还没有这个mainid的分组，则创建一个新的ArrayList
                if (!jtDetailMap.containsKey(mainid)) {
                    ArrayList<Object> objects = new ArrayList<>();
                    jtDetailMap.put(mainid, objects);
                }
                // 将当前map添加到对应的分组中
                jtDetailMap.get(mainid).add(jo);
            }

            appendLog("getContractFinancialData--jtDetailMap:" + JSONObject.toJSONString(jtDetailMap));

            RecordSet rs3 = new RecordSet();
            String sql3 = "SELECT t1.* \n" +
                    " FROM uf_zchtcwsjda_dt4 t1 \n" +
                    " INNER JOIN uf_zchtcwsjda t2 ON t1.mainid = t2.id \n" +
                    " INNER JOIN uf_zchtcwsjda_dt3 t3 ON t3.mainid = t2.id \n" +
                    " WHERE (t1.sfycjlc = '' OR t1.sfycjlc IS NULL) \n" + // 是否已经创建过流程 不为空说明创建过
                    " AND t1.srqrlx = 0 " +
                    " AND YEAR(t1.cwszsj) = YEAR(GETDATE()) " +
                    " AND MONTH(t1.cwszsj) = MONTH(GETDATE()) \n" +
                    " AND t1.khmc = t3.khmc and t1.glfylx = t3.fylx and t3.sfayqrsrsc = 0 " +
                    " AND t1.sfyf = 0 ";
            appendLog("查询收入合同财务数据档案的明细4数据sql3: " + sql3);
            rs3.executeQuery(sql3);
            mapList = QueryUtil.getMapList(rs3);
            for (Map<String, Object> jo : mapList) {
                String mainid = Util.null2String(jo.get("mainid"));
                if (!yfjzDetailMap.containsKey(mainid)) {
                    ArrayList<Object> objects = new ArrayList<>();
                    yfjzDetailMap.put(mainid, objects);
                }
                // 将当前map添加到对应的分组中
                yfjzDetailMap.get(mainid).add(jo);
            }

            appendLog("getContractFinancialData--yfjzDetailMap:" + JSONObject.toJSONString(yfjzDetailMap));
        } catch (Exception e) {
            errMsg = "getContractFinancialData Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("getContractFinancialData--end");
        }
    }

    private void getConfigInfo() {
        try {
            appendLog("getConfigInfo---start");
            String dayOfMonth = TimeUtil.getDayOfMonth();
            //获取当前年月
            YearMonth yearMonth = YearMonth.now();
            //获取本月的天数
            int daysInMonth = yearMonth.lengthOfMonth();
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            Set<String> list = new HashSet<>(); // 无序集合
            String sql = "select * from uf_dsrwzdcflcpz";
            recordSet.executeQuery(sql);
            while (recordSet.next()) {
                //付款单位
                String fkdw = recordSet.getString("gs");
                //计提触发周期
                String jtcfzq = recordSet.getString("jtcfzq");
                //计提触发流程
                String jtcflc = recordSet.getString("jtcflc");
                if (StringUtils.isNotBlank(jtcflc)) {
                    workflowId = jtcflc;
                }
                appendLog("workflowId: " + workflowId);
                if (StringUtils.isNotBlank(fkdw)) {
                    //当前日期属于预提触发周期
                    if (StringUtils.isNotBlank(jtcfzq)) {
                        String[] split = jtcfzq.split(",");
                        for (int i = 0; i < split.length; i++) {
                            int rqIndex = Integer.parseInt(split[i].trim());
                            if (Integer.parseInt(dayOfMonth) == daysInMonth - rqIndex || daysInMonth - rqIndex <= 0) {
                                list.add(fkdw);
                            }
                        }
                    }
                }
            }
            if (!list.isEmpty()) {
                fkdws = StringUtils.join(list, ",");
            }

            appendLog("fkdws:" + fkdws);
        } catch (Exception e) {
            errMsg = "getConfigInfo Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            appendLog("getConfigInfo---end");
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void appendLog(String logMsg) {
        logger.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

    private void afterExecute() {
        try {
            appendLog("afterExecute----Start");
            //清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            //插入二开日志
            ModuleResult moduleResult = SDLog.saveLog(sdLog, sdLogUtil.getFullLog());
            appendLog("插入二开日志 moduleResult " + JSONObject.toJSONString(moduleResult));
        } catch (Exception e) {
            appendLog("afterExecute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("afterExecute----end");
        }
    }

    //预计结转
    private String assembleNewRequestDto1(Map<String, Object> mainModuleData) {
        String requestid = "";
        NewRequestDto newRequestDto = new NewRequestDto();
        try {
            ArrayList<String> detailIds = new ArrayList<>();
            String mainid = Util.null2String(mainModuleData.get("id"));
            appendLog("assembleNewRequestDto1 mainid : " + mainid + " start ");
            List<Object> details = jtDetailMap.get(mainid);
            appendLog("assembleNewRequestDto1 details : " + JSONObject.toJSONString(details));
            List<NewRequestDto.MainData> mainDataList = new ArrayList<>();
            for (int i = 0; i < workflowConfigs.size(); i++) {
                JSONObject workflowConfig = workflowConfigs.getJSONObject(i);
                //转换规则
                String zhgz = Util.null2String(workflowConfig.get("zhgz"));
                //建模字段
                String jmzd = Util.null2String(workflowConfig.get("jmzdm"));
                //流程字段
                String lczd = Util.null2String(workflowConfig.get("lczdm"));
                //字段位置
                String zdwz = Util.null2String(workflowConfig.get("lczdly"));
                //自定义转换规则
                String zdyzhgz = Util.null2String(workflowConfig.get("zdyzhgz"));
                //建模字段Value
                String jmzdValue = Util.null2String(mainModuleData.get(jmzd));
                if ("0".equals(zdwz)) {
                    NewRequestDto.MainData main = new NewRequestDto.MainData();
                    main.setFieldName(lczd);
                    //字段位置在主表
                    if ("0".equals(zhgz)) {
                        //直传不做转换
                        main.setFieldValue(jmzdValue);
                    } else if ("1".equals(zhgz)) {
                        //固定值
                        main.setFieldValue(zdyzhgz);
                    } else if ("2".equals(zhgz)) {
                        //自定义执行sql
                        String fieldValue = Util.null2String(DAOUtil.selectFirstColumn(zdyzhgz, jmzdValue));
                        main.setFieldValue(fieldValue);
                    }
                    mainDataList.add(main);
                }
            }
            NewRequestDto.MainData main = new NewRequestDto.MainData();
            main.setFieldName("jtlx");
            main.setFieldValue(3);
            mainDataList.add(main);
            appendLog("assembleNewRequestDto1 List<NewRequestDto.MainData> mainDataList : " + JSONObject.toJSONString(mainDataList));

            boolean flag = false;
            if (details != null && !details.isEmpty()) {
                List<NewRequestDto.DetailData> detailDatas = new ArrayList<>();
                NewRequestDto.DetailData detailData = new NewRequestDto.DetailData();
                WfInfo wfInfoByWfId = WfUtil.getWfInfoByWfId(workflowId);
                detailData.setTableDBName(wfInfoByWfId.getFormtableName() + "_dt" + glmx);

                List<NewRequestDto.WorkflowRequestTableRecords> workflowRequestTableRecordList = new ArrayList();
                for (int i = 0; i < details.size(); i++) {
                    NewRequestDto.WorkflowRequestTableRecords workflowRequestTableRecords = new NewRequestDto.WorkflowRequestTableRecords();
                    Map<String, Object> detail = (Map<String, Object>) details.get(i);
                    List<NewRequestDto.WorkflowRequestTableFields> workflowRequestTableFields = new ArrayList();
                    String detailId = Util.null2String(detail.get("id"));
                    detailIds.add(detailId);
                    flag = true;
                    for (int j = 0; j < workflowConfigs.size(); j++) {
                        JSONObject workflowConfig = workflowConfigs.getJSONObject(j);
                        //转换规则
                        String zhgz = Util.null2String(workflowConfig.get("zhgz"));
                        //建模字段
                        String jmzd = Util.null2String(workflowConfig.get("jmzdm"));
                        //流程字段
                        String lczd = Util.null2String(workflowConfig.get("lczdm"));
                        //字段位置
                        String zdwz = Util.null2String(workflowConfig.get("lczdly"));
                        //自定义转换规则
                        String zdyzhgz = Util.null2String(workflowConfig.get("zdyzhgz"));
                        //建模字段Value
                        String jmzdValue = Util.null2String(detail.get(jmzd));
                        if (!"0".equals(zdwz)) {
                            NewRequestDto.WorkflowRequestTableFields workflowRequestTableField = new NewRequestDto.WorkflowRequestTableFields();
                            workflowRequestTableField.setFieldName(lczd);
                            //字段位置在主表
                            if ("0".equals(zhgz)) {
                                //直传不做转换
                                workflowRequestTableField.setFieldValue(jmzdValue);
                            } else if ("1".equals(zhgz)) {
                                //固定值
                                workflowRequestTableField.setFieldValue(zdyzhgz);
                            } else if ("2".equals(zhgz)) {
                                //自定义执行sql
                                String fieldValue = Util.null2String(DAOUtil.selectFirstColumn(zdyzhgz, jmzdValue));
                                workflowRequestTableField.setFieldValue(fieldValue);
                            }
                            workflowRequestTableFields.add(workflowRequestTableField);
                        }
                    }
                    workflowRequestTableRecords.setWorkflowRequestTableFields(workflowRequestTableFields);
                    workflowRequestTableRecordList.add(workflowRequestTableRecords);
                }
                detailData.setWorkflowRequestTableRecords(workflowRequestTableRecordList);
                detailDatas.add(detailData);
                newRequestDto.setDetailData(detailDatas);

            }
            newRequestDto.setWorkflowId(workflowId);
            newRequestDto.setRequestName("支出合同财务数据台账自动触发计提和补票流程");
            newRequestDto.setMainData(mainDataList);
            appendLog("newRequestDto:" + JSONObject.toJSONString(newRequestDto));
            if (!flag) {
                return requestid;
            }
            // 调用创建请求的接口

            String createResult = WfUtil.createRequest(new User(1), newRequestDto);
            JSONObject createResultObj = JSONObject.parseObject(createResult);
            appendLog("createResultObj :" + JSONObject.toJSONString(createResultObj));
            // 检查创建接口响应
            if (createResultObj == null) {
                appendLog("未获取到流程创建接口响应");
            } else {
                String code = Util.null2String(createResultObj.get("code"));
                if (!"SUCCESS".equals(code)) {
                    appendLog("流程创建出错：" + createResult);
                } else {
                    JSONObject dataObj = createResultObj.getJSONObject("data");
                    if (dataObj != null && dataObj.containsKey("requestid")) {
                        requestid = Util.null2String(dataObj.get("requestid"));
                        if (!detailIds.isEmpty()) {
                            String sql = "update uf_zchtcwsjda_dt4 set sfycjlc = " + requestid + " where id in (" + String.join(",", detailIds) + ")";
                            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                            recordSet.executeUpdate(sql);
                            DBUtil.clearThreadLocalRecordSet();
                        }
                    } else {
                        appendLog("未获取到新建流程requestid");
                    }
                }
            }
        } catch (Exception e) {
            appendLog("assembleNewRequestDto1 Exception:" + SDUtil.getExceptionDetail(e));
        }
        return requestid;
    }

    //预按月未付款
    private String assembleNewRequestDto2(Map<String, Object> mainModuleData) {
        String requestid = "";
        NewRequestDto newRequestDto = new NewRequestDto();
        try {
            ArrayList<String> detailIds = new ArrayList<>();
            String mainid = Util.null2String(mainModuleData.get("id"));
            appendLog("assembleNewRequestDto2 mainid : " + mainid + " start ");
            List<Object> details = yfjzDetailMap.get(mainid);
            appendLog("assembleNewRequestDto2 details : " + JSONObject.toJSONString(details));
            List<NewRequestDto.MainData> mainDataList = new ArrayList<>();
            for (int i = 0; i < workflowConfigs.size(); i++) {
                JSONObject workflowConfig = workflowConfigs.getJSONObject(i);
                //转换规则
                String zhgz = Util.null2String(workflowConfig.get("zhgz"));
                //建模字段
                String jmzd = Util.null2String(workflowConfig.get("jmzdm"));
                //流程字段
                String lczd = Util.null2String(workflowConfig.get("lczdm"));
                //字段位置
                String zdwz = Util.null2String(workflowConfig.get("lczdly"));
                //自定义转换规则
                String zdyzhgz = Util.null2String(workflowConfig.get("zdyzhgz"));
                //建模字段Value
                String jmzdValue = Util.null2String(mainModuleData.get(jmzd));
                if ("0".equals(zdwz)) {
                    NewRequestDto.MainData main = new NewRequestDto.MainData();
                    main.setFieldName(lczd);
                    //字段位置在主表
                    if ("0".equals(zhgz)) {
                        //直传不做转换
                        main.setFieldValue(jmzdValue);
                    } else if ("1".equals(zhgz)) {
                        //固定值
                        main.setFieldValue(zdyzhgz);
                    } else if ("2".equals(zhgz)) {
                        //自定义执行sql
                        String fieldValue = Util.null2String(DAOUtil.selectFirstColumn(zdyzhgz, jmzdValue));
                        main.setFieldValue(fieldValue);
                    }
                    mainDataList.add(main);
                }
            }
            NewRequestDto.MainData main = new NewRequestDto.MainData();
            main.setFieldName("jtlx");
            main.setFieldValue(0);
            mainDataList.add(main);
            appendLog("assembleNewRequestDto2 List<NewRequestDto.MainData> mainDataList : " + JSONObject.toJSONString(mainDataList));
            boolean flag = false;
            if (details != null && !details.isEmpty()) {
                List<NewRequestDto.DetailData> detailDatas = new ArrayList<>();
                NewRequestDto.DetailData detailData = new NewRequestDto.DetailData();
                WfInfo wfInfoByWfId = WfUtil.getWfInfoByWfId(workflowId);
                detailData.setTableDBName(wfInfoByWfId.getFormtableName() + "_dt" + glmx);
                List<NewRequestDto.WorkflowRequestTableRecords> workflowRequestTableRecordList = new ArrayList();
                for (int i = 0; i < details.size(); i++) {
                    Map<String, Object> detail = (Map<String, Object>) details.get(i);
                    NewRequestDto.WorkflowRequestTableRecords workflowRequestTableRecords = new NewRequestDto.WorkflowRequestTableRecords();
                    List<NewRequestDto.WorkflowRequestTableFields> workflowRequestTableFields = new ArrayList();
                    String detailId = Util.null2String(detail.get("id"));
                    detailIds.add(detailId);
                    flag = true;
                    for (int j = 0; j < workflowConfigs.size(); j++) {
                        JSONObject workflowConfig = workflowConfigs.getJSONObject(j);
                        //转换规则
                        String zhgz = Util.null2String(workflowConfig.get("zhgz"));
                        //建模字段
                        String jmzd = Util.null2String(workflowConfig.get("jmzdm"));
                        //流程字段
                        String lczd = Util.null2String(workflowConfig.get("lczdm"));
                        //字段位置
                        String zdwz = Util.null2String(workflowConfig.get("lczdly"));
                        //自定义转换规则
                        String zdyzhgz = Util.null2String(workflowConfig.get("zdyzhgz"));
                        //建模字段Value
                        String jmzdValue = Util.null2String(detail.get(jmzd));
                        if (!"0".equals(zdwz)) {
                            NewRequestDto.WorkflowRequestTableFields workflowRequestTableField = new NewRequestDto.WorkflowRequestTableFields();
                            workflowRequestTableField.setFieldName(lczd);
                            //字段位置在主表
                            if ("0".equals(zhgz)) {
                                //直传不做转换
                                workflowRequestTableField.setFieldValue(jmzdValue);
                            } else if ("1".equals(zhgz)) {
                                //固定值
                                workflowRequestTableField.setFieldValue(zdyzhgz);
                            } else if ("2".equals(zhgz)) {
                                //自定义执行sql
                                String fieldValue = Util.null2String(DAOUtil.selectFirstColumn(zdyzhgz, jmzdValue));
                                workflowRequestTableField.setFieldValue(fieldValue);
                            }
                            workflowRequestTableFields.add(workflowRequestTableField);
                        }
                    }

                    workflowRequestTableRecords.setWorkflowRequestTableFields(workflowRequestTableFields);
                    workflowRequestTableRecordList.add(workflowRequestTableRecords);
                }

                detailData.setWorkflowRequestTableRecords(workflowRequestTableRecordList);
                detailDatas.add(detailData);
                newRequestDto.setDetailData(detailDatas);

            }
            newRequestDto.setWorkflowId(workflowId);
            newRequestDto.setRequestName("支出合同财务数据台账自动触发计提和补票流程");
            newRequestDto.setMainData(mainDataList);
            if (!flag) {
                return requestid;
            }
            // 调用创建请求的接口
            appendLog("newRequestDto:" + JSONObject.toJSONString(newRequestDto));
            String createResult = WfUtil.createRequest(new User(1), newRequestDto);
            JSONObject createResultObj = JSONObject.parseObject(createResult);
            appendLog("createResultObj :" + JSONObject.toJSONString(createResultObj));
            // 检查创建接口响应
            if (createResultObj == null) {
                appendLog("未获取到流程创建接口响应");
            } else {
                String code = Util.null2String(createResultObj.get("code"));
                if (!"SUCCESS".equals(code)) {
                    appendLog("流程创建出错：" + createResult);
                } else {
                    JSONObject dataObj = createResultObj.getJSONObject("data");
                    if (dataObj != null && dataObj.containsKey("requestid")) {
                        requestid = Util.null2String(dataObj.get("requestid"));
                        if (!detailIds.isEmpty()) {
                            String sql = "update uf_zchtcwsjda_dt4 set sfycjlc = " + requestid + " where id in (" + String.join(",", detailIds) + ")";
                            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                            recordSet.executeUpdate(sql);
                            DBUtil.clearThreadLocalRecordSet();
                        }
                    } else {
                        appendLog("未获取到新建流程requestid");
                    }
                }
            }
        } catch (Exception e) {
            appendLog("assembleNewRequestDto2 Exception:" + SDUtil.getExceptionDetail(e));
        }
        return requestid;
    }
}
