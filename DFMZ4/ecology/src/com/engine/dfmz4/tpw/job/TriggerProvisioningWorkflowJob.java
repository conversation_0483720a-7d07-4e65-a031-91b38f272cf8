package com.engine.dfmz4.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.dfmz4.tpw.job.util.DAOUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.dto.NewRequestDto;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.time.YearMonth;
import java.util.*;

/**
 * @FileName TriggerProvisioningWorkflowJob
 * @Description 收入合同财务数据台账自动触发预提流程
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/14
 */


public class TriggerProvisioningWorkflowJob extends BaseCronJob {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private SDLogUtil sdLogUtil;
    private SDLog sdLog;
    private String errMsg;
    private String fkdws;
    //关联的要新建流程的明细
    private String glmx;
    private String workflowId;
    private JSONArray workflowConfigs;
    private List<Map<String, Object>> mainDatas;
    private HashMap<String, List<Object>> detailMap;

    @Override
    public void execute() {
        try {
            _init();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }
            appendLog("TriggerProvisioningWorkflowJob----Start");
            executeMy();
        } catch (Exception e) {
            appendLog("execute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("TriggerProvisioningWorkflowJob----END");
            afterExecute();
        }
    }

    private void executeMy() {
        try {
            appendLog("executeMy---start");
            for (int i = 0; i < mainDatas.size(); i++) {
                Map<String, Object> mainData = mainDatas.get(i);
                assembleNewRequestDto(mainData);
            }
        } catch (Exception e) {
            errMsg = "executeMy:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        } finally {
            appendLog("executeMy---end");
        }
    }

    private void _init() {
        try {
            errMsg = "";
            fkdws = "";
            sdLogUtil = new SDLogUtil();
            workflowConfigs = new JSONArray();
            mainDatas = new ArrayList<>();
            detailMap = new HashMap<>();
            //初始化日志bean
            sdLog = new SDLog(1,
                    this.getClass().getSimpleName(),
                    this.getClass().getName(),
                    SDLog.TYPE_JOB,
                    "收入合同财务数据台账自动触发预提流程");
            sdLog.setRelate_module("定时任务");
            sdLog.setRelate_table("");
            sdLog.setRelate_dataid("");
            //获取自动触发流程配置表信息
            getConfigInfo();
            if (StringUtils.isBlank(workflowId) || StringUtils.isBlank(fkdws)) {
                errMsg = "获取自动触发流程配置表信息失败 workflowId is blank or fkdws(付款单位) is blank";
                return;
            }
            //获取流程字段配置表信息
            getWorkflowFieldConfig();
            if (workflowConfigs.isEmpty()) {
                errMsg = "获取流程字段配置表信息失败";
                return;
            }
            //获取满足条件的收入合同财务数据档案的明细4（uf_srhtcwsjda_dt4）的数据
            getContractFinancialData();
            if (mainDatas.isEmpty()) {
                errMsg = "没有符合创建流程条件的mainDatas";
            } else if (detailMap.isEmpty()) {
                errMsg = "没有符合创建流程条件的detailMap";
            }
        } catch (Exception e) {
            errMsg = "TriggerProvisioningWorkflowJob----_init:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        }
    }

    private void getWorkflowFieldConfig() {
        try {
            appendLog("getWorkflowFieldConfig--start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String sql = "select * from uf_zdcflczdpz where lc = ? ";
            recordSet.executeQuery(sql, workflowId);
            if (recordSet.next()) {
                glmx = recordSet.getString("glmx");
            }
            sql = "select a.* from uf_zdcflczdpz_dt1 a inner join uf_zdcflczdpz b on a.mainid = b.id where b.lc = ? ";
            recordSet.executeQuery(sql, workflowId);
            workflowConfigs = QueryUtil.getJSONList(recordSet);
            appendLog("getWorkflowFieldConfig--workflowConfigs" + workflowConfigs.toJSONString());
        } catch (Exception e) {
            errMsg = "getWorkflowFieldConfig Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("getWorkflowFieldConfig--end");
        }
    }

    private void getContractFinancialData() {
        try {
            appendLog("getContractFinancialData--start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String sql1 = "select t1.* from uf_srhtcwsjda t1 " +
                    " inner join uf_htxx t2 on t1.htmc = t2.id " +
                    " where t1.htjelx = 1 " + //合同金额类型 = 固定金额
                    " and (t2.sjbj <> '' AND t2.sjbj IS NOT NULL) " +
                    " and t1.ftlx = 3 " +  //分摊类型 = 不分摊
                    " and t1.wfhtzt in (" + fkdws + ") " + //我方合同主体
                    " and t2.sfshht = 1";  // 是否为事后合同
            appendLog("查询收入合同财务数据档案的主表数据sql: " + sql1);
            recordSet.executeQuery(sql1);
            mainDatas = QueryUtil.getMapList(recordSet);

            appendLog("getContractFinancialData--mainDatas: " + JSONObject.toJSONString(mainDatas));

            String sql = "SELECT t1.* \n" +
                    " FROM uf_srhtcwsjda_dt4 t1 \n" +
                    " INNER JOIN uf_srhtcwsjda t2 ON t1.mainid = t2.id \n" +
                    " WHERE  t1.srqrlx = 0 " + // 收入确认类型 = 按月-每月
                    " AND (t1.sfycjlc = '' or t1.sfycjlc is null) " +
                    " AND YEAR(t1.cwszsj) = YEAR(GETDATE()) " +
                    " AND MONTH(t1.cwszsj) = MONTH(GETDATE()) \n" +
                    " AND t1.sfys <>  0 \n" +
                    " AND (t1.zxkpsj is null OR t1.zxkpsj = '' OR YEAR(t1.cwszsj) <> YEAR(t1.zxkpsj) OR MONTH(t1.cwszsj) <> MONTH(t1.zxkpsj))"; //财务所属时间不等于开票时间
            appendLog("查询收入合同财务数据档案的明细4数据sql: " + sql);
            RecordSet rs2 = new RecordSet();
            rs2.executeQuery(sql);
            List<Map<String, Object>> mapList = QueryUtil.getMapList(rs2);

            for (Map<String, Object> jo : mapList) {
                String mainid = Util.null2String(jo.get("mainid"));

                // 如果detailMap中还没有这个mainid的分组，则创建一个新的ArrayList
                if (!detailMap.containsKey(mainid)) {
                    ArrayList<Object> objects = new ArrayList<>();
                    detailMap.put(mainid, objects);
                }

                // 将当前map添加到对应的分组中
                detailMap.get(mainid).add(jo);
            }

            appendLog("getContractFinancialData--detailMap:" + JSONObject.toJSONString(detailMap));
        } catch (Exception e) {
            errMsg = "getContractFinancialData Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("getContractFinancialData--end");
        }
    }

    private void getConfigInfo() {
        try {
            appendLog("getConfigInfo---start");
            String dayOfMonth = TimeUtil.getDayOfMonth();
            //获取当前年月
            YearMonth yearMonth = YearMonth.now();
            //获取本月的天数
            int daysInMonth = yearMonth.lengthOfMonth();
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            Set<String> list = new HashSet<>(); // 无序集合
            String sql = "select * from uf_dsrwzdcflcpz";
            recordSet.executeQuery(sql);
            while (recordSet.next()) {
                //付款单位
                String fkdw = recordSet.getString("gs");
                //预提触发周期
                String ytcfzq = recordSet.getString("ytcfzq");
                //预提触发流程
                String ytcflc = recordSet.getString("ytcflc");
                if (StringUtils.isNotBlank(ytcflc)) {
                    workflowId = ytcflc;
                }
                if (StringUtils.isNotBlank(fkdw)) {
                    //当前日期属于预提触发周期
                    if (StringUtils.isNotBlank(ytcfzq)) {
                        String[] split = ytcfzq.split(",");
                        for (int i = 0; i < split.length; i++) {
                            int rqIndex = Integer.parseInt(split[i].trim());
                            if (Integer.parseInt(dayOfMonth) == daysInMonth - rqIndex || daysInMonth - rqIndex <= 0) {
                                list.add(fkdw);
                            }
                        }
                    }
                }
            }
            if (!list.isEmpty()) {
                fkdws = StringUtils.join(list, ",");
            }
            appendLog("workflowId---" + workflowId);
            appendLog("fkdws:" + fkdws);
        } catch (Exception e) {
            errMsg = "getConfigInfo Exception :" + SDUtil.getExceptionDetail(e);
            appendLog(errMsg);
        } finally {
            appendLog("getConfigInfo---end");
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void appendLog(String logMsg) {
        logger.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

    private void afterExecute() {
        try {
            appendLog("afterExecute----Start");
            //清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            //插入二开日志
            ModuleResult moduleResult = SDLog.saveLog(sdLog, sdLogUtil.getFullLog());
            appendLog("插入二开日志 moduleResult " + JSONObject.toJSONString(moduleResult));
        } catch (Exception e) {
            appendLog("afterExecute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("afterExecute----end");
        }
    }

    private void assembleNewRequestDto(Map<String, Object> mainModuleData) {
        try {
            NewRequestDto newRequestDto = new NewRequestDto();
            ArrayList<String> detailIds = new ArrayList<>();
            String mainid = Util.null2String(mainModuleData.get("id"));
            List<Object> details = detailMap.get(mainid);
            List<NewRequestDto.MainData> mainDataList = new ArrayList<>();
            for (int i = 0; i < workflowConfigs.size(); i++) {
                JSONObject workflowConfig = (JSONObject) workflowConfigs.get(i);
                //转换规则
                String zhgz = Util.null2String(workflowConfig.get("zhgz"));
                //建模字段
                String jmzd = Util.null2String(workflowConfig.get("jmzdm"));
                //流程字段
                String lczd = Util.null2String(workflowConfig.get("lczdm"));
                //字段位置
                String zdwz = Util.null2String(workflowConfig.get("lczdly"));
                //自定义转换规则
                String zdyzhgz = Util.null2String(workflowConfig.get("zdyzhgz"));
                //建模字段Value
                String jmzdValue = Util.null2String(mainModuleData.get(jmzd));
                if ("0".equals(zdwz)) {
                    NewRequestDto.MainData main = new NewRequestDto.MainData();
                    main.setFieldName(lczd);
                    //字段位置在主表
                    if ("0".equals(zhgz)) {
                        //直传不做转换
                        main.setFieldValue(jmzdValue);
                    } else if ("1".equals(zhgz)) {
                        //固定值
                        main.setFieldValue(zdyzhgz);
                    } else if ("2".equals(zhgz)) {
                        //自定义执行sql
                        String fieldValue = Util.null2String(DAOUtil.selectFirstColumn(zdyzhgz, jmzdValue));
                        main.setFieldValue(fieldValue);
                    }
                    mainDataList.add(main);
                }
            }

            NewRequestDto.MainData main = new NewRequestDto.MainData();
            main.setFieldName("ytlx");
            main.setFieldValue("1");
            mainDataList.add(main);

            if (details != null && !details.isEmpty()) {
                List<NewRequestDto.DetailData> detailDatas = new ArrayList<>();
                NewRequestDto.DetailData detailData = new NewRequestDto.DetailData();
                WfInfo wfInfoByWfId = WfUtil.getWfInfoByWfId(workflowId);
                detailData.setTableDBName(wfInfoByWfId.getFormtableName() + "_dt" + glmx);
                NewRequestDto.WorkflowRequestTableRecords workflowRequestTableRecords = new NewRequestDto.WorkflowRequestTableRecords();
                List<NewRequestDto.WorkflowRequestTableRecords> workflowRequestTableRecordList = new ArrayList();
                for (int i = 0; i < details.size(); i++) {
                    Map<String, Object> detail = (Map<String, Object>) details.get(i);
                    String detailId = Util.null2String(detail.get("id"));
                    detailIds.add(detailId);
                    List<NewRequestDto.WorkflowRequestTableFields> workflowRequestTableFields = new ArrayList();
                    for (int j = 0; j < workflowConfigs.size(); j++) {
                        JSONObject workflowConfig = (JSONObject) workflowConfigs.get(j);
                        //转换规则
                        String zhgz = Util.null2String(workflowConfig.get("zhgz"));
                        //建模字段
                        String jmzd = Util.null2String(workflowConfig.get("jmzdm"));
                        //流程字段
                        String lczd = Util.null2String(workflowConfig.get("lczdm"));
                        //字段位置
                        String zdwz = Util.null2String(workflowConfig.get("lczdly"));
                        //自定义转换规则
                        String zdyzhgz = Util.null2String(workflowConfig.get("zdyzhgz"));
                        //建模字段Value
                        String jmzdValue = Util.null2String(detail.get(jmzd));
                        if (!"0".equals(zdwz)) {
                            NewRequestDto.WorkflowRequestTableFields workflowRequestTableField = new NewRequestDto.WorkflowRequestTableFields();
                            workflowRequestTableField.setFieldName(lczd);
                            //字段位置在主表
                            if ("0".equals(zhgz)) {
                                //直传不做转换
                                workflowRequestTableField.setFieldValue(jmzdValue);
                            } else if ("1".equals(zhgz)) {
                                //固定值
                                workflowRequestTableField.setFieldValue(zdyzhgz);
                            } else if ("2".equals(zhgz)) {
                                //自定义执行sql
                                String fieldValue = Util.null2String(DAOUtil.selectFirstColumn(zdyzhgz, jmzdValue));
                                workflowRequestTableField.setFieldValue(fieldValue);
                            }
                            workflowRequestTableFields.add(workflowRequestTableField);
                        }
                    }
                    workflowRequestTableRecords.setRecordOrder(i);
                    workflowRequestTableRecords.setWorkflowRequestTableFields(workflowRequestTableFields);
                    workflowRequestTableRecordList.add(workflowRequestTableRecords);
                }

                detailData.setWorkflowRequestTableRecords(workflowRequestTableRecordList);
                detailDatas.add(detailData);
                newRequestDto.setDetailData(detailDatas);
                newRequestDto.setWorkflowId(workflowId);
                newRequestDto.setRequestName("收入合同财务数据台账自动触发预提流程");
                newRequestDto.setMainData(mainDataList);

                appendLog("newRequestDto:" + JSONObject.toJSONString(newRequestDto));
                // 调用创建请求的接口
                String createResult = WfUtil.createRequest(new User(1), newRequestDto);
                JSONObject createResultObj = JSONObject.parseObject(createResult);
                // 检查创建接口响应
                if (createResultObj == null) {
                    appendLog("未获取到流程创建接口响应");
                } else {
                    String code = Util.null2String(createResultObj.get("code"));
                    if (!"SUCCESS".equals(code)) {
                        appendLog("流程创建出错：" + createResult);
                    } else {
                        JSONObject dataObj = createResultObj.getJSONObject("data");
                        if (dataObj != null && dataObj.containsKey("requestid")) {
                            String requestid = Util.null2String(dataObj.get("requestid"));
                            if (!detailIds.isEmpty()) {
                                String sql = "update uf_srhtcwsjda_dt4 set sfycjlc = " + requestid + " where id in (" + String.join(",", detailIds) + ")";
                                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                                recordSet.executeUpdate(sql);
                                DBUtil.clearThreadLocalRecordSet();
                            }
                        } else {
                            appendLog("未获取到新建流程requestid");
                        }
                    }
                }
            }

        } catch (Exception e) {
            appendLog("assembleNewRequestDto Exception:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }
}
