package com.api.zsd.zgfx.action;

import com.api.zsd.zgfx.action.base.AbstractAction;
import com.api.zsd.zgfx.action.exception.ActionBizException;
import com.api.zsd.zgfx.action.thread.factory.DestroyItkShellProcessThreadFactory;
import com.api.zsd.zgfx.action.util.DAOUtil;
import org.apache.commons.lang.StringUtils;
import weaver.file.Prop;
import weaver.general.Util;
import weaver.soa.workflow.request.RequestInfo;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.api.zsd.zgfx.action.base.AbstractAction.ExecuteResultEnum.N;
import static com.api.zsd.zgfx.action.base.AbstractAction.ExecuteResultEnum.Y;

/**
 * 直观复兴：hcp活动预申请流程 集成 Ariba 流程Action
 *
 * <AUTHOR>
 * @date 2021-12-20
 */
public class HcpAribaIntegrationAction extends AbstractAction {

    private static final ExecutorService executorService;

    static {
        executorService = new ThreadPoolExecutor(5, 30, 3L, TimeUnit.MINUTES,
                new SynchronousQueue<>(), new DestroyItkShellProcessThreadFactory());
    }

    /**
     * itk 安装路径
     */
    private String itkHomePath;

    /**
     * 脚本执行等待时间
     * 大于0则等待指定秒数
     */
    private String scriptWaitTimeSeconds;

    @Override
    protected ExecuteResultEnum doService(RequestInfo requestInfo) throws Exception {
        itkHomePath = getConfigColumnName("itkHomePath");
        log.info("itkHomePath: {}， scriptWaitTimeSeconds, {}", itkHomePath, scriptWaitTimeSeconds);
        if (StringUtils.isEmpty(itkHomePath)) {
            errorMessage("itk安装目录未配置");
            return N;
        }
        File file = new File(itkHomePath);
        if (!file.exists() || !file.isDirectory()) {
            errorMessage("itk安装目录不存在或不是文件夹");
            return N;
        }

        // 读取流程表单中的 hcpsqlx【HCP申请类型】字段：0-新增，1-追加，2-取消
        String hcpApplyType = mainData.get(getConfigColumnName("hcpsqlx"));
        log.info("HCP申请类型: {}", hcpApplyType);
        if (hcpApplyType == null) {
            errorMessage("HCP申请类型为空");
            return N;
        }

        switch (hcpApplyType) {
            case "0":
                doAdd();
                break;
            case "1":
                doAdjust();
                break;
            case "2":
                //计算当前流程明细表的总金额
                BigDecimal workflowTotAmount = BigDecimal.ZERO;
                BigDecimal modeDetailTotalAmount = BigDecimal.ZERO;
                for (Map<String, String> item : detailData.get(0)) {
                    String amount = item.get(getConfigColumnName("dt_rmbje"));
                    workflowTotAmount = workflowTotAmount.add(new BigDecimal(amount));
                }
                //计算建模数据明细表的总金额
                List<String> modeDetailAmountList = DAOUtil.selectFirstColumnList("select " + getConfigColumnName("dt_rmbje")
                        + " from uf_hdxz_dt1 where mainid =?", mainData.get(getConfigColumnName("zjdhcpsqsx")));
                for (String item : modeDetailAmountList) {
                    modeDetailTotalAmount = modeDetailTotalAmount.add(new BigDecimal(item));
                }
                // 先计算并比较金额，如果为0则调用全部取消，否则调用部分取消
                BigDecimal diff = workflowTotAmount.add(modeDetailTotalAmount);
                if (diff.compareTo(BigDecimal.ZERO) <= 0) {
                    doCancel();
                } else {
                    doAdjust();
                }
                break;
            default:
                errorMessage("选择的HCP申请类型不为新增、追加或取消");
                return N;
        }
        return Y;
    }

    /**
     * 全部取消
     */
    private void doCancel() throws IOException {
        /*
         1. WBSElement.csv
         */
        // 获取、处理表单字段
        String modeDataId = mainData.get(getConfigColumnName("zjdhcpsqsx"));
        String workflowNumber = DAOUtil.selectFirstColumn("select " + getConfigColumnName("lcbh") + " from uf_hdxz where id =?", modeDataId);
        String title = DAOUtil.selectFirstColumn("select requestname from workflow_requestbase where requestid =?", requestId);
        log.info("modeDataId[{}], workflowNumber[{}], title[{}]", modeDataId, workflowNumber, title);
        log.info("WBSElement.csv: POSID[{}], POST1[{}]", workflowNumber, title + "（已取消）");
        // 在内存中生成 csv 文件字节信息
        byte[] wbsElementCsvFileBytes = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("POSID");
            add("PSPHI");
            add("POST1");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(workflowNumber);
                add("");
                add(title + "（已取消）");
            }});
        }});
        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Collections.singletonList(wbsElementCsvFileBytes),
                itkHomePath + File.separator + "temp" + File.separator + "wbs" + File.separator + "InDir",
                Collections.singletonList("WBSElement.csv"), "ImportWBSElement-" + requestId + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-wbs-incremental-load.bat"});

        /*
         1. BudgetCodeStatus.csv
         */
        log.info("BudgetCodeStatus.csv: BudgetCode[{}], Status[{}]", workflowNumber, "Closed");
        // 在内存中生成 csv 文件字节信息
        byte[] budgetCodeStatusCsvFileBytes = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("BudgetCode");
            add("Status");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(workflowNumber);
                add("Closed");
            }});
        }});
        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Collections.singletonList(budgetCodeStatusCsvFileBytes),
                itkHomePath + File.separator + "temp" + File.separator + "budgetcodestatus" + File.separator + "InDir",
                Collections.singletonList("BudgetCodeStatus.csv"), "BudgetCodeStatus-" + requestId + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-budget-code-status.bat"});
    }

    /**
     * 追加/部分取消
     */
    private void doAdjust() throws IOException {
        /*
         1. BudgetAdjustments.csv
         */
        String modeDataId = mainData.get(getConfigColumnName("zjdhcpsqsx"));
        String workflowNumber = DAOUtil.selectFirstColumn("select " + getConfigColumnName("lcbh") + " from uf_hdxz where id =?", modeDataId);
        String referenceRequestId = DAOUtil.selectFirstColumn("select " + getConfigColumnName("mode_requestid") + " from uf_hdxz where id = ?", modeDataId);
        SimpleDateFormat dateSdf = new SimpleDateFormat("MM/dd/yyyy");
        SimpleDateFormat yearSdf = new SimpleDateFormat("yyyy");
        String crtDate = dateSdf.format(new Date());
        String crtYear = yearSdf.format(new Date());
        //计算当前流程明细表的总金额
        BigDecimal wfTotalAmount = BigDecimal.ZERO;
        for (Map<String, String> item : detailData.get(0)) {
            String amount = item.get(getConfigColumnName("dt_rmbje"));
            wfTotalAmount = wfTotalAmount.add(new BigDecimal(amount));
        }
        //计算建模数据明细表申请类型字段不为0的总金额
        BigDecimal modeDataAdjustAmount = BigDecimal.ZERO;
        List<String> modelAmountList = DAOUtil.selectFirstColumnList("select " + getConfigColumnName("dt_rmbje")
                + " from uf_hdxz_dt1 where mainid =? and " + getConfigColumnName("dt_sqlx") + "!=0", modeDataId);
        for (String item : modelAmountList) {
            modeDataAdjustAmount = modeDataAdjustAmount.add(new BigDecimal(item));
        }
        //调整额
        BigDecimal adjustment = wfTotalAmount.add(modeDataAdjustAmount);

        //新调整额
        String zjzje = DAOUtil.selectFirstColumn("select zjzje from uf_hdxz where " + getConfigColumnName("lcbh") + " =?", workflowNumber);
        String zjqkxzje = DAOUtil.selectFirstColumn("SELECT " + getConfigColumnName("zjqkxzje") + " from  formtable_main_9 where " + getConfigColumnName("lcbh") + " = ? and " + getConfigColumnName("hcpsqlx") + " = 0", workflowNumber);
        BigDecimal zjzjeBg = BigDecimal.ZERO;
        if (org.springframework.util.StringUtils.hasLength(zjzje)) {
            zjzjeBg = new BigDecimal(zjzje);
        }
        BigDecimal zjqkxzjeBg = BigDecimal.ZERO;
        if (org.springframework.util.StringUtils.hasLength(zjqkxzje)) {
            zjqkxzjeBg = new BigDecimal(zjqkxzje);
        }

        BigDecimal adjustmentNew = zjzjeBg.subtract(zjqkxzjeBg);

        log.info("modeDataId[{}], workflowNumber[{}], crtDate[{}], crtYear[{}], workflowTotAmount[{}], modeDataAdjustAmount[{}]",
                modeDataId, workflowNumber, crtDate, crtYear, wfTotalAmount, modeDataAdjustAmount);
        log.info("BudgetAdjustments.csv: Adjustment[{}], BudgetCode[{}]", adjustmentNew, workflowNumber);
        // 在内存中生成 csv 文件字节信息
        byte[] budgetAdjustments = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("Currency");
            add("Adjustment");
            add("BudgetCode");
            add("FiscalYear");
            add("AccountingPeriod");
            add("Comment");
            add("TransactionDate");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add("CNY");
                add(adjustmentNew.toString());
                add(workflowNumber);
                add("FY" + crtYear);
                add(referenceRequestId);
                add("");
                add(crtDate);
            }});
        }});

        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Collections.singletonList(budgetAdjustments),
                itkHomePath + File.separator + "temp" + File.separator + "budgetadjustments" + File.separator + "InDir",
                Collections.singletonList("BudgetAdjustments.csv"), "LoadBudgetAdjustments-" + requestId + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-budget-adjustments.bat"});
    }


    /**
     * 新增
     */
    private void doAdd() throws IOException {
        /*
         1. WBSElement.csv
         2. CompanyCodeWBSCombo.csv
         */
        // 获取、处理表单字段
        String workflowNumber = mainData.get(getConfigColumnName("lcbh"));
        String title = DAOUtil.selectFirstColumn("select requestname from workflow_requestbase where requestid=?", requestId);
        String subCompanyId = mainData.get(getConfigColumnName("sqfb"));
        String subCompanyName = DAOUtil.selectFirstColumn("select SUBCOMPANYNAME from hrmsubcompany where id=?", subCompanyId);
        String subCompanyCode = "直观复星（香港）有限公司".equalsIgnoreCase(subCompanyName.trim()) ? "6400" : "6300";
        log.info("workflowNumber[{}], title[{}], subCompanyId[{}], subCompanyName[{}], subCompanyCode[{}]",
                workflowNumber, title, subCompanyId, subCompanyName, subCompanyCode);
        log.info("WBSElement.csv: POSID[{}], POST1[{}]", workflowNumber, title);
        log.info("CompanyCodeWBSCombo.csv: POSID[{}], PBUKR[{}]", workflowNumber, subCompanyCode);
        // 在内存中生成 csv 文件字节信息
        byte[] wbsElementCsvFileBytes = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("POSID");
            add("PSPHI");
            add("POST1");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(workflowNumber);
                add("");
                add(title);
            }});
        }});
        byte[] companyCodeWbsComboCsvFileBytes = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("PBUKR");
            add("POSID");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(subCompanyCode);
                add(workflowNumber);
            }});
        }});
        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Arrays.asList(wbsElementCsvFileBytes, companyCodeWbsComboCsvFileBytes),
                itkHomePath + File.separator + "temp" + File.separator + "wbs" + File.separator + "InDir",
                Arrays.asList("WBSElement.csv", "CompanyCodeWBSCombo.csv"), "ImportBatchData-" + requestId + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-wbs-incremental-load.bat"});

        /*
         3. BudgetHeader.csv
         4. BudgetPeriods.csv
         5. BudgetLineItems.csv
         6. BudgetAllocation.csv
         */
        // 获取、处理表单字段
        SimpleDateFormat normalSdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat aribaSdf = new SimpleDateFormat("MM/dd/yyyy");
        SimpleDateFormat yearSdf = new SimpleDateFormat("yyyy");
        String crtYear = yearSdf.format(new Date());
        String activeStartDate = mainData.get(getConfigColumnName("hdksrq"));
        String startDate = aribaSdf.format(normalSdf.parse(activeStartDate, new ParsePosition(0))) + " 00:00:00 +0800";
        String totalAmount = mainData.get(getConfigColumnName("zjqkxzje"));
        log.info("crtYear: {}, activeStartDate: {}, aribaStartDate: {}, totalAmount: {}",
                crtYear, activeStartDate, startDate, totalAmount);
        // 在内存中生成 csv 文件字节信息
        byte[] budgetHeader = writeCsvAndConvert2InputStream(Collections.singletonList("Title"), Collections.singletonList(Collections.singletonList(workflowNumber)));
        byte[] budgetPeriods = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("EndDate");
            add("FiscalYear");
            add("AccountingPeriod");
            add("StartDate");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add("12/31/9999 23:59:59 +0800");
                add("FY" + crtYear);
                add(requestId);
                add(startDate);
            }});
        }});
        byte[] budgetLineItems = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("LineNumber");
            add("Account");
            add("Asset");
            add("CostCenter");
            add("GeneralLedger");
            add("InternalOrder");
            add("WBSElement");
            add("CompanyCode");
            add("SubNumber");
            add("ProcurementUnit");
            add("Budget Check Disabled");
            add("BudgetCode");
            add("Rank");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add("1");
                add("");
                add("");
                add("");
                add("");
                add("");
                add(workflowNumber);
                add(subCompanyCode);
                add("");
                add("");
                add("");
                add(workflowNumber);
                add("10");
            }});
        }});
        byte[] budgetAllocation = writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("Currency");
            add("AvailableCurrency");
            add("AllocationAmt");
            add("BudgetCode");
            add("FiscalYear");
            add("AccountingPeriod");
            add("AvailableAmt");
            add("PercentageTolerance");
            add("LineNumber");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add("CNY");
                add("");
                add(totalAmount);
                add(workflowNumber);
                add("FY" + crtYear);
                add(requestId);
                add("");
                add("15");
                add("1");
            }});
        }});
        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Arrays.asList(budgetHeader, budgetPeriods, budgetLineItems, budgetAllocation),
                itkHomePath + File.separator + "temp" + File.separator + "budgets" + File.separator + "InDir",
                Arrays.asList("BudgetHeader.csv", "BudgetPeriods.csv", "BudgetLineItems.csv", "BudgetAllocation.csv"), "LoadBudget-" + requestId + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-budgets.bat"});
    }

    /**
     * 生产压缩包文件
     *
     * @param csvFileInputBytesArray 文件字节输入流
     * @param path                   目录
     * @param csvFileNames           csv名
     * @param zipFileName            压缩包名
     */
    private void generateCsvZip(List<byte[]> csvFileInputBytesArray, String path, List<String> csvFileNames, String zipFileName) {
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(path + File.separator + zipFileName), StandardCharsets.UTF_8)) {
            for (int i = 0; i < csvFileInputBytesArray.size(); i++) {
                zipOutputStream.putNextEntry(new ZipEntry(csvFileNames.get(i)));
                InputStream csvInput = new ByteArrayInputStream(csvFileInputBytesArray.get(i));
                int tmp;
                while ((tmp = csvInput.read()) != -1) {
                    zipOutputStream.write(tmp);
                }
                zipOutputStream.closeEntry();
            }
            zipOutputStream.flush();
        } catch (IOException e) {
            log.error("生成压缩文件异常", e);
            throw new ActionBizException("生成压缩文件异常: " + e.getMessage());
        }
    }

    /**
     * 运行shell脚本
     *
     * @param command 命令
     */
    private void callShell(String[] command) throws IOException {
        log.info("ariba sh command: [{}]", String.join(" ", command));
        final int waitSeconds = Util.getIntValue(scriptWaitTimeSeconds, 0);
        Process process = Runtime.getRuntime().exec(command);
        Future<?> future = null;
        if (waitSeconds > 0) {
            future = executorService.submit(() -> {
                try {
                    log.info("{}: ariba-shell process will be destroyed after exec {} seconds", Thread.currentThread().getName(), waitSeconds);
                    Thread.sleep(waitSeconds * 1000L);
                    process.destroy();
                    log.info("{}: process destroyed");
                } catch (InterruptedException e) {
                    // ignored
                    log.error("{} interrupted, exceptionMessage: {}", Thread.currentThread().getName(), e.getMessage());
                }
            });
        }
        StringJoiner normalInputContent = new StringJoiner("\n");
        StringJoiner errorInputContent = new StringJoiner("\n");
        try (InputStreamReader inputStream = new InputStreamReader(process.getInputStream());
             InputStreamReader errorStream = new InputStreamReader(process.getErrorStream());
             LineNumberReader inputLn = new LineNumberReader(inputStream);
             LineNumberReader errorLn = new LineNumberReader(errorStream)) {
            String line;

            try {
                while ((line = inputLn.readLine()) != null) {
                    normalInputContent.add(line);
                }
                while ((line = errorLn.readLine()) != null) {
                    errorInputContent.add(line);
                }
            } catch (Exception e) {
                // ignored
            }

            int waitForCode = process.waitFor();
            if (future != null) {
                future.cancel(true);
            }
            log.info("waitForCode: {}", waitForCode);
            log.info("output: {}", normalInputContent);
            log.info("error: {}", errorInputContent);

            if (!normalInputContent.toString().toLowerCase().contains("Data transfer complete".toLowerCase())) {
                throw new ActionBizException("执行ITK脚本同步数据到Ariba失败：" +
                        (StringUtils.isEmpty(normalInputContent.toString()) ? errorInputContent : normalInputContent.toString()));
            }
        } catch (InterruptedException e) {
            // ignored
            log.error("执行ITK脚本中断", e);
            throw new ActionBizException("执行ITK脚本中断: " + e.getMessage());
        }
    }

    /**
     * 生成 cdv 文件的字节数据
     *
     * @param header csv 标题
     * @param rows   csv 数据
     * @return 字节数据
     * @throws IOException ioe
     */
    private byte[] writeCsvAndConvert2InputStream(List<String> header, List<List<String>> rows) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8), 1024)) {
            // 文件模版头
            writer.write("\"UTF-8\"");
            writer.newLine();
            // 写入文件表头
            writeCsvRow(writer, header);
            // 文件内容
            for (List<String> row : rows) {
                writeCsvRow(writer, row);
            }

            writer.flush();
            return outputStream.toByteArray();
        }
    }

    /**
     * 写 csv 数据行
     *
     * @param writer writer
     * @param row    行数据
     * @throws IOException ioe
     */
    private void writeCsvRow(BufferedWriter writer, List<String> row) throws IOException {
        for (int i = 0; i < row.size(); i++) {
            writer.write((row.get(i) == null ? "" : row.get(i))
                    + ((i == row.size() - 1) ? "" : ","));
        }
        writer.newLine();
    }

    /**
     * 从配置文件中读取配置的字段名
     *
     * @param tag 配置项
     * @return 配置值
     */
    private String getConfigColumnName(String tag) {
        return Prop.getPropValue("zsd_zgfx_hcp_ariba", tag);
    }

    public String getScriptWaitTimeSeconds() {
        return scriptWaitTimeSeconds;
    }

    public void setScriptWaitTimeSeconds(String scriptWaitTimeSeconds) {
        this.scriptWaitTimeSeconds = scriptWaitTimeSeconds;
    }
}
