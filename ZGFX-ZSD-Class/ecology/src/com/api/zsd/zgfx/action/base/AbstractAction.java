package com.api.zsd.zgfx.action.base;

import com.weaver.general.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.workflow.form.FormManager;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/22
 */
public abstract class AbstractAction implements Action {
    protected static Logger log;

    public enum ExecuteResultEnum {
        /**
         * 成功
         */
        Y,
        /**
         * 失败
         */
        N
    }

    private String actionName;
    protected RequestInfo requestInfo;
    protected String requestId;
    protected String formTableName;
    protected String workflowId;
    protected Integer nodeId;
    protected Map<String, String> mainData;
    protected List<List<Map<String, String>>> detailData;

    /**
     * 初始化属性
     */
    private void initProperties(RequestInfo requestInfo) {
        actionName = this.getClass().getName();
        log = LoggerFactory.getLogger(this.getClass());
        this.requestInfo = requestInfo;
        requestId = requestInfo.getRequestid();
        formTableName = getTableName(requestInfo);
        workflowId = requestInfo.getWorkflowid();
        nodeId = requestInfo.getRequestManager().getNodeid();
        mainData = getMainParam(requestInfo);
        detailData = getAllDetailParam(requestInfo);
    }

    @Override
    public final String execute(RequestInfo requestInfo) {
        long start = System.currentTimeMillis();
        initProperties(requestInfo);
        log.info("===== Execute Action[{}] Start=====", actionName);
        log.info("=== Param: workflowId[{}], requestId[{}], nodeId[{}], formTableName[{}] ===",
                workflowId, requestId, nodeId, formTableName);
        ExecuteResultEnum executeResult;
        try {
            log.info("=== Biz Code Start ===");
            executeResult = doService(requestInfo);
            log.info("=== Biz Code End ===");
        } catch (Exception e) {
            log.error("=== Biz Code Error[{}], Error Message[{}] ===", actionName, e.getMessage(), e);
            errorMessage(this.getClass().getSimpleName() + "执行异常: " + e.getMessage());
            executeResult = ExecuteResultEnum.N;
        }
        long end = System.currentTimeMillis();
        log.info("===== Execute Action[{}] End, Time[{}] =====", actionName, end - start);
        return ExecuteResultEnum.Y == executeResult ? SUCCESS : FAILURE_AND_CONTINUE;
    }

    /**
     * 实际处理业务
     *
     * @param requestInfo requestInfo
     * @return {@link Action#SUCCESS} <br/> {@link Action#FAILURE_AND_CONTINUE}
     * @throws Exception exception
     */
    protected abstract ExecuteResultEnum doService(RequestInfo requestInfo) throws Exception;

    protected void errorMessage(String message) {
        requestInfo.getRequestManager().setMessagecontent(message);
    }

    private String getTableName(RequestInfo requestInfo) {
        int formId = requestInfo.getRequestManager().getFormid();
        FormManager fManager = new FormManager();
        return fManager.getTablename(formId);
    }

    /**
     * 获取主表数据
     *
     * @param requestInfo requestInfo
     * @return map
     */
    public static Map<String, String> getMainParam(RequestInfo requestInfo) {
        Map<String, String> map = new HashMap<>(16);
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (Property property : properties) {
            String name = property.getName();
            String value = Util.null2String(property.getValue());
            if (!"".equals(value)) {
                map.put(name, value);
            }
        }
        return map;
    }


    /**
     * 获取所有明细表数据信息
     *
     * @param requestInfo requestInfo
     * @return list&lt;list&lt;map>>
     */
    public static List<List<Map<String, String>>> getAllDetailParam(RequestInfo requestInfo) {
        List<List<Map<String, String>>> result = new ArrayList<>();
        int detailTableLength = requestInfo.getDetailTableInfo().getDetailTable().length;
        for (int i = 0; i < detailTableLength; i++) {
            result.add(getDetailParam(requestInfo, i));
        }
        return result;
    }

    /**
     * 获取指定明细表索引的明细信息
     *
     * @param requestInfo      requestInfo
     * @param detailTableIndex 明细表序号-1
     * @return list&lt;map>
     */
    public static List<Map<String, String>> getDetailParam(RequestInfo requestInfo, int detailTableIndex) {
        DetailTable[] detailTable = requestInfo.getDetailTableInfo().getDetailTable();
        if (detailTable.length < detailTableIndex + 1) {
            return Collections.emptyList();
        } else {
            DetailTable dt = detailTable[detailTableIndex];
            Row[] rows = dt.getRow();
            List<Map<String, String>> list = new ArrayList<>();
            for (Row row : rows) {
                Map<String, String> map = new HashMap<>(16);
                Cell[] cells = row.getCell();
                for (Cell c1 : cells) {
                    String name = c1.getName();
                    String value = c1.getValue();
                    if (value != null && !"".equals(value)) {
                        map.put(name, value);
                    }
                }
                list.add(map);
            }
            return list;
        }
    }
}
