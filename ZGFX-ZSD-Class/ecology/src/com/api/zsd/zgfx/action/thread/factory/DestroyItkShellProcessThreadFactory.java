package com.api.zsd.zgfx.action.thread.factory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 销毁 ariba shell 脚本进程，避免程序永久阻塞
 *
 * <AUTHOR>
 * @date 2021-12-22
 */
public class DestroyItkShellProcessThreadFactory implements ThreadFactory {
    public static String NAME_PREFIX = "DESTROY-ITK-SHELL-PROCESS-THREAD-";
    private static final AtomicLong THREAD_NUMBER = new AtomicLong(1);

    @Override
    public Thread newThread(Runnable r) {
        return new Thread(r == null ? () -> {
        } : r,
                NAME_PREFIX + THREAD_NUMBER.getAndIncrement());
    }
}
