package com.api.zsd.zgfx.action.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * data access object util
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2021/7/30
 */
public class DAOUtil {
    private static final Logger log = LoggerFactory.getLogger(DAOUtil.class);
    /**
     * sql
     */
    private static final String PREPARING_LOG_STRING = "==>  Preparing: {}";
    /**
     * 参数
     */
    private static final String PARAMETERS_LOG_STRING = "==> Parameters: {}";
    /**
     * 查询结果（数据）
     */
    private static final String QUERY_RESULT_LOG_STRING = "==>     Result: {}";
    /**
     * 更新结果（更新条数）
     */
    private static final String UPDATE_RESULT_LOG_STRING = "==>    Updates: {}";
    /**
     * 查询数据量（查询数据数量）
     */
    private static final String COUNT_RESULT_LOG_STRING = "<==      Total: {}";


    private DAOUtil() {
    }

    /**
     * 日志记录 sql 和 参数
     *
     * @param sql  sql语句
     * @param args 参数
     */
    private static void logSql(String sql, Object... args) {
        log.info(PREPARING_LOG_STRING, sql);
        if (args != null && args.length > 0) {
            log.info(PARAMETERS_LOG_STRING, Arrays.stream(args)
                    .map(it -> it == null ? "null" : it + "(" + it.getClass().getSimpleName() + ")")
                    .collect(Collectors.joining(", ")));
        }
    }

    /**
     * 查询一条数据，获取第一个字段
     *
     * @param sql  sql语句
     * @param args 参数
     * @return 查询结果
     */
    public static String selectFirstColumn(String sql, Object... args) {
        return selectFirstColumn(new RecordSet(), sql, args);
    }


    /**
     * 查询一条数据，获取第一个字段
     *
     * @param recordSet rs
     * @param sql       sql语句
     * @param args      参数
     * @return 查询结果
     */
    public static String selectFirstColumn(RecordSet recordSet, String sql, Object... args) {
        logSql(sql, args);
        recordSet.executeQuery(sql, args);
        if (recordSet.next()) {
            String result = recordSet.getString(1);
            log.info(QUERY_RESULT_LOG_STRING, result);
            return result;
        }
        return null;
    }

    /**
     * 执行sql语句，获取每一行数据的第一个字段，返回list
     *
     * @param sql  sql语句
     * @param args 参数
     * @return 查询结构
     */
    public static List<String> selectFirstColumnList(String sql, Object... args) {
        return selectFirstColumnList(new RecordSet(), sql, args);
    }

    /**
     * 执行sql语句，获取每一行数据的第一个字段，返回list
     *
     * @param recordSet rs
     * @param sql       sql语句
     * @param args      参数
     * @return 查询结构
     */
    public static List<String> selectFirstColumnList(RecordSet recordSet, String sql, Object... args) {
        logSql(sql, args);
        List<String> result = new ArrayList<>();
        recordSet.executeQuery(sql, args);
        log.info(COUNT_RESULT_LOG_STRING, recordSet.getCounts());
        while (recordSet.next()) {
            result.add(recordSet.getString(1));
        }
        return result;
    }

}
