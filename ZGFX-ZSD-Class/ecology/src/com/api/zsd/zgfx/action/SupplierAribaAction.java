package com.api.zsd.zgfx.action;

import com.api.zsd.zgfx.action.base.AbstractAction;
import com.api.zsd.zgfx.action.exception.ActionBizException;
import com.api.zsd.zgfx.action.thread.factory.DestroyItkShellProcessThreadFactory;
import com.api.zsd.zgfx.action.util.DAOUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.soa.workflow.request.RequestInfo;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.concurrent.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.api.zsd.zgfx.action.base.AbstractAction.ExecuteResultEnum.N;
import static com.api.zsd.zgfx.action.base.AbstractAction.ExecuteResultEnum.Y;
import static org.apache.commons.lang3.StringUtils.isEmpty;

/**
 * 直观复兴：供应商申请表流程 集成 Ariba 流程Action
 *
 * <AUTHOR>
 */
public class SupplierAribaAction extends AbstractAction {

    /**
     * itk 安装路径
     */
    private String itkHomePath;

    /**
     * bankInfoName 字段ID：测试[15022]、生产[12264]
     */
    private String bankInfoNameFieldId;

    /**
     * 变更时查询供应商名称时的建模表名称，默认uf_Aribagysmk
     */
    private String supplierModeTableName = "uf_Aribagysmk";
    /**
     * 变更时查询供应商名称时的建模表字段名，默认gysmc
     */
    private String supplierModeFieldName = "gysmc";

    /**
     * 脚本执行等待时间
     * 大于0则等待指定秒数
     */
    private String scriptWaitTimeSeconds;

    private static final ExecutorService executorService;

    static {
        executorService = new ThreadPoolExecutor(5, 30, 3L, TimeUnit.MINUTES,
                new SynchronousQueue<>(), new DestroyItkShellProcessThreadFactory());
    }

    @Override
    protected ExecuteResultEnum doService(RequestInfo requestInfo) throws Exception {

        //读取流程表单中的	sqlx【申请类型】字段：0-新增，1-变更
        String supplierApplyType = mainData.get("sqlx");
        log.info("供应商申请类型: {}", supplierApplyType);
        if (supplierApplyType == null) {
            errorMessage("申请类型为空");
            return N;
        }
        if (isEmpty(bankInfoNameFieldId)) {
            errorMessage("节点动作配置不完整");
            return N;
        }

        switch (supplierApplyType) {
            case "0":
                doAdd();
                break;
            case "1":
                doUpdate();
                break;
            default:
                errorMessage("选择的申请类型不为新增或变更");
                return N;
        }
        return Y;
    }


    /**
     * 新增
     */
    private void doAdd() throws IOException {
        /*
         * SupplierConsolidated.csv;
         * SupplierLocationConsolidated.csv;
         * PurchaseOrgSupplierCombo.csv
         * RemittanceLocationConsolidated.csv
         * */
        // 在内存中生成 csv 文件字节信息
        byte[] supplierConsolidatedCsvFileBytes = createSupplierConsolidatedCsv(true);
        byte[] supplierLocationConsolidatedCsvFileBytes = createSupplierLocationConsolidatedCsv(true);
        byte[] purchaseOrgSupplierComboCsvFileBytes = createPurchaseOrgSupplierComboCsv();
        byte[] remittanceLocationConsolidatedCsvFileBytes = createRemittanceLocationConsolidatedCsv(true);
        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Arrays.asList(supplierConsolidatedCsvFileBytes, supplierLocationConsolidatedCsvFileBytes, purchaseOrgSupplierComboCsvFileBytes, remittanceLocationConsolidatedCsvFileBytes),
                itkHomePath + File.separator + "temp" + File.separator + "supplier" + File.separator + "InDir",
                Arrays.asList("SupplierConsolidated.csv", "SupplierLocationConsolidated.csv", "PurchaseOrgSupplierCombo.csv", "RemittanceLocationConsolidated.csv"),
                "Supplier-" + requestId + "-" + System.currentTimeMillis() + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-suuplier-incremental-load.bat"});
    }

    /**
     * 变更
     */
    private void doUpdate() throws IOException {
        /*
         * SupplierConsolidated.csv;
         * SupplierLocationConsolidated.csv;
         * PurchaseOrgSupplierCombo.csv
         * RemittanceLocationConsolidated.csv
         * */
        // 在内存中生成 csv 文件字节信息
        byte[] supplierConsolidatedCsvFileBytes = createSupplierConsolidatedCsv(false);
        byte[] supplierLocationConsolidatedCsvFileBytes = createSupplierLocationConsolidatedCsv(false);
        byte[] purchaseOrgSupplierComboCsvFileBytes = createPurchaseOrgSupplierComboCsv();
        byte[] remittanceLocationConsolidatedCsvFileBytes = createRemittanceLocationConsolidatedCsv(false);
        // 将多个 csv 文件字节流，生成压缩文件写入磁盘
        generateCsvZip(Arrays.asList(supplierConsolidatedCsvFileBytes, supplierLocationConsolidatedCsvFileBytes, purchaseOrgSupplierComboCsvFileBytes, remittanceLocationConsolidatedCsvFileBytes),
                itkHomePath + File.separator + "temp" + File.separator + "supplier" + File.separator + "InDir",
                Arrays.asList("SupplierConsolidated.csv", "SupplierLocationConsolidated.csv", "PurchaseOrgSupplierCombo.csv", "RemittanceLocationConsolidated.csv"),
                "Supplier-" + requestId + "-" + System.currentTimeMillis() + ".zip");
        // 使用 git-bash 调用 shell 脚本
        callShell(new String[]{itkHomePath + File.separator + "bin" + File.separator + "aribafiletransfer.bat",
                itkHomePath + File.separator + "bin" + File.separator + "intufosun-suuplier-incremental-load.bat"});
    }

    /**
     * 生成SupplierConsolidated.csv文件字节流
     *
     * @param isAdd 是否是新增流程，true-新增，false-变更
     * @return csv文件字节流
     */
    private byte[] createSupplierConsolidatedCsv(boolean isAdd) throws IOException {
        String vendorId = mainData.get("venderid");
        String name;//供应商名称
        if (isAdd) {
            name = mainData.get("gysmc");
        } else {
            name = getSupplierNameById(mainData.get("gysxz"));
        }
        String corporateURL = "";//供应商英文名，空串
        String corporateEmailAddress = mainData.get("yjdz");//邮箱-邮件地址
        String yy = mainData.get("yy1");
        if (yy.equals("0")) {
            yy = "SimplifiedChinese";
        } else if (yy.equals("1")) {
            yy = "English";
        }
        String preferredLanguage = yy;//首选语音-语言
        log.info("SupplierConsolidated.csv: VendorID/SystemID/AddressID[{}], Name[{}], CorporateURL[{}], CorporateEmailAddress[{}], PreferredLanguage[{}]",
                vendorId, name, corporateURL, corporateEmailAddress, preferredLanguage);
        return writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("VendorID");
            add("SystemID");
            add("AddressID");
            add("Name");
            add("Name");
            add("CorporateURL");
            add("CorporateEmailAddress");
            add("PreferredLanguage");
            add("ImportCtrl");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(vendorId);
                add(vendorId);
                add(vendorId);
                add(name);
                add(name);
                add(corporateURL);
                add(corporateEmailAddress);
                add(preferredLanguage);
                add("Both");
            }});
        }});
    }

    /**
     * 生成SupplierLocationConsolidated.csv文件字节流
     *
     * @param isAdd 是否是新增流程，true-新增，false-变更
     * @return csv文件字节流
     */
    private byte[] createSupplierLocationConsolidatedCsv(boolean isAdd) throws IOException {
        String vendorId = mainData.get("venderid");
        String name;//供应商名称
        if (isAdd) {
            name = mainData.get("gysmc");
        } else {
            name = getSupplierNameById(mainData.get("gysxz"));
        }
        String jnw = mainData.get("jnw");
        if (jnw.equals("0")) {
            jnw = "zh_CN";
        } else if (jnw.equals("1")) {
            jnw = "en_US";
        }
        String locale = jnw;//地区-境内/外
        String country = mainData.get("gjwybsf");//国家-国家唯一标识符
        String regionAndCity = mainData.get("cs");//省份\城市-城市
        String street = mainData.get("xxdzjd");//街道-详细地址（街道）
        String postalCode = mainData.get("yb");//邮编-邮编
        String contactName = mainData.get("lxr");//联系人姓名-联系人
        String emailAddress = mainData.get("yjdz");//联系人邮箱-邮件地址
        String phone = mainData.get("lxfs");//联系人电话-联系方式
        log.info("SupplierLocationConsolidated.csv: VendorID/LocationID/ContactID[{}], Name[{}], Locale[{}], Country[{}], Region/City[{}], Street[{}]," +
                        " PostalCode[{}], ContactName[{}], EmailAddress[{}], Phone[{}]",
                vendorId, name, locale, country, regionAndCity, street, postalCode, contactName, emailAddress, phone);
        return writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("VendorID");
            add("LocationID");
            add("ContactID");
            add("Name");
            add("Locale");
            add("Country");
            add("Region");
            add("City");
            add("Street");
            add("PostalCode");
            add("ContactName");
            add("EmailAddress");
            add("Phone");
            add("PreferredOrderingMethod");
            add("PCardAcceptanceLevel");
            add("ChangeOrderRestrictions");
            add("GRBasedInvoicingPreferred");
            add("SplitOrderOnShipTo");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(vendorId);
                add(vendorId);
                add(vendorId);
                add(name);
                add(locale);
                add(country);
                add(regionAndCity);
                add(regionAndCity);
                add(street);
                add(postalCode);
                add(contactName);
                add(emailAddress);
                add(phone);
                add("Email");
                add("0");
                add("0");
                add("Yes");
                add("True");
            }});
        }});
    }

    /**
     * 生成PurchaseOrgSupplierCombo.csv文件字节流
     *
     * @return csv文件字节流
     */
    private byte[] createPurchaseOrgSupplierComboCsv() throws IOException {
        String vendorId = mainData.get("venderid");
        String INCO1 = mainData.get("gjmytkwybsf");//国际贸易条款唯一标识符
        String ZTERM = mainData.get("zqwybsf");//账期唯一标识符
        log.info("PurchaseOrgSupplierCombo.csv: LIFNR[{}], INCO1[{}], ZTERM[{}]", vendorId, INCO1, ZTERM);
        return writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("EKORG");
            add("LIFNR");
            add("XERSY");
            add("INCO1");
            add("ZTERM");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add("1000");
                add(vendorId);
                add("X");
                add(INCO1);
                add(ZTERM);
            }});
        }});
    }


    /**
     * 生成RemittanceLocationConsolidated.csv文件字节流
     *
     * @param isAdd 是否是新增流程，true-新增，false-变更
     * @return csv文件字节流
     */
    private byte[] createRemittanceLocationConsolidatedCsv(boolean isAdd) throws IOException {
        String vendorId = mainData.get("venderid");
        String name;//供应商名称
        if (isAdd) {
            name = mainData.get("gysmc") + "-付款信息";
        } else {
            name = getSupplierNameById(mainData.get("gysxz")) + "-付款信息";
        }
        String state = mainData.get("cs");//城市
        String postalCode = mainData.get("yb");//邮编
        String countryUniqueName = mainData.get("gjwybsf");//国家唯一标识符
        String lines = mainData.get("gjwybsf").equals("CN") ? mainData.get("xxdzjd") : "";//汇款地点街道 - 境内"",境外"详细地址街道"
        String city = mainData.get("cs");//
        String bankID = mainData.get("yxlxh");//银行联行号
        String bankIDType = mainData.get("jnw").equals("0") ? "CNAPS" : "";//BankID类型,境内外字段为0取CNAPS
        String IbanID = mainData.get("swiftcodeiban");
        String accountName = mainData.get("yxhm");//银行户名
        String bankInfoName = DAOUtil.selectFirstColumn("select SELECTNAME from workflow_selectitem where FIELDID = '" + bankInfoNameFieldId + "' and SELECTVALUE = ?", mainData.get("yxzh11"));//开户银行-选择框
        String bankName = mainData.get("khx");//开户行
        String bankAccountID = mainData.get("yxzh");//银行账户
        String bankStreet = mainData.get("jnw").equals("0") ? "" : mainData.get("yxdzzyw");//银行地址 - 境内"",境外"yxdzzyw-银行地址(中英文)"
        log.info("RemittanceLocationConsolidated.csv: RemittanceLocationID/SupplierLocationID/ContactId[{}], AddressUniqueName[{}], Name[{}]," +
                        " State[{}], PostalCode[{}], CountryUniqueName[{}], Lines[{}], City[{}], BankID[{}], BankIDType[{}], IbanID[{}]," +
                        " AccountName[{}], BankInfoName[{}], BankName[{}], BankAccountID[{}], BankStreet[{}]",
                vendorId, vendorId + "-1", name, state, postalCode, countryUniqueName, lines, city, bankID, bankIDType, IbanID,
                accountName, bankInfoName, bankName, bankAccountID, bankStreet);
        return writeCsvAndConvert2InputStream(new ArrayList<String>() {{
            add("RemittanceLocationID");
            add("SupplierLocationID");
            add("ContactId");
            add("AddressUniqueName");
            add("Name");
            add("State");
            add("PostalCode");
            add("CountryUniqueName");
            add("Lines");
            add("City");
            add("BankAccountIDType");
            add("BankAccountType");
            add("BankAddressUniqueName");
            add("BankID");
            add("BankIDType");
            add("IbanID");
            add("AccountName");
            add("BankInfoName");
            add("BankName");
            add("BankAccountID");
            add("BankRegion");
            add("BankCity");
            add("BankCountry");
            add("BankStreet");
            add("PaymentMethod");
        }}, new ArrayList<List<String>>() {{
            add(new ArrayList<String>() {{
                add(vendorId);
                add(vendorId);
                add(vendorId);
                add(vendorId + "-1");
                add(name);
                add(state);
                add(postalCode);
                add(countryUniqueName);
                add(lines);
                add(city);
                add("");
                add("savings");
                add("");
                add(bankID);
                add(bankIDType);
                add(IbanID);
                add(accountName);
                add(bankInfoName);
                add(bankName);
                add(bankAccountID);
                add("");
                add("");
                add("");
                add(bankStreet);
                add("wire");
            }});
        }});
    }

    /**
     * 生产压缩包文件
     *
     * @param csvFileInputBytesArray 文件字节输入流
     * @param path                   目录
     * @param csvFileNames           csv名
     * @param zipFileName            压缩包名
     */
    private void generateCsvZip(List<byte[]> csvFileInputBytesArray, String path, List<String> csvFileNames, String zipFileName) {
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(Files.newOutputStream(Paths.get(path + File.separator + zipFileName)), StandardCharsets.UTF_8)) {
            for (int i = 0; i < csvFileInputBytesArray.size(); i++) {
                zipOutputStream.putNextEntry(new ZipEntry(csvFileNames.get(i)));
                InputStream csvInput = new ByteArrayInputStream(csvFileInputBytesArray.get(i));
                int tmp;
                while ((tmp = csvInput.read()) != -1) {
                    zipOutputStream.write(tmp);
                }
                zipOutputStream.closeEntry();
            }
            zipOutputStream.flush();
        } catch (IOException e) {
            log.error("生成压缩文件异常", e);
            throw new ActionBizException("生成压缩文件异常: " + e.getMessage());
        }
    }

    /**
     * 运行shell脚本
     *
     * @param command 命令
     */
    private void callShell(String[] command) throws IOException {
        log.info("ariba sh command: [{}]", String.join(" ", command));
        final int waitSeconds = Util.getIntValue(scriptWaitTimeSeconds, 0);
        Process process = Runtime.getRuntime().exec(command);
        Future<?> future = null;
        if (waitSeconds > 0) {
            future = executorService.submit(() -> {
                try {
                    log.info("{}: ariba-shell process will be destroyed after exec {} seconds", Thread.currentThread().getName(), waitSeconds);
                    Thread.sleep(waitSeconds * 1000L);
                    process.destroy();
                    log.info("{}: process destroyed", Thread.currentThread().getName());
                } catch (InterruptedException e) {
                    // ignored
                    log.error("{} interrupted, exceptionMessage: {}", Thread.currentThread().getName(), e.getMessage());
                }
            });
        }
        StringJoiner normalInputContent = new StringJoiner("\n");
        StringJoiner errorInputContent = new StringJoiner("\n");
        try (InputStreamReader inputStream = new InputStreamReader(process.getInputStream());
             InputStreamReader errorStream = new InputStreamReader(process.getErrorStream());
             LineNumberReader inputLn = new LineNumberReader(inputStream);
             LineNumberReader errorLn = new LineNumberReader(errorStream)) {
            String line;

            try {
                while ((line = inputLn.readLine()) != null) {
                    normalInputContent.add(line);
                }
                while ((line = errorLn.readLine()) != null) {
                    errorInputContent.add(line);
                }
            } catch (Exception e) {
                // ignored
            }

            int waitForCode = process.waitFor();
            if (future != null) {
                future.cancel(true);
            }
            log.info("waitForCode: {}", waitForCode);
            log.info("output: {}", normalInputContent);
            log.info("error: {}", errorInputContent);

            if (!normalInputContent.toString().toLowerCase().contains("Data transfer complete".toLowerCase())) {
                throw new ActionBizException("执行ITK脚本同步数据到Ariba失败：" +
                        (StringUtils.isEmpty(normalInputContent.toString()) ? errorInputContent : normalInputContent.toString()));
            }
        } catch (InterruptedException e) {
            // ignored
            log.error("执行ITK脚本中断", e);
            throw new ActionBizException("执行ITK脚本中断: " + e.getMessage());
        }
    }

    /**
     * 生成 cdv 文件的字节数据
     *
     * @param header csv 标题
     * @param rows   csv 数据
     * @return 字节数据
     * @throws IOException ioe
     */
    private byte[] writeCsvAndConvert2InputStream(List<String> header, List<List<String>> rows) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8), 1024)) {
            // 文件模版头
            writer.write("\"UTF-8\"");
            writer.newLine();
            // 写入文件表头
            writeCsvRow(writer, header);
            // 文件内容
            for (List<String> row : rows) {
                writeCsvRow(writer, row);
            }

            writer.flush();
            return outputStream.toByteArray();
        }
    }

    /**
     * 写 csv 数据行
     *
     * @param writer writer
     * @param row    行数据
     * @throws IOException ioe
     */
    private void writeCsvRow(BufferedWriter writer, List<String> row) throws IOException {
        for (int i = 0; i < row.size(); i++) {
            writer.write((row.get(i) == null ? "" :
                    // 如果字符串中有逗号，则要引号括起来
                    (row.get(i).contains(",") ? "\"" + row.get(i) + "\"" :
                            row.get(i))) + ((i == row.size() - 1) ? "" : ","));
        }
        writer.newLine();
    }

    private String getSupplierNameById(String supplierId) {
        RecordSet recordSet = new RecordSet();
        recordSet.execute("select " + supplierModeFieldName + " from " + supplierModeTableName + " where id = " + supplierId);
        return recordSet.next() ? recordSet.getString(1) : "";
    }

    public String getItkHomePath() {
        return itkHomePath;
    }

    public void setItkHomePath(String itkHomePath) {
        this.itkHomePath = itkHomePath;
    }

    public String getScriptWaitTimeSeconds() {
        return scriptWaitTimeSeconds;
    }

    public void setScriptWaitTimeSeconds(String scriptWaitTimeSeconds) {
        this.scriptWaitTimeSeconds = scriptWaitTimeSeconds;
    }

    public String getBankInfoNameFieldId() {
        return bankInfoNameFieldId;
    }

    public void setBankInfoNameFieldId(String bankInfoNameFieldId) {
        this.bankInfoNameFieldId = bankInfoNameFieldId;
    }

    public String getSupplierModeTableName() {
        return supplierModeTableName;
    }

    public void setSupplierModeTableName(String supplierModeTableName) {
        this.supplierModeTableName = supplierModeTableName;
    }

    public String getSupplierModeFieldName() {
        return supplierModeFieldName;
    }

    public void setSupplierModeFieldName(String supplierModeFieldName) {
        this.supplierModeFieldName = supplierModeFieldName;
    }
}
