package com.api.cube.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.browser.bean.*;
import com.api.browser.service.BrowserValueInfoService;
import com.api.browser.util.*;
import com.api.cube.bean.Header;
import com.api.cube.bean.RightMenu;
import com.api.cube.bean.TabPane;
import com.api.cube.constant.SearchConstant;
import com.api.cube.util.*;
import com.api.formmode.cache.CustomSearchComInfo;
import com.api.formmode.cache.ModeComInfo;
import com.api.formmode.cache.ModeExpandPageComInfo;
import com.api.formmode.cache.ModeFormComInfo;
import com.cloudstore.dev.api.bean.SplitPageBean;
import com.cloudstore.dev.api.bean.TimeMarker;
import com.cloudstore.dev.api.dao.Dao_Table;
import com.cloudstore.dev.api.dao.Dao_TableFactory;
import com.engine.common.entity.EncryptFieldEntity;
import com.engine.common.service.HrmCommonService;
import com.engine.common.service.impl.HrmCommonServiceImpl;
import com.engine.common.util.ParamUtil;
import com.engine.cube.biz.BrowserHelper;
import com.engine.cube.biz.DetachHelper;
import com.engine.cube.biz.PageExpandHelper;
import com.engine.cube.util.AddSeclevelUtil;
import com.engine.cube.util.ListUtil;
import com.engine.cube.util.PageExpandUtil;
import com.engine.encrypt.biz.EncryptFieldConfigComInfo;
import com.weaver.formmodel.util.DateHelper;
import com.weaver.formmodel.util.NumberHelper;
import com.weaver.formmodel.util.StringHelper;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;
import weaver.archives.ArchivesUtil;
import weaver.archives.util.ArchivesFModeUtil;
import weaver.conn.RecordSet;
import weaver.conn.constant.DBConstant;
import weaver.dateformat.DateTransformer;
import weaver.filter.XssUtil;
import weaver.formmode.FormModeConfig;
import weaver.formmode.cache.StringCacheMap;
import weaver.formmode.customjavacode.CustomJavaCodeRun;
import weaver.formmode.dao.FormInfoDao;
import weaver.formmode.data.ModeDataIDUpdateSingle;
import weaver.formmode.excel.ExpExcelUtil;
import weaver.formmode.excel.ListExceloutTask;
import weaver.formmode.expcard.service.ExpCardTask;
import weaver.formmode.log.FormmodeLog;
import weaver.formmode.search.CustomSearchBatchEditUtil;
import weaver.formmode.search.FormModeRightInfo;
import weaver.formmode.search.FormModeTransMethod;
import weaver.formmode.search.editplugin.AbstractPluginElement;
import weaver.formmode.search.editplugin.PluginElementClassName;
import weaver.formmode.service.CommonConstant;
import weaver.formmode.service.CustomSearchService;
import weaver.formmode.service.ModelInfoService;
import weaver.formmode.setup.ExpandBaseRightInfo;
import weaver.formmode.setup.ModeRightInfo;
import weaver.formmode.setup.ModeSetUtil;
import weaver.formmode.tree.CustomTreeData;
import weaver.formmode.tree.HrmTreeDataUtil;
import weaver.formmode.view.ModeShareManager;
import weaver.formmode.virtualform.VirtualFormHandler;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.hrm.company.DepartmentComInfo;
import weaver.hrm.company.SubCompanyComInfo;
import weaver.hrm.resource.ResourceComInfo;
import weaver.servicefiles.DataSourceXML;
import weaver.systeminfo.SystemEnv;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.api.cube.constant.SearchConstant.*;

public class CubeSearchService extends BaseBean {
    private String isTreeType;//查询树显示转换
    private String dataSourceDBType;
    private String dataSourceDBTypeOrgin = "";
    private int customId;
    private int formId;
    private int modeId;
    private boolean isVirtualForm;
    private String tableName;
    private String detailTable;
    private String dataSource;
    private String primaryKey;
    private boolean noRightList;
    private int viewType;
    private User user;
    private int pageNum;
    private int isCustom;
    private StringBuffer jsBody = new StringBuffer();
    private String isbill = "1";
    private String openType;
    private String sliderPercentage;
    private String disquerycheck;
    private String groupName;
    private String groupFieldid;//分组字段ID
    private String selectedKey;
    private String groupNameAlias;
    private String sqlPrimaryKey;
    private String defaulSql;
    private String searchconditiontype;
    private String javafilename;
    private int viewtype = 0;
    private boolean isRight = false;
    private boolean isDel = false;
    private boolean isBatchEdit = false;//批量修改权限
    private boolean isRead = false;//查看权限
    private boolean isEdit = false;//批量修改状态，当前是否批量修改查询
    private String treenodeid;
    private String type;
    private String javafileaddress;
    private Set<String> customButtonParameter = new HashSet<String>();  //自定义按钮js函数参数相关，参数列若未勾选为显示字段则放在此属性中
    private String displayType;
    private String grouptype;     //用于分组链接
    private boolean isChexkAdvanedDate = true;
    private boolean isFilter = false;//是否有数据筛选的按钮
    private static Map<String, Long> SecondaryMap;
    /**
     * 是否是批量导入数据还原(没有权限也是false)
     */
    private boolean isBackUpData = false;
    private String suffix = "_bak";
    /**
     * 批量导入数据类型是否是覆盖(不是覆盖则是还原)
     */
    private boolean isCoverType = false;
    private String backuplogid = "";
    /**
     * 备份表的字段
     */
    private List<String> backupFields = new ArrayList<String>();
    /**
     * 权限预判断,当前用户是否拥有所有数据权限(如果有,就不需要关联权限明细表查询)
     */
    private boolean hasUserRight = false;
    //标识虚拟表单是否在sqlfrom中拼接了权限
    private boolean virtualHasRightSql = false;

    public void setNoEscape(boolean noEscape) {
        this.noEscape = noEscape;
    }

    private boolean noEscape = false; // 默认需要转义

    private String sort1141500;//需求库排序字段,qc1141500

    public void setGrouptype(String grouptype) {
        this.grouptype = grouptype;
    }

    public boolean checkSearchRight(HttpServletRequest request, HttpServletResponse response) {
        this.viewtype = Util.getIntValue(request.getParameter("viewtype"), 0);
        this.customId = Util.getIntValue(request.getParameter("customid"));
        RecordSet rs = new RecordSet();
        User user = HrmUserVarify.getUser(request, response);
        FormModeRightInfo formModeRightInfo = new FormModeRightInfo();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        this.modeId = Util.getIntValue(customSearchComInfo.getModeId("" + customId));
        String backuptype = Util.null2String(request.getParameter("backuptype"));
        if ("backupdata".equals(backuptype)) {//如果是批量导入数据还原
            String backuplogid = Util.null2String(request.getParameter("backuplogid"));
            this.setBackUpRight(user, this.customId + "", backuplogid);
        }
        //============================================权限判断====================================
        if (viewtype == 3) {//监控权限判断
            boolean isHavepageRight = formModeRightInfo.isHavePageRigth(customId, 4);
            if (isHavepageRight) {
                formModeRightInfo.setUser(user);
                isRight = formModeRightInfo.checkUserRight(customId, 4);
            } else {  //如果自定义查询页面无监控权限，则检查全局监控权限
                modeRightInfo.setModeId(modeId);
                modeRightInfo.setType(viewtype);
                modeRightInfo.setUser(user);

                isRight = modeRightInfo.checkUserRight(viewtype);
            }
            modeRightInfo.setModeId(modeId);
            modeRightInfo.setType(viewtype);
            modeRightInfo.setUser(user);
            if (modeRightInfo.checkUserRight(viewtype)) {
                isDel = true;
            }
        } else {
            isDel = true;
            //批量修改权限
            rs.executeQuery("select * from mode_searchPageshareinfo where righttype=2 and pageid = ?", customId);
            if (rs.next()) {
                formModeRightInfo.setUser(user);
                isBatchEdit = formModeRightInfo.checkUserRight(customId, 2);
                isRead = formModeRightInfo.checkUserRight(customId, 1);
            }
            if (isBatchEdit && isRead) {
                isRight = true;
            } else {
                //自定义页面查看权限
                rs.executeQuery("select * from mode_searchPageshareinfo where righttype=1 and pageid = ? ", customId);
                if (rs.next()) {
                    formModeRightInfo.setUser(user);
                    isRight = formModeRightInfo.checkUserRight(customId, 1);
                } else {  //没有设置任何查看权限数据，则认为有权限查看
                    isRight = false;
                }
            }
        }
        if (isBackUpData) {
            return true;
        }
        return isRight;
    }

    /**
     * 检查是否拥有批量编辑权限
     *
     * @param request
     * @param response
     * @return
     */
    public boolean checkBatchEditRight(HttpServletRequest request, HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        this.viewtype = Util.getIntValue(request.getParameter("viewtype"), 0);
        this.customId = Util.getIntValue(request.getParameter("customid"));
        RecordSet rs = new RecordSet();
        boolean isRight = false;
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        int modeId = Util.getIntValue(customSearchComInfo.getModeId(customId + ""), 0);
        String formId = customSearchComInfo.getFormId(customId + "");
        boolean isVirtualForm = "1".equals(new ModeFormComInfo().getIsVirtualForm(formId));
        String norightlist = customSearchComInfo.getNoRightList(customId + "");
        if (!"1".equals(norightlist) && !isVirtualForm) {
            rs.execute("select * from mode_searchPageshareinfo where righttype=2 and pageid = " + customId);
            if (rs.next()) {
                FormModeRightInfo FormModeRightInfo = new FormModeRightInfo();
                FormModeRightInfo.setUser(user);
                isRight = FormModeRightInfo.checkUserRight(customId, 2);
            }
        }
        return isRight;
    }

    public Map<String, Object> getSearchBase(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        ModeComInfo modeComInfo = new ModeComInfo();
        this.treenodeid = Util.null2String(request.getParameter("treenodeid"));
        this.grouptype = Util.null2String(request.getParameter("grouptype"));
        this.type = Util.null2String(request.getParameter("type"));
        String customId = "" + this.customId;
        String modeId = Util.null2String(customSearchComInfo.getModeId(customId));
        String formId = Util.null2String(customSearchComInfo.getFormId(customId));

        boolean isVirtualForm = VirtualFormHandler.isVirtualForm(formId);    //是否是虚拟表单
        Map<String, Object> vFormInfo = new HashMap<String, Object>();
        String vdatasource = "";
        String vprimarykey = "id";
        if (isVirtualForm) {
            vFormInfo = VirtualFormHandler.getVFormInfo(formId);
            vdatasource = Util.null2String(vFormInfo.get("vdatasource"));    //虚拟表单数据源
            vprimarykey = Util.null2String(vFormInfo.get("vprimarykey"));    //虚拟表单主键列名称
        }
        String vformtype = Util.null2String(vFormInfo.get("vformtype"));
        String vsql = Util.null2String(vFormInfo.get("vsql"));
        if ("2".equals(vformtype)) {
            if ("".equals(vdatasource) || DataSourceXML.SYS_LOCAL_POOLNAME.equals(vdatasource) || "local".equals(vdatasource)) {
                RecordSet rs = new RecordSet();
                rs.executeQuery("select  tablename,isencrypt,desensitization from enc_field_config_info");
                boolean encrypt = false;
                while (rs.next()) {
                    String tablename = Util.null2String(rs.getString("tablename"));
                    String isencrypt = Util.null2String(rs.getString("isencrypt"));
                    String desensitization = Util.null2String(rs.getString("desensitization"));
                    encrypt = vsql.indexOf(tablename) > 0 && ("1".equals(isencrypt) || "1".equals(desensitization));
                    if (encrypt) break;
                }
                if (encrypt) {
                    result.put("encrypt", true);
                    result.put("error", SystemEnv.getHtmlLabelName(530230, user.getLanguage()));
                    return result;
                }
            }
        }

        String tabid = Util.null2String(request.getParameter("tabid"));
        String isfromTab = Util.null2String(request.getParameter("isfromTab"));
        String isfromRight = Util.null2String(request.getParameter("isfromRight"));
        int pageSize = Util.getIntValue(customSearchComInfo.getPageNumber(customId));
        boolean isShowCondition = "1".equalsIgnoreCase(customSearchComInfo.getIsShowQueryCondition(customId));
        boolean hasQuickSearch = !"1".equalsIgnoreCase(customSearchComInfo.getDisQuickSearch(customId));
        String disquerycheck = Util.null2String(customSearchComInfo.getDisquerycheck(customId));
        CubeFieldService cubeFieldService = new CubeFieldService();
        List<String> keySearchFields = cubeFieldService.getKeySearchFields(customId, user.getLanguage());
        Map<String, String> keyFieldidInfo = new HashMap<String, String>();
        if (keySearchFields.size() == 1) {//如果关键字只设置一个
            keyFieldidInfo = cubeFieldService.getKeyFieldid(customId);
        }
        boolean hasKeySearch = keySearchFields.size() > 0;
        String customName = Util.null2String(customSearchComInfo.getCustomName(customId));
        if (customName.contains("~`~`")) {
            customName = Util.formatMultiLang(customName, Util.null2String(user.getLanguage()));
        }
        String customDesc = Util.null2String(customSearchComInfo.getCustomDesc(customId));
        this.noRightList = "1".equals(customSearchComInfo.getNoRightList(customId));
        boolean defaultshare = "1".equals(Util.null2String(modeComInfo.getDefaultshared(modeId)));
        boolean nondefaultshared = "1".equals(Util.null2String(modeComInfo.getNondefaultshared(modeId)));
        boolean secondPassword = "1".equals(Util.null2String(customSearchComInfo.getSecondPassword(customId)));
        String datashowtypeValue = Util.null2o(customSearchComInfo.getDatashowtype(customId));
        String isTreeSearch = Util.null2String(customSearchComInfo.getTreeSearch(customId));
        String datashowtype = "";
        if (datashowtypeValue.equals("1")) {
            datashowtype = "imgType";
        } else if (datashowtypeValue.equals("2")) {
            datashowtype = "excel";
            String excelFixed = Util.null2String(customSearchComInfo.getExcelFixed(customId));
            result.put("excelFixed", excelFixed);
        } else if (datashowtypeValue.equals("4") || isTreeSearch.equals("1")) {
            datashowtype = "treeType";
        } else if (datashowtypeValue.equals("5")) {
            datashowtype = "verticalType";
        }
        boolean isquicksearch = false;//快捷搜索添加是否开启
        String isshowtype = "";//快捷搜索展示方式
        boolean ishidename = false;//快捷搜索是否显示名称
        String sql = "select isquicksearch,isshowtype,ishidename from mode_quicksearch_setting where customid=?";
        RecordSet rs = new RecordSet();
        JSONObject quicksearch = new JSONObject();
        rs.executeQuery(sql, customId);
        if (rs.next()) {
            isquicksearch = !isBackUpData && "1".equals(rs.getString("isquicksearch"));
            isshowtype = Util.null2s(rs.getString("isshowtype"), "1");
            ishidename = "1".equals(rs.getString("ishidename"));
        }
        if (isquicksearch) {//如果开启了快捷搜索,则查询其信息
            JSONArray quickinfo = GetQuickSearchInfo(customId, user, request);
            quicksearch.put("quicksearchinfo", quickinfo);
        }
        quicksearch.put("isquicksearch", isquicksearch);
        quicksearch.put("isshowtype", isshowtype);
        quicksearch.put("ishidename", ishidename);

        // 处理页面扩展关联字段不回显
        if (isfromTab.equals("1")) {
            result.put("isfromTab", isfromTab);
        }
        if (isfromRight.equals("1")) {
            result.put("isfromRight", isfromRight);
        }

        if ((StringUtils.isNotBlank(isfromTab) || StringUtils.isNotBlank(isfromRight)) && StringUtils.isNotBlank(tabid)) {
            rs.executeQuery("select modeid from mode_pagerelatefield where  pageexpandid = ?", tabid);
            String expendModeId = "";
            while (rs.next()) {
                // tab来源模块
                expendModeId = Util.null2String(rs.getString("modeid"));
            }
            RecordSet tabRs = new RecordSet();
            String expendSql = "select hreffieldname from mode_pagerelatefielddetail where mainid in (select id from mode_pagerelatefield where modeid = ? and pageexpandid = ?)";
            tabRs.executeQuery(expendSql, expendModeId, tabid);
            List<String> herfFieldNames = new ArrayList<>();
            while (tabRs.next()) {
                herfFieldNames.add(Util.null2String(tabRs.getString("hreffieldname")));
            }

            String targetModelFieldSql = "select id,fieldname from workflow_billfield where billid = (select formid from modeinfo where id = ?) and (detailtable is null or detailtable='')";
            List<String> linkFields = new ArrayList<>();
            tabRs.executeQuery(targetModelFieldSql, modeId);
            while (tabRs.next()) {
                String fieldId = Util.null2String(tabRs.getString("id"));
                String fieldName = Util.null2String(tabRs.getString("fieldname"));
                herfFieldNames.forEach(name -> {
                    if (name.equals(fieldName)) {
                        linkFields.add(fieldId);
                    }
                });

            }
            quicksearch.put("linkFields", linkFields);
        }


        boolean isCharts = false;//快捷搜索添加是否开启
        sql = "SELECT count(mainid) count from mode_chartsbase base left join mode_chartsdetail d on d.mainid=base.id where customid=?";
        rs.executeQuery(sql, customId);
        if (rs.next()) {
            isCharts = rs.getInt("count") > 0 ? true : false;
        }


        Header header = new Header();
        sql = "select t1.* from mode_customsearch t,modetreefield t1 where t.id=? and t1.id=t.appid";
        rs.executeQuery(sql, customId);
        String iconColor = "";
        String iconBgcolor = "";
        String icon = "";
        if (rs.next()) {
            iconColor = Util.null2String(rs.getString("iconColor"));
            iconBgcolor = Util.null2String(rs.getString("iconBg"));
            icon = Util.null2String(rs.getString("icon"));
        }
        if (!iconColor.isEmpty()) {
            header.setIconColor(iconColor);
        }
        if (!iconBgcolor.isEmpty()) {
            header.setIconBgcolor(iconBgcolor);
        }
        if (!icon.isEmpty()) {
            header.setIcon(icon);
        }

        String dataBoard = "";// 数据图表看板
        boolean isImgType = false;// 数据列表
        sql = "select t.dataBoard,t.datashowtype from mode_customsearch t where t.id=?";
        rs.executeQuery(sql, customId);
        if (rs.next()) {
            dataBoard = Util.null2String(rs.getString("dataBoard"));
            if ("1".equals(Util.null2String(rs.getString("datashowtype")))) {
                isImgType = true;
            }
        }

        boolean isfrontmultlang = false;
        if (!"".equals(modeId) && Util.isEnableMultiLang()) {
            sql = "select isfrontmultlang from modeinfo where id=? ";
            rs.executeQuery(sql, modeId);
            if (rs.next()) {
                isfrontmultlang = "1".equals(rs.getString("isfrontmultlang"));
            }
        }
        if (!"".equals(modeId)) {
            rs.executeQuery("select dsdesignerid from modehtmllayout where modeid=? and type=4 and isdefault=1", modeId);
            if (rs.next()) {
                if (!Util.null2String(rs.getString("dsdesignerid")).equals("")) {
                    String printEdcUrl = weaver.general.GCONST.getContextPath() + "/spa/edc/static4engine/report/engine.html#/edcreportengine/sheetView?id=" + Util.null2String(rs.getString("dsdesignerid"));
                    result.put("printEdcUrl", printEdcUrl);
                }
            }
        }
        boolean isSuspend = false;
        boolean isShowIcon = false;
        int showButtomNum = 5;
        if (!"".equals(customId)) {
            sql = "select isSuspend,isShowIcon,showButtomNum from mode_custombutton_setting where customid=? ";
            rs.executeQuery(sql, customId);
            if (rs.next()) {
                isSuspend = "1".equals(rs.getString("isSuspend"));
                isShowIcon = "1".equals(rs.getString("isShowIcon"));
                showButtomNum = Util.getIntValue(Util.null2String(rs.getString("showButtomNum")), 5);
            }
        }

        header.setName(customName);
        header.setTitle(customDesc);
        header.setTabs(this.getSearchGroupTabs(customId, user));
        header.setSelectedKey(selectedKey);
        result.put("quickSearch", quicksearch);
        result.put("isCharts", isCharts);
        result.put("customId", customId);
        result.put("modeId", modeId);
        result.put("disquerycheck", disquerycheck);
        result.put("isfrontmultlang", isfrontmultlang);
        result.put("formId", formId);
        result.put("isShowCondition", isShowCondition);
        result.put("hasQuickSearch", hasQuickSearch);
        result.put("defaultshare", defaultshare);
        result.put("nondefaultshared", nondefaultshared);
        result.put("hasKeySearch", hasKeySearch);
        result.put("keySearchFields", keySearchFields);
        result.put("keyFieldidInfo", keyFieldidInfo);
        result.put("top", header);
        result.put("isImgType", isImgType);
        result.put("treeFieldStr", this.getTreeFieldStr(request, response));
        result.put("pageSize", pageSize);
        if (!StringHelper.isEmpty(tabid)) {
            result.put("isInnerTab", this.isInnerTab(tabid));
        }
        result.put("secondPassword", secondPassword);
        result.put("isSuspend", isSuspend);
        result.put("isShowIcon", isShowIcon);
        result.put("showButtomNum", showButtomNum);
        result.put("datashowtype", datashowtype);
        result.put("dataBoard", dataBoard.isEmpty() ? null : JSONObject.parse(dataBoard));
        double originalTableWidth = getOriginalTableWidth(customId, user.getUID());
//        double originalTableWidth=0;
//        rs.executeQuery("select colwidth from mode_CustomDspField where customid=? and isshow=1", customId);
//        while(rs.next()){
//        	double colwidth=rs.getDouble("colwidth");
//        	if(colwidth<=0){
//        		colwidth=5;
//        	}
//        	originalTableWidth+=colwidth;
//        }
//        int num = getIsCustomButtonShowList();
//        if(num>0){
//            int colnum = num>5?5:num;
//            originalTableWidth+=colnum;
//        }
        result.put("originalTableWidth", originalTableWidth + "%");
        result.put("isBackUpData", this.isBackUpData);
        result.put("isCoverType", this.isCoverType);

        //获取行点击事件信息
        Map<String, Map<String, Object>> rowClick = new HashMap<String, Map<String, Object>>();
        if (datashowtypeValue.equals("0") || datashowtypeValue.equals("")) {
            rs.executeQuery("select id,opentype,width,hreftype,hrefid,hreftarget from mode_searchRowClick where id in (select max(id) from mode_searchRowClick  where customid=? group by opentype)", customId);//同一类型的如果配置了多个则只取最新的一个
            while (rs.next()) {
                String opentype = Util.null2String(rs.getString("opentype"));
                String width = Util.null2String(rs.getString("width"));
                String hreftype = Util.null2String(rs.getString("hreftype"));
                String id = Util.null2String(rs.getString("id"));
                String hreftarget = Util.null2String(rs.getString("hreftarget"));
                rowClick.put(opentype, BrowserHelper.constructMap("id", id, "hreftype", hreftype, "hreftarget", hreftarget, "width", width));
            }
        }
        result.put("rowClick", rowClick);
        return result;
    }

    /**
     * 获取快捷搜索条件
     *
     * @param customid
     * @return
     */
    public JSONArray GetQuickSearchInfo(String customid, User user, HttpServletRequest request) {
        JSONArray datas = new JSONArray();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        String ss = request.getQueryString();
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        String defSql = Util.toScreenToEdit(customSearchComInfo.getDefaultSql(customid), user.getLanguage());

        Map<String, List<JSONObject>> groupMap = new HashMap<String, List<JSONObject>>();

        // 表单字段
        String sql = "SELECT ID,fieldid,customname,fieldhtmltype,typeTemp,fielddbtype,fieldname,orderid,groupid,showmodel FROM (";
        sql += "select quick.ID,bill.id fieldid,quick.customname,bill.fieldhtmltype,bill.type typeTemp,bill.fielddbtype,fieldname,orderid,groupid,showmodel from mode_quicksearch_condition quick left join workflow_billfield bill on bill.id=quick.fieldid where customid=? AND quick.fieldid NOT IN(-1,-2) ";

        List args = new ArrayList();
        args.add(customid);
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        String formId = customSearchComInfo.getFormId(customid);
        boolean isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        if (!isVirtualForm) {
            // 加入创建日期
            sql += "UNION ALL ";
            sql += "SELECT id,1 fieldid,customname,'3' fieldhtmltype,2 typeTemp, 'char(10)' fielddbtype,'modedatacreatedate' fieldname,orderid,groupid,showmodel ";
            sql += "FROM mode_quicksearch_condition WHERE customid=? AND fieldid =-1 ";

            // 加入创建人
            sql += "UNION ALL ";
            //sql += "SELECT id,2 fieldid,customname,'3' fieldhtmltype,1 TYPE, 'int' fielddbtype,'modedatacreater' fieldname,orderid   ";
            sql += "SELECT id,2 fieldid,customname,'3' fieldhtmltype,1 typeTemp, 'int' fielddbtype,'modedatacreater' fieldname,orderid,groupid,showmodel  ";
            sql += "FROM mode_quicksearch_condition WHERE customid=? AND fieldid =-2 ";

            //添加最后修改人
            sql += "UNION ALL ";
            sql += "SELECT id,7 fieldid,customname,'3' fieldhtmltype,1 typeTemp, 'int' fielddbtype,'modedatacreater' fieldname,orderid,groupid,showmodel ";
            sql += "FROM mode_quicksearch_condition WHERE customid=? AND fieldid =-7 ";

            //添加最后修改日期时间
            sql += "UNION ALL ";
            sql += "SELECT id,8 fieldid,customname,'3' fieldhtmltype,290 typeTemp, 'char(10)' fielddbtype,'modedatacreatedate' fieldname,orderid,groupid,showmodel  ";
            sql += "FROM mode_quicksearch_condition WHERE customid=? AND fieldid =-8 ";

            //添加密级字段int
            sql += "UNION ALL ";
            sql += "SELECT id,9 fieldid,customname,'5' fieldhtmltype,2 typeTemp, 'int' fielddbtype,'seclevel' fieldname,orderid,groupid,showmodel  ";
            sql += "FROM mode_quicksearch_condition WHERE customid=? AND fieldid =-9 ";

            args.add(customid);
            args.add(customid);
            args.add(customid);
            args.add(customid);
            args.add(customid);
        }

        // 按照字段排序
        sql += ") a order by groupid asc,orderid asc,id asc";

        rs.executeQuery(sql, args);
        while (rs.next()) {
            int viewAttr = 2;
            JSONObject json = new JSONObject();
            String fieldhtmltype = Util.null2String(rs.getString("fieldhtmltype"));
            String fieldtype = Util.null2String(rs.getString("typeTemp"));
            String id = Util.null2String(rs.getString("id"));
            String fieldnames = Util.null2String(rs.getString("fieldname"));
            String fieldid = Util.null2String(rs.getString("fieldid"));
            String groupid = Util.null2String(rs.getString("groupid"));
            float orderid = Util.getFloatValue(Util.null2String(rs.getString("orderid")), 0);
            String showmodel = Util.null2String(rs.getString("showmodel"));
            String querysql = "select t.* from mode_CustomDspField t where t.customid=? and t.fieldid=?";
            String conditiontransition = "";
            String searchparavalue = "";
            String searchparavalue1 = "";
            String conditionValue = "";
            String conditionValue1 = "";
            String conditionValue2 = "";
            String fieldname_seclevel = Util.null2String(rs.getString("fieldname"));//用于验证是否是密级字段；
            String showtype = Util.null2o(rs.getString("showtype"));//显示模式主要 针对浏览框 0：浏览框模式 1：下拉拉框模式
            rs1.executeQuery(querysql, customid, "1".equals(fieldid) ? "-1" : "2".equals(fieldid) ? "-2" : "7".equals(fieldid) ? "-7" : "8".equals(fieldid) ? "-8" : fieldid);
            int count = 0;
            String requiredCon = "";
            if (rs1.next()) {
                count++;
                conditiontransition = Util.null2String(rs1.getString("conditiontransition"));
                requiredCon = Util.null2String(rs1.getString("requiredCon"));
                if ("1".equals(requiredCon)) {
                    viewAttr = 3;
                }
                conditionValue = Util.null2String(rs1.getString("conditionValue"));
                conditionValue1 = Util.null2String(rs1.getString("conditionValue1"));
                conditionValue2 = Util.null2String(rs1.getString("conditionValue2"));
                String searchparaname = Util.null2String(rs1.getString("searchparaname"));
                String searchparaname1 = Util.null2String(rs1.getString("searchparaname1"));
                if (!StringHelper.isEmpty(searchparaname)) {
                    searchparavalue = replaceParams(Util.null2String(request.getParameter(searchparaname)), user, false);
                }

                if (!StringHelper.isEmpty(searchparaname1)) {
                    searchparavalue1 = replaceParams(Util.null2String(request.getParameter(searchparaname1)), user, true);
                }
                if ("".equals(searchparavalue)) {
                    if ("3".equals(fieldhtmltype) && "1".equals(fieldtype)) {   //人力资源
                        if (!"".equals(conditionValue1) && !"null".equals(conditionValue1)) {
                            searchparavalue = conditionValue1;
                        } else if (conditiontransition.equalsIgnoreCase("1") && "".equals(conditionValue1) && !"-5".equals(conditionValue)) {
                            searchparavalue = conditionValue;
                        } else {
                            searchparavalue = conditionValue;
                        }
                    } else if ("3".equals(fieldhtmltype) && ("2".equals(fieldtype) || "19".equals(fieldtype) || "290".equals(fieldtype))) {
                        if ("3".equals(fieldhtmltype) && "19".equals(fieldtype)) {   //时间
                            if ("".equals(conditionValue) && "".equals(conditionValue1)) {
                                searchparavalue = searchparavalue;
                            } else {
                                searchparavalue = conditionValue + "," + conditionValue1;
                            }
                        } else if ("3".equals(fieldhtmltype) && "2".equals(fieldtype)) {   //日期
                            if ("".equals(conditionValue) && "".equals(conditionValue1)) {
                                searchparavalue = searchparavalue;
                            } else {
                                searchparavalue = conditionValue + "," + conditionValue1;
                            }
                        } else if (!"".equals(conditionValue1) && !"null".equals(conditionValue1)) {
                            if (!"".equals(conditionValue1) && !"null".equals(conditionValue1)) {
                                searchparavalue = conditionValue1 + "," + conditionValue2;      //处理时间，日期回显
//                                searchparavalue1 = conditionValue1;
                            } else {
                                searchparavalue = conditionValue;
                            }
                        }
                    } else {
                        String acValue = conditionValue;
                        if (!"".equals(conditionValue1) && !"null".equals(conditionValue1)) {
                            if (!"".equals(conditionValue2) && !"null".equals(conditionValue2)) {
                                conditionValue = conditionValue + "," + conditionValue1 + "," + conditionValue2;
                            } else {
                                conditionValue = conditionValue + "," + conditionValue1;
                            }
                        }
                        if (!"".equals(conditionValue)) {
                            searchparavalue = conditionValue;
                        }
                        if ("1".equals(fieldhtmltype) && !"1".equals(fieldtype)) {
                            searchparavalue = acValue;
                        }
                    }
                }
            }
            if (conditiontransition.equalsIgnoreCase("1") && fieldhtmltype.equals("3")) {
                fieldtype = convertSingleBrowserTypeToMulti_All(fieldtype);
            }
            String fielddbtype = Util.null2String(rs.getString("fielddbtype"));
            if ("".equals(fieldid)) {
                continue;
            }

            String fieldname = "con_" + (Util.getIntValue(fieldid) > 0 ? fieldid : "_" + (-Util.getIntValue(fieldid)));
            if (StringHelper.isEmpty(searchparavalue) && StringHelper.isEmpty(searchparavalue1)) {//con_fielid格式作为查询条件，解析到查询条件上
                searchparavalue = Util.null2String(request.getParameter(fieldname));
            }


            String customname = Util.null2String(rs.getString("customname"));
            customname = Util.formatMultiLang(customname, user.getLanguage() + "");
            if ("1".equals(fieldhtmltype)) {
                if ("1".equals(fieldtype)) {//单行文本
                    if (!searchparavalue.isEmpty()) {
                        json.put("value", searchparavalue);
                    }
                } else {//整数或浮点数
                    sql = "select id,customname,minnum,maxnum from mode_quicksearch_detail where cid=? order by orderid asc,id asc";
                    rs1.executeQuery(sql, id);
                    JSONArray arr = new JSONArray();
                    JSONObject obj1 = new JSONObject();
                    if ("1".equals(requiredCon)) {
                        obj1.put("key", "");
                    } else {
                        obj1.put("key", "-1");
                    }
                    obj1.put("showname", "");
                    arr.add(obj1);
                    boolean flg = false;
                    Map<String, String> map = new HashMap<String, String>();
                    while (rs1.next()) {
                        String minnum = Util.null2String(rs1.getString("minnum"));
                        String maxnum = Util.null2String(rs1.getString("maxnum"));
                        String fieldValue = minnum + "," + maxnum;
                        map.put(rs1.getString("id"), fieldValue);
                        boolean flgs = false;
                        if (!"".equals(minnum) && !searchparavalue.isEmpty()) {
                            float num = Util.getFloatValue(minnum);
                            float num2 = Util.getFloatValue(maxnum);
                            if (Util.getFloatValue(searchparavalue) == Util.getFloatValue("0")) {
                                if (num <= Util.getFloatValue(searchparavalue) && num2 >= Util.getFloatValue(searchparavalue) && !flg) {
                                    flg = true;
                                    flgs = true;
                                    //json.put("value", rs1.getString("id"));
                                }
                            } else {
                                if (num <= Util.getFloatValue(searchparavalue) && num2 >= Util.getFloatValue(searchparavalue) && !flg) {
                                    flg = true;
                                    flgs = true;
                                    //json.put("value", rs1.getString("id"));
                                }
                            }
                        }
                        JSONObject obj2 = new JSONObject();
                        obj2.put("key", rs1.getString("id"));
                        obj2.put("selected", flgs);
                        obj2.put("showname", Util.formatMultiLang(rs1.getString("customname"), user.getLanguage() + ""));
                        arr.add(obj2);
                    }
                    json.put("floatValue", map);
                    json.put("options", arr);
                }
            } else if ("2".equals(fieldhtmltype)) {//多文本
                if (!searchparavalue.isEmpty()) {
                    json.put("value", searchparavalue);
                }
            } else if ("5".equals(fieldhtmltype) && "1".equals(fieldtype)) {//选择框
                RecordSet record = new RecordSet();
                StringBuffer sql1 = new StringBuffer();
                Integer fieldlabel = 0;
                String parentDomkey = "";
                Integer pubchilchoiceId = 0;

                sql1.append(" select a.id, b.fieldlabel, b.pubchilchoiceId     ");
                sql1.append("   from mode_customdspfield a, workflow_billfield b                         ");
                sql1.append("  where a.fieldid = b.id and b.id=?                                         ");
                sql1.append("    and a.customId = ?                                                      ");
                record.executeQuery(sql1.toString(), fieldid, customId);
                while (record.next()) {
                    fieldlabel = record.getInt("fieldlabel");
                    pubchilchoiceId = record.getInt("pubchilchoiceId");
                }
                if (pubchilchoiceId > 0) {
                    parentDomkey = "con_" + pubchilchoiceId;
                } else {//#866910
                    String dlxzksql = "select t.id from workflow_billfield t where t.childfieldid=?";
                    rs1.executeQuery(dlxzksql, fieldid);
                    if (rs1.next()) {
                        String parentfieldid = Util.null2String(rs1.getString(1));
                        if (!parentfieldid.isEmpty()) {
                            parentDomkey = "con_" + parentfieldid;
                        }
                    }
                }
                ConditionFactory conditionFactory = new ConditionFactory(user);
                CubeFieldService cfs = new CubeFieldService();
                List<SearchConditionOption> options = new ArrayList<SearchConditionOption>();
                SearchConditionItem conditionJson = new SearchConditionItem();
                if (!"seclevel".equals(fieldname_seclevel)) {
                    conditionJson = conditionFactory.createCondition(ConditionType.SELECT, fieldlabel, fieldname + "," + parentDomkey, cfs.getWorkflowSelectItems("" + fieldid, user.getLanguage(), true));
                    //options = cfs.getWorkflowSelectItems(""+fieldid, user.getLanguage(),true);
                    options = conditionJson.getOptions();
                    if ("1".equals(conditiontransition) && options.size() > 0 && "".equals(options.get(0).getKey())) {//选择框多选时去除空选项
                        options.remove(0);
                    }
                } else {
                    //密级修改为从人力资源那么获取
                    Map<String, Object> otherParam = new HashMap<>();
                    AddSeclevelUtil addSeclevelUtil = new AddSeclevelUtil();
                    otherParam.put("fieldlabel", fieldlabel);
                    otherParam.put("fieldname", fieldname);
                    otherParam.put("parentDomkey", parentDomkey);
                    conditionJson = addSeclevelUtil.getSecLevelOptions(conditionFactory, user, otherParam);

//                    HrmClassifiedProtectionBiz hrmClassifiedProtectionBiz = new HrmClassifiedProtectionBiz();
//                    SearchConditionOption searchConditionOption = new SearchConditionOption();
//                    searchConditionOption.setKey("");
//                    searchConditionOption.setShowname("");
//                    searchConditionOption.setChilditemid(Util.splitString("", ","));
//                    options.add(searchConditionOption);
//                    //密级调用人力资源的 资源密级下拉选项
//                    List<SearchConditionOption> comOptions = hrmClassifiedProtectionBiz.getResourceOptionListByUser(user);
//                    if (null != comOptions && comOptions.size() > 0) {
//                        for (SearchConditionOption searchConditionOptioninner : comOptions) {
//                            SearchConditionOption searchConditionOption_inner = new SearchConditionOption();
//                            String selectvalue = searchConditionOptioninner.getKey();
//                            String selectname = searchConditionOptioninner.getShowname();
//                            searchConditionOption_inner.setKey(selectvalue);
//                            searchConditionOption_inner.setShowname(selectname);
//                            searchConditionOption_inner.setChilditemid(Util.splitString("", ","));
//                            options.add(searchConditionOption_inner);
//
//                        }
//                        conditionJson = conditionFactory.createCondition(ConditionType.SELECT, fieldlabel, fieldname + "," + parentDomkey, options);
//                    }
                    options = conditionJson.getOptions();
                    if ("1".equals(conditiontransition) && options.size() > 0 && "".equals(options.get(0).getKey())) {//选择框多选时去除空选项
                        options.remove(0);
                    }
                }


                json.put("options", options);
                json.put("parentDomkey", conditionJson.getParentDomkey());
                json.put("domkey", conditionJson.getDomkey());
                if (!searchparavalue.isEmpty()) {
                    json.put("value", searchparavalue);
                }
                if (conditiontransition.equalsIgnoreCase("1")) {
                    json.put("detailtype", "2");
                    json.put("multiple", true);
                } else {
                    json.put("detailtype", "");
                }
                json.put("fieldid", fieldid);
                json.put("customname", customname);
            } else if ("5".equals(fieldhtmltype) && "2".equals(fieldtype)) {//多选
                RecordSet record = new RecordSet();
                StringBuffer sql1 = new StringBuffer();
                Integer fieldlabel = 0;
                String parentDomkey = "";
                Integer pubchilchoiceId = 0;

                sql1.append(" select a.id, b.fieldlabel,b.pubchilchoiceId   ");
                sql1.append("   from mode_customdspfield a, workflow_billfield b                         ");
                sql1.append("  where a.fieldid = b.id and b.id=?                                         ");
                sql1.append("    and a.customId = ?                                                      ");
                record.executeQuery(sql1.toString(), fieldid, customId);
                while (record.next()) {
                    fieldlabel = record.getInt("fieldlabel");
                    pubchilchoiceId = record.getInt("pubchilchoiceId");
                }
                if (pubchilchoiceId > 0) {
                    parentDomkey = "con_" + pubchilchoiceId;
                } else {//#866910
                    String dlxzksql = "select t.id from workflow_billfield t where t.childfieldid=?";
                    rs1.executeQuery(dlxzksql, fieldid);
                    if (rs1.next()) {
                        String parentfieldid = Util.null2String(rs1.getString(1));
                        if (!parentfieldid.isEmpty()) {
                            parentDomkey = "con_" + parentfieldid;
                        }
                    }
                }
                ConditionFactory conditionFactory = new ConditionFactory(user);
                CubeFieldService cfs = new CubeFieldService();
                SearchConditionItem conditionJson = new SearchConditionItem();
                if (!"seclevel".equals(fieldname_seclevel)) {
                    conditionJson = conditionFactory.createCondition(ConditionType.SELECT, fieldlabel, fieldname + "," + parentDomkey, cfs.getWorkflowSelectItems("" + fieldid, user.getLanguage(), true));
                } else {
                    //密级修改为从人力资源那么获取
                    Map<String, Object> otherParam = new HashMap<>();
                    AddSeclevelUtil addSeclevelUtil = new AddSeclevelUtil();
                    otherParam.put("fieldlabel", fieldlabel);
                    otherParam.put("fieldname", fieldname);
                    otherParam.put("parentDomkey", parentDomkey);
                    conditionJson = addSeclevelUtil.getSecLevelOptions(conditionFactory, user, otherParam);

                }
                List<SearchConditionOption> options = new ArrayList<SearchConditionOption>();
                options = conditionJson.getOptions();
                if (!searchparavalue.isEmpty()) {
                    json.put("value", searchparavalue);
                }
                json.put("options", options);
                json.put("parentDomkey", conditionJson.getParentDomkey());
                json.put("domkey", conditionJson.getDomkey());
                json.put("viewAttr", viewAttr);
                json.put("detailtype", "2");
                json.put("customname", customname);
            } else if ("5".equals(fieldhtmltype) && "3".equals(fieldtype)) {//单选
                RecordSet record = new RecordSet();
                StringBuffer sql1 = new StringBuffer();
                Integer fieldlabel = 0;
                String parentDomkey = "";
                Integer pubchilchoiceId = 0;

                sql1.append(" select a.id, b.fieldlabel, b.pubchilchoiceId     ");
                sql1.append("   from mode_customdspfield a, workflow_billfield b                         ");
                sql1.append("  where a.fieldid = b.id and b.id=?                                         ");
                sql1.append("    and a.customId = ?                                                      ");
                record.executeQuery(sql1.toString(), fieldid, customId);
                while (record.next()) {
                    fieldlabel = record.getInt("fieldlabel");
                    pubchilchoiceId = record.getInt("pubchilchoiceId");
                }
                if (pubchilchoiceId > 0) {
                    parentDomkey = "con_" + pubchilchoiceId;
                } else {//#866910
                    String dlxzksql = "select t.id from workflow_billfield t where t.childfieldid=?";
                    rs1.executeQuery(dlxzksql, fieldid);
                    if (rs1.next()) {
                        String parentfieldid = Util.null2String(rs1.getString(1));
                        if (!parentfieldid.isEmpty()) {
                            parentDomkey = "con_" + parentfieldid;
                        }
                    }
                }
                ConditionFactory conditionFactory = new ConditionFactory(user);
                CubeFieldService cfs = new CubeFieldService();
                SearchConditionItem conditionJson = conditionFactory.createCondition(ConditionType.SELECT, fieldlabel, fieldname + "," + parentDomkey, cfs.getWorkflowSelectItems("" + fieldid, user.getLanguage(), true));
                List<SearchConditionOption> options = new ArrayList<SearchConditionOption>();
                options = conditionJson.getOptions();
                if ("1".equals(conditiontransition) && options.size() > 0 && "".equals(options.get(0).getKey())) {//选择框多选时去除空选项
                    options.remove(0);
                }
                json.put("options", options);
                if (!searchparavalue.isEmpty()) {
                    json.put("value", searchparavalue);
                }
                json.put("detailtype", "3");
                json.put("parentDomkey", conditionJson.getParentDomkey());
                json.put("domkey", conditionJson.getDomkey());
                json.put("supportCancel", true);
                json.put("fieldid", fieldid);
                json.put("customname", customname);
            } else if ("4".equals(fieldhtmltype) && "1".equals(fieldtype)) {//勾选框
                RecordSet record = new RecordSet();
                StringBuffer sql1 = new StringBuffer();
                Integer fieldlabel = 0;

                sql1.append(" select a.id, b.fieldlabel   ");
                sql1.append("   from mode_customdspfield a, workflow_billfield b                         ");
                sql1.append("  where a.fieldid = b.id and b.id=?                                         ");
                sql1.append("    and a.customId = ?                                                      ");
                record.executeQuery(sql1.toString(), fieldid, customId);
                while (record.next()) {
                    fieldlabel = record.getInt("fieldlabel");
                }
                ConditionFactory conditionFactory = new ConditionFactory(user);
                CubeFieldService cfs = new CubeFieldService();
                SearchConditionItem conditionJson = conditionFactory.createCondition(ConditionType.SELECT, fieldlabel, fieldname, this.getCheckOptions(user.getLanguage()));
                List<SearchConditionOption> options = new ArrayList<SearchConditionOption>();
                options = conditionJson.getOptions();

                json.put("options", options);
                json.put("domkey", conditionJson.getDomkey());
                json.put("viewAttr", viewAttr);
                json.put("detailtype", "3");
                json.put("customname", customname);
            } else if ("3".equals(fieldhtmltype)) {//日期
                BrowserBean browserProps = null;
                if ("2".equals(fieldtype) || "19".equals(fieldtype) || "290".equals(fieldtype)) {//日期 时间 日期时间
                    if (searchparavalue.indexOf(",") > -1) {
                        String[] vals = searchparavalue.split(",");
                        if (vals.length > 0) {
                            searchparavalue = vals[0];
                            if (vals.length > 1) {
                                searchparavalue1 = vals[1];
                                if (vals.length > 2) {
                                    searchparavalue1 = vals[1] + vals[2];
                                }
                            }
                        }
                    }
                    if ("2".equals(fieldtype) && searchparavalue.isEmpty()) {
                        searchparavalue = "0"; // 全部
                    }
                    if ("2".equals(fieldtype) && !searchparavalue.isEmpty()) {
                        if (-1 != Util.getIntValue(searchparavalue)) {
                            json.put("value", new String[]{searchparavalue, "", ""});
                        } else {
                            json.put("value", new String[]{"6", searchparavalue, searchparavalue1});
                        }
                    } else {
                        ArrayList<String> dataValue = new ArrayList<String>();
                        if ("".equals(searchparavalue) && "".equals(searchparavalue1)) {
                            json.put("value", dataValue);
                        } else {
                            dataValue.add(searchparavalue);
                            dataValue.add(searchparavalue1);
                            json.put("value", dataValue);
                        }
//                        json.put("value", searchparavalue);
                    }
                } else if ("402".equals(fieldtype) || "403".equals(fieldtype)) {//年、年月
                    if (!searchparavalue.isEmpty()) {
                        json.put("value", searchparavalue);
                    }
                } else if ("161".equals(fieldtype) || "162".equals(fieldtype)) {
                    browserProps = new BrowserBean(fieldtype);
                    BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                    List<Object> valueObj = null;
                    List<Map<String, Object>> replaceDatas = new ArrayList<Map<String, Object>>();
                    try {
                        valueObj = browserValueInfoService.getBrowserValueInfo(Util.getIntValue(fieldtype), fielddbtype, 0, searchparavalue, user.getLanguage(), -1);
                        for (int i = 0; i < valueObj.size(); i++) {
                            Map<String, Object> map = new HashMap<String, Object>();
                            BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                            map.put("id", m.getId());
                            map.put("name", m.getName());
                            map.put("count", m.getCount());
                            replaceDatas.add(map);
                        }
                        json.put("replaceDatas", replaceDatas);
                        browserProps.setReplaceDatas(replaceDatas);

                    } catch (Exception e) {
                    }
                    new BrowserInitUtil().initCustomizeBrow(browserProps, fielddbtype, Util.getIntValue(fieldtype), user.getUID());
                    browserProps.getDataParams().put("formmodefieldid", fieldid);
                    browserProps.getConditionDataParams().put("formmodefieldid", fieldid);
                    browserProps.getCompleteParams().put("formmodefieldid", fieldid);
                } else if ("256".equals(fieldtype) || "257".equals(fieldtype)) {
                    browserProps = new BrowserBean(fieldtype);
                    browserProps.getDataParams().put("cube_treeid", fielddbtype);
                    browserProps.getCompleteParams().put("cube_treeid", fielddbtype);
                    BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                    List<Object> valueObj = null;
                    List<Map<String, Object>> replaceDatas = new ArrayList<Map<String, Object>>();
                    try {
                        valueObj = browserValueInfoService.getBrowserValueInfo(Util.getIntValue(fieldtype), fielddbtype, 0, searchparavalue, user.getLanguage(), -1);
                        for (int i = 0; i < valueObj.size(); i++) {
                            Map<String, Object> map = new HashMap<String, Object>();
                            BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                            map.put("id", m.getId());
                            map.put("name", m.getName());
                            map.put("count", m.getCount());
                            replaceDatas.add(map);
                        }
                        json.put("replaceDatas", replaceDatas);
                        browserProps.setReplaceDatas(replaceDatas);

                    } catch (Exception e) {
                    }
                    new BrowserInitUtil().initBrowser(browserProps, user.getLanguage());
                } else if ("1".equals(fieldtype) || ("1".equals(conditiontransition) && "17".equals(fieldtype))) {   //人力资源
                    if ("1".equals(conditiontransition) && "1".equals(fieldtype)) {
                        browserProps = new BrowserBean(fieldtype);
                        browserProps.setViewAttr(viewAttr);
                        List<Object> objs = null;
                        if (!searchparavalue.isEmpty()) {
                            if (Util.getIntValue(searchparavalue) == 0 || "-1".equals(searchparavalue) || Util.getIntValue(searchparavalue) == -2
                                    || Util.getIntValue(searchparavalue) == -3 || Util.getIntValue(searchparavalue) == -4 || Util.getIntValue(searchparavalue) == -5
                                    || Util.getIntValue(searchparavalue) == -6 || Util.getIntValue(searchparavalue) == -7 || Util.getIntValue(searchparavalue) == -8) {
                                json.put("value", new String[]{searchparavalue, "", ""});
                            } else {
                                try {
                                    objs = new BrowserValueInfoService().getBrowserValueInfo(1, null, 0, searchparavalue, user.getLanguage(), -1);
                                } catch (Exception e) {
                                }
                                String valueSpan = "";
                                if (objs != null) {
                                    for (Object obj : objs) {
                                        valueSpan += ((BrowserValueInfo) obj).getName() + ",";
                                    }
                                    valueSpan = valueSpan.substring(0, valueSpan.length() - 1);
                                }
                                json.put("value", new Object[]{"-5", new Object[]{searchparavalue, valueSpan, objs}});
                            }
                        } else {
                            json.put("value", new Object[]{"-5", "", ""});
                        }
                        new BrowserInitUtil().initBrowser(browserProps, user.getLanguage());
                    } else {
                        browserProps = new BrowserBean(fieldtype);
                        browserProps.setViewAttr(viewAttr);
                        List<Object> objs = null;
                        if (!searchparavalue.isEmpty()) {
                            if (Util.getIntValue(searchparavalue) <= 0) {
                                json.put("value", new String[]{searchparavalue, ""});
                            } else {
                                try {
                                    objs = new BrowserValueInfoService().getBrowserValueInfo(1, null, 0, searchparavalue, user.getLanguage(), -1);
                                } catch (Exception e) {
                                }
                                String valueSpan = "";
                                if (objs != null) {
                                    for (Object obj : objs) {
                                        valueSpan += ((BrowserValueInfo) obj).getName() + ",";
                                    }
                                }
                                json.put("value", new Object[]{"-5", new Object[]{searchparavalue, valueSpan, objs}});
                            }
                        } else {
                            json.put("value", new Object[]{"-5", "", ""});
                        }
                        new BrowserInitUtil().initBrowser(browserProps, user.getLanguage());
                    }
                } else if ("4".equals(fieldtype) || "57".equals(fieldtype)) {
                    browserProps = new BrowserBean(fieldtype);
                    if (!searchparavalue.isEmpty()) {
                        BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                        List<BrowserValueInfo> valueObj = null;
                        List<Map<String, Object>> replaceDatas = new ArrayList<Map<String, Object>>();
                        try {
                            valueObj = browserValueInfoService.getBrowserValueInfo(fieldtype, String.valueOf(fielddbtype), 0, searchparavalue, user.getLanguage(), "", "");
                            for (int i = 0; i < valueObj.size(); i++) {
                                Map<String, Object> map = new HashMap<String, Object>();
                                BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                                map.put("id", m.getId());
                                map.put("name", m.getName());
                                map.put("count", m.getCount());
                                replaceDatas.add(map);
                            }
                            json.put("replaceDatas", replaceDatas);
                            browserProps.setReplaceDatas(replaceDatas);
                        } catch (Exception e) {
                        }
                    }
                    browserProps.setViewAttr(viewAttr);
                    Map<String, Object> map = new HashMap<String, Object>();
                    Map<String, Object> map1 = new HashMap<String, Object>();
                    Map<String, Object> map2 = new HashMap<String, Object>();
                    map.put("fromModule", "model");
                    map1.put("fromModule", "model");
                    map2.put("fromModule", "model");
                    browserProps.setDataParams(map1);
                    browserProps.setDestDataParams(map2);
                    browserProps.setCompleteParams(map);
                    new BrowserInitUtil().initBrowser(browserProps, user.getLanguage());
                } else if ("164".equals(fieldtype) || "194".equals(fieldtype)) {
                    browserProps = new BrowserBean(fieldtype);
                    if (!searchparavalue.isEmpty()) {
                        BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                        List<BrowserValueInfo> valueObj = null;
                        List<Map<String, Object>> replaceDatas = new ArrayList<Map<String, Object>>();
                        try {
                            valueObj = browserValueInfoService.getBrowserValueInfo(fieldtype, String.valueOf(fielddbtype), 0, searchparavalue, user.getLanguage(), "", "");
                            for (int i = 0; i < valueObj.size(); i++) {
                                Map<String, Object> map = new HashMap<String, Object>();
                                BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                                map.put("id", m.getId());
                                map.put("name", m.getName());
                                map.put("count", m.getCount());
                                replaceDatas.add(map);
                            }
                            json.put("replaceDatas", replaceDatas);
                            browserProps.setReplaceDatas(replaceDatas);
                        } catch (Exception e) {
                        }
                    }
                    browserProps.setViewAttr(viewAttr);
                    Map<String, Object> map = new HashMap<String, Object>();
                    Map<String, Object> map1 = new HashMap<String, Object>();
                    Map<String, Object> map2 = new HashMap<String, Object>();
                    map.put("fromModule", "model");
                    map1.put("fromModule", "model");
                    map2.put("fromModule", "model");
                    browserProps.setDataParams(map1);
                    browserProps.setDestDataParams(map2);
                    browserProps.setCompleteParams(map);
                    new BrowserInitUtil().initBrowser(browserProps, user.getLanguage());
                } else {
                    browserProps = new BrowserBean(fieldtype);
                    if (!searchparavalue.isEmpty()) {
                        BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                        List<Object> valueObj = null;
                        List<Map<String, Object>> replaceDatas = new ArrayList<Map<String, Object>>();
                        try {
                            valueObj = browserValueInfoService.getBrowserValueInfo(Util.getIntValue(fieldtype), fielddbtype, 0, searchparavalue, user.getLanguage(), -1);
                            for (int i = 0; i < valueObj.size(); i++) {
                                Map<String, Object> map = new HashMap<String, Object>();
                                if ("141".equals(fieldtype)) {
                                    map = (Map<String, Object>) valueObj.get(i);
                                } else {
                                    BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                                    map.put("id", m.getId());
                                    map.put("name", m.getName());
                                    map.put("count", m.getCount());
                                }
                                replaceDatas.add(map);
                            }
                            json.put("replaceDatas", replaceDatas);
                            browserProps.setReplaceDatas(replaceDatas);

                        } catch (Exception e) {
                        }
                    }
                    //Map<String,Object> map = new HashMap<>();
                    //map.put("fromModule","model");
                    //browserProps.setDataParams(map);
                    //browserProps.setDestDataParams(map);
                    //browserProps.setCompleteParams(map);
                    browserProps.setViewAttr(viewAttr);
                    new BrowserInitUtil().initBrowser(browserProps, user.getLanguage());
                }
                if (conditiontransition.equalsIgnoreCase("1")) {
                    //  browserProps.setIsMultCheckbox(true);
                    browserProps.setType(fieldtype);
                }

                json.put("browserProps", browserProps);
            }
            json.put("conditiontransition", conditiontransition);
            json.put("fieldid", fieldid);
            json.put("customname", customname);
            json.put("fieldtype", fieldtype);
            json.put("viewAttr", viewAttr);
            json.put("requiredCon", requiredCon);
            json.put("fieldhtmltype", fieldhtmltype);
            json.put("showtype", showtype);//显示模式主要 针对浏览框 0：浏览框模式 1：下拉拉框模式
            json.put("groupid", groupid);
            json.put("showmodel", showmodel);
            json.put("orderid", orderid);
            if (!"".equals(groupid) && !"0".equals(groupid)) {
                List<JSONObject> list = groupMap.get(groupid);
                if (null == list) {
                    list = new ArrayList<JSONObject>();
                }
                list.add(json);
                groupMap.put(groupid, list);
            } else {
                datas.add(json);
            }
        }

        rs.executeQuery("select * from mode_customsearchgroup where customid=?", customid);
        Map<String, String> group = new HashMap<String, String>();
        while (rs.next()) {
            float minOrder = 10000000;
            JSONObject json = new JSONObject();
            String id = Util.null2String(rs.getString("id"));
            String groupname = Util.null2String(rs.getString("groupname"));
            List<JSONObject> list = groupMap.get(id);//当前分组下的所有快捷搜索
            if (null == list) continue;

            json.put("groupid", id);
            json.put("groupname", groupname);
            JSONArray chrild = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                float childorderid = Util.getFloatValue(Util.null2String(list.get(i).get("orderid")), 0);
                //取分组里orderid最小的一个分组的orderid
                if (childorderid < minOrder) {
                    minOrder = childorderid;
                }
                chrild.add(list.get(i));
            }
            json.put("orderid", minOrder);
            json.put("searchGroup", chrild);
            datas.add(json);
        }

        return datas;
    }

    public String[] getSearchDate(String datetype) {
        String[] dateStr = new String[2];
        weaver.general.DateUtil du = new weaver.general.DateUtil();
        int valueType = Util.getIntValue(datetype, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        switch (valueType) {
            case 1:
                //当前日期
                dateStr[0] = sdf.format(new Date());
                dateStr[1] = sdf.format(new Date());
                break;
            case 2:
                //本周
                Calendar cal = Calendar.getInstance();
                cal.set(Calendar.DAY_OF_WEEK, cal.getActualMinimum(Calendar.DAY_OF_WEEK));//周日
                dateStr[0] = sdf.format(cal.getTime());
                cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));//周六
                dateStr[1] = sdf.format(cal.getTime());
                break;
            case 3:
                //本月
                dateStr[0] = sdf.format(du.getFirstDayOfMonth());
                dateStr[1] = sdf.format(du.getLastDayOfMonth());
                break;
            case 4:
                //本季
                dateStr[0] = sdf.format(du.getFirstDayOfQuarter());
                dateStr[1] = sdf.format(du.getLastDayOfQuarter());
                break;
            case 5:
                //本年
                dateStr[0] = du.getYearDateStart();
                dateStr[1] = du.getYearDateEnd();
                break;
            case 7:
                //上一月
                Date date = new Date();
                Calendar cDay = Calendar.getInstance();
                cDay.setTime(date);
                cDay.add(Calendar.MONTH, -1);
                cDay.set(Calendar.DAY_OF_MONTH, 1);
                dateStr[0] = sdf.format(cDay.getTime());
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.add(Calendar.DATE, -1);
                dateStr[1] = sdf.format(calendar.getTime());
                break;
            case 8:
                //上一年
                date = new Date();
                cDay = Calendar.getInstance();
                cDay.setTime(date);
                int year = cDay.get(Calendar.YEAR) - 1;
                dateStr[0] = year + "-01-01";
                dateStr[1] = year + "-12-31";
                break;
            default:
                dateStr[0] = sdf.format(new Date());
                dateStr[1] = sdf.format(new Date());
                break;
        }

        return dateStr;
    }

    public static String convertSingleBrowserTypeToMulti_All(String fieldtype) {
        switch (fieldtype) {
//            case "1":return "17";//人力资源
            case "7":
                return "18";//多客户
            case "9":
                return "37";//多文档
//            case "4":return "57";//多部门
            case "267":
                return "65";//多角色
            case "8":
                return "135";//多项目
            case "16":
                return "152";//多流程
            case "161":
                return "162";//自定义多选
            case "165":
                return "166";//分权多人力资源
            case "167":
                return "168";//分权多部门
            case "169":
                return "170";//分权多分部
//            case "164":return "194";//多分部
            case "256":
                return "257";//自定义树形多选
            case "24":
                return "278";//多岗位
            case "292":
                return "293";//多发票
            case "87":
                return "184";//会议室
            case "23":
                return "315";//多资产
            case "25":
                return "317";//多资产组
            case "179":
                return "314";//多资产资料
            default:
                return fieldtype;
        }
    }

    public Map<String, Object> getRightMenus(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        if (isBackUpData) {//不需要右键菜单
            result.put("rightMenus", new ArrayList<RightMenu>());
            return result;
        }
        User user = HrmUserVarify.getUser(request, response);
        List<RightMenu> rightMenus = new ArrayList<RightMenu>();
        Map<String, String> urlMap = new HashMap<String, String>();
        Map<String, String> confirmMap = new HashMap<String, String>();
        FormModeConfig formModeConfig = new FormModeConfig();
        String enableTreeList = formModeConfig.getEnableTreeList();  //树形展示是有formmode.properties中控制的。即使链接链接有treetype也不行。enableTreeList为y时开启
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery("select istreesearch,disquerycheck,datashowtype,linemergel,datashowtype from mode_customsearch where id=?", customId);
        String isTreeSearch = "";
        String linemergel = "";
        String disquerycheck = "";//xk：勾选无check框时，数据筛选不生效
        String datashowtype = "";
        if (recordSet.next()) {
            linemergel = Util.null2String(recordSet.getString("linemergel"));
            isTreeSearch = Util.null2String(recordSet.getString("isTreeSearch"));
            if (!isTreeSearch.equals("1")) {
                isTreeSearch = Util.null2String(recordSet.getString("datashowtype")).equals("4") ? "1" : "";
            }
            disquerycheck = recordSet.getString("disquerycheck");
            datashowtype = Util.null2String(recordSet.getString("datashowtype"));
        }
        boolean isTree = true;
        if (enableTreeList.equals("y") && isTreeSearch.equals("1")) {
            isTree = false;
        }
        boolean islinemergel = false;
        if (linemergel.equals("1")) {
            islinemergel = true;
        }
        boolean isPhoto = false;//图片看板模式 excel列表、轴性查询列表不显示显示定制列
        if (datashowtype.equals("1") || datashowtype.equals("2") || datashowtype.equals("5")) {
            isPhoto = true;
        }
        if (this.isEdit && !isVirtualForm && !noRightList) {
            rightMenus.add(new RightMenu("batchEditSave", SystemEnv.getHtmlLabelName(385215, Util.getIntValue(user.getLanguage())), "icon-coms-Preservation", "batchEditSave"));//批量保存
            rightMenus.add(new RightMenu("submitData", SystemEnv.getHtmlLabelName(197, user.getLanguage()), "icon-coms-search", "submitData"));
            rightMenus.add(new RightMenu("batchEditGoBack", SystemEnv.getHtmlLabelName(1290, user.getLanguage()), "icon-coms-Revoke", "batchEditGoBack"));
        } else {
            String customId = Util.null2String(request.getParameter("customid"));
            CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
            int modeId = Util.getIntValue(customSearchComInfo.getModeId(customId), 0);
            String formId = customSearchComInfo.getFormId(customId);
            String detailtable = Util.null2String(customSearchComInfo.getDetailTable(customId));
            boolean isVirtualForm = "1".equals(new ModeFormComInfo().getIsVirtualForm(formId));
            boolean isVformview = false;
            if (isVirtualForm) {
                Map<String, Object> vmap = VirtualFormHandler.getVFormInfo(formId);
                isVformview = "1".equals(vmap.get("virtualformtype")) || "2".equals(vmap.get("vformtype"));
            }
            ExpandBaseRightInfo rightInfo = new ExpandBaseRightInfo();
            rightInfo.setUser(user);
            ModeRightInfo modeRightInfo = new ModeRightInfo();
            modeRightInfo.setModeId(modeId);
            modeRightInfo.setUser(user);
            modeRightInfo.setType(1);
            boolean CreateRight = modeRightInfo.checkUserRight(1);
            // 改造兼容档案权限
            ArchivesUtil archivesUtil = new ArchivesUtil();
            CreateRight = CreateRight || archivesUtil.checkHasCreateRight(formId, String.valueOf(modeId), String.valueOf(user.getUID()));
            boolean BatchImportRight = false;
            modeRightInfo.setType(4);
            if (isVirtualForm) {//虚拟表单不能批量导入
                BatchImportRight = false;
            } else {
                BatchImportRight = modeRightInfo.checkUserRight(4);
                if (!BatchImportRight) {
                    BatchImportRight = HrmUserVarify.checkUserRight("ModeSetting:All", user);
                }
            }
            RecordSet rs = new RecordSet();
            boolean hasBatchEditRight = false;//有没有批量修改权限
            boolean ishisData = false;
            if (this.isBatchEdit && viewType == 0 && !isVirtualForm && !"advanced".equals(this.type) && !noRightList) {
                rs.executeQuery("select * from mode_pageexpand where modeid = " + modeId + " and issystemflag=15");
                if (!rs.next()) {//如果mode_pageexpand表中没有批量修改对应的数据,那么这个查询列表的数据是历史数据,在批量操作那没有保存过
                    ishisData = true;
                }
                hasBatchEditRight = true;
            }
            String sql = "";
            String sqlwhere = "";
            Map<String, String> expenParam = new HashMap<>();
            if (modeId == 0) {
                sqlwhere = " and a.modeid  is null ";
            } else {
                sqlwhere = " and a.modeid = ? ";
            }
            String nullFun = CommonConstant.DB_ISNULL_FUN;
            sql = "select a.id,a.expenddesc,b.isuse,b.isfilter,a.expendname,a.icon,a.checkselectrow,"
                    + nullFun + "(b.showorder,a.showorder) showorder," +
                    "a.issystem,a.issystemflag,a.defaultenable,a.hreftype," +
                    "            a.isenabletip,a.tiptype,a.tipdatasourceid,a.tipsql,a.tipjk,  " +
                    "a.hrefid,a.hreftarget,a.opentype,b.listbatchname ,b.isshortcutbutton,a.confirmprompt,a.isbatch,a.isshow " +
                    " from mode_pageexpand a left join mode_batchset b on a.id = b.expandid and b.customsearchid =  ? "
                    + " where (a.isbatch in(1,2)  or a.confirmprompt is not null) " + sqlwhere
                    + "  order by showorder asc,a.issystem desc,a.id asc";
            if (modeId == 0) {
                rs.executeQuery(sql, customId);
            } else {
                rs.executeQuery(sql, customId, modeId);
            }

            Map<String, Object> params = new HashMap<>();
            params.put("formId", formId);
            params.put("billid", "1");//用列表查询 指定列表中的第一个数据，用户提示需求#819808
            while (rs.next()) {
                String detailid = Util.null2String(rs.getString("id"));
                String expendname = Util.null2String(rs.getString("expendname"));
                String issystemflag = Util.null2String(rs.getString("issystemflag"));
                String isCheckSelectRow = Util.null2String(rs.getString("checkselectrow"));
                String confirmprompt = Util.null2String(rs.getString("confirmprompt"));
                String isbatch = Util.null2String(rs.getString("isbatch"));
                String isshow = Util.null2String(rs.getString("isshow"));
                if (!"".equals(confirmprompt)) {
                    confirmMap.put(detailid, confirmprompt);
                }
                //当是卡片页面 或者没有显示页面扩展时，不执行后边逻辑 只返回二次确认参数给自定义按钮调用
                if ("0".equals(isbatch) || !"1".equals(isshow)) {
                    continue;
                }
                if (StringHelper.isEmpty(isCheckSelectRow)) {
                    isCheckSelectRow = "-1";
                }
                if (!Util.isEnableMultiLang()) {
                    expendname = Util.formatMultiLang(expendname, Util.null2String(user.getLanguage()));
                }
                //获取页面扩展括号内的内容
                //1、封装相关参数
                expenParam.put("isenabletip", Util.null2String(rs.getString("isenabletip")));
                expenParam.put("tiptype", Util.null2String(rs.getString("tiptype")));
                expenParam.put("tipdatasourceid", Util.null2String(rs.getString("tipdatasourceid")));
                expenParam.put("tipsql", Util.null2String(rs.getString("tipsql")));
                expenParam.put("tipjk", Util.null2String(rs.getString("tipjk")));
                //2、获取相关拼接内容#800358
                String expendContext = PageExpandUtil.getExpendContext(expenParam, user, params);
                //expendname +=expendContext;
                // 检查是否有该页面扩展按钮权限
                if (modeId != 0) {
                    if (!rightInfo.checkExpandRight(detailid, String.valueOf(modeId)) && !"15".equals(issystemflag) && !"16".equals(issystemflag) && !"105".equals(issystemflag)) {
                        // 没有权限，不显示
                        continue;
                    }
                }

                String issystem = Util.null2String(rs.getString("issystem"));
                String defaultenable = Util.null2String(rs.getString("defaultenable"));
                String isuse = Util.null2String(rs.getString("isuse"));
                String isfilter = "1".equals(disquerycheck) ? "0" : Util.null2String(rs.getString("isfilter"));//xk:数据筛选
                String hreftarget = Util.null2String(rs.getString("hreftarget"));
                String listbatchname = Util.null2String(rs.getString("listbatchname"));
                int issystemflagval = Util.getIntValue(rs.getString("issystemflag"), 0);
                if (isVirtualForm && isVformview && (issystemflagval == 101 || issystemflagval == 102)) {
                    continue;
                }
                if (issystemflagval == 1 || issystemflagval == 2 || issystemflagval == 3 || issystemflagval == 4 || issystemflagval == 5 ||
                        issystemflagval == 6 || issystemflagval == 7 || issystemflagval == 8 || issystemflagval == 100 || issystemflagval == 101 ||
                        issystemflagval == 102 || issystemflagval == 103 || issystemflagval == 104 || issystemflagval == 105 || issystemflagval == 9 ||
                        issystemflagval == 10 || issystemflagval == 106 || issystemflagval == 11 || issystemflagval == 12 || issystemflagval == 13 ||
                        issystemflagval == 14 || issystemflagval == 15 || issystemflagval == 170 || issystemflagval == 171 || issystemflagval == 110 ||
                        issystemflagval == 167 || issystemflagval == 16 || issystemflagval == 17 || issystemflagval == 172) {
                    if (Util.formatMultiLang(expendname, "7").equals(listbatchname)) {
                        listbatchname = expendname + expendContext;
                    }
                }
                listbatchname += expendContext;
                String opentype = Util.null2String(rs.getString("opentype"));
                String hreftype = Util.null2String(rs.getString("hreftype"));
                String hrefid = Util.null2String(rs.getString("hrefid"));
                String icon = Util.null2String(rs.getString("icon"));
                double showorder = Util.getDoubleValue(rs.getString("showorder"));
                boolean isQuickButton = "1".equals(rs.getString("isshortcutbutton"));
                if ("5".equals(hreftype) || "6".equals(hreftype)) {
                    continue;
                }
                if (listbatchname.equals("")) {
                    listbatchname = expendname;
                } else {
                    if (!Util.isEnableMultiLang()) {
                        listbatchname = Util.formatMultiLang(listbatchname, Util.null2String(user.getLanguage()));
                    }
                }

                if (ishisData && hasBatchEditRight && (showorder >= 107)) {//如果是历史数据,默认展示批量修改菜单
                    rightMenus.add(new RightMenu("doBatchEdit", "".equals(listbatchname) ? SystemEnv.getHtmlLabelName(25465, user.getLanguage()) : listbatchname, "icon-mode-batch-editing", "doBatchEdit", isCheckSelectRow, isfilter).isQuickButton(false));
                    ishisData = false;//只用添加一次
                }

                if (issystemflag.equals("")) {
                    issystemflag = "0";
                }
                if (issystem.equals("1")) {
                    if (isuse.equals("")) {
                        isuse = defaultenable;
                        if (issystemflag.equals("15")) {
                            isuse = "1";
                        }
                    }
                    if (isuse.equals("0")) {
                        continue;
                    }

                    if (issystemflag.equals("100")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(197, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-search", "submitData").isQuickButton(isQuickButton));
                    } else if (hasBatchEditRight && issystemflag.equals("15")) {//批量修改
                        rightMenus.add(new RightMenu("doBatchEdit", "".equals(listbatchname) ? SystemEnv.getHtmlLabelName(25465, user.getLanguage()) : listbatchname, "icon-mode-batch-editing", "doBatchEdit").isQuickButton(isQuickButton));
                    } else if (CreateRight && issystemflag.equals("16") && detailtable.equals("")) {//批量新增
                        rightMenus.add(new RightMenu("doBatchAdd", "".equals(listbatchname) ? SystemEnv.getHtmlLabelName(500091, user.getLanguage()) : listbatchname, "icon-coms-Batch-add", "doBatchAdd").isQuickButton(isQuickButton));
                    } else if (CreateRight && issystemflag.equals("101")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(82, user.getLanguage());
                        }
                        if (StringHelper.isEmpty(rs.getString("isshortcutbutton"))) {
                            isQuickButton = true;
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-New-Flow", "Add").isQuickButton(isQuickButton));
                    } else if (BatchImportRight && issystemflag.equals("103")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(26601, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-export", "BatchImport").isQuickButton(isQuickButton));
                    } else if (issystemflag.equals("102")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(91, user.getLanguage());
                        }
                        if (viewtype == 3) {
                            if (isDel) {
                                rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-delete", "Del").isQuickButton(isQuickButton));
                            }
                        } else {
                            rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-delete", "Del").isQuickButton(isQuickButton));
                        }

                    } else if (issystemflag.equals("8")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(33418, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-Reset", "resetSearch").isQuickButton(isQuickButton));
                    } else if (issystemflag.equals("12")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(125512, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "anticon anticon-qrcode", "batchCreateQRCode").isQuickButton(isQuickButton));
                    } else if (issystemflag.equals("171")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(126684, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-mode-barcode", "batchCreateBARCode").isQuickButton(isQuickButton));
                    } else if (!isVirtualForm && issystemflag.equals("104")) {// 虚拟表单能用批量共享
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(18037, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-Batch-sharing", "batchShare").isQuickButton(isQuickButton));
                    } else if (issystemflag.equals("105")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(17416, user.getLanguage());
                        }
                        addExportMenu(rightMenus, customId, modeId, detailid, listbatchname, isQuickButton, user);
                    } else if (issystemflag.equals("106") && isTree && !islinemergel && !isPhoto) {// 显示定制列
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(32535, user.getLanguage());
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-task-list", "columnMake").isQuickButton(isQuickButton));
                    } else if (issystemflag.equals("167")) {// 批量设置标签
                        ModelInfoService modelInfoService = new ModelInfoService();
                        Map<String, Object> data = modelInfoService.getModelInfoById(modeId);
                        int istagset = Util.getIntValue(Util.null2String(data.get("istagset")), 0);
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(384962, user.getLanguage());
                        }
                        if (istagset == 1) {
                            rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-integration-o", "setTag").isQuickButton(isQuickButton));
                        }
                    }
                    if (issystemflag.equals("110")) {// 地图页面
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(389877, Util.getIntValue(user.getLanguage()));//地图页面
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-position", "showMapPage").isQuickButton(isQuickButton));
                    }
                    if (issystemflag.equals("172")) {
                        if (listbatchname.equals("")) {
                            listbatchname = SystemEnv.getHtmlLabelName(26382, Util.getIntValue(user.getLanguage()));//批量打印
                        }
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-task-list", "doBatchPrint").isQuickButton(isQuickButton));
                    }
                } else {
                    if (isuse.equals("0") || isuse.equals("")) {
                        continue;
                    }
                    if (hreftype.equals("3")) {
                        recordSet.executeQuery("select istreesearch,disquerycheck,datashowtype from mode_customsearch where id=?", hrefid);
                        String isTreeSearchval = "";
                        if (recordSet.next()) {
                            isTreeSearchval = Util.null2String(recordSet.getString("istreeSearch"));
                            if (!isTreeSearchval.equals("1")) {
                                isTreeSearchval = Util.null2String(recordSet.getString("datashowtype")).equals("4") ? "1" : "";
                            }
                        }

                        if (enableTreeList.equals("y") && isTreeSearchval.equals("1")) {
                            if (hreftarget.indexOf("displayType=treeType") == -1) {
                                hreftarget += "&displayType=treeType";
                            }
                        }
                    } else if (hreftype.equals("7")) {//新建流程,需要解析卡片页面数据id
                        String cardid = Util.null2String(request.getParameter("cardid"));
                        hreftarget = PageExpandHelper.getCardIdLink(hreftarget, cardid, hrefid, detailid);
                    }
                    urlMap.put(detailid, hreftarget);
                    if ("".equals(icon)) {
                        if ("4".equals(hreftype)) {
                            icon = "icon-mode-batch-editing";
                        } else {
                            icon = "icon-coms-List-display";
                        }
                    }
                    if (hreftype.equals("4")) {// 批量修改
                        rightMenus.add(new RightMenu(detailid, "".equals(listbatchname) ? SystemEnv.getHtmlLabelName(25465, user.getLanguage()) : listbatchname, icon, "batchmodifyfeildvalue", isCheckSelectRow, isfilter).isQuickButton(isQuickButton));
                    } else if (opentype.equals("1")) {// 默认窗口，当前窗口
                        rightMenus.add(new RightMenu(detailid, listbatchname, icon, "windowOpenOnSelf", isCheckSelectRow, isfilter).isQuickButton(isQuickButton));
                    } else if (opentype.equals("2")) {// 弹出窗口
                        rightMenus.add(new RightMenu(detailid, listbatchname, icon, "windowOpenOnNew", isCheckSelectRow, isfilter).isQuickButton(isQuickButton));
                    } else if (opentype.equals("3")) {// 其它
                        rightMenus.add(new RightMenu(detailid, listbatchname, icon, "doCustomFunction", isCheckSelectRow, isfilter).isQuickButton(isQuickButton));
                    }
                }
            }
            if (ishisData && hasBatchEditRight) {//如果是历史数据并且是最后一项,默认展示批量修改菜单
                rightMenus.add(new RightMenu("doBatchEdit", SystemEnv.getHtmlLabelName(25465, user.getLanguage()), "icon-mode-batch-editing", "doBatchEdit").isQuickButton(false));
            }


        }
        if (enableTreeList.equals("y") && isTreeSearch.equals("1")) {
            rightMenus.add(new RightMenu("ChangeSearch", SystemEnv.getHtmlLabelName(506808, user.getLanguage()), "icon-mode-batch-editing", "ChangeSearch").isQuickButton(false));
        }
        //判断是否自定义拖动了列宽和显示列定制
        //boolean isCleanCol = false;
        String isCleanColSql = "select 1 from cloudstore_defcol where pageuid = 'mode_customsearch:" + customId + "' and userid = " + user.getUID();
        RecordSet rs = new RecordSet();
        rs.executeSql(isCleanColSql);
        if (rs.next()) {
            //isCleanCol = true;
            rightMenus.add(new RightMenu("CleanCol", SystemEnv.getHtmlLabelName(503389, user.getLanguage()), "icon-mode-batch-editing", "CleanCol").isQuickButton(false));
        }

        result.put("rightMenus", rightMenus);
        result.put("urlMap", urlMap);
        result.put("confirmMap", confirmMap);
        return result;
    }

    private void addExportMenu(List<RightMenu> rightMenus, String customId, int modeId, String detailid, String listbatchname, boolean isQuickButton, User user) {
        //导出按钮权限重新改造
        //先判断查询列表有没有设置权限
        FormModeRightInfo formModeRightInfo = new FormModeRightInfo();
        formModeRightInfo.setUser(user);
        ExpandBaseRightInfo rightInfo = new ExpandBaseRightInfo();
        rightInfo.setUser(user);
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        rs.executeQuery("select syncexport from  mode_customsearch where id=?", customId);
        if (rs.next()) {
            String syncexport = Util.null2String(rs.getString("syncexport"));
            if (syncexport.equals("1")) {
                //判断有没有权限
                boolean isExport = formModeRightInfo.checkUserRight(Util.getIntValue(customId, 0), 5);
                if (isExport) {//有权限
                    rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-leading-in", "getAllExcelOut").isQuickButton(isQuickButton));
                }
            } else {
                if (modeId != 0) {
                    if (rightInfo.checkExpandRight(detailid, String.valueOf(modeId))) {
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-leading-in", "getAllExcelOut").isQuickButton(isQuickButton));
                    }
                } else {
                    //判断有没有权限
                    boolean isExport = formModeRightInfo.checkUserRight(Util.getIntValue(customId, 0), 5);
                    if (isExport) {//有权限
                        rightMenus.add(new RightMenu(detailid, listbatchname, "icon-coms-leading-in", "getAllExcelOut").isQuickButton(isQuickButton));
                    }
                }
            }
        }
    }

    private Dao_Table getDaoTableByPool(String poolname) {
        String dbType;
        RecordSet rs = new RecordSet();
        if (StringUtils.isBlank(poolname)) {
            dbType = rs.getDBType();
        } else {
            dbType = rs.getDBTypeByPoolName(poolname);
        }
        if ("oracle".equals(dbType))
            return Dao_TableFactory.getInstance().getDao("Dao_TableOracle");
        else if (DBConstant.DB_TYPE_MYSQL.equals(dbType))
            return Dao_TableFactory.getInstance().getDao("Dao_TableMysql");
        else
            return Dao_TableFactory.getInstance().getDao("Dao_TableSqlServer");
        // ue = Util_Ehcache.getIstance();
    }


    /**
     * 获取编辑表格数据
     *
     * @param request
     * @param response
     * @return
     * @throws ServletException
     */
    public Map<String, Object> getBatchEidtData(HttpServletRequest request, HttpServletResponse response) throws ServletException {
        String dataKey = "";
        user = HrmUserVarify.getUser(request, response);
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        SplitPageBean bean = null;
        Map<String, Object> pageMap = getSplitBase(request, response);
        dataKey = StringHelper.null2String(pageMap.get(BrowserConstant.BROWSER_RESULT_DATA));
        try {
            bean = new SplitPageBean(request, dataKey, "RootMap", "operates", "head", "sql", "checkboxpopedom", "browser", "otherHeads");
        } catch (Exception e) {
        }
        if (bean == null) {
            throw new ServletException(SystemEnv.getHtmlLabelName(384286, user.getLanguage()));//分页查询不存在
        }
        customId = Util.getIntValue(request.getParameter("customid"));
        int pageIndex = Util.getIntValue(request.getParameter("current"), 1);
        String tablePageSize = bean.getRootMap().getString("pagesize");
        int pageSize = Util.getIntValue(tablePageSize);
        if (pageSize < 1) {
            pageSize = 10;
        }
        // 分页逻辑上移
        String current = Util.null2String(pageIndex);
        int minInt = (Integer.valueOf(current) - 1) * pageSize + 1;
        int maxInt = minInt + pageSize - 1;
        String min = minInt + "";
        String max = maxInt + "";
        String poolname = bean.getSql().getString("poolname");
        Dao_Table dao = getDaoTableByPool(poolname);
        String sortParams = "";
        TimeMarker timeMarker = new TimeMarker();
        timeMarker.setMark(true);
        timeMarker.markStart();
        // 加入分页逻辑
        String pageUId = "";
        if (null != bean.getRootMap()) {
            pageUId = Util.null2String(bean.getRootMap().getString("pageUid"));
        }

        if (null != bean.getHeads()) {
            JSONArray jsonArr = bean.getHeads();
            JSONObject didObj = new JSONObject();
            didObj.put("dataIndex", "formmodeid");
            didObj.put("dbField", "formmodeid");
            didObj.put("display", "false");
            didObj.put("hide", "true");
            didObj.put("isInputCol", "false");
            didObj.put("isPrimarykey", "false");
            didObj.put("oldWidth", "0.00");
            didObj.put("showType", "0");
            didObj.put("title", "formmodeid");
            jsonArr.add(didObj);
            bean.setHeads(jsonArr);
            bean.mergeHeads();
        }
        List<Map<String, String>> dataList = dao.getDevTableDatas(bean.getSql(), bean.getHeads(), min, max, sortParams, bean.getBroList(), timeMarker);
        if (pageUId.startsWith("mode_customsearch:")) {
            CubeSearchTransMethod.clearCache(pageUId);
        }
        //总记录数
        JSONObject sqlMap = bean.getSql();
        int count = dao.getDevTableCount(sqlMap);

        Map map = getSearchFieldBase(customId, user);
        List<Map<String, String>> list = (ArrayList<Map<String, String>>) map.get("resultList");
        double avgwidth = Util.getDoubleValue(Util.null2String(map.get("avgwidth")));
        double allWidth = Util.getDoubleValue(Util.null2String(map.get("allWidth")));
        //重新计算每列宽度，保证所有列宽度之和<=100 ,前端使用allWidth%控制编辑表格容器宽度出现滚动条
        double p = 1;
        if (allWidth > 100) {
            p = (100.0 / allWidth);
            if (p > 0.5) {
                p = p - 0.1;
            } else if (p > 0.1) {
                p = p - 0.01;
            }
        }
        JSONArray columnsArray = new JSONArray();
        Map<String, String> relateMap = new HashMap<String, String>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> fieldMap = list.get(i);
            String id = fieldMap.get("id");
            String text = fieldMap.get("text");
            String column = fieldMap.get("column");
            double colwidth = Util.getDoubleValue(fieldMap.get("colwidth"));
            if (colwidth <= 0) {
                colwidth = avgwidth;
            }
            colwidth = colwidth * p;
            String viewtype = fieldMap.get("viewtype");
            int viewAttr = 1;
            String editable = fieldMap.get("editable");//是否批量修改
            if ("1".equals(editable)) {
                viewAttr = 2;
            }
            JSONObject jsonObject = new JSONObject();
            String key = column + "_" + id;
            relateMap.put(column, key);
            if ("1".equals(viewtype)) {
                key = key.replaceFirst("d_", "");
            }
            if (id.equals("-1") || id.equals("-2") || id.equals("-3")) {
                JSONObject com = new JSONObject();
                com.put("label", "");
                com.put("type", "INPUT");
                com.put("editType", "1");
                com.put("key", key);
                com.put("viewAttr", 1);
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(com);
                jsonObject.put("com", jsonArray);
            } else {
                //获取组件
                jsonObject.putAll(FieldUtil.createEditTableField(id, user, viewAttr));
            }
            relateMap.put(column, key);
            jsonObject.put("title", text);
            jsonObject.put("dataIndex", key);
            jsonObject.put("key", key);
            jsonObject.put("colSpan", 1);
            jsonObject.put("width", colwidth + "%");
            columnsArray.add(jsonObject);
        }

        //---------------下面加入datas---------------
        int totalPage = count / pageSize;
        if (totalPage % pageSize > 0 || totalPage == 0) {
            totalPage++;
        }
        JSONArray datasArray = new JSONArray();
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> dataMap = dataList.get(i);
            JSONObject rowObj = new JSONObject();
            if (dataMap.containsKey("randomFieldId")) {//主表id
                String val = StringHelper.null2String(dataMap.get("randomFieldId"));
                rowObj.put("id", val);
            }
            if (dataMap.containsKey("randomFieldSubKeyd_id")) {//子表id-可能为空
                String val = StringHelper.null2String(dataMap.get("randomFieldSubKeyd_id"));
                rowObj.put("d_id", val);
            }
            if (dataMap.containsKey("formmodeid")) {//模块id
                String val = StringHelper.null2String(dataMap.get("formmodeid"));
                rowObj.put("formmodeid", val);
            }
            for (int j = 0; j < list.size(); j++) {
                Map<String, String> tempMap = list.get(j);
                String colname = tempMap.get("column");
                String val = StringHelper.null2String(dataMap.get(colname));
                String span = StringHelper.null2String(dataMap.get(colname + "span"));
                String castKey = colname;
                if (relateMap.containsKey(colname)) {
                    castKey = relateMap.get(colname);
                    rowObj.put(castKey, val);
                    rowObj.put(castKey + "span", span);
                }
                rowObj.put("isModify", false);//是否修改过
            }
            datasArray.add(rowObj);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("columns", columnsArray);
        result.put("datas", datasArray);
        result.put("totalPage", totalPage);
        result.put("pageSize", pageSize);
        result.put("count", count);
        result.put("dataKey", dataKey);
        result.put("allColWidth", allWidth > 100 ? allWidth : 100);
        return result;
    }


    public Map<String, Object> saveBatchEidtData(HttpServletRequest request, HttpServletResponse response) throws ServletException, InstantiationException, IllegalAccessException, ClassNotFoundException {
        RecordSet rs = new RecordSet();
        Map<String, Object> result = new HashMap<String, Object>();
        String sql = "select a.modeid,a.formid,a.detailtable,b.tablename,b.detailkeyfield from mode_customsearch a left join workflow_bill b on a.formid=b.id where a.id=" + customId;
        rs.execute(sql);
        if (rs.next()) {
            String clientaddress = request.getRemoteAddr();
            int modeid = 0;
            int formid = 0;
            String tablename = "";
            String detailtable = "";
            String detailkeyfield = "";
            modeid = Util.getIntValue(rs.getString("modeid"), 0);
            formid = Util.getIntValue(rs.getString("formid"), 0);
            tablename = Util.null2String(rs.getString("tablename"));
            detailtable = Util.null2String(rs.getString("detailtable"));
            detailkeyfield = Util.null2String(rs.getString("detailkeyfield"));

            CustomSearchService customSearchService = new CustomSearchService();
            List<Map<String, Object>> editableFields = customSearchService.getEditableFieldsById(customId);
            Map<String, Object> editableFieldMap = new HashMap<String, Object>();
            AbstractPluginElement pluginElement = null;
            for (int i = 0; i < editableFields.size(); i++) {
                Map<String, Object> editableField = editableFields.get(i);
                int fieldId = Util.getIntValue(Util.null2String(editableField.get("id")), 0);
                String fieldhtmltype = Util.null2String(editableField.get("fieldhtmltype"));
                String fieldtype = Util.null2String(editableField.get("type"));
                String elementClassName = PluginElementClassName.getElementClassName(fieldhtmltype, fieldtype);
                pluginElement = (AbstractPluginElement) Class.forName(elementClassName).newInstance();
                String pluginName = pluginElement.getEditPluginName(fieldId);
                editableFieldMap.put(pluginName, editableField);
            }

            boolean batchEditResult = false;
            StringBuffer batchEditMessage = new StringBuffer();
            CustomSearchBatchEditUtil customSearchBatchEditUtil = new CustomSearchBatchEditUtil();
            Map<String, Object> needLogFieldsMap = customSearchBatchEditUtil.getNeedLogFields(formid);
            Map<String, Object> modeRightRelatedFieldsMap = new HashMap<String, Object>();
            String modifiedRows = Util.null2String(request.getParameter("modifiedRows"));
            JSONObject modifiedRowsObject = (JSONObject) JSONObject.toJSON(modifiedRows);
            JSONObject mRows = modifiedRowsObject.getJSONObject("mrows");
            Iterator rowKey = mRows.keySet().iterator();
            List<String> billList = new ArrayList<String>();
            List<Integer> modeList = new ArrayList<Integer>();
            while (rowKey.hasNext()) {
                String rowIndex = Util.null2String(rowKey.next());
                JSONObject rowObject = mRows.getJSONObject(rowIndex);
                String updateMaintableSql = "";
                String updateDetailtableSql = "";
                boolean ismodifyneedlogfield = false;
                boolean ismodifyRelatedField = false;
                customSearchBatchEditUtil.resetNeedLogFields(needLogFieldsMap);
                String mainid = Util.null2String(rowObject.get("id"));//主表id
                String detailid = Util.null2String(rowObject.get("d_id"));//明细表id，当主表存在数据，明细表没有数据时可能值为空
                JSONArray fields = rowObject.getJSONArray("fields");
                modeid = Util.getIntValue(rowObject.getString("formmodeid"));//模块id
                if (modeid == 0) continue;
                Map<String, Object> rightRelatedFieldsMap = null;
                if (modeRightRelatedFieldsMap.containsKey(String.valueOf(modeid))) {
                    rightRelatedFieldsMap = (Map<String, Object>) modeRightRelatedFieldsMap.get(String.valueOf(modeid));
                } else {
                    rightRelatedFieldsMap = customSearchBatchEditUtil.getRightRelatedFields(modeid);
                    modeRightRelatedFieldsMap.put(String.valueOf(modeid), rightRelatedFieldsMap);
                }

                Map<String, Object> modifyRightRelatedFieldsMap = new HashMap<String, Object>();

                Vector mainV = new Vector();
                Vector detailV = new Vector();

                for (int j = 0; j < fields.size(); j++) {
                    JSONObject fieldObject = fields.getJSONObject(j);
                    String pluginName = Util.null2String(fieldObject.get("pluginname"));//
                    if (!editableFieldMap.containsKey(pluginName)) continue;
                    Map<String, Object> editableField = (Map<String, Object>) editableFieldMap.get(pluginName);
                    String fieldId = Util.null2String(editableField.get("id"));
                    String fieldName = Util.null2String(editableField.get("fieldname"));
                    String fieldHtmlType = Util.null2String(editableField.get("fieldhtmltype"));
                    String fieldType = Util.null2String(editableField.get("type"));
                    String fieldDbType = Util.null2String(editableField.get("fielddbtype"));
                    int viewtype = Util.getIntValue(Util.null2String(editableField.get("viewtype")), 0);

                    String fieldValue = Util.null2String(fieldObject.get("value"));//新值
                    String oldFieldValue = Util.null2String(fieldObject.get("oval"));//旧值
                    Object realFieldValue = customSearchBatchEditUtil.AnalyzeStorageValue(fieldValue, fieldHtmlType, fieldType, fieldDbType);
                    boolean isEqualVal = customSearchBatchEditUtil.judgeEqualFieldValue(fieldHtmlType, fieldType, oldFieldValue, realFieldValue, fieldValue);
                    if (isEqualVal) continue;
                    //记录日志字段
                    if (needLogFieldsMap.containsKey(fieldId)) {
                        JSONObject jsonObject = (JSONObject) needLogFieldsMap.get(fieldId);
                        jsonObject.put("nfieldvalue", realFieldValue);
                        jsonObject.put("ofieldvalue", oldFieldValue);
                        ismodifyneedlogfield = true;
                    }

                    //判断权限关联字段值是否改变
                    if (rightRelatedFieldsMap.containsKey(fieldId)) {
                        modifyRightRelatedFieldsMap.put(fieldId, rightRelatedFieldsMap.get(fieldId));
                        ismodifyRelatedField = true;
                    }

                    if (viewtype == 0) {
                        updateMaintableSql += "," + fieldName + "=?";
                        mainV.addElement(realFieldValue);
                    } else {
                        if (!"".equals(detailid)) {
                            updateDetailtableSql += "," + fieldName + "=?";
                            detailV.addElement(realFieldValue);
                        } else {
                            updateDetailtableSql += "," + fieldName + "";
                            detailV.addElement(realFieldValue);
                        }
                    }
                }

                if (mainV.size() > 0) {
                    updateMaintableSql = updateMaintableSql.substring(1);
                    updateMaintableSql = "update " + tablename + " set " + updateMaintableSql + " where id = " + mainid;
                    boolean uResult = rs.executeSql(updateMaintableSql, false, mainV);
                }
                String newDetailId = "";
                if (detailV.size() > 0) {
                    if (!"".equals(detailid)) {
                        updateDetailtableSql = updateDetailtableSql.substring(1);
                        updateDetailtableSql = "update " + detailtable + " set " + updateDetailtableSql + " where id = " + detailid;
                        rs.executeSql(updateDetailtableSql, false, detailV);
                    } else {
                        String modeuuid = UUID.randomUUID().toString();
                        updateDetailtableSql = "insert into " + detailtable + " (mainid" + updateDetailtableSql + ",modeuuid) values (" + mainid;
                        for (int k = 0; k < detailV.size(); k++) {
                            updateDetailtableSql += ",?,'" + modeuuid + "'";
                        }
                        updateDetailtableSql += ")";
                        rs.executeSql(updateDetailtableSql, false, detailV);
                        rs.executeQuery("select id from " + detailtable + " where modeuuid=?", modeuuid);
                        if (rs.next()) {
                            newDetailId = Util.null2String(rs.getString("id"));
                        }
                    }
                }

                if (ismodifyneedlogfield) {
                    customSearchBatchEditUtil.saveLogFieldsModifyInfo(Util.getIntValue(mainid, 0), detailid, newDetailId, modeid, user, clientaddress, "5", needLogFieldsMap);
                }

                if (!billList.contains(mainid)) {
                    billList.add(mainid);
                    modeList.add(modeid);
                }
            }

            for (int i = 0; i < billList.size(); i++) {
                String bid = billList.get(i);
                int mid = modeList.get(i);
                ModeRightInfo ModeRightInfo = new ModeRightInfo();
                ModeRightInfo.rebuildModeDataShareByEdit(-1, mid, Util.getIntValue(bid, 0));
            }
            result.put("s", 1);
        }
        return result;
    }


    public Map getSearchFieldBase(int customid, User user) {
        RecordSet RecordSet = new RecordSet();
        RecordSet rs = new RecordSet();
        String detailfieldAlias = "d_";
        String formID = "0";
        String modeid = "0";
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        rs.execute("select a.*,b.tablename,b.detailkeyfield from mode_customsearch a left join workflow_bill b on a.formid=b.id where a.id=" + customid);
        String vdatasource = "";
        String vprimarykey = "";
        boolean isVirtualForm = false;
        int num = 0;
        double allWidth = 0;
        if (rs.next()) {
            formID = Util.null2String(rs.getString("formid"));
            modeid = "" + Util.getIntValue(rs.getString("modeid"), 0);
            isVirtualForm = VirtualFormHandler.isVirtualForm(formID);    //是否是虚拟表单
            Map<String, Object> vFormInfo = new HashMap<String, Object>();
            if (isVirtualForm) {
                vFormInfo = VirtualFormHandler.getVFormInfo(formID);
                vdatasource = Util.null2String(vFormInfo.get("vdatasource"));    //虚拟表单数据源
                vprimarykey = Util.null2String(vFormInfo.get("vprimarykey"));    //虚拟表单主键列名称
            }
            String istagsetsql = "select istagset from modeinfo where id=?";
            String istagset = "0";
            rs.executeQuery(istagsetsql, modeid);
            if (rs.next()) {
                istagset = Util.null2String(rs.getString("istagset"));
            }
            String sqlwhere = " and Mode_CustomDspField.isshow='1' ";
            String sql = "select isorder,ColWidth,workflow_billfield.id as id,workflow_billfield.fieldname as name,Mode_CustomDspField.shownamelabel as label" +
                    ",workflow_billfield.fielddbtype as dbtype ,workflow_billfield.fieldhtmltype as httype, workflow_billfield.type as typeTemp" +
                    ",Mode_CustomDspField.showorder,Mode_CustomDspField.istitle,Mode_CustomDspField.isstat,Mode_CustomDspField.showmethod" +
                    ",viewtype,workflow_billfield.detailtable,Mode_CustomDspField.ismaplocation,Mode_CustomDspField.editable" +
                    " from workflow_billfield,Mode_CustomDspField,Mode_CustomSearch " +
                    " where Mode_CustomDspField.customid=Mode_CustomSearch.id and Mode_CustomSearch.id=" + customid +
                    "  " + sqlwhere + " and workflow_billfield.billid=" + formID + "  and   workflow_billfield.id=Mode_CustomDspField.fieldid" +
                    " union select isorder,ColWidth,Mode_CustomDspField.fieldid as id,'1' as name,2 as label,'3' as dbtype, '4' as httype,5 as typeTemp " +
                    ",Mode_CustomDspField.showorder,Mode_CustomDspField.istitle,Mode_CustomDspField.isstat,Mode_CustomDspField.showmethod" +
                    ",0 as viewtype,'' as detailtable,Mode_CustomDspField.ismaplocation,Mode_CustomDspField.editable" +
                    " from Mode_CustomDspField ,Mode_CustomSearch" +
                    " where Mode_CustomDspField.customid=Mode_CustomSearch.id and Mode_CustomSearch.id=" + customid +
                    "  " + sqlwhere + "  and Mode_CustomDspField.fieldid<0" +
                    " order by showorder,id asc";
            RecordSet.execute(sql);
            RecordSet.beforFirst();

            while (RecordSet.next()) {
                String text = "";
                String column = "";
                String fieldid = Util.null2String(RecordSet.getString("id"));
                String editable = Util.null2String(RecordSet.getString("editable"));
                double colwidth = Util.getDoubleValue(RecordSet.getString("colwidth"));
                if (colwidth > 0) {
                    num++;
                    allWidth += colwidth;
                } else {
                    colwidth = 0;
                }
                int viewtype = RecordSet.getInt("viewtype");
                Map<String, String> map = new HashMap<String, String>();
                if (RecordSet.getString("id").equals("-1")) {
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(722, user.getLanguage()));
                    column = "modedatacreatedate";
                } else if (RecordSet.getString("id").equals("-2")) {
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(882, user.getLanguage()));
                    column = "modedatacreater";
                } else if (RecordSet.getString("id").equals("-3")) {
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(81287, user.getLanguage()));
                    column = "id";
                } else if (RecordSet.getString("id").equals("-4")) { //标签
                    if (!"1".equals(istagset)) {
                        continue;
                    }
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(176, user.getLanguage()));
                    column = "modelableid";
                } else if (RecordSet.getString("id").equals("-5")) { //草稿
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(385992, user.getLanguage()));
                    column = "modedatastatus";
                } else if (RecordSet.getString("id").equals("-6")) {//操作列
                    continue;
                } else if (RecordSet.getString("id").equals("-7")) {
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(3002, user.getLanguage()));
                    column = "modedatamodifier";
                } else if (RecordSet.getString("id").equals("-8")) {
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(516915, user.getLanguage()));
                    column = "modedatamodifydatetime";
                } else if (RecordSet.getString("id").equals("-9")) {//密级信息
                    text = Util.toHtmlForSplitPage(SystemEnv.getHtmlLabelName(526505, user.getLanguage()));
                    column = "seclevel";
                } else {
                    String name = RecordSet.getString("name");
                    String label = RecordSet.getString("label");
                    String fieldAlias = name;
                    if (viewtype == 1) {
                        fieldAlias = detailfieldAlias + name;
                    }
                    if (isVirtualForm) {
                        if (modeid.equals("0")) {
                            modeid = "virtual";
                        }
                    }
                    label = SystemEnv.getHtmlLabelName(Util.getIntValue(label), user.getLanguage());
                    text = Util.toHtmlForSplitPage(label);
                    column = fieldAlias;
                }
                map.put("id", fieldid);
                map.put("text", text);
                map.put("column", column);
                map.put("editable", editable + "");
                map.put("colwidth", colwidth + "");
                map.put("viewtype", viewtype + "");
                if (!RecordSet.getString("id").equals("-9")) {
                    map.put("htmltype", RecordSet.getString("httype"));
                    map.put("type", RecordSet.getString("typeTemp"));
                    map.put("dbtype", RecordSet.getString("dbtype"));
                } else {
                    map.put("htmltype", "5");
                    map.put("type", "1");
                    map.put("dbtype", "integer");
                }

                list.add(map);
            }
        }

        double avgwidth = 0;
        if (num > 0) {
            avgwidth = allWidth / (num * 1.0);
        } else {
            if (list.size() < 10 && list.size() > 0) {
                avgwidth = 100.0 / (list.size() * 1.0);
            } else {
                avgwidth = 10.0;
            }
        }
        allWidth = (list.size() - num) * avgwidth + allWidth;

        Map map = new HashMap();
        map.put("resultList", list);
        map.put("vdatasource", vdatasource);
        map.put("vprimarykey", vprimarykey);
        map.put("avgwidth", avgwidth);
        map.put("allWidth", allWidth);
        return map;
    }

    private String getTreeFieldStr(HttpServletRequest request, HttpServletResponse response) {
        String treenodeid = Util.null2String(request.getParameter("treenodeid"));
        if ("".equals(treenodeid)) {
            return null;
        }
        String customTreeDataId = Util.null2String(request.getParameter("customTreeDataId"));
        String treeconvalue = "";
        if (customTreeDataId.length() > treenodeid.length() + 1) {
            treeconvalue = customTreeDataId.substring(treenodeid.length() + 1);
        }
        RecordSet rs = new RecordSet();
        String treeFieldStr = "";
        if (!treenodeid.equals("")) {
            String treeFieldSql = "select d.id,d.fieldname from mode_customtreedetail a ,mode_customsearch b,modeinfo c,workflow_billfield d where a.id=? and a.hreftype=3 and a.hrefid=b.id and b.modeid=c.id and c.formid=d.billid and UPPER(a.hrefrelatefield)=UPPER(d.fieldname) and (d.detailtable is null or d.detailtable='')";
            rs.executeQuery(treeFieldSql, treenodeid);
            if (rs.next()) {
                String fieldid = rs.getString("id");
                if (!StringHelper.isEmpty(treeconvalue)) {
                    treeFieldStr = "&field" + fieldid + "=" + treeconvalue;
                }
            }
        }
        return treeFieldStr;
    }

    private boolean getHasKeySearch(String customId) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select 1 from mode_customdspfield where iskey = '1' and customid = ? ", customId);
        return rs.next();
    }

    private boolean isInnerTab(String tabid) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select tabshowtype from mode_pageexpand where id = ? ", tabid);
        if (rs.next()) {
            return "1".equals(rs.getString(1));
        }
        return false;
    }

    private String getRealDetailTable(String detailTable) {

        RecordSet rs = new RecordSet();
        rs.executeQuery("SELECT COUNT(1) cou FROM workflow_billdetailtable WHERE tablename=?", detailTable);
        if (rs.next() && rs.getInt("cou") > 0) {
            return detailTable;
        } else {
            rs.executeUpdate("update mode_customSearch set detailtable='' where id=?", this.customId);
        }

        return "";
    }

    public List<TabPane> getSearchGroupTabs(String customId, User user) {
        if (isBackUpData) {
            return null;
        }
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        String sql = " select fieldid from mode_customDspField where customid = ? and isgroup  = '1' ";
        rs.executeQuery(sql, customId);
        if (rs.next()) {
            String fieldid = Util.null2String(rs.getString(1));
            String groupsql = "select * from customfieldgroupsetting where customid=" + customId + " and fieldid=" + fieldid;
            rs.executeSql(groupsql);
            int getid = 0;
            while (rs.next()) {
                getid++;
                break;
            }
            List<TabPane> tabs = new ArrayList<TabPane>();
            Map<String, Object> groupParams = new HashMap<>();
            if (getid == 0) {
                List<SearchConditionOption> options = new CubeFieldService().getWorkflowSelectItems(fieldid, user.getLanguage(), false);
                TabPane pane = new TabPane();
                int groupInt = 0;
                pane.setTitle(SystemEnv.getHtmlLabelName(332, user.getLanguage()));//全部
                pane.setKey("all");
                tabs.add(pane);
                String myadviceBz = "";
                String myadvice = "";
                rs1.execute("  select count(*) from workflow_bill where tablename = 'uf_cubeset'");
                if (rs1.next()) {
                    int count = Util.getIntValue(rs1.getCounts());
                    if (count == 1) {
                        rs1.execute(" select z from uf_cubeset where bs = 'myadvice:" + customId + "'");
                        if (rs1.next()) {
                            myadvice = Util.null2String(rs1.getString("z"), "");
                        }
                    }
                }
                if (myadvice.equals("1")) {
                    pane = new TabPane();
                    pane.setTitle("我的建议");//我的建议
                    pane.setKey("myadvice");
                    tabs.add(pane);
                }
                for (SearchConditionOption option : options) {
                    pane = new TabPane();
                    pane.setKey(option.getKey());
                    pane.setTitle(option.getShowname());
                    groupInt++;
                    groupParams.put(groupInt + "", option.getKey());
                    tabs.add(pane);
                }
                if (null != this.grouptype && !this.grouptype.equals("")) {
                    String groupValue = (String) groupParams.get(this.grouptype);
                    if (null != groupValue && !groupValue.equals("")) {
                        if (groupValue.equals("1000")) {
                            selectedKey = "all";
                        } else if (groupValue.equals("999")) {
                            selectedKey = "myadvice";
                        } else {
                            selectedKey = groupValue;
                        }
                    } else if (this.grouptype.equals("0")) {
                        selectedKey = "all";
                    }
                }
                pane = new TabPane();
                pane.setTitle(SystemEnv.getHtmlLabelName(81307, user.getLanguage()));//未分组
                pane.setKey("empty");
                tabs.add(pane);
            } else {
                /*Map<String,String> map = new HashMap();
                rs.executeQuery("select t.SELECTVALUE,t.SELECTNAME from workflow_SelectItem t where t.FIELDID= ? ", fieldid);
                while(rs.next()){
                    map.put(Util.null2String(rs.getString("SELECTVALUE")),Util.null2String(rs.getString("SELECTNAME")));
                }*/
                StringBuffer buffersql = new StringBuffer();
                buffersql.append(" select b.id, b.customid,b.fieldid,b.fieldoptvalue,a.selectname fieldoptvaluespan, b.isshow,b.isdefault, b.fieldcolor");
                buffersql.append(" from customfieldgroupsetting b");
                buffersql.append(" LEFT JOIN  workflow_SelectItem a");
                buffersql.append(" on a.fieldid = " + fieldid);
                buffersql.append(" and a.isbill = 1");
                buffersql.append(" and a.fieldid = b.fieldid");
                buffersql.append(" and a.selectvalue = b.fieldoptvalue");
                buffersql.append(" where b.fieldid = " + fieldid);
                buffersql.append(" and b.fieldoptvalue not in (select t1.selectValue from workflow_selectitem t1 where t1.fieldid = " + fieldid + " and t1.cancel = '1' )  ");
                buffersql.append(" and b.customid = " + customId + " order by b.orderid");
                //sql.append("select * from customfieldgroupsetting where customid=? and fieldid=? order by id");
                rs.executeSql(buffersql.toString());
                TabPane pane = new TabPane();
                int groupInt = 0;
                while (rs.next()) {
                    String fieldoptvalue = Util.null2String(rs.getString("fieldoptvalue"));
                    String isshow = Util.null2String(rs.getString("isshow"));
                    String isdefault = Util.null2String(rs.getString("isdefault"));
                    String fieldcolor = Util.null2String(rs.getString("fieldcolor"));
                    if (isshow.equals("1")) {
                        pane = new TabPane();
                        if (fieldoptvalue.equals("1000")) {
                            pane.setTitle(SystemEnv.getHtmlLabelName(332, user.getLanguage()));//全部
                            pane.setKey("all");
                        } else if (fieldoptvalue.equals("999")) {
                            pane.setTitle("我的建议");//我的建议
                            pane.setKey("myadvice");
                        } else {
                            pane.setTitle(Util.null2String(rs.getString("fieldoptvaluespan")));
                            pane.setKey(fieldoptvalue);
                        }
                        if (isdefault.equals("1")) {
                            if (fieldoptvalue.equals("1000")) {
                                selectedKey = "all";
                            } else if (fieldoptvalue.equals("999")) {
                                selectedKey = "myadvice";
                            } else {
                                selectedKey = fieldoptvalue;
                            }
                        }
                        if (isshow.equals("1")) {
                            groupParams.put(groupInt + "", fieldoptvalue);
                            groupInt++;
                        }
                        pane.setColor(fieldcolor);
                        tabs.add(pane);
                    }
                }
                if (null != this.grouptype && !this.grouptype.equals("")) {
                    String groupValue = (String) groupParams.get(this.grouptype);
                    if (null != groupValue && !groupValue.equals("")) {
                        if (groupValue.equals("1000")) {
                            selectedKey = "all";
                        } else if (groupValue.equals("999")) {
                            selectedKey = "myadvice";
                        } else {
                            selectedKey = groupValue;
                        }
                    }
                }
                String selectsql = "select a.SELECTVALUE,a.SELECTNAME from workflow_SelectItem a where a.fieldid = ? and (a.cancel is null or a.cancel = '0' )  and a.SELECTVALUE not in " +
                        "(select b.fieldoptvalue from customfieldgroupsetting b where b.fieldid = ?  and b.customid = ?) ORDER BY a.SELECTVALUE";
                rs.executeQuery(selectsql, fieldid, fieldid, customId);
                while (rs.next()) {
                    pane = new TabPane();
                    pane.setTitle(Util.null2String(rs.getString("SELECTNAME")));
                    pane.setKey(Util.null2String(rs.getString("SELECTVALUE")));
                    tabs.add(pane);
                }
                pane = new TabPane();
                pane.setTitle(SystemEnv.getHtmlLabelName(81307, user.getLanguage()));//未分组
                pane.setKey("empty");
                tabs.add(pane);
            }
            return tabs;
        }
        return null;
    }

    public boolean getCheckAdvanedData(HttpServletRequest request) {    //验证高级查询是否有未填项
        boolean checkAdvanedData = true;
        RecordSet rs = new RecordSet();
        RecordSet qRs = new RecordSet();
        String customid = Util.null2String(request.getParameter("customid"));
        String boardid = Util.null2String(request.getParameter("boardid"));
        if ("".equals(customid) && !"".equals(boardid)) customid = Util.null2String(request.getAttribute("customid"));
        String isNewTableSearch = Util.null2String(request.getParameter("isNewTableSearch"));
        String canIsQuickSearch = Util.null2String(request.getParameter("canIsQuickSearch"));
        boolean isQuick = false;
        String quickSql = "select * from mode_quicksearch_setting where isquicksearch=1 and customid=?";
        qRs.executeQuery(quickSql, customid);
        if (qRs.next()) {
            isQuick = true;
        }
        if ("1".equals(isNewTableSearch)) {

            String sql = "select a.fieldid,a.requiredCon,a.isquery,a.isadvancedquery,w.fieldhtmltype,w.type typeTemp from mode_customdspfield a,workflow_billfield w where customid=? and a.fieldid=w.id ";
            List args = new ArrayList();
            args.add(customid);
            CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
            ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
            String formId = Util.null2String(customSearchComInfo.getFormId(customid));
            boolean isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
            if (!isVirtualForm) {
                sql += "UNION ALL ";
                sql += "SELECT a.fieldid,a.requiredCon,a.isquery,a.isadvancedquery,'3',";
                sql += "CASE WHEN fieldid=-1 THEN 2 ELSE 1 END FROM mode_customdspfield a ";
                sql += "WHERE customid=? AND fieldid IN(-1,-2,-7,-8,-9)";

                args.add(customid);
            }
            rs.executeQuery(sql, args);

            while (rs.next()) {
                String fieldid = Util.null2String(rs.getString("fieldid"));
                String field = fieldid;
                String isquery = Util.null2String(rs.getString("isquery"));
                String fieldhtmltype = Util.null2String(rs.getString("fieldhtmltype"));
                String type = Util.null2String(rs.getString("typeTemp"));
                if ("-1".equals(fieldid) || "-2".equals(fieldid) || "-3".equals(fieldid) || "-4".equals(fieldid) || "-5".equals(fieldid) || "-7".equals(fieldid) || "-8".equals(fieldid)) {
                    fieldid = "con_" + fieldid.replace("-", "");
                } else {
                    fieldid = "con_" + fieldid;
                }
                String requiredCon = Util.null2String(rs.getString("requiredCon"));
                if ("1".equals(requiredCon)) {
                    String isnull = Util.null2String(request.getParameter(fieldid));
                    if ("1".equals(isquery)) {
                        if ("3".equals(fieldhtmltype) && "1".equals(type)) {    //人力资源
                            if ("-5".equals(isnull) || "-5,".equals(isnull)) {
                                isnull = "";
                            }
                        } else if ("3".equals(fieldhtmltype) && "2".equals(type)) { //日期
                            if ("6".equals(isnull) || "6,,".equals(isnull)) {
                                isnull = "";
                            }
                        }
                        if ("".equals(isnull) || null == isnull) {
                            checkAdvanedData = false;
                            return checkAdvanedData;
                        }
                    } else if ("1".equals(canIsQuickSearch) || isQuick) {
                        RecordSet recordSet = new RecordSet();
                        recordSet.executeQuery("select * from mode_quicksearch_condition where customid=? and fieldid=?", customid, field);
                        if (recordSet.next()) {
                            String fid = Util.null2String(recordSet.getString("fieldid"));
                            fid = Math.abs(Integer.parseInt(fid)) + ""; //系统字段id取绝对值
                            isnull = Util.null2String(request.getParameter(fid));
                            if (StringUtils.isEmpty(isnull)) {
                                isnull = Util.null2String(request.getParameter("con_" + fid));
                            }

                            if ("3".equals(fieldhtmltype) && "1".equals(type)) {     //人力资源
                                String[] nulls = isnull.split(",");
                                if (nulls.length < 2) {
                                    if ("-5".equals(nulls[0])) {
                                        isnull = "";
                                    }
                                } else {
                                    if ("-5".equals(nulls[0]) && "".equals(nulls[1])) {
                                        isnull = "";
                                    }
                                }
                            } else if ("3".equals(fieldhtmltype) && "2".equals(type)) { //日期
                                if ("6".equals(isnull) || "6,,".equals(isnull)) {
                                    isnull = "";
                                }
                            }
                            if ("".equals(isnull) || null == isnull || isnull.equals("-1")) {
                                checkAdvanedData = false;
                                return checkAdvanedData;
                            }
                        }
                    }
                }
            }
        } else {

            String sql = "select a.fieldid,a.requiredCon,a.isquery,a.isadvancedquery,w.fieldhtmltype,w.type typeTemp from mode_customdspfield a,workflow_billfield w where customid=? and a.fieldid=w.id ";
            List args = new ArrayList();
            args.add(customid);
            CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
            ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
            String formId = Util.null2String(customSearchComInfo.getFormId(customid));
            boolean isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
            if (!isVirtualForm) {
                sql += "UNION ALL ";
                sql += "SELECT a.fieldid,a.requiredCon,a.isquery,a.isadvancedquery,'3',";
                sql += "CASE WHEN fieldid=-1 THEN 2 ELSE 1 END FROM mode_customdspfield a ";
                sql += "WHERE customid=? AND fieldid IN(-1,-2,-7,-8,-9)";

                args.add(customid);
            }
            rs.executeQuery(sql, args);
            while (rs.next()) {
                String fieldid = Util.null2String(rs.getString("fieldid"));
                String field = fieldid;
                String isquery = Util.null2String(rs.getString("isquery"));
                String fieldhtmltype = Util.null2String(rs.getString("fieldhtmltype"));
                String type = Util.null2String(rs.getString("typeTemp"));
                if ("-1".equals(fieldid) || "-2".equals(fieldid) || "-3".equals(fieldid) || "-4".equals(fieldid) || "-5".equals(fieldid) || "-7".equals(fieldid) || "-8".equals(fieldid)) {
                    fieldid = "con_" + fieldid.replace("-", "");
                } else {
                    fieldid = "con_" + fieldid;
                }
                String requiredCon = Util.null2String(rs.getString("requiredCon"));
                if ("1".equals(requiredCon)) {
                    String isnull = Util.null2String(request.getParameter(fieldid));
                    if ("1".equals(isquery)) {
                        boolean isHrm = false;//如果是人员字段 其值可能是-1
                        if ("3".equals(fieldhtmltype) && "1".equals(type)) {
                            isHrm = true;
                            if ("-5".equals(isnull)) {
                                isnull = "";
                            }
                        } else if ("3".equals(fieldhtmltype) && "2".equals(type)) { //日期
                            if ("6".equals(isnull) || "6,,".equals(isnull)) {
                                isnull = "";
                            }
                        } else if ("1".equals(fieldhtmltype) && !"1".equals(type)) {
                            if (",".equals(isnull)) {
                                isnull = "";
                            }
                        }
                        if ("".equals(isnull) || null == isnull || isnull.equals("-1") && !isHrm) {
                            checkAdvanedData = false;
                            return checkAdvanedData;
                        }
                    } else if ("1".equals(canIsQuickSearch) || isQuick) {
                        RecordSet recordSet = new RecordSet();
                        recordSet.executeQuery("select * from mode_quicksearch_condition where customid=? and fieldid=?", customid, field);
                        if (recordSet.next()) {
                            String fid = Util.null2String(recordSet.getString("fieldid"));
                            fid = "-1".equals(fid) ? "1" : "-2".equals(fid) ? "2" : fid;
                            isnull = Util.null2String(request.getParameter(fid));
                            if ("3".equals(fieldhtmltype) && "1".equals(type)) {     //人力资源
                                String[] nulls = isnull.split(",");
                                if (nulls.length < 2) {
                                    if ("-5".equals(nulls[0])) {
                                        isnull = "";
                                    }
                                } else {
                                    if ("-5".equals(nulls[0]) && "".equals(nulls[1])) {
                                        isnull = "";
                                    }
                                }
                            } else if ("3".equals(fieldhtmltype) && "2".equals(type)) { //日期
                                if ("6".equals(isnull) || "6,,".equals(isnull)) {
                                    isnull = "";
                                }
                            } else if ("1".equals(fieldhtmltype) && !"1".equals(type)) {
                                if (",".equals(isnull)) {
                                    isnull = "";
                                }
                            }
                            if ((!"0".equals(isquery)) && ("".equals(isnull) || null == isnull || isnull.equals("-1"))) {
                                checkAdvanedData = false;
                                return checkAdvanedData;
                            }
                        }
                    }
                }
            }
        }
        return checkAdvanedData;
    }

    private boolean getCheckQualSearch(HttpServletRequest request) {   //快捷搜索处是否有未填项
        boolean checkQualSearch = true;
        RecordSet rs = new RecordSet();
        String isQuickSearch = Util.null2String(request.getParameter("isQuickSearch"));
        String customid = Util.null2String(request.getParameter("customid"));

        String sql = "select a.fieldid,a.requiredCon,a.isquery,a.isadvancedquery,w.fieldhtmltype,w.type typeTemp from mode_customdspfield a,workflow_billfield w where customid=? and a.fieldid=w.id ";

        List args = new ArrayList();
        args.add(customid);
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        String formId = Util.null2String(customSearchComInfo.getFormId(customid));
        boolean isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        if (!isVirtualForm) {
            sql += "UNION ALL ";
            sql += "SELECT a.fieldid,a.requiredCon,a.isquery,a.isadvancedquery,'3',";
            sql += "CASE WHEN fieldid=-1 THEN 2 when fieldid=-8 then 290 ELSE 1 END FROM mode_customdspfield a ";
            sql += "WHERE customid=? AND fieldid IN(-1,-2,-7,-8,-9)";

            args.add(customid);
        }

        rs.executeQuery(sql, args);
        while (rs.next()) {
            String requiredCon = Util.null2String(rs.getString("requiredCon"));
            String fieldid = Util.null2String(rs.getString("fieldid"));
            String fieldhtmltype = Util.null2String(rs.getString("fieldhtmltype"));
            String type = Util.null2String(rs.getString("typeTemp"));
            if ("1".equals(requiredCon)) {
                if ("1".equals(isQuickSearch)) {
                    RecordSet recordSet = new RecordSet();
                    recordSet.executeQuery("select * from mode_quicksearch_condition where customid=? and fieldid=?", customid, fieldid);
                    if (recordSet.next()) {
                        String fid = Util.null2String(recordSet.getString("fieldid"));
                        String isnull = Util.null2String(request.getParameter(fid.replace("-", "")));
                        if ("3".equals(fieldhtmltype) && "1".equals(type)) {   //单人力资源
                            String[] nulls = isnull.split(",");
                            if (nulls.length < 2) {
                                if ("-5".equals(nulls[0])) {
                                    isnull = "";
                                }
                            } else {
                                if ("-5".equals(nulls[0]) && "".equals(nulls[1])) {
                                    isnull = "";
                                }
                            }
                        } else if ("3".equals(fieldhtmltype) && "2".equals(type)) { //日期
                            isnull = "".equals(isnull) ? "0" : isnull; // 如果是初始进入页面，日期默认值：“全部”，那么 isnull的值为空。所以这里修改为 -1
                            if ("6".equals(isnull) || "6,,".equals(isnull)) {
                                isnull = "";
                            }
                        } else if ("3".equals(fieldhtmltype) && "290".equals(type)) {//日期时间
                            if (isnull.trim().equals(",")) {
                                isnull = "";
                            }
                        }
                        if ("".equals(isnull) || null == isnull || isnull.equals("-1")) {
                            checkQualSearch = false;
                            return checkQualSearch;
                        }
                    }
                }
            }
        }
        return checkQualSearch;
    }

    private boolean getAdvanceCheck(HttpServletRequest request) {    //设置高级搜索的校验
        boolean checkAdvanedData = true;
//        String isQuickSearch = Util.null2String(request.getParameter("isQuickSearch"));
//        if("1".equals(isQuickSearch)){
//            return getCheckQualSearch(request);
//        }
        RecordSet rs = new RecordSet();
        String customid = Util.null2String(request.getParameter("customid"));
        rs.executeQuery("select fieldid,requiredCon,isquery,isadvancedquery from mode_customdspfield where customid=?", customid);
        while (rs.next()) {
            String fieldid = Util.null2String(rs.getString("fieldid"));
            String isadvancedquery = Util.null2String(rs.getString("isadvancedquery"));
            if ("-1".equals(fieldid) || "-2".equals(fieldid) || "-3".equals(fieldid) || "-4".equals(fieldid) || "-5".equals(fieldid) || "-7".equals(fieldid) || "-8".equals(fieldid) || "-9".equals(fieldid)) {
                fieldid = "con_" + fieldid.replace("-", "");
            } else {
                fieldid = "con_" + fieldid;
            }
            String requiredCon = Util.null2String(rs.getString("requiredCon"));
            if ("1".equals(requiredCon)) {
                String isnull = Util.null2String(request.getParameter(fieldid));
                if ("1".equals(isadvancedquery)) {
                    if ("".equals(isnull) || null == isnull) {
                        checkAdvanedData = false;
                        return checkAdvanedData;
                    }
                }
            }
        }
        return checkAdvanedData;
    }

    public Map<String, Object> getSplitBase(HttpServletRequest request, HttpServletResponse response) {
        RecordSet rs = new RecordSet();
        Map<String, Object> result = new HashMap<String, Object>();
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        String isOnlyQuick = Util.null2String(request.getParameter("isOnlyQuick"));
        String isTempSearch = Util.null2String(request.getParameter("isTempSearch"));
        this.viewType = Util.getIntValue(request.getParameter("viewtype"), 0);
        this.sort1141500 = Util.null2String(request.getParameter("sort1141500"));
        if (!"1".equals(isTempSearch)) {
            if ("1".equals(isOnlyQuick)) {
                this.isChexkAdvanedDate = getCheckQualSearch(request);
            } else if (this.viewType == 2) {
                this.isChexkAdvanedDate = getAdvanceCheck(request);
            } else {
                this.isChexkAdvanedDate = getCheckAdvanedData(request);
            }
        }
        int pageIndex = Util.getIntValue(request.getParameter("current"), 1);
        int treePageSize = Util.getIntValue(request.getParameter("pagesize"), 10);
        String menuIds = Util.null2String(request.getParameter("menuIds"));
        String rootids = Util.null2String(request.getParameter("rootids"));
        this.displayType = Util.null2String(request.getParameter("displayType"));
        boolean isExcel = this.displayType.equalsIgnoreCase("excel");
        this.user = HrmUserVarify.getUser(request, response);
        this.isEdit = Util.null2String(request.getParameter("isEdit")).equals("true") ? true : false;
        this.customId = Util.getIntValue(request.getParameter("customid"));
        String customId = "" + this.customId;
        this.formId = Util.getIntValue(customSearchComInfo.getFormId(customId), 0);
        String formId = "" + this.formId;
        int linemergel = Util.getIntValue(customSearchComInfo.getLinemergel(customId), 0);
        this.modeId = Util.getIntValue(customSearchComInfo.getModeId(customId), 0);
        this.noRightList = isBackUpData || "1".equals(customSearchComInfo.getNoRightList(customId));
        this.isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        this.tableName = modeFormComInfo.getTableName(formId);
        this.detailTable = getRealDetailTable(customSearchComInfo.getDetailTable(customId));
        this.pageNum = Util.getIntValue(customSearchComInfo.getPageNumber(customId));
        this.isCustom = Util.getIntValue(customSearchComInfo.getisCustom(customId));
        this.openType = customSearchComInfo.getOpenType(customId);
        this.sliderPercentage = customSearchComInfo.getSliderPercentage(customId);
        this.defaulSql = Util.toScreenToEdit(customSearchComInfo.getDefaultSql(customId), user.getLanguage());
        this.searchconditiontype = customSearchComInfo.getSearchConditionType(customId);
        this.javafilename = customSearchComInfo.getJavaFileName(customId);
        this.javafileaddress = customSearchComInfo.getJavaFileAddress(customId);
        this.grouptype = Util.null2String(request.getParameter("grouptype"));
        this.disquerycheck = Util.null2String(customSearchComInfo.getDisquerycheck(customId));
        if (isBackUpData) {
            this.defaulSql = "";
            if (!isCoverType) {
                this.disquerycheck = "1";
            } else {
                this.disquerycheck = "0";
            }
        }
        if (isVirtualForm) {
            this.dataSource = modeFormComInfo.getVDataSource(formId);
            String confirmSql = "select id  from datasourcesetting where pointid=\'" + dataSource + "\'";
            rs.execute(confirmSql);
            if (rs.getCounts() > 0 || "$ECOLOGY_SYS_LOCAL_POOLNAME".equals(dataSource)) {
                DataSourceXML dataSourceXML = new DataSourceXML();
                this.dataSourceDBType = dataSourceXML.getDataSourceDBType(dataSource);
                this.primaryKey = modeFormComInfo.getVPrimaryKey(formId);
                if ("2".equals(modeFormComInfo.getVformtype(this.formId))) {
                    String tempRealName = modeFormComInfo.getVsql(this.formId);
                    this.tableName = replaceParamBefUse(tempRealName);
                } else {
                    this.tableName = VirtualFormHandler.getRealFromName(tableName);
                }
            } else {
                result.put("error", SystemEnv.getHtmlLabelName(389199, user.getLanguage()));
                return result;
            }
        } else {
            this.dataSource = null;
            this.dataSourceDBType = rs.getDBType();
            this.dataSourceDBTypeOrgin = rs.getOrgindbtype();
            this.primaryKey = "id";
        }
        //---冻结列---
        int fixednumberForth = 0;
        int fixednumberBack = 0;
        int istreesearch = 0;
        rs.executeQuery("select appid,fixednumberforth,fixednumberback,istreesearch from mode_customsearch where id = ?", customId);
        if (rs.next()) {
            fixednumberForth = Util.getIntValue(rs.getString("fixednumberforth"), 0);
            fixednumberBack = Util.getIntValue(rs.getString("fixednumberback"), 0);
            istreesearch = Util.getIntValue(rs.getString("istreesearch"), 0);
        }
        String istagset = "0";
        boolean isfrontmultlang = false;
        //if (Util.isEnableMultiLang()) {
        String sql = "select istagset,isfrontmultlang from modeinfo where id=?";
        rs.executeQuery(sql, modeId);
        if (rs.next()) {
            istagset = Util.null2String(rs.getString("istagset"));
            isfrontmultlang = Util.isEnableMultiLang() ? "1".equals(rs.getString("isfrontmultlang")) : isfrontmultlang;
        }
        //}
        //是否开启列表未读，反馈标识功能,1未读、2反馈和3已读
        int enabled = Util.getIntValue(request.getParameter("enabled"), 0);
        SplitTableOperateBean operateBean = this.getOperateList();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        Map<String, Object> preCheck = modeRightInfo.preCheckUserRight(user, modeId);
        this.hasUserRight = isBackUpData || (boolean) preCheck.get("isview");
        if (isEdit) {//批量修改判断修改权限
            this.hasUserRight = (boolean) preCheck.get("isedit");
        }
        String backfields = "";
        backfields = this.getBackFields(rs);
        String sqlFrom = this.getSqlFrom();
        StringBuilder sqlWhere = new StringBuilder(this.getSqlWhere(request, response));
        if (hasUserRight && viewType != 3 && !noRightList && !isVirtualForm) {
            sqlWhere.append(ModeRightInfo.getStatusSqlWhere(SearchConstant.MAIN_TABLE_ALIAS, tableName, user.getUID()));
        }
        String orderby = this.getOrderBy();
        String countColumnsDbType = "";
        BoolAttr counttransmethod = BoolAttr.TRUE;
        String groupSqlWhere = this.getGroupSqlWhere(request, response);

//       Map<String, Object> groupCount = null;

        String datasqlwhere = Util.null2String(request.getParameter("datasqlwhere"));
        if (!datasqlwhere.equals("")) {
            try {
                BASE64Decoder decoder = new BASE64Decoder();
                datasqlwhere = new String(decoder.decodeBuffer(datasqlwhere), "UTF-8");
                datasqlwhere = URLDecoder.decode(datasqlwhere, "utf-8");

            } catch (Exception e) {
                // TODO: handle exception
            }
            sqlWhere.append(datasqlwhere.trim().startsWith("and") ? datasqlwhere : " and " + datasqlwhere);
        }
//        if(!StringHelper.isEmpty(this.groupName)&& !isBackUpData) {
//            groupCount = this.getGroupCount(sqlWhere, sqlFrom, dataSource);
//        }
        if (!StringHelper.isEmpty(groupSqlWhere)) {
            sqlWhere.append(" and ").append(groupSqlWhere);
        }
        //如果是批量修改只查询出正式数据
        if (this.isEdit && backfields.indexOf(SearchConstant.MAIN_TABLE_ALIAS + ".modedatastatus") > -1) {
            sqlWhere.append(" and (" + SearchConstant.MAIN_TABLE_ALIAS + ".modedatastatus is null or " + SearchConstant.MAIN_TABLE_ALIAS + " .modedatastatus<>1) ");
        }


        int pageSize = this.pageNum;
        String pageId = CubeUtil.getPageId(customId);
        if (isCustom == 1) {
            pageSize = Util.getIntValue(PageIdConst.getPageSize(pageId, user.getUID(), "formmode:pagenumber"), pageSize);
        }
        //qc442606 当pagesize为-1 修改默认为10
        if (pageSize < 0) {
            pageSize = 10;
        }
        SplitTableBean splitTableBean = new SplitTableBean();
        Checkboxpopedom checkboxpopedom = new Checkboxpopedom();
        List<SplitTableColBean> columns = new ArrayList<SplitTableColBean>();
        rs.beforFirst();
        StringBuffer allstatfield = new StringBuffer();
        //HashMap<String,Object> countColumnsDbType = new HashMap<String, Object>();
        String decimalFormat = "";
        boolean selectedDataIdField = false;
        List<String> editableFields = new ArrayList<String>();
        if (this.isEdit) {
            editableFields = FieldUtil.getEditableFieldsByCustomid(customId);
        }
        String allfieldids = "";
        Map<String, SplitTableColBean> allfieldMap = new HashMap<>();
        int clnconut = rs.getCounts();

        //获取锁定列列宽和，用于计算锁定列列宽
        int forthwidth = 0;
        int backwidth = 0;
        int windowWidth = Util.getIntValue(request.getParameter("windowWidth"));
        if (!(clnconut <= (fixednumberForth + fixednumberBack) || (fixednumberForth == 0 && fixednumberBack == 0))) {
            int forthtemp = fixednumberForth;
            int backtemp = fixednumberBack;
            int temp = 0;
            while (rs.next()) {
                int colwidth = Util.getIntValue(rs.getString("colwidth"));
                if (forthtemp > 0) {
                    forthwidth += colwidth;
                    forthtemp--;
                } else {
                    if (temp + backtemp >= clnconut) {
                        backwidth += colwidth;
                        backtemp--;
                    }
                }
                temp++;
            }
        }

        //自定义按钮的参数字段，没有勾选的时候也需要返回，放到前面，防止覆盖勾选了的字段
        for (String param : this.customButtonParameter) {
            SplitTableColBean splitTableColBean = new SplitTableColBean("15%", param, param, param);
            splitTableColBean.setDisplay("none");
            columns.add(splitTableColBean);
        }

        int i = 0;
        int treeDataWidth = 0;
        rs.beforFirst();
        while (rs.next()) {
            String fieldid = rs.getString("id");
            if ("-3".equals(fieldid)) {
                selectedDataIdField = true;
            }
            allfieldids = "," + fieldid;
            String isorder = rs.getString("isorder");
            String ordertype = rs.getString("ordertype");
            if ("a".equalsIgnoreCase(ordertype)) {
                ordertype = "ascend";
            } else {
                ordertype = "descend";
            }
            String showmethod = Util.null2String(rs.getString("showmethod"));
            if (this.isEdit) {
                showmethod = "0";
            }
            String colwidth = Util.null2String(rs.getString("colwidth"));
            treeDataWidth += Util.getIntValue(colwidth);
            int shownamelabel = Util.getIntValue(rs.getString("shownamelabel"), 0);
            String name = rs.getString("name");
            if (isVirtualForm) {
                if (primaryKey.equalsIgnoreCase(name)) {
                    selectedDataIdField = true;
                }
            }
            int label = Util.getIntValue(rs.getString("label"));
            if (shownamelabel != 0 && shownamelabel != -1) {
                label = shownamelabel;
            }
            String htmltype = rs.getString("httype");
            String type = rs.getString("typeTemp");
            String id = rs.getString("id");
            String dbtype = rs.getString("dbtype");
            String qfws = rs.getString("qfws");//千分位小数位数
            String istitle = rs.getString("istitle");
            String ismaplocation = Util.getIntValue(rs.getString("ismaplocation"), 0) + "";
            int field_viewtype = rs.getInt("viewtype");
            SplitTableColBean splitTableColBean = new SplitTableColBean();
            String encryptTable = tableName;
            if (field_viewtype == 1) {//明细
                encryptTable = detailTable;
            }
            //单行文本类型的,   多行文本  多行文本(html)
            if ("1".equals(htmltype) || "2".equals(htmltype) && "1".equals(type) || "2".equals(htmltype) && "2".equals(type)) {
                splitTableColBean.setTablename(encryptTable);
            }
            int alignment = Util.getIntValue(rs.getString("alignment"), 1);
            String hreflink = this.isEdit ? "" : Util.toHtmlForSplitPage(Util.null2String(rs.getString("hreflink")));
            String classname = "";
            if (alignment == 1) {
                classname = "";//左对齐
            } else if (alignment == 3) {
                classname = "column-text-right";//右对齐
            } else {
                classname = "column-text-center";//居中对齐
            }
            if (type.equals("2") && htmltype.equals("2")) {
                if (classname.equals("")) {
                    classname += "customWrap";
                } else {
                    classname += " customWrap";
                }
            }

            if (type.equals("1") && htmltype.equals("4")) {
                classname += " checkBox";
//                splitTableColBean.setHtmlType(Util.getIntValue(htmltype));   -- 暂不修改云商店代码。此处先拼接classname处理
//                splitTableColBean.setType(Util.getIntValue(type));
            }

            splitTableColBean.setClassName(classname);
            if (!"1".equals(istagset) && "-4".equals(fieldid)) {
                continue;
            } else {
                columns.add(splitTableColBean);
            }

            splitTableColBean.setWidth(colwidth);
            if (fixednumberForth > 0 || fixednumberBack > 0) {
                if (clnconut > (fixednumberForth + fixednumberBack) && i < fixednumberForth) {
                    splitTableColBean.setFixed("left");
                    if (StringHelper.isEmpty(colwidth) || "0".equals(colwidth)) {
                        colwidth = windowWidth * 0.05 + "px";
                    } else {
                        colwidth = fixednumberForth * 100 * Integer.valueOf(colwidth) / forthwidth + "px";
                    }
                } else if (clnconut > (fixednumberForth + fixednumberBack) && fixednumberBack >= clnconut - i) {
                    splitTableColBean.setFixed("right");
                    if (StringHelper.isEmpty(colwidth) || "0".equals(colwidth)) {
                        colwidth = windowWidth * 0.05 + "px";
                    } else {
                        colwidth = fixednumberBack * 100 * Integer.valueOf(colwidth) / backwidth + "px";
                    }
                } else {
                    splitTableColBean.setFixed("none");
                    if (StringHelper.isEmpty(colwidth) || "0".equals(colwidth)) {
                        colwidth = windowWidth * 0.05 + "px";
                    } else {
                        colwidth = Integer.valueOf(colwidth) * windowWidth / (100 - backwidth - forthwidth) + "px";
                    }
                }
                splitTableColBean.setWidth(colwidth);
            }

            if ("-1".equals(fieldid)) { //创建日期
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".modedatacreatedate," + MAIN_TABLE_ALIAS + ".modedatacreatetime");
                }
                if (label == 0) {
                    label = 722;
                }
                String text = Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()));
                text = Util.formatMultiLang(text.trim(), user.getLanguage() + "");
                splitTableColBean.setColumn("modedatacreatedate");
                splitTableColBean.setText(Util.toHtmlForSplitPage(text.trim()));
                splitTableColBean.setOtherpara("column:modedatacreatetime+" + customId + "+" + showmethod + "+column:" + primaryKey + "+" + formId);
                splitTableColBean.setTransmethod(isExcel ? ExcelTrans.getDateTime : "com.api.cube.util.CubeSearchTransMethodProxy.getSearchResultCreateTime");
            } else if ("-2".equals(fieldid)) { //创建人
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".modedatacreater");
                }
                if (label == 0) {
                    label = 882;
                }
                String text = Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()));
                text = Util.formatMultiLang(text.trim(), user.getLanguage() + "");
                splitTableColBean.setColumn("modedatacreater");
                splitTableColBean.setText(Util.toHtmlForSplitPage(text.trim()));
                splitTableColBean.setOtherpara("column:modedatacreatertype");
                splitTableColBean.setTransmethod(isExcel ? ExcelTrans.getHrmResource : "com.api.cube.util.CubeSearchTransMethodProxy.getSearchResultName");
            } /*else if ("-3".equals(fieldid)) { // 数据ID
                selectedDataIdField = true;
                if("1".equals(isorder)){
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS+"."+primaryKey);
                }
                if(label==0){
                    label = 81287;
                }

                splitTableColBean.setColumn(primaryKey);
                splitTableColBean.setText(Util.toHtmlForSplitPage(Util.null2String(SystemEnv.getHtmlLabelName(label,user.getLanguage()))));
                splitTableColBean.setOtherpara("column:dataid");
                splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getDataId");
            }*/ else if ("-4".equals(fieldid)) { //标签
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".modelableid");
                }
                if (label == 0) {
                    label = 176;
                }
                String text = Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()));
                text = Util.formatMultiLang(text.trim(), user.getLanguage() + "");
                splitTableColBean.setColumn("modelableid");
                splitTableColBean.setText(Util.toHtmlForSplitPage(text.trim()));
                splitTableColBean.setOtherpara("modeid:" + modeId);
                splitTableColBean.setTransmethod(isExcel ? ExcelTrans.getTagName : "com.api.cube.util.CubeSearchTransMethodProxy.getTabName");
            } else if ("-5".equals(fieldid)) {
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".modedatastatus");
                }
                if (label == 0) {
                    label = 385992;
                }
                splitTableColBean.setColumn("modedatastatus");
                splitTableColBean.setOtherpara(user.getLanguage() + "");
                splitTableColBean.setText(Util.toHtmlForSplitPage(Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()))));
                splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getModedatastatus");
            } else if ("-6".equals(fieldid)) {//操作列
                int num = 0;
                if (istreesearch == 1) {
                    num = getIsCustomButtonShowListForGroup(this.customId);
                } else {
                    num = getIsCustomButtonShowList(this.customId);
                }
                if (num > 0) {//存在自定义按钮且开启了在列表上直接显示
                    if (label == 0) {
                        label = 30585;//操作
                    }
                    String isshowlist = "isshowlist";
                    splitTableColBean.setColumn(isshowlist);
                    splitTableColBean.setText(Util.toHtmlForSplitPage(Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()))));
                    splitTableColBean.setOtherpara("column:" + primaryKey + "+" + customId + "+" + user.getUID() + "+" + modeId + "+" + formId);
                    splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getOperates");
                }
            } else if ("-7".equals(fieldid)) {//最后修改人
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".modedatamodifier");
                }
                if (label == 0) {
                    label = 3002;
                }
                String text = Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()));
                text = Util.formatMultiLang(text.trim(), user.getLanguage() + "");
                splitTableColBean.setColumn("modedatamodifier");
                splitTableColBean.setText(Util.toHtmlForSplitPage(text.trim()));
                splitTableColBean.setOtherpara("column:modedatacreatertype");
                splitTableColBean.setTransmethod(isExcel ? ExcelTrans.getHrmResource : "com.api.cube.util.CubeSearchTransMethodProxy.getSearchResultName");
            } else if ("-8".equals(fieldid)) {//最后修改时间
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".modedatamodifydatetime");
                }
                if (label == 0) {
                    label = 516915;
                }
                String text = Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()));
                text = Util.formatMultiLang(text.trim(), user.getLanguage() + "");
                splitTableColBean.setColumn("modedatamodifydatetime");
                splitTableColBean.setText(Util.toHtmlForSplitPage(text.trim()));
                splitTableColBean.setOtherpara("column:modedatamodifydatetime");
            } else if ("-9".equals(fieldid)) {//密级字段
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(MAIN_TABLE_ALIAS + ".seclevel,");
                }
                if (label == 0) {
                    label = 526505;
                }
                splitTableColBean.setColumn("seclevel");
                splitTableColBean.setOtherpara(user.getLanguage() + "+" + "column:modesecrettime");

                splitTableColBean.setText(Util.toHtmlForSplitPage(Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()))));
                if (!isExcel) {
                    splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getSeclevelLable");
                } else {
                    splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getSeclevelForExcel");
                }

            } else {
                String fieldname = (field_viewtype == 1 ? DETAIL_FIELD_ALIAS : "") + name;
                if ("-3".equals(fieldid)) {
                    fieldname = "id";
                    name = "id";
                	/*if(isVirtualForm){
                		fieldname="ID";
                	}*/
                    if (label == 0) {
                        label = 81287;
                    }
                }
                String orderkey = (field_viewtype == 1 ? DETAIL_TABLE_ALIAS : MAIN_TABLE_ALIAS) + "." + name;
                String text = Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()));
                text = Util.formatMultiLang(text.trim(), user.getLanguage() + "");
                // String text = LabelUtil.getMultiLangLabel(label+"");
                splitTableColBean.setText(Util.toHtmlForSplitPage(text));
                splitTableColBean.setColumn(fieldname);
                if ("1".equals(isorder)) {
                    splitTableColBean.setOrderkey(orderkey);
                }
                String formmodeid = "" + modeId;
                if (isVirtualForm) {
                    if (modeId == 0) {
                        formmodeid = "virtual";
                    }
                }
                if (viewType == 3) { //监控
                    splitTableColBean.setOtherpara("column:" + primaryKey + "+" + id + "+" + htmltype + "+" + type + "+" + user.getLanguage() + "+" + isbill + "+" + dbtype + "+" + istitle + "+" + formmodeid + "+" + formId + "+" + viewType + "+" + ismaplocation + "+" + openType + "+" + customId + "+fromsearchlist" + "+" + showmethod + (detailTable.equals("") ? "" : ("'+column:d_id+" + detailTable + "'")) + "+" + sliderPercentage + "+" + qfws + "+" + this.isEdit);
                } else {
                    splitTableColBean.setOtherpara("column:" + primaryKey + "+" + id + "+" + htmltype + "+" + type + "+" + user.getLanguage() + "+" + isbill + "+" + dbtype + "+" + istitle + "+" + formmodeid + "+" + formId + "+0+" + ismaplocation + "+" + openType + "+" + customId + "+fromsearchlist" + "+" + showmethod + "+" + user.getUID() + "+" + enabled + (detailTable.equals("") ? "" : ("'+column:d_id+" + detailTable + "'")) + "+" + sliderPercentage + "+" + qfws + "+" + this.isEdit);
                }
                if (isVirtualForm && ("modelableid".equalsIgnoreCase(name) || "modedatastatus".equalsIgnoreCase(name))) {// 虚拟表单、标签、草稿需要重新解析
                    String temtype = type;
                    if ("modelableid".equalsIgnoreCase(name)) {//标签
                        temtype = "-4";
                    } else if ("modedatastatus".equalsIgnoreCase(name)) {//草稿
                        temtype = "-5";
                    }
                    splitTableColBean.setOtherpara("column:" + primaryKey + "+" + id + "+" + htmltype + "+" + temtype + "+" + user.getLanguage() + "+" + isbill + "+" + dbtype + "+" + istitle + "+" + formmodeid + "+" + formId + "+0+" + ismaplocation + "+" + openType + "+" + customId + "+fromsearchlist" + "+" + showmethod + "+" + user.getUID() + "+" + enabled + (detailTable.equals("") ? "" : ("'+column:d_id+" + detailTable + "'")) + "+" + sliderPercentage + "+" + qfws + "+" + this.isEdit);
                }
                splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethod.getOthers");
                //批量修改
                if (this.isEdit) {
                    if ("3".equals(htmltype) && !"2".equals(type) && !"19".equals(type) && editableFields.contains(fieldid)) {
                        splitTableColBean.setTransmethod("com.api.cube.util.searchEdit.ButtonElementTransMethod.analyzeValue");
                        splitTableColBean.setOtherpara(id + "+" + type + "+" + dbtype + "+" + user.getLanguage());
                    }
                    if ("6".equals(htmltype) && editableFields.contains(fieldid)) {
                        splitTableColBean.setTransmethod("com.api.cube.util.searchEdit.UploadElementTransMethod.analyzeValue");
                        splitTableColBean.setOtherpara(id + "+" + type + "+" + dbtype + "+" + user.getUID());
                    }
                    if ("1".equals(htmltype) && "1".equals(type) && isfrontmultlang) {
                        splitTableColBean.setIsBase64(BoolAttr.TRUE);
                    }
                	/*if ("1".equals(htmltype)) {
                		splitTableColBean.setTransmethod("");
					}*/
                    splitTableColBean.setTransMethodForce("true");
                }
                if (isExcel) {
                    splitTableColBean.setTransmethod(ExcelTrans.getOthers);
                    String temtype = type;
                    if (isVirtualForm && ("modelableid".equalsIgnoreCase(name) || "modedatastatus".equalsIgnoreCase(name))) {// 虚拟表单、标签、草稿需要重新解析
                        if ("modelableid".equalsIgnoreCase(name)) {//标签
                            temtype = "-4";
                        } else if ("modedatastatus".equalsIgnoreCase(name)) {//草稿
                            temtype = "-5";
                        }
                    }
                    splitTableColBean.setOtherpara("column:" + primaryKey + "+" + id + "+" + htmltype + "+" + temtype + "+" + dbtype + "+" +
                            user.getLanguage() + "+" + formmodeid + "+" + formId + "+" + customId + "+" + hreflink + "+0+"
                            + openType + "+" + user.getUID() + "+" + detailTable + "+" + (detailTable.isEmpty() ? "0" : "column:d_id") + "+" + this.isEdit);

                    if (isVirtualForm) {
                        if ("modedatacreater".equalsIgnoreCase(name) || "modedatamodifier".equalsIgnoreCase(name)) {//创建人
                            splitTableColBean.setOtherpara("column:modedatacreatertype");
                            splitTableColBean.setTransmethod(ExcelTrans.getHrmResource);
                        } else if ("modedatacreatedate".equalsIgnoreCase(name)) {//创建日期
                            splitTableColBean.setOtherpara("column:modedatacreatetime+" + customId + "+" + showmethod + "+column:" + primaryKey + "+" + formId);
                            splitTableColBean.setTransmethod(ExcelTrans.getDateTime);
                        } else if ("modedatamodifydatetime".equalsIgnoreCase(name)) {
                            splitTableColBean.setOtherpara("column:modedatamodifydatetime");
                            splitTableColBean.setTransmethod("");
                        }

                    }

                }
            }
            if (Util.getIntValue(rs.getString("isstat"), 0) == 1 && Util.getIntValue(rs.getString("httype")) == 1) {
                EncryptFieldEntity encryptFieldEntity = new EncryptFieldConfigComInfo().getFieldEncryptConfig(encryptTable, name);
                if (!(encryptFieldEntity != null && "1".equals(encryptFieldEntity.getIsEncrypt()))) {//数据库加密字段不允许合计
                    if (field_viewtype == 1) {
                        name = DETAIL_FIELD_ALIAS + name;
                    }
                    allstatfield.append(name).append(",");
                    int temptype = Util.getIntValue(rs.getString("typeTemp"));
                    if (temptype == 5) {
                        countColumnsDbType += "," + name;
                    }
                    int digitsIndex = dbtype.indexOf(",");
                    int decimaldigits = 2;
                    if (digitsIndex > -1) {
                        decimaldigits = Util.getIntValue(dbtype.substring(digitsIndex + 1, dbtype.length() - 1), 2);
                        decimalFormat += "%." + decimaldigits + "f|";
                    } else {
                        if (qfws != null && !"".equals(qfws) && !qfws.equals("0")) {
                            decimalFormat += "%." + qfws + "f|";
                        } else {
                            decimalFormat += "%.0f|";
                        }
                    }
                }
            }
            allfieldMap.put(fieldid, splitTableColBean);
            i++;
        }
        //虚拟表单自定义按钮链接到建模卡片,id无需解析,屏蔽这里,否则虚拟表单若展示了主键,会出现重复列,造成tabstring解析错误
        if (!selectedDataIdField /*&&!isVirtualForm   QC:508441 */) {//没有勾选数据的id 就创建一个隐藏的列 如果勾选了就没必要创建一个
            SplitTableColBean splitTableColBean = new SplitTableColBean();
            int label = 81287;
            splitTableColBean.setColumn(primaryKey);
            splitTableColBean.setText(Util.toHtmlForSplitPage(Util.null2String(SystemEnv.getHtmlLabelName(label, user.getLanguage()))));
            splitTableColBean.setOtherpara("column:dataid");
            splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getDataId");
            splitTableColBean.setDisplay("none");
            columns.add(splitTableColBean);
        }

        RecordSet rs2 = new RecordSet();
        rs2.executeQuery("select * from mode_customSearchButton where objid=? and isshow=1  order by showorder asc,id desc", customId);
        if (rs2.getCounts() > 0) {
            //自定义解析查询列表的自定义按钮是需要判断是否显示
            SplitTableColBean splitTableColBean1 = new SplitTableColBean();
            splitTableColBean1.setColumn("operatesshow");
            splitTableColBean1.setText("operatesshow");
            splitTableColBean1.setOtherpara(customId + "+" + user.getUID() + "+" + modeId + "+" + formId);
            splitTableColBean1.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getSearchResultOperation1");
            splitTableColBean1.setDisplay("none");
            splitTableColBean1.setTransMethodForce("true");
            columns.add(splitTableColBean1);
            //点赞按钮的信息
            SplitTableColBean splitTableColBean2 = new SplitTableColBean();
            splitTableColBean2.setColumn("likesinfo");
            splitTableColBean2.setText("likesinfo");
            splitTableColBean2.setOtherpara(modeId + "+" + user.getUID());
            splitTableColBean2.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.analyzeLikesButton");
            splitTableColBean2.setDisplay("none");
            splitTableColBean2.setTransMethodForce("true");
            columns.add(splitTableColBean2);
        }

        RecordSet rs3 = new RecordSet();
        RecordSet rs4 = new RecordSet();//用于查询关联其他表单 千分位 浮点数小数等信息
        String rowstylefieldid = "";
        String rowstylefield = "";
        if (allfieldids.length() > 0) {
            String showchangefieldid = "";
            rs2.executeQuery(" select c.fieldid,c.rowbackvalue,c.rowfontvalue from customfieldshowchange c,mode_customdspfield f where (c.rowbackvalue is not null or c.rowfontvalue is not null ) " +
                    " and f.fieldid=c.fieldid and f.customid=c.customid and f.showmethod=1 and f.isshow=1 and f.isshow=1  and c.customid=?  ", customId);
            while (rs2.next()) {
                if (showchangefieldid.indexOf("," + rs2.getString("fieldid")) == -1) {
                    if ("".equals(rs2.getString("rowbackvalue")) && "".equals(rs2.getString("rowfontvalue"))) {
                        continue;
                    }

                    if (allfieldMap.get(rs2.getString("fieldid")) != null) {
                        rowstylefieldid = rs2.getString("fieldid");
                    }
                    showchangefieldid += "," + rowstylefieldid;
                }
            }
            if (showchangefieldid.length() > 0) {
                showchangefieldid = showchangefieldid.substring(1);
                rowstylefield = "rowstylefield";
                SplitTableColBean tmpSplitTableColBean = allfieldMap.get(rowstylefieldid);
                SplitTableColBean splitTableColBean = new SplitTableColBean();
                splitTableColBean.setColumn(rowstylefield);
                splitTableColBean.setText(rowstylefield);
                splitTableColBean.setOtherpara(tmpSplitTableColBean.getOtherpara() + "_showchangesplit_" + showchangefieldid + "_showchangesplit_" + rowstylefieldid);
                splitTableColBean.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getFormmodeRowstyleColumn");
                splitTableColBean.setDisplay("none");
                splitTableColBean.setTransMethodForce("true");
                columns.add(splitTableColBean);
                splitTableBean.setRowstylefield(rowstylefield);
            }
        }

        //统计数据查询
//        ArrayList<Map<String, String>> countData = new ArrayList<Map<String, String>>();
//        if (!isBackUpData) {
//        	String numberstr="decimal";
//            String split="";
//            String DB_ISNULL_FUN="isnull";
////            if(null!=this.dataSourceDBType &&"oracle".endsWith(this.dataSourceDBType)){
////                numberstr="number";
////                split="0";
////                DB_ISNULL_FUN="nvl";
////            }else if("mysql".endsWith(this.dataSourceDBType) || this.dataSourceDBType.startsWith("mysql")){
////                DB_ISNULL_FUN="ifnull";
////            }
//
//            if(null !=this.dataSourceDBType){
//                if(this.dataSourceDBType.contains("oracle")){
//                    DB_ISNULL_FUN = "nvl";
//                }else if(this.dataSourceDBType.contains("mysql")){
//                    DB_ISNULL_FUN = "ifnull";
//                }else{
//                    DB_ISNULL_FUN = "isnull";
//                }
//
//            }else{
//                DB_ISNULL_FUN = CommonConstant.DB_ISNULL_FUN;
//
//            }
//
//            String sql2 ="SELECT distinct a.*,c.fieldhtmltype,c.type,c.qfws,c.detailtable FROM mode_customcountset a LEFT JOIN mode_customsearch b ON a.customid=b.id "+
//                    " LEFT JOIN workflow_billfield c ON a.countfield=c.fieldname AND c.billid=b.formid  where customid=? and a.isenable=1 order by a.orderid ,a.id asc";
//            rs2.executeQuery(sql2,customId);
//            Map<String,String> temMap = new HashMap<String,String>();
//           // #783597 原先sql 查询存在关联问题 方法根据关联其他表单的必填字段判断是否是其他表单类型，然后根据 formid 和   countfield 去查询千分位等信息
//            String vdatasource ="";//虚拟表单数据源
//            while(rs2.next()){
//                String id=Util.null2String(rs2.getString("id"));
//                String formtype=Util.null2String(rs2.getString("formtype"));
//                String temformid=Util.null2String(rs2.getString("formid"));
//                String defaultsql=Util.null2String(rs2.getString("defaultsql"));
//                String formname=Util.null2String(rs2.getString("formname"));
//                String countfield=Util.null2String(rs2.getString("countfield"));
//                String detailtable=Util.null2String(rs2.getString("detailtable"));
//                String fieldhtmltype="";
//                String fieldtype="";
//                String temValue = Util.null2String(temMap.get(id));
//                if(temValue.equals(id)){
//                    continue;
//                }else{
//                    temMap.put(id,id);
//                }
//                int qfws = 0;
//                if("".equals(defaultsql)){
//                    qfws=Util.getIntValue(Util.null2String(rs2.getString("qfws")),0);
//                    fieldhtmltype=Util.null2String(rs2.getString("fieldhtmltype"));
//                    fieldtype=Util.null2String(rs2.getString("type"));
//                }else{
//                    //单独查询  formid -52_1
//                    String formid=Util.null2String(rs2.getString("formid"));
//                    if(formid.contains("_")){
//                        rs4.execute("SELECT c.fieldhtmltype,c.type,c.qfws FROM workflow_billfield c " +
//                                " where c.fieldname ='"+countfield+"' and  c.billid=(select billid from Workflow_billdetailtable where tablename='"+formname+"')");
//                    }else{
//                        String  sqlParam = "SELECT c.fieldhtmltype,c.type,c.qfws FROM workflow_billfield c where c.fieldname ='"+countfield+"' and  c.billid="+formid;
//                        rs4.execute(sqlParam);
//                    }
//                    while(rs4.next()) {
//                        qfws=Util.getIntValue(Util.null2String(rs4.getString("qfws")),0);
//                        fieldhtmltype=Util.null2String(rs4.getString("fieldhtmltype"));
//                        fieldtype=Util.null2String(rs4.getString("type"));
//                    }
//
//                }
//
//
//                String formid=Util.null2String(rs2.getString("formid"));
//                String statisticaltype =Util.null2String(rs2.getString("statisticaltype"));//统计类型：0：求和统计，1：总数统计，2：平均数统计 3：最大值统计 4：最小值统计
//                String conditionsql =  Util.null2String(rs2.getString("conditionsql"));
//                if(!"".equals(conditionsql)){
//                    conditionsql =   " and " + conditionsql;
//                }
//                String tempSqlwhere = sqlWhere + conditionsql;//新定义tempSqlwhere 用于统计计算
//                String countnum="";
//                Map<String,String> newList = new HashMap<String, String>();
//                newList.put("id",Util.null2String(rs2.getString("id")));
//                newList.put("name",Util.null2String(rs2.getString("name")));
//                newList.put("icon",Util.null2String(rs2.getString("icon")));
//                newList.put("setdesc",Util.null2String(rs2.getString("setdesc")));
//                String sqlcount="";
//                if("0".equals(formtype)){
//                    if("1".equals(fieldhtmltype)&&"5".equals(fieldtype)){
//                        //	fieldtype 没找到相关说明
//                        // fieldhtmltype  1：单行文本框<br> 2：多行文本框<br> 3：浏览按钮<br> 4：check框<br> 5：选择框
//                        if(null !=this.dataSourceDBType && this.dataSourceDBType.contains("sqlserver")){
//                                if("0".equals(statisticaltype)){
//                                    //sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                    sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0) ) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                                }else if("1".equals(statisticaltype)){
//                                    countfield = "id";
//                                    // sqlcount="SELECT count(1) as "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                    sqlcount="SELECT count(1) as "+countfield+sqlFrom+tempSqlwhere;
//
//                                }else if("2".equals(statisticaltype)){
//                                    //  sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                    sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                                }else if("3".equals(statisticaltype)){
//                                    //   sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                    sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                                }else{
//                                    // sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                    sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                                }
//                        }else{
//                            if("0".equals(statisticaltype)){
//                                //sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                            }else if("1".equals(statisticaltype)){
//                                countfield = "id";
//                                // sqlcount="SELECT count(1) as "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                sqlcount="SELECT count(1) as "+countfield+sqlFrom+tempSqlwhere;
//
//                            }else if("2".equals(statisticaltype)){
//                                //  sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                            }else if("3".equals(statisticaltype)){
//                                //   sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                            }else{
//                                // sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
//                                sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere;
//
//                            }
//                        }
//
//
//                    }else{
//                        if(null !=this.dataSourceDBType && this.dataSourceDBType.contains("sqlserver")){
//                                if("0".equals(statisticaltype)){
//                                    sqlcount="select sum(cast("+DB_ISNULL_FUN+"(t1."+countfield+",0) as "+numberstr+"(38,"+qfws+"))) "+sqlFrom+tempSqlwhere;
//                                }else if("1".equals(statisticaltype)){
//                                    sqlcount="select count(1) "+sqlFrom+tempSqlwhere;
//                                }else if("2".equals(statisticaltype)){
//                                    sqlcount="select avg(cast("+DB_ISNULL_FUN+"(t1."+countfield+",0) as "+numberstr+"(38,"+qfws+"))) "+sqlFrom+tempSqlwhere;
//                                }else if("3".equals(statisticaltype)){
//                                    sqlcount="select max("+DB_ISNULL_FUN+"(t1."+countfield+",0)) "+sqlFrom+tempSqlwhere;
//                                }else{
//                                    sqlcount="select min("+DB_ISNULL_FUN+"(t1."+countfield+",0)) "+sqlFrom+tempSqlwhere;
//                                }
//                        }else{
//                            if("0".equals(statisticaltype)){
//                                sqlcount="select sum("+DB_ISNULL_FUN+"(t1."+countfield+",0) ) "+sqlFrom+tempSqlwhere;
//                            }else if("1".equals(statisticaltype)){
//                                sqlcount="select count(1) "+sqlFrom+tempSqlwhere;
//                            }else if("2".equals(statisticaltype)){
//                                sqlcount="select avg("+DB_ISNULL_FUN+"(t1."+countfield+",0) ) "+sqlFrom+tempSqlwhere;
//                            }else if("3".equals(statisticaltype)){
//                                sqlcount="select max("+DB_ISNULL_FUN+"(t1."+countfield+",0)) "+sqlFrom+tempSqlwhere;
//                            }else{
//                                sqlcount="select min("+DB_ISNULL_FUN+"(t1."+countfield+",0)) "+sqlFrom+tempSqlwhere;
//                            }
//                        }
//
//                    }
//                }else{
//
//
//                    String otherFormKey = ",b1.id as bid ";
//                    String distinctfield = countfield;
//                    boolean hasVprimarykey = true;
//                    //虚拟表主键处理
//                    String otherFormKeysql = "select vprimarykey from Modeformextend a , workflow_bill b where a.formid = b.id and a.formid ="+temformid;
//                    String vsqlFormSql = " select vsql , vdatasource from ModeFormExtend where formid =  "+temformid;
//                    rs3.executeSql(otherFormKeysql);
//                    while(rs3.next()){
//                        if(!StringHelper.isEmpty(Util.null2String(rs3.getString("vprimarykey")))){
//                            otherFormKey = ",b1." +Util.null2String(rs3.getString("vprimarykey")).toLowerCase()+" as bid ";
//                            hasVprimarykey = false;
//                        }
//                    }
//                    //视图且Modeformextend查不到主键的情况，置为空
//                    if("1".equals(formtype) && StringHelper.isEmpty(formid) && hasVprimarykey){
//                        otherFormKey = " ";
//                        distinctfield = "*";
//                    }
//                    String sqlWhere1 = sqlWhere;
//                    //String sqlWhere1= tempSqlwhere;
//                    if(!StringHelper.isEmpty(detailTable)&&formname.equalsIgnoreCase(detailTable)) {
//                        sqlWhere1 = sqlWhere1.replace("d1.","b1.");
//                    }
//                    rs3.execute(vsqlFormSql);
//
//                    while(rs3.next()){
//                        if(!"".equals(Util.null2String(rs3.getString("vsql")))){
//                            formname = "("+Util.null2String(rs3.getString("vsql"))+")";
//                        }
//                        vdatasource = Util.null2String(rs3.getString("vdatasource"));
//                    }
//                    if("1".equals(fieldhtmltype)&&"5".equals(fieldtype)){
//                        //sqlcount="SELECT sum(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+"))) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+","+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m "; //改造前的sql
//                        if(null !=this.dataSourceDBType  && this.dataSourceDBType.contains("sqlserver")){
//                            if("0".equals(statisticaltype)){
//                                sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0) ) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("1".equals(statisticaltype)){
//                                sqlcount="SELECT c  from ( SELECT DISTINCT count(1) c "+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("2".equals(statisticaltype)){
//                                sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0) ) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("3".equals(statisticaltype)){
//                                sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else{
//                                sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }
//
//                        }else{
//                            if("0".equals(statisticaltype)){
//                                sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("1".equals(statisticaltype)){
//                                sqlcount="SELECT c  from ( SELECT DISTINCT count(1) c "+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("2".equals(statisticaltype)){
//                                sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("3".equals(statisticaltype)){
//                                sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else{
//                                sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+" "+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }
//                        }
//
//
//                    }else{
//                    	//sqlcount="SELECT sum(m."+countfield+") from ( SELECT DISTINCT t1.id,b1."+countfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";  //改造前的统计函数sql
//                        if(null !=this.dataSourceDBType  && this.dataSourceDBType.contains("sqlserver")){
//                            if("0".equals(statisticaltype)){
//                                sqlcount="select sum(cast("+DB_ISNULL_FUN+"(m."+countfield+",0) as "+numberstr+"(38,"+qfws+"))) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("1".equals(statisticaltype)){
//                                sqlcount="select c from ( SELECT DISTINCT count(1) c "+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("2".equals(statisticaltype)){
//                                sqlcount="select avg(cast("+DB_ISNULL_FUN+"(m."+countfield+",0) as "+numberstr+"(38,"+qfws+"))) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("3".equals(statisticaltype)){
//                                sqlcount="select max("+DB_ISNULL_FUN+"(m."+countfield+",0)) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else{
//                                sqlcount="select min("+DB_ISNULL_FUN+"(m."+countfield+",0)) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }
//
//                        }else{
//                            if("0".equals(statisticaltype)){
//                                sqlcount="select sum("+DB_ISNULL_FUN+"(m."+countfield+",0)) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("1".equals(statisticaltype)){
//                                sqlcount="select c from ( SELECT DISTINCT count(1) c "+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("2".equals(statisticaltype)){
//                                sqlcount="select avg("+DB_ISNULL_FUN+"(m."+countfield+",0)) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else if("3".equals(statisticaltype)){
//                                sqlcount="select max("+DB_ISNULL_FUN+"(m."+countfield+",0)) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }else{
//                                sqlcount="select min("+DB_ISNULL_FUN+"(m."+countfield+",0)) from ( SELECT DISTINCT b1."+distinctfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";
//                            }
//                        }
//
//                    }
//
//                }
//
////                if("oracle".endsWith(this.dataSourceDBType)){
////                    numberstr="number";
////                    split="0";
////                    DB_ISNULL_FUN="nvl";
////                }else if("mysql".endsWith(this.dataSourceDBType)){
////                    DB_ISNULL_FUN="ifnull";
////                }
//
//                writeLog("countsql"+sqlcount);
//                if(!"".equals(vdatasource) && !"$ECOLOGY_SYS_LOCAL_POOLNAME".equals(vdatasource)){
//                    //sqlcount = "select sum(ifnull(m.FDS,0)) from ( SELECT DISTINCT t1.id,b1.FDS,b1.id as bid  from uf_cydzwbsjy2020 t1,(select * from uf_cydzwbsjy2020) b1  where 1=1  and '2020-03-24 17:41:40'='2020-03-24 17:41:40'  and b1.wb2 = t1.wb2 ) m ";
//                    rs3.executeQueryWithDatasource(sqlcount,vdatasource);
//                }else{
//
//                    rs3.executeSql(sqlcount,this.dataSource);
//                }
//                if(rs3.next()){
//                    countnum=Util.null2String(rs3.getString(1));
//                }
//                boolean flag=true;
//                if("".equals(countnum) ){
//                    countnum="0";
//                }else if(countnum.indexOf("-")>-1){
//                    countnum=countnum.replace("-","");
//                    flag=false;
//                }
//                try{
//                    if(!("1".equals(statisticaltype))){//statisticaltype = 1 是统计总条数不需要进行数据格式化
//                        //countnum=NumberHelper.moneyAddCommaSub(countnum);
//                    	//countnum=Util.toDecimalDigits(countnum,qfws);
//                        if("1".equals(fieldhtmltype)&&"4".equals(fieldtype)){
//                            countnum=Util.toDecimalDigits(countnum,2);//金额转换单独处理
//                        }else{
//                            if(!formid.equals("") || "0".equals(formtype)){
//                        		countnum=Util.toDecimalDigits(countnum,qfws);//为数字统计补该字段的小数位
//                        	}else{
//                        		countnum=Util.toDecimalDigits(countnum,2);//没有选表单时默认两位小数
//                        	}
//                        }
//                    }
//                    countnum=NumberHelper.moneyAddCommaSub(countnum);
//                }catch(Exception e){
//                }
//                if(!flag){
//                   countnum="-"+countnum;
//                }
//                newList.put("countnum",countnum);
//                countData.add(newList);
//            }
//		}

        if (disquerycheck.equals("1")) {
            splitTableBean.setCheckboxpopedom(null);
            splitTableBean.setTableType("none");
        } else {
            checkboxpopedom.setId("checkbox");
            checkboxpopedom.setShowmethod("true");
            splitTableBean.setCheckboxpopedom(checkboxpopedom);
            splitTableBean.setTableType("checkbox");

            backfields = setFilterColumn(columns, backfields, sqlFrom, sqlWhere.toString(), formId);//xk:数据筛选相关代码
        }
        if (isBackUpData) {
            if (!"".equals(this.detailTable)) {
                sqlFrom = sqlFrom.replace(this.detailTable, this.detailTable + this.suffix);
            }
            sqlFrom = sqlFrom.replace(" " + this.tableName + " ", " " + this.tableName + this.suffix + " ");
            if (isCoverType) {
                sqlWhere.append(" and formmodeisrecover=0 ");
            } else {
                sqlWhere.append(" and formmodeisrecover=1 and  formmodelogid=").append(backuplogid);
            }
        }

        splitTableBean.setInstanceid("workflowRequestListTable");
        splitTableBean.setPageID(pageId);
        splitTableBean.setPageUID(pageId);
        splitTableBean.setPagesize("" + pageSize);
        splitTableBean.setSqlprimarykey(this.sqlPrimaryKey);
        splitTableBean.setOpenPrimaryKeyOrder(BoolAttr.TRUE);
        splitTableBean.setBackfields(backfields);
        splitTableBean.setSqlform(sqlFrom);
        //自定义条件
        RecordSet rsRow = new RecordSet();
        List rowClick = new ArrayList();
        String rowSql = " select b.fieldid, a.fieldname, a.viewtype, a.fieldhtmltype, a.type typeTemp, b.conditiontransition,b.isquery,b.isadvancedquery " +
                "   from mode_customdspfield b left join  workflow_billfield a on a.id = b.fieldid "
                + " where  b.customid = ? and (a.detailtable is null  or a.detailtable = '' or a.detailtable = ?)  and (fieldid > 0 or fieldid=-3) and b.isquery=1";
        rsRow.executeQuery(rowSql, customId, detailTable);
        while (rsRow.next()) {
            rowClick.add(Util.getIntValue(rsRow.getString("fieldid")));
        }
        //行点击事件传的条件
        Map<String, Object> map = ParamUtil.request2Map(request);
        if (map.containsKey("viewFrom") && "rowClick".equals(map.get("viewFrom").toString())) {
            String tempKey;
            String columnName;
            for (Object key : map.keySet()) {
                tempKey = Util.null2String(key);
                if (map.containsKey(tempKey) && tempKey.startsWith("con_") && tempKey.split("_").length == 2) {
                    int tempKeyIntVal = Util.getIntValue(tempKey.split("_")[1], 0);
                    if (tempKeyIntVal > 9) { // 1-9为系统字段
                        columnName = Util.null2String(getFieldNameById(tempKey.split("_")[1]));
                        if (!"".equals(columnName) && !"-1".equals(columnName) && !rowClick.contains(tempKeyIntVal)) {      //只有当没有固定查询条件的时候，才走这边。
                            sqlWhere.append(" and ").append(columnName).append("='").append(map.get(tempKey)).append("'");
                        }
                    }
                }
            }
            new FormmodeLog().writeLog(this.getClass().getName() + "#rowClick", sqlWhere.toString());
        }

        splitTableBean.setSqlwhere(sqlWhere.toString());
        if (!countColumnsDbType.isEmpty()) {
            countColumnsDbType = countColumnsDbType.substring(1);
            splitTableBean.setCountColumnsDbType(countColumnsDbType);
        }
        splitTableBean.setCounttransmethod(counttransmethod);
        splitTableBean.setSqlorderby(orderby);
        splitTableBean.setSumColumns(isBackUpData ? "" : allstatfield.toString());
        splitTableBean.setDecimalFormat(decimalFormat);
        splitTableBean.setPoolname(dataSource);
        splitTableBean.setOperates(operateBean);
        splitTableBean.setCols(columns);
        splitTableBean.setSqlsortway(" ");
        if (this.isEdit) {//表单编辑时候需要返回明文 , excel
            splitTableBean.setReturnDecryptData(true);
        }
        FormModeConfig formModeConfig = new FormModeConfig();
        String enableTreeList = formModeConfig.getEnableTreeList();  //树形展示是有formmode.properties中控制的。即使链接链接有treetype也不行。enableTreeList为y时开启
        Map treeMap = new HashMap<>();
        RecordSet recordSet = new RecordSet();
        String isTreeSearch = "";
        String treeposition = "";
        recordSet.executeQuery("select isTreeSearch,treeposition,datashowtype from mode_customsearch where id=?", customId);
        if (recordSet.next()) {
            isTreeSearch = Util.null2String(recordSet.getString("isTreeSearch"));
            if (!isTreeSearch.equals("1")) {
                isTreeSearch = Util.null2String(recordSet.getString("datashowtype")).equals("4") ? "1" : "";
            }
            treeposition = Util.null2String(recordSet.getString("treeposition"));
        }
        if (!this.displayType.equals("") && this.displayType.toLowerCase().equals("treetype") && enableTreeList.equals("y") && isTreeSearch.equals("1")) {
            CubeTreeTypeSearch cubeTreeTypeSearch = new CubeTreeTypeSearch();
            cubeTreeTypeSearch.setNeedId(primaryKey, Util.getIntValue(customId), modeId, Util.getIntValue(formId));
            treeMap = cubeTreeTypeSearch.treeType(backfields, sqlFrom, sqlWhere.toString(), orderby, customId, columns, pageIndex, treePageSize, rootids, user);
        }
        String windowTitle = "";
        String istree = Util.null2String(request.getParameter("istree"));
        String tabid = Util.null2String(request.getParameter("tabid"));
        if (null == menuIds || menuIds.equals("")) {
            RecordSet titileRs = new RecordSet();
            titileRs.executeQuery("select customname from mode_customsearch where id = ?", customId);
            if (titileRs.next()) {
                if (null == datasqlwhere || datasqlwhere.equals("")) {
                    windowTitle = Util.null2String(titileRs.getString("customname"));
                }
            }
        }
        if (istree.equals("true") || !tabid.equals("")) {    //当为树内嵌或者tab页内嵌时，不改变标题
            windowTitle = "";
        }
        //获取当前页面的操作时间
        Calendar today = Calendar.getInstance();
        String formatdate = Util.add0(today.get(Calendar.YEAR), 4) + "-"
                + Util.add0(today.get(Calendar.MONTH) + 1, 2) + "-"
                + Util.add0(today.get(Calendar.DAY_OF_MONTH), 2);
        String formattime = Util.add0(today.get(Calendar.HOUR_OF_DAY), 2) + ":"
                + Util.add0(today.get(Calendar.MINUTE), 2) + ":"
                + Util.add0(today.get(Calendar.SECOND), 2);
        String time = formatdate + " " + formattime;
        String advanedData = SystemEnv.getHtmlLabelName(512283, user.getLanguage());//"查询条件/快捷搜索条件有必填字段未填写，请填写后再试！"
        Map<String, Object> param = ParamUtil.request2Map(request);
        rebuildOrderByData(param, this.customId, this.user, splitTableBean.getSqlform());
        result = SplitTableUtil.makeListDataResult(splitTableBean);
        if (linemergel == 1) {//如果开启了合并列
            List<Map<String, Object>> mergeInfos = getLineMergeInfos(customId, 0);
            List<String> mergeFields = getMergeFields(customId, 0);
            result.put("mergeFields", mergeFields);
            result.put("mergeInfos", mergeInfos);
        }
        //统计显示位置
        int countDisplayPosition = 0;
        rs.executeQuery("select countdisplayposition from mode_customcount_setting where customid=?", customId);
        if (rs.next()) {
            countDisplayPosition = Util.getIntValue(rs.getString("countdisplayposition"), 0);
        }
        result.put("countDisplayPosition", countDisplayPosition);
        result.put("windowTitle", windowTitle);
        result.put("isChexkAdvanedDate", this.isChexkAdvanedDate ? "" : advanedData);
        result.put("enableTreeList", enableTreeList);
        result.put("treemap", treeMap);
        result.put("isTreeSearch", isTreeSearch);
        result.put("treeposition", treeposition);
//      result.put("groupCount",groupCount);
        result.put("buttonBody", this.jsBody);
//      result.put("countData",countData);
        result.put("treeDataWidth", treeDataWidth);
        result.put("changeDataTime", time);
        return result;
    }

    /**
     * 通过字段id获取字段名
     *
     * @param id
     * @return
     */
    private String getFieldNameById(String id) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select fieldname from workflow_billfield where id = ?", id);
        if (rs.next()) {
            return rs.getString("fieldname");
        }
        return "-1";
    }


    public String toBase64ForMergeLine(String s) {
        if (s == null || "".equals(s)) {
            return "base64_";
        }
        try {
            BASE64Encoder base64Encoder = new BASE64Encoder();
            return "base64_" + base64Encoder.encode(s.getBytes("UTF-8")).replaceAll("\n", "").replaceAll("\r", "");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return "base64_" + s;
        }


    }

    /**
     * 获取合并列信息
     *
     * @param customid
     * @return
     */
    public List<Map<String, Object>> getLineMergeInfos(String customid, int type) {
        this.customId = Util.getIntValue(customid, 0);
        List<Map<String, Object>> mergeInfos = new ArrayList<Map<String, Object>>();
        RecordSet rSet = new RecordSet();
        if (rSet.getDBType().toLowerCase().contains("postgresql")) {
            rSet.executeQuery("select * from mode_searchLineMergel where customid=? and COALESCE(supid,-1)=-1", customid);
        } else {
            rSet.executeQuery("select * from mode_searchLineMergel where customid=? and (supid is null or supid='')", customid);
        }
        while (rSet.next()) {
            Map<String, Object> mergeinfo = new HashMap<String, Object>();
            String id = Util.null2String(rSet.getString("id"));
            String mergelinename = Util.null2String(rSet.getString("mergelinename"));
            if (type == 1) {
                mergeinfo.put("mergename", toBase64ForMergeLine(mergelinename));
            } else {
                mergeinfo.put("mergename", Util.formatMultiLang(mergelinename, user.getLanguage() + ""));
            }
            List<String> fields = new ArrayList<String>();
            if (type == 2 || type == 0) {
                Map<String, String> fields4Exp = getMergeFields4Exp(customid, id);
                List<String> fields4ExpValues = new ArrayList<String>();
                for (String key : fields4Exp.keySet()) {
                    fields4ExpValues.add(fields4Exp.get(key));
                }
                mergeinfo.put("fields", fields4ExpValues);
                mergeinfo.put("fieldcount", getAllFieldCounts(id, type));
                mergeinfo.put("maxTier", getMaxTier(customid, fields4Exp.keySet()));
            } else {
                fields = getchildFields(id, type);
                mergeinfo.put("fields", fields);
            }
            List<Map<String, Object>> children = getChildren(id, type);
            mergeinfo.put("children", children);
            if (children.size() > 0) {
                mergeInfos.add(mergeinfo);
            }
        }
        return mergeInfos;
    }

    /**
     * 获取合并列最大层数
     *
     * @param customid
     * @param fieldids
     * @return
     */
    public int getMaxTier(String customid, Set<String> fieldids) {
        int maxTier = 0;
        RecordSet rs = new RecordSet();
        String fieldidString = String.join(",", fieldids);
        String sql = "select id from mode_searchLineMergel where customid=? and fieldid in (" + fieldidString + ")";
        rs.executeQuery(sql, customid);
        while (rs.next()) {
            int tempTier = getTierByFieldid(rs.getString("id"), 0);
            if (tempTier > maxTier) {
                maxTier = tempTier;
            }
        }
        return maxTier;
    }

    /**
     * 获取字段层级
     *
     * @param mergeid
     * @param tier
     * @return
     */
    public int getTierByFieldid(String mergeid, int tier) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select supid from mode_searchLineMergel where id=?", mergeid);
        if (rs.next()) {
            tier++;
            if (Util.null2String(rs.getString("supid")).equals("")) {
                return tier;
            } else {
                tier = getTierByFieldid(Util.null2String(rs.getString("supid")), tier);
            }
        }
        return tier;
    }


    /**
     * 获取所有被合并的字段----导出
     *
     * @param
     * @return
     */
    public Map<String, String> getMergeFields4Exp(String customid, String firstSupid) {
        Map<String, String> fields = new HashMap<String, String>();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        rs.executeQuery("select a.fieldid from mode_searchLineMergel a  where fieldid is not null and fieldid !=0 and firstSupid=? and customid=?", firstSupid, customid);
        while (rs.next()) {
            String fieldid = rs.getString("fieldid");
            String fieldname = "";
            String viewtype = "";
            if (fieldid.equals("-3")) {
                fieldname = "id";
            } else if (fieldid.equals("-1")) {
                fieldname = "modedatacreatedate";
            } else if (fieldid.equals("-2")) {
                fieldname = "modedatacreater";
            } else if (fieldid.equals("-7")) {
                fieldname = "modedatamodifier";
            } else if (fieldid.equals("-8")) {
                fieldname = "modedatamodifydatetime";
            } else if (fieldid.equals("-5")) {
                fieldname = "modedatastatus";
            } else if (fieldid.equals("-9")) {
                fieldname = "seclevel";
            } else if (Util.getIntValue(fieldid, 0) < 0) {
                continue;
            } else if (Util.getIntValue(fieldid, 0) > 0) {
                rs1.executeQuery("select fieldname,viewtype from workflow_billfield where id=?", fieldid);
                if (rs1.next()) {
                    fieldname = rs1.getString("fieldname");
                    viewtype = rs1.getString("viewtype");
                }
            }
            if (viewtype.equals("1")) {
                fieldname = "d_" + fieldname;
            }
            fields.put(fieldid, fieldname);
        }
        return fields;
    }


    /**
     * 获取所有被合并的字段
     *
     * @param
     * @return
     */
    public List<String> getMergeFields(String customid, int type) {
        List<String> fields = new ArrayList<String>();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        rs.executeQuery("select a.fieldid from mode_searchLineMergel a  where fieldid is not null and fieldid !=0 and customid=?", customid);
        while (rs.next()) {
            String fieldid = rs.getString("fieldid");
            String fieldname = "";
            String viewtype = "";
            if (fieldid.equals("-3")) {
                fieldname = "id";
            } else if (fieldid.equals("-1")) {
                fieldname = "modedatacreatedate";
            } else if (fieldid.equals("-2")) {
                fieldname = "modedatacreater";
            } else if (fieldid.equals("-7")) {
                fieldname = "modedatamodifier";
            } else if (fieldid.equals("-8")) {
                fieldname = "modedatamodifydatetime";
            } else if (fieldid.equals("-5")) {
                fieldname = "modedatastatus";
            } else if ("-9".equals(fieldid)) {
                fieldname = "seclevel";
            } else if (Util.getIntValue(fieldid, 0) < 0) {
                continue;
            } else if (Util.getIntValue(fieldid, 0) > 0) {
                rs1.executeQuery("select fieldname,viewtype from workflow_billfield where id=?", fieldid);
                if (rs1.next()) {
                    fieldname = rs1.getString("fieldname");
                    viewtype = rs1.getString("viewtype");
                }
            }
            if (viewtype.equals("1")) {
                fieldname = "d_" + fieldname;
            }
            if (type == 0) {
                fields.add(fieldname);
            } else {
                fields.add(fieldid);
            }
        }
        return fields;
    }

    /**
     * 获取合并列中的字段
     *
     * @param mergeid
     * @return
     */
    public List<String> getchildFields(String mergeid, int type) {
        List<String> fields = new ArrayList<String>();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        rs.executeQuery("select a.fieldid from mode_searchLineMergel a "
                + "left join mode_customdspfield c on a.fieldid=c.fieldid where a.fieldid is not null and a.fieldid != 0 and a.customid=? and (c.customid =? or c.customid is null) and firstSupid=? and c.isshow=1", customId, customId, mergeid);
        while (rs.next()) {
            String fieldid = rs.getString("fieldid");
            String fieldname = "";
            String viewtype = "";
            if (fieldid.equals("-3")) {
                fieldname = "id";
            } else if (fieldid.equals("-1")) {
                fieldname = "modedatacreatedate";
            } else if (fieldid.equals("-2")) {
                fieldname = "modedatacreater";
            } else if (fieldid.equals("-7")) {
                fieldname = "modedatamodifier";
            } else if (fieldid.equals("-8")) {
                fieldname = "modedatamodifydatetime";
            } else if (fieldid.equals("-5")) {
                fieldname = "modedatastatus";
            } else if ("-9".equals(fieldid)) {
                fieldname = "seclevel";
            } else if (Util.getIntValue(fieldid, 0) < 0) {
                continue;
            } else if (Util.getIntValue(fieldid, 0) > 0) {
                rs1.executeQuery("select fieldname,viewtype from workflow_billfield where id=?", fieldid);
                if (rs1.next()) {
                    fieldname = rs1.getString("fieldname");
                    viewtype = rs1.getString("viewtype");
                }
            }
            if (viewtype.equals("1")) {
                fieldname = "d_" + fieldname;
            }
            if (type == 0) {
                fields.add(fieldname);
            } else {
                fields.add(fieldid);
            }
        }
        return fields;
    }

    public ArrayList<String> getchildFields(String mergeid, ArrayList<String> fields) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select a.id,a.fieldid,c.isshow from mode_searchLineMergel a  "
                + "left join mode_customdspfield c on a.fieldid=c.fieldid where a.supid=?  and a.customid=? and (c.customid=? or c.customid is null) ", mergeid, customId, customId);
        while (rs.next()) {
            String fieldid = Util.null2String(rs.getString("fieldid"));
            String id = Util.null2String(rs.getString("id"));
            if (fieldid.equals("")) {
                fields.addAll(getchildFields(id, fields));
            } else {
                String isshow = Util.null2String(rs.getString("isshow"));
                if (isshow.equals("1")) {
                    fields.add(fieldid);
                }

            }
        }
        return fields;
    }

    /**
     * 获取合并列中的字段数量
     *
     * @param mergeid
     * @return
     */
    public int getAllFieldCounts(String mergeid, int type) {
        int count = 0;
        RecordSet rs = new RecordSet();
        rs.executeQuery("select count(a.id) as fieldcount from mode_searchLineMergel a "
                + "left join mode_customdspfield c on a.fieldid=c.fieldid  where a.fieldid is not null and a.fieldid != 0 and c.isshow=1 and c.customid=?  and a.firstSupid=?", customId, mergeid);
        if (rs.next()) {
            count = rs.getInt("fieldcount");
        }
        return count;
    }


    /**
     * 获取合并列中的字段数量
     *
     * @param mergeid
     * @return
     */
    public int getChildFieldCounts(String mergeid, int count) {
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        rs.executeQuery("select id,fieldid from mode_searchLineMergel where supid=?", mergeid);
        while (rs.next()) {
            String fieldid = Util.null2String(rs.getString("fieldid"));
            String id = Util.null2String(rs.getString("id"));
            if (fieldid.equals("")) {
                count = getChildFieldCounts(id, count);
            } else {
                rs1.executeQuery("select isshow from mode_customdspfield where fieldid=? and customid=?", fieldid, customId);
                if (rs1.next()) {
                    if (Util.null2String(rs1.getString("isshow")).equals("1")) {
                        count++;
                    }
                }
            }
        }
        return count;
    }


    /**
     * 合并列获取下级
     *
     * @param mergeid
     * @return
     */
    public List<Map<String, Object>> getChildren(String mergeid, int type) {
        List<Map<String, Object>> childrens = new ArrayList<Map<String, Object>>();
        List<String> ids = new ArrayList<String>();
        //排序后的id集合
        List<String> ids_new = new ArrayList<String>();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        rs.executeQuery("select a.id from mode_searchLineMergel a left join mode_customdspfield b on a.fieldid=b.fieldid  where a.supid=? and a.customid=? AND (b.customid=? or b.customid is null)", mergeid, customId, customId);
        while (rs.next()) {
            String id = rs.getString("id");
            ids.add(id);
        }
        ids_new = sortMergeLine(ids);
        for (String mergelineid : ids_new) {
            rs.executeQuery("select a.*,b.showorder,b.isshow,b.shownamelabel from mode_searchLineMergel a left join mode_customdspfield b on a.fieldid=b.fieldid  "
                    + "where a.id=? and a.customid=? AND (b.customid=? or b.customid is null)", mergelineid, customId, customId);
            if (rs.next()) {
                Map<String, Object> children = new HashMap<String, Object>();
                String id = rs.getString("id");
                String fieldid = Util.null2String(rs.getString("fieldid"));
                String fieldname = "";
                String viewtype = "";
                if (!fieldid.equals("")) {
                    if (fieldid.equals("-3")) {
                        fieldname = "id";
                    } else if (fieldid.equals("-1")) {
                        fieldname = "modedatacreatedate";
                    } else if (fieldid.equals("-2")) {
                        fieldname = "modedatacreater";
                    } else if (fieldid.equals("-7")) {
                        fieldname = "modedatamodifier";
                    } else if (fieldid.equals("-8")) {
                        fieldname = "modedatamodifydatetime";
                    } else if ("-9".equals(fieldid)) {
                        fieldname = "seclevel";
                    } else if (fieldid.equals("-5")) {
                        fieldname = "modedatastatus";
                    } else if (Util.getIntValue(fieldid, 0) < 0) {
                        continue;
                    } else if (Util.getIntValue(fieldid, 0) > 0) {
                        rs1.executeQuery("select fieldname,viewtype from workflow_billfield where id=?", fieldid);
                        if (rs1.next()) {
                            fieldname = rs1.getString("fieldname");
                            viewtype = rs1.getString("viewtype");
                        }
                    }
                    String isshow = Util.null2String(rs.getString("isshow"));
                    int shownamelabel = Util.getIntValue(Util.null2String(rs.getString("shownamelabel")), 0);
                    String showname = SystemEnv.getHtmlLabelName(shownamelabel, user.getLanguage());
                    if (viewtype.equals("1")) {
                        fieldname = "d_" + fieldname;
                    }
                    if (isshow.equals("1")) {
                        children.put("fieldname", fieldname);
                        if (type == 1) {
                            children.put("fieldid", fieldid);
                            children.put("showname", showname);
                        }
                        if (type == 2 || type == 0) {
                            children.put("fieldTier", getTierByFieldid(id, 0));
                        }
                        childrens.add(children);
                    }

                } else {
                    int childCount = getChildFieldCounts(id, 0);
                    if (childCount > 0) {
                        String mergelinename = Util.null2String(rs.getString("mergelinename"));
                        if (type == 1) {
                            children.put("mergename", toBase64ForMergeLine(mergelinename));
                        } else {
                            children.put("mergename", Util.formatMultiLang(mergelinename, user.getLanguage() + ""));
                        }
                        children.put("fieldcount", childCount);
                        if (type == 1) {
                            ArrayList<String> fields = new ArrayList<String>();
                            children.put("fields", getchildFields(id, fields));
                        }
                        children.put("children", getChildren(id, type));
                        childrens.add(children);
                    }
                }
            }
        }
        return childrens;
    }

    //按照字段显示顺序，排序合并列
    public List<String> sortMergeLine(List<String> ids) {
        List<String> ids_new = new ArrayList<String>();
        String id_order = "";
        List<String> id_orders = new ArrayList<String>();
        for (String id : ids) {
            float order = getMinOrder(id, 10000000);
            id_order = id + "_" + order;
            id_orders.add(id_order);
        }
        ids_new = sortMap(id_orders);
        return ids_new;
    }

    /**
     * 按照显示顺序从小到大排序
     *
     * @param id_orders
     * @return
     */
    public List<String> sortMap(List<String> id_orders) {
        List<String> ids = new ArrayList<String>();
        for (int i = 0; i < id_orders.size() - 1; i++) {//按照显示顺序排序
            for (int j = 0; j < id_orders.size() - 1 - i; j++) {
                try {
                    float order1 = Util.getFloatValue(id_orders.get(j).split("_")[1], 0);
                    String id1 = id_orders.get(j).split("_")[0];
                    float order2 = Util.getFloatValue(id_orders.get(j + 1).split("_")[1], 0);
                    String id2 = id_orders.get(j + 1).split("_")[0];
                    if (order2 < order1) {
                        id_orders.set(j, id2 + "_" + order2);
                        id_orders.set(j + 1, id1 + "_" + order1);
                    }
                } catch (Exception e) {
                    continue;
                }
            }
        }
        for (String id : id_orders) {
            ids.add(id.split("_")[0]);
        }

        return ids;
    }

    public float getMinOrder(String id, float minOrder) {
        float order = minOrder;
        RecordSet rs = new RecordSet();
        rs.executeQuery("select a.*,b.showorder from mode_searchLineMergel a left join mode_customdspfield b on a.fieldid=b.fieldid and a.customid=b.customid  where (a.supid=? or a.id=?)", id, id);
        while (rs.next()) {
            String id1 = Util.null2String(rs.getString("id"));
            String fieldid = Util.null2String(rs.getString("fieldid"));
            if (!fieldid.equals("")) {
                float showorder = Util.getFloatValue(Util.null2String(rs.getString("showorder")), 0);
                if (showorder < minOrder) {
                    order = showorder;
                    minOrder = showorder;
                }
            } else if (!id.equals(id1)) {
                float showorder = getMinOrder(id1, order);
                if (showorder < minOrder) {
                    order = showorder;
                    minOrder = showorder;
                }
            }
        }
        return order;
    }


    /**
     * 更新满足置顶条件的数据  mode_top_
     *
     * @param parms
     * @param customId
     * @param sqlFrom
     * @param user
     * @return
     */
    public void rebuildOrderByData(Map<String, Object> parms, int customId, User user, String sqlFrom) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
        String curDate = sdf.format(date);
        String defaultsqltop = "";
        CubeSearchService cubeSearchService = new CubeSearchService();
        //获取mode_customsearch id
        int id = customId;
        //置顶标识
        int enabledtop = 0;
        String detailTable = "";
        String relationCondition = " ";
        int modeid = 0;
        if (!("".equals(sqlFrom))) {
            //获取表名
            String tableName = sqlFrom.substring(5, sqlFrom.indexOf("t1")).trim();
            if (!isVirtualForm) ModeDataIDUpdateSingle.INSTANCE.updateModifyInfo(tableName);
            if (id != 0) {
                String topColumn = "mode_top_" + id;
                //查询 mode_customsearch 表里面的信息，enabledtop  defaultsqltop
                String sqlForModeCustomsearch = "select enabledtop,defaultsqltop,detailtable,modeid from mode_customsearch where id = " + id;
                RecordSet rs = new RecordSet();
                rs.execute(sqlForModeCustomsearch);
                while (rs.next()) {
                    enabledtop = Util.getIntValue(Util.null2String(rs.getString("enabledtop"), "0"));
                    defaultsqltop = Util.null2String(rs.getString("defaultsqltop"), "");
                    String temp = Util.null2String(rs.getString("detailtable"));
                    detailTable = !"".equals(temp) ? "," + temp + " d1 " : "";
                    relationCondition = !"".equals(temp) ? " and t1.id = d1.mainid  " : " ";
                    modeid = Util.getIntValue(Util.null2String(rs.getString("modeid"), "0"));
                }

                defaultsqltop = cubeSearchService.handleDefaultSql(parms, defaultsqltop, user);
                if (enabledtop == 1) {
                    //查询设置表单的表 存不存在标记置顶的字段
                    try {
                        String sqlGetTopCol = "select id, " + topColumn + " from " + tableName + " where 1=2 ";
                        rs.execute(sqlGetTopCol);
                        if ("".equals(rs.getExceptionMsg())) {//rs对象内部处理了异常，只能根据msg 来判断是否有异常，先保留try catch

                            //将当天的数据全部更新为null
                            String sqlUpdateDayData = " update " + tableName + " set " + topColumn + "=null where modedatacreatedate='" + curDate + "' and " + topColumn + "=1";
                            // String sqlUpdateDayData = " update " + tableName + " set " + topColumn + "=null ";
                            rs.execute(sqlUpdateDayData);

                            //查询出当天满足条件的(当天数据量较小)
                            //  String sqlGetCurData = " select id from " + tableName + " t1 where t1.modedatacreatedate='" + curDate + "' and " + defaultsqltop;
                            if (defaultsqltop.indexOf("d1.") == -1) {
                                detailTable = "";
                                relationCondition = "";
                            }
                            String sqlGetCurData = " select t1.id from " + tableName + " t1  " + detailTable + "  where t1.modedatacreatedate='" + curDate + "' and " + defaultsqltop + relationCondition + " and t1.formmodeid =" + modeid + " and t1." + topColumn + " is null ";

                            rs.execute(sqlGetCurData);
                            int dataId = 0;
                            if ("".equals(rs.getExceptionMsg())) {
                                while (rs.next()) {
                                    dataId = Util.getIntValue(rs.getString("id"), 0);
                                    sqlUpdateDayData = " update " + tableName + " set " + topColumn + "=1 where id=" + dataId;
                                    rs.execute(sqlUpdateDayData);
                                }
                            }


                        }
                        //开启一步线程处理

                    } catch (Exception e) {
                        //打印日志，列不存在

                    }
                }
            }
        }


    }

    public HashMap<String, Object> getConditionParamter(HttpServletRequest request, HttpServletResponse response) {
        HashMap<String, Object> dataSource = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        RecordSet rsExpend = new RecordSet();
        User user = HrmUserVarify.getUser(request, response);
        String customId = Util.null2String(request.getParameter("customid"));
        String isfromTab = Util.null2String(request.getParameter("isfromTab"));
        String isfromRight = Util.null2String(request.getParameter("isfromRight"));
        String tabid = Util.null2String(request.getParameter("tabid"));
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();

        String currentModeId = Util.null2String(customSearchComInfo.getModeId(customId));
        if (isfromTab.equals("1")) {
            dataSource.put("isfromTab", isfromTab);
        }
        if (isfromRight.equals("1")) {
            dataSource.put("isfromRight", isfromRight);
        }
        if ((StringUtils.isNotBlank(isfromTab) || StringUtils.isNotBlank(isfromRight)) && StringUtils.isNotBlank(tabid)) {
            // 处理页面扩展关联字段不回显
            rs.executeQuery("select modeid from mode_pagerelatefield where  pageexpandid = ?", tabid);
            String expendModeId = "";
            while (rs.next()) {
                expendModeId = Util.null2String(rs.getString("modeid"));
            }

            RecordSet tabRs = new RecordSet();
            String expendSql = "select hreffieldname from mode_pagerelatefielddetail where mainid in (select id from mode_pagerelatefield where modeid = ? and pageexpandid = ? and hrefid = ?)";
            tabRs.executeQuery(expendSql, expendModeId, tabid, customId);
            List<String> herfFieldNames = new ArrayList<>();
            while (tabRs.next()) {
                herfFieldNames.add(Util.null2String(tabRs.getString("hreffieldname")));
            }

            String targetModelFieldSql = "select id,fieldname from workflow_billfield where billid = (select formid from modeinfo where id = ?) and (detailtable is null or detailtable='')";
            List<String> linkFields = new ArrayList<>();
            tabRs.executeQuery(targetModelFieldSql, currentModeId);
            while (tabRs.next()) {
                String fieldId = Util.null2String(tabRs.getString("id"));
                String fieldName = Util.null2String(tabRs.getString("fieldname"));
                herfFieldNames.forEach(name -> {
                    if (name.equals(fieldName)) {
                        linkFields.add("con_" + fieldId);
                    }
                });

            }

            dataSource.put("linkFields", linkFields);
        }

        String defSql = Util.toScreenToEdit(customSearchComInfo.getDefaultSql(customId), user.getLanguage());
        String searchType = Util.null2String(request.getParameter("type"));  //判断是高级查询还是普通查询
        rs.executeQuery("select a.fieldid,a.isquery,a.isadvancedquery,a.searchparaname,a.searchparaname1,a.conditiontransition, b.fieldhtmltype,b.fieldname, b.type typeTemp,b.fielddbtype,a.conditionValue,a.conditionValue1,a.conditionValue2 " +
                "    from mode_customdspfield a left join  workflow_billfield b on a.fieldid = b.id " +
                "    where  a.customid = ?  ", customId);
        while (rs.next()) {
            String conditiontransition = Util.null2String(rs.getString("conditiontransition"));
            String searchparaname = Util.null2String(rs.getString("searchparaname"));
            String isquery = Util.null2String(rs.getString("isquery"));
            String fieldnamess = Util.null2String(rs.getString("fieldname"));
            String conditionValue = Util.null2String(rs.getString("conditionValue"));
            String conditionValue1 = Util.null2String(rs.getString("conditionValue1"));
            String conditionValue2 = Util.null2String(rs.getString("conditionValue2"));
            String isadvancedquery = Util.null2String(rs.getString("isadvancedquery"));
            String searchparaname1 = Util.null2String(rs.getString("searchparaname1"));
            int fieldid = rs.getInt("fieldid");
            int fieldhtmltype = Util.getIntValue(rs.getString("fieldhtmltype"));
            int type = Util.getIntValue(rs.getString("typeTemp"));
            String fielddbtype = Util.null2String(rs.getString("fielddbtype"));
            String fieldname = "con_" + (fieldid > 0 ? fieldid : "" + (-fieldid));
            String searchparavalue = null;
            String searchparavalue1 = null;
            if (!StringHelper.isEmpty(searchparaname)) {
                searchparavalue = replaceParams(Util.null2String(request.getParameter(searchparaname)), user, false);
            }

            if (!StringHelper.isEmpty(searchparaname1)) {
                searchparavalue1 = replaceParams(Util.null2String(request.getParameter(searchparaname1)), user, true);
            }
            boolean isquicksearch = false;
            String sql1 = "select isquicksearch from mode_quicksearch_setting where customid=?";//查询列表是否开启快捷查询
            rs1.executeQuery(sql1, customId);
            while (rs1.next()) {
                isquicksearch = "1".equals(rs1.getString("isquicksearch"));
            }
            if ((isquicksearch) && fieldhtmltype == 1 && (type == 2 || type == 3 || type == 4 || type == 5)) {
                String sql = "select id,customname,minnum,maxnum from mode_quicksearch_detail where fieldid=? and customid=? order by orderid";
                rs1.executeQuery(sql, fieldid, customId);
                boolean flg = false;
                while (rs1.next()) {
                    String minnum = Util.null2String(rs1.getString("minnum"));
                    String maxnum = Util.null2String(rs1.getString("maxnum"));
                    if (!"".equals(minnum) && !StringHelper.isEmpty(searchparavalue)) {
                        float num = Util.getFloatValue(minnum);
                        float num2 = Util.getFloatValue(maxnum);
                        if (num <= Util.getFloatValue(searchparavalue) && num2 >= Util.getFloatValue(searchparavalue) && !flg) {
                            flg = true;
                            searchparavalue = num + "";
                            searchparavalue1 = num2 + "";
                        }
                    }
                }
            }

            String conValue = "";
            if (StringHelper.isEmpty(searchparavalue) && StringHelper.isEmpty(searchparavalue1)) {//con_fielid格式作为查询条件，解析到查询条件上
                conValue = Util.null2String(request.getParameter(fieldname));
                boolean isDefSql = true;
//                if(!"".equals(defSql)&&defSql.indexOf(fieldnamess) > -1){
//                    isDefSql = false;
//                }
                if (fieldhtmltype == 1 && "1".equals(isquery) && StringHelper.isEmpty(conValue) && isDefSql) {//浮点数，正整数类型
                    if (type == 1) {
                        conValue = conditionValue;
                    } else {
                        conValue = conditionValue + "," + conditionValue1;
                    }
                } else if (fieldhtmltype == 3 && type == 19 && "1".equals(isquery) && StringHelper.isEmpty(conValue)) {  //时间
                    if ("".equals(conditionValue) && "".equals(conditionValue1)) {
                        conValue = conValue;
                    } else {
                        conValue = conditionValue + "," + conditionValue1;
                    }
                } else if ((fieldhtmltype == 3 && type == 290 || fieldid == -8) && ("1".equals(isquery) || isquicksearch) && StringHelper.isEmpty(conValue)) {  //日期时间
                    if ("".equals(conditionValue2) && "".equals(conditionValue1)) {
                        conValue = conValue;
                    } else {
                        conValue = conditionValue1 + "," + conditionValue2;
                    }
                }
//                else if(fieldhtmltype==3&&type==57&&"1".equals(isquery)&&StringHelper.isEmpty(conValue)){  //多部门
//                    conValue = conditionValue+","+conditionValue1;
//                }
                else if ("1".equals(isquery) && StringHelper.isEmpty(conValue) && !"null".equals(conditionValue) && !"".equals(conditionValue) && isDefSql) {
                    if (!"".equals(conditionValue1) && !"null".equals(conditionValue1) && conditionValue2.indexOf("null") < 0) {
                        if (!"".equals(conditionValue2) && !"null".equals(conditionValue2)) {
                            conditionValue = conditionValue + "," + conditionValue1 + "," + conditionValue2;
                        } else {
                            conditionValue = conditionValue + "," + conditionValue1;
                        }
                    }
                    if (fieldhtmltype == 3 && type == 1 && "-5".equals(conditionValue) && ("".equals(conditionValue2) || conditionValue2.indexOf("null") > -1)) {
                        conditionValue = "";
                    }
//                    else if(fieldhtmltype==3&&type==57&&("".equals(conditionValue2)||conditionValue2.indexOf("null")>-1)){
//                        conditionValue = "";
//                    }
                    conValue = conditionValue;
                }
            }

            if (!conValue.equals("") && (isquery.equals("0") && searchType.equals("base") || isadvancedquery.equals("0") && searchType.equals("advanced"))) {//为开启查询条件
//            	if(type==57){
//            		dataSource.put("dbm_"+fieldname, "0");
//            	}
                if (fieldid != -8) {
                    dataSource.put(fieldname, conValue);
                }
            } else {
                if (fieldid > 0) {
//                    if(type==57){
//                        fieldname="dbm_"+fieldname;
//                	}
                    dataSource.put(fieldname, this.getConditionParamterValue(fieldhtmltype, type, conValue, user, fielddbtype, searchparavalue, searchparavalue1, conditiontransition));
                } else if (fieldid == -1) {
                    dataSource.put(fieldname, this.getConditionParamterValue(3, 2, conValue, user, fielddbtype, searchparavalue, searchparavalue1, conditiontransition));
                } else if (fieldid == -2) {
                    dataSource.put(fieldname, this.getConditionParamterValue(3, 1, conValue, user, fielddbtype, searchparavalue, searchparavalue1, conditiontransition));
                } else if (fieldid == -7) {
                    dataSource.put(fieldname, this.getConditionParamterValue(3, 1, conValue, user, fielddbtype, searchparavalue, searchparavalue1, conditiontransition));
                } else if (fieldid == -8) {
                    dataSource.put(fieldname, this.getConditionParamterValue(3, 290, conValue, user, fielddbtype, searchparavalue, searchparavalue1, conditiontransition));
                } else if (fieldid == -3) {
//                	if(Util.getIntValue(searchparavalue) >0){
//                        dataSource.put(fieldname,searchparavalue);
//                    }
                    dataSource.put(fieldname, this.getConditionParamterValue(1, 1, conValue, user, "int", searchparavalue, searchparavalue1, conditiontransition));
                } else if (fieldid == -4) {
                    if (!StringHelper.isEmpty(searchparavalue)) {
                        FormModeTransMethod formMethod = new FormModeTransMethod();
                        String name = formMethod.getTabLabel(searchparavalue, currentModeId);
                        Map<String, Object> map = BrowserHelper.constructMap("value", searchparavalue, "valueSpan", name);
                        dataSource.put(fieldname, map);
                    }
                } else if (fieldid == -5) {
                    if (!StringHelper.isEmpty(searchparavalue)) {
                        dataSource.put(fieldname, searchparavalue);
                    }
                }
            }
        }

        return dataSource;
    }

    private String replaceParams(String value, User user, Boolean dateFlag) {
        if ("$date$".equalsIgnoreCase(value)) {
            if (dateFlag) {
                return DateHelper.getCurrentDate();
            } else {
                return "1";
            }

        } else if ("$ThisWeek$".equalsIgnoreCase(value)) {
            return "2";
        } else if ("$ThisMonth$".equalsIgnoreCase(value)) {
            return "3";
        } else if ("$ThisSeason$".equalsIgnoreCase(value)) {
            return "4";
        } else if ("$ThisYear$".equalsIgnoreCase(value)) {
            return "5";
        } else if ("$LastMonth$".equalsIgnoreCase(value)) {
            return "7";
        } else if ("$LastYear$".equalsIgnoreCase(value)) {
            return "8";
        } else if ("$UserId$".equalsIgnoreCase(value)) {
            return "" + user.getUID();
        } else if ("$DepartmentId$".equalsIgnoreCase(value)) {
            try {
                ResourceComInfo resourceComInfo = new ResourceComInfo();
                return resourceComInfo.getDepartmentID("" + user.getUID());
            } catch (Exception e) {
            }
        } else if ("$SubcompanyId$".equalsIgnoreCase(value)) {
            try {
                ResourceComInfo resourceComInfo = new ResourceComInfo();
                return resourceComInfo.getSubCompanyID("" + user.getUID());
            } catch (Exception e) {
            }
        } else if ("$yesterday$".equalsIgnoreCase(value)) {
            return "9";
        } else if ("$tomorrow$".equalsIgnoreCase(value)) {
            return "10";
        } else if ("$lastWeek$".equalsIgnoreCase(value)) {
            return "11";
        } else if ("$nextWeek$".equalsIgnoreCase(value)) {
            return "12";
        }
        return value;
    }

    /**
     * 查询执行的时候是分页组件解析的xml 数据
     * cube_vsql 做的查询如果有写 <> 相关条件会导致分页组件解析异常
     * Util中没有比较合适的替换参数的方法，可能会导致参数替换多，导致查询解析异常
     *
     * @param ss
     * @return
     */
    private String replaceParamBefUse(String ss) {
        char c[] = ss.toCharArray();
        char ch;
        int i = 0;
        StringBuffer buf = new StringBuffer();

        while (i < c.length) {
            ch = c[i++];

            if (ch == '<')
                buf.append("&lt;");
            else if (ch == '>')
                buf.append("&gt;");
            else
                buf.append(ch);
        }
        return buf.toString();
    }


    private Object getConditionParamterValue(int fieldhtmltype, int type, String conValue, User user, String fielddbtype, String searchparavalue, String searchparavalue1, String conditiontransition) {
        if (conditiontransition.equalsIgnoreCase("1")) {
            type = Util.getIntValue(convertSingleBrowserTypeToMulti_All(type + ""));
        }
        if (!conValue.equals("")) {
            String[] conValues = conValue.split(",");
            if (conValues.length == 1) {
                searchparavalue = conValues[0];
            } else if (conValues.length == 2) {
                searchparavalue = conValues[0];
                searchparavalue1 = conValues[1];
            } else if (conValues.length == 3) {
                searchparavalue = conValues[1];
                searchparavalue1 = conValues[2];
            }
//            if(fieldhtmltype==3&&type==57){
//                if(conValues.length==1){
//                    searchparavalue = conValues[0];
//                }else if(conValues.length>1){
//                    searchparavalue = conValues[0];
//                    if("0".equals(searchparavalue)){
//                        searchparavalue1 = conValue.substring(2,conValue.length());
//                    }else{
//                        searchparavalue1 = conValue.substring(3,conValue.length());
//                    }
//                }
//
//            }
            if (conditiontransition.equalsIgnoreCase("1") && type == 1) {
                if (conValues.length >= 3) {
                    String vs = conValues[0];
                    if ("-5".equals(vs)) {
                        searchparavalue = conValue.substring(3, conValue.length());
                    }
                }
            }
        } else if (!StringHelper.isEmpty(searchparavalue)) {
            conValue = searchparavalue;
        }
        if (StringHelper.isEmpty(searchparavalue) && StringHelper.isEmpty(searchparavalue1) && conValue.split(",").length < 4) {
            return null;
        }
        HashMap<String, Object> result = new HashMap<String, Object>();
        if (fieldhtmltype == 1) {
            if (type == 1) {
                return searchparavalue;
            } else {
                return new String[]{searchparavalue, searchparavalue1};
            }
        } else if (fieldhtmltype == 3) {
            if (type == 2) { //日期
                if (Util.getIntValue(searchparavalue) > 0) {
                    String[] dates = new String[2];
                    if (searchparavalue.equals("1")) { //当前日期
                        dates = getSearchDate("1");
                    } else if (searchparavalue.equals("2")) { //本周
                        dates = getSearchDate("2");
                    } else if (searchparavalue.equals("3")) {//本月
                        dates = getSearchDate("3");
                    } else if (searchparavalue.equals("7")) {//上一月
                        dates = getSearchDate("7");
                    } else if (searchparavalue.equals("4")) {//本季
                        dates = getSearchDate("4");
                    } else if (searchparavalue.equals("5")) {//本年
                        dates = getSearchDate("5");
                    } else if (searchparavalue.equals("8")) {//上一年
                        dates = getSearchDate("8");
                    }
                    return new String[]{searchparavalue, dates[0], dates[1]};
                } else {
                    return new String[]{"6", searchparavalue, searchparavalue1};
                }
            } else if (type == 1 && !"1".equalsIgnoreCase(conditiontransition)) {
                if (Util.getIntValue(searchparavalue) <= 0) {
                    return new String[]{searchparavalue, ""};
                } else {
                    List<Object> objs = null;
                    try {
                        objs = new BrowserValueInfoService().getBrowserValueInfo(1, null, 0, searchparavalue, user.getLanguage(), -1);
                    } catch (Exception e) {
                    }
                    String valueSpan = "";
                    if (objs != null) {
                        for (Object obj : objs) {
                            valueSpan += ((BrowserValueInfo) obj).getName() + ",";
                        }
                    }
                    return new Object[]{"-5", new Object[]{searchparavalue, valueSpan, objs}};
                }
            } else if (type == 19) {
                return new String[]{searchparavalue, searchparavalue1};
            } else if (type == 1 && "1".equals(conditiontransition)) {
                String temp = "";
                if ("-5".equals(searchparavalue) && conValue.length() >= 3) {//人力资源字段
                    temp = conValue.substring(2);
                }
                String[] ids = StringUtils.split(StringUtils.isEmpty(temp) ? searchparavalue : temp, ",");
                if (ids.length >= 1) {
                    List<Object> multiHrm = new ArrayList<>();
                    multiHrm.add("-5");
                    List<Object> data = new ArrayList<Object>();
                    List<Object> dataBrowser = new ArrayList<Object>();
                    data.add(StringUtils.join(ids, ","));
                    String valueSpan = "";
                    for (String id : ids) {
                        if (Util.getIntValue(id) <= 0) {
                            return new String[]{id, ""};
                        } else {
                            List<Object> objs = null;
                            try {
                                objs = new BrowserValueInfoService().getBrowserValueInfo(1, null, 0, id, user.getLanguage(), -1);

                            } catch (Exception e) {
                            }
                            if (objs != null) {
                                for (Object obj : objs) {
                                    valueSpan += ((BrowserValueInfo) obj).getName() + ",";
                                }
                            }
                            dataBrowser.add(objs.get(0));
                        }
                    }
                    data.add(valueSpan);
                    data.add(dataBrowser);
                    multiHrm.add(data);
                    return multiHrm;
                } else {
                    if (Util.getIntValue(searchparavalue) != -5) {
                        return new String[]{searchparavalue, ""};
                    } else {
                        List<Object> objs = null;
                        try {
                            objs = new BrowserValueInfoService().getBrowserValueInfo(1, null, 0, searchparavalue1, user.getLanguage(), -1);
                        } catch (Exception e) {
                        }
                        String valueSpan = "";
                        if (objs != null) {
                            for (Object obj : objs) {
                                valueSpan += ((BrowserValueInfo) obj).getName() + ",";
                            }
                        }
                        return new Object[]{"-5", new Object[]{searchparavalue1, valueSpan, objs}};
                    }
                }
            } else if (type == 4 || type == 57) {
                BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                List<BrowserValueInfo> valueObj = null;
                String name = "";
                try {
                    valueObj = browserValueInfoService.getBrowserValueInfo(String.valueOf(type), fielddbtype, 0, conValue, user.getLanguage(), "", "");
                    for (int i = 0; i < valueObj.size(); i++) {
                        BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                        name += m.getName() + ",";
                    }
                } catch (Exception e) {
                }
                if (name.endsWith(",")) {
                    name = name.substring(0, name.length() - 1);
                }
                return BrowserHelper.constructMap("value", conValue, "valueSpan", name, "valueObj", valueObj);
            } else if (type == 164 || type == 194) {
                BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                List<BrowserValueInfo> valueObj = null;
                String name = "";
                try {
                    valueObj = browserValueInfoService.getBrowserValueInfo(String.valueOf(type), fielddbtype, 0, conValue, user.getLanguage(), "", "");
                    for (int i = 0; i < valueObj.size(); i++) {
                        BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                        name += m.getName() + ",";
                    }
                } catch (Exception e) {
                }
                if (name.endsWith(",")) {
                    name = name.substring(0, name.length() - 1);
                }
                // return new Object[]{searchparavalue,name,valueObj};
                return BrowserHelper.constructMap("value", conValue, "valueSpan", name, "valueObj", valueObj);
            } else {
                BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
                List<Object> valueObj = null;
                String name = "";
                try {
                    valueObj = browserValueInfoService.getBrowserValueInfo(type, fielddbtype, 0, conValue, user.getLanguage(), -1);
                    for (int i = 0; i < valueObj.size(); i++) {
                        BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                        name += m.getName() + ",";
                    }
                } catch (Exception e) {
                }
                if (name.endsWith(",")) {
                    name = name.substring(0, name.length() - 1);
                }
                if (type == 290) {
                    List<Object> conValueObj = new ArrayList<Object>();
                    String[] strcon = conValue.split(",");
                    conValueObj = Arrays.asList(strcon);
                    return BrowserHelper.constructMap("value", conValueObj, "valueSpan", name, "valueObj", valueObj);
                } else {
                    return BrowserHelper.constructMap("value", conValue, "valueSpan", name, "valueObj", valueObj);
                }
                // return new Object[]{searchparavalue,name,valueObj};

                /*if(type==57){//多部门
                	List<Object> list=new ArrayList<Object>();
                	List<Object> list1=new ArrayList<Object>();
                	list.add("0");
                	list1.add(conValue);
                	list1.add(name);
                	list1.add(valueObj);
                	list.add(list1);
                	return list;
                }else{
                	return BrowserHelper.constructMap("value",conValue,"valueSpan",name,"valueObj",valueObj);
                }*/
            }
        } else if (fieldhtmltype == 6) {
            BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
            List<Object> valueObj = null;
            String name = "";
            try {
                valueObj = browserValueInfoService.getBrowserValueInfo(9, null, 0, conValue, user.getLanguage(), -1);
                for (int i = 0; i < valueObj.size(); i++) {
                    BrowserValueInfo m = (BrowserValueInfo) valueObj.get(i);
                    name += m.getName() + ",";
                }
            } catch (Exception e) {
            }
            if (name.endsWith(",")) {
                name = name.substring(0, name.length() - 1);
            }
            return BrowserHelper.constructMap("value", conValue, "valueSpan", name, "valueObj", valueObj);
        } else if (fieldhtmltype == 5 && conditiontransition.equalsIgnoreCase("0")) {
            return conValue;
        } else if (fieldhtmltype == 5 && conditiontransition.equalsIgnoreCase("1")) {
            return conValue;
        } else {
            return searchparavalue;
        }
    }

    private Map<String, Object> getGroupCount(String sqlWhere, String sqlFrom, String poolname, User user) {
        Map<String, Object> result = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        RecordSet rs2 = new RecordSet();
        if (sqlWhere.indexOf("where 1=2") > -1) {
            sqlWhere = sqlWhere.replace("where 1=2", "where 1=1");
        }
        FormModeConfig formModeConfig = new FormModeConfig();
        String enableTreeList = formModeConfig.getEnableTreeList();  //树形展示是有formmode.properties中控制的。即使链接链接有treetype也不行。enableTreeList为y时开启
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery("select istreesearch,treeposition,datashowtype from mode_customsearch where id=?", customId);
        String isTreeSearch = "";
        String treeposition = "";
        String treeSqlWhere = "";
        if (recordSet.next()) {
            isTreeSearch = Util.null2String(recordSet.getString("isTreeSearch"));
            if (!isTreeSearch.equals("1")) {
                isTreeSearch = Util.null2String(recordSet.getString("datashowtype")).equals("4") ? "1" : "";
            }
            treeposition = Util.null2String(recordSet.getString("treeposition"));
        }
        if ("1".equals(isTreeSearch) && !"".equals(treeposition) && "y".equals(enableTreeList) && "".equals(isTreeType)) {
            treeSqlWhere = " and (t1." + treeposition + "='' or t1." + treeposition + " is null) ";
        }
        String sql = "select tt." + groupName + ", sum(1) count from (select " + groupNameAlias + groupName + sqlFrom + sqlWhere + treeSqlWhere + ") tt group by tt." + groupName;
        String groupsql = "select * from customfieldgroupsetting where customid=? and fieldid=? and isshow=0 order by id";
        String myAdviceSql = "select * from customfieldgroupsetting where customid=? and fieldid=? and fieldoptvalue='999' order by id";
        String myAdviceSqlGrouop = "select tt." + groupName + ", sum(1) count from (select " + groupNameAlias + groupName + sqlFrom + sqlWhere + treeSqlWhere + " and modedatacreater = " + user.getUID() + ") tt group by tt." + groupName;

        rs.executeQuery(groupsql, customId, groupFieldid);
        rs1.executeQuery(myAdviceSql, customId, groupFieldid);
        ArrayList<String> notshow = new ArrayList<String>();
        while (rs.next()) {
            notshow.add(rs.getString("fieldoptvalue"));
        }
        if (rs1.next()) {
            rs2.executeQuery(myAdviceSqlGrouop);
            int myadviceCount = 0;
            while (rs2.next()) {
                int count = rs2.getInt(2);
                myadviceCount += count;
            }
            result.put("myadvice", "" + myadviceCount);
        }

        if (StringHelper.isEmpty(poolname)) {
            rs.executeQuery(sql);
        } else {
            rs.executeSql(sql, true, poolname);
        }
        int all = 0;
        while (rs.next()) {
            String key = rs.getString(1);
            int count = rs.getInt(2);
            all += count;
            if (notshow.contains(key)) {
                continue;
            }
            if (!this.isChexkAdvanedDate) {
                count = 0;
                all = 0;
            }
            if (StringHelper.isEmpty(key)) {
                result.put("empty", "" + count);
            } else {
                result.put(key, "" + count);
            }
        }
        if (!notshow.contains("1000")) {
            result.put("all", "" + all);
        }
        return result;
    }

    private String getGroupSqlWhere(HttpServletRequest request, HttpServletResponse response) {
        String groupValue = Util.null2String(request.getParameter("groupValue"));
        StringBuffer sqlwhere = new StringBuffer();
        RecordSet rs = new RecordSet();
        rs.executeQuery("select a.fieldname, a.viewtype,a.id from workflow_billfield a, mode_customDspfield b" +
                " where a.id = b.fieldid and b.customid = ? and b.isgroup = 1 ", customId);
        if (rs.next()) {
            this.groupName = rs.getString(1);
            this.groupNameAlias = ("1".equals(rs.getString(2)) ? DETAIL_TABLE_ALIAS : MAIN_TABLE_ALIAS) + ".";
            this.groupFieldid = rs.getString(3);
            if (null != this.grouptype && !this.grouptype.equals("") && groupValue.equals("")) {
                int groupInt1 = 0;
                Map<String, Object> groupParams1 = new HashMap<>();
                List groupL = new ArrayList<>();
                rs.executeQuery("select * from customfieldgroupsetting t where t.customid=? and t.fieldid=? and t.isshow=1 order by orderid", customId, groupFieldid);
                while (rs.next()) {
                    String fieldoptvalue = Util.null2String(rs.getString("fieldoptvalue"));
                    if (null != fieldoptvalue && !fieldoptvalue.equals("")) {
                        groupParams1.put(groupInt1 + "", fieldoptvalue);
                        groupInt1++;
                        groupL.add(groupParams1);
                    }
                }
                if (groupL.size() > 0) {
                    String groupValues = (String) groupParams1.get(this.grouptype);
                    if (null != groupValues && !groupValues.equals("")) {
                        if (groupValues.equals("1000")) {
                            groupValue = "all";
                        } else if (groupValues.equals("999")) {
                            groupValue = "myadvice";
                        } else {
                            groupValue = groupValues;
                        }
                    } else {
                        rs.executeQuery("select * from customfieldgroupsetting t where t.customid=? and t.fieldid=? and t.isshow=1 and t.isdefault=1", customId, groupFieldid);
                        if (rs.next()) {
                            String fieldoptvalue = Util.null2String(rs.getString("fieldoptvalue"));
                            if (!fieldoptvalue.isEmpty() && groupValue.isEmpty()) {
                                groupValue = fieldoptvalue;
                                if (fieldoptvalue.equals("1000")) {
                                    groupValue = "all";
                                } else if (groupValues.equals("999")) {
                                    groupValue = "myadvice";
                                }
                            }
                        }
                    }
                } else {
                    List<SearchConditionOption> options = new CubeFieldService().getWorkflowSelectItems(groupFieldid, user.getLanguage(), false);
                    int groupInt = 0;
                    Map<String, Object> groupParams = new HashMap<>();
                    for (SearchConditionOption option : options) {
                        groupInt++;
                        groupParams.put(groupInt + "", option.getKey());
                    }
                    String groupValu2 = (String) groupParams.get(this.grouptype);
                    if (null != groupValu2 && !groupValu2.equals("")) {
                        if (groupValu2.equals("1000")) {
                            groupValue = "all";
                        } else if (groupValu2.equals("999")) {
                            groupValue = "myadvice";
                        } else {
                            groupValue = groupValu2;
                        }
                    } else if (this.grouptype.equals("0")) {
                        groupValue = "all";
                    }
                }
            } else {
                rs.executeQuery("select * from customfieldgroupsetting t where t.customid=? and t.fieldid=? and t.isshow=1 and t.isdefault=1", customId, groupFieldid);
                if (rs.next()) {
                    String fieldoptvalue = Util.null2String(rs.getString("fieldoptvalue"));
                    if (!fieldoptvalue.isEmpty() && groupValue.isEmpty()) {
                        groupValue = fieldoptvalue;
                        if (fieldoptvalue.equals("1000")) {
                            groupValue = "all";
                        } else if (fieldoptvalue.equals("999")) {
                            groupValue = "myadvice";
                        }
                    }
                }
            }
            if (!"all".equalsIgnoreCase(groupValue)) {
                if (!StringHelper.isEmpty(this.groupName) && !StringHelper.isEmpty(groupValue)) {
                    if (groupValue.equals("myadvice")) {
                        StringBuffer myadvicesqlwhere = new StringBuffer();
                        sqlwhere = myadvicesqlwhere.append(this.groupNameAlias).append("modedatacreater = ").append(this.user.getUID());
                    } else {
                        sqlwhere.append(this.groupNameAlias).append(this.groupName);
                        if ("empty".equalsIgnoreCase(groupValue)) {
                            sqlwhere.append(" is null ");
                        } else {
                            sqlwhere.append(" = '").append(groupValue).append("' ");
                        }
                    }
                }
            }
        }
        return sqlwhere.toString();
    }

    private SplitTableOperateBean getOperateList() {//(jsbody customButtonParameter 还是要展示全部，不然sql会出问题 ) and (isshowlist <>1 or isshowlist is null)
        SplitTableOperateBean operateBean = new SplitTableOperateBean();
        if (isBackUpData) {
            return null;
        }
        RecordSet rs = new RecordSet();
        rs.executeQuery("select * from mode_customSearchButton where objid=? and isshow=1 order by showorder asc,id desc", customId);
        if (rs.getCounts() < 1) {
            return null;
        }
        Popedom popedom = new Popedom();
        popedom.setTransmethod("com.api.cube.util.CubeSearchTransMethodProxy.getSearchResultOperation");
        popedom.setOtherpara(customId + "+" + user.getUID() + "+" + modeId + "+" + formId);
        List<Operate> ls = new ArrayList<Operate>();
        int index = 1;
        Map<String, String> expenParam = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        while (rs.next()) {
            int pageExpandId = Util.getIntValue(rs.getString("pageexpandid"), 0);
            String buttonName = Util.null2String(rs.getString("buttonname"));
            buttonName = Util.formatMultiLang(buttonName.trim(), user.getLanguage() + "");
            String hreftype = Util.null2String(rs.getString("hreftype"));
            String hreftargetParid = Util.null2String(rs.getString("hreftargetParid"));
            String hreftargetParval = Util.null2String(rs.getString("hreftargetParval"));
            String hreftarget = Util.null2String(rs.getString("hreftarget"));
            String jsmethodname = Util.null2String(rs.getString("jsmethodname"));
            String jsParameter = Util.null2String(rs.getString("jsParameter"));
            String jsmethodbody = Util.null2String(rs.getString("jsmethodbody"));
            String isshowlist = Util.null2String(rs.getString("isshowlist"));// 1 直接展示的查询列表上
            String icon = Util.null2String(rs.getString("icon"));

            String sqlForExpand = "select a.id, a.expendname, a.showtype, a.opentype, a.hreftype," +
                    "            a.hrefid, a.hreftarget, a.showorder, a.issystem, a.groupid," +
                    "            a.showorder,a.defaultselect,a.isenabletip,a.tiptype," +
                    "            a.tipdatasourceid,a.tipsql,a.tipjk " +
                    "       from mode_pageexpand a " +
                    " where a.id = ?" +
                    "   order by a.showorder asc,a.id desc ";
            RecordSet rsForExpand = new RecordSet();
            rsForExpand.executeQuery(sqlForExpand, pageExpandId);

            while (rsForExpand.next()) {
                //获取页面扩展括号内的内容
                //1、封装相关参数
                expenParam.put("isenabletip", Util.null2String(rsForExpand.getString("isenabletip")));
                expenParam.put("tiptype", Util.null2String(rsForExpand.getString("tiptype")));
                expenParam.put("tipdatasourceid", Util.null2String(rsForExpand.getString("tipdatasourceid")));
                expenParam.put("tipsql", Util.null2String(rsForExpand.getString("tipsql")));
                expenParam.put("tipjk", Util.null2String(rsForExpand.getString("tipjk")));
            }
            //2、获取相关拼接内容#800358
            params.put("formId", formId + "");
            params.put("billid", "1");//用列表查询 指定列表中的第一个数据，用户提示需求#819808
            String expendContext = PageExpandUtil.getExpendContext(expenParam, user, params);
            if (pageExpandId == 0) {
                expendContext = "";
            }

            if (buttonName.indexOf("&") > -1) {
                buttonName = buttonName.replace("&", "&amp;");
            }
            Operate operate = new Operate();
            operate.setIndex("" + index);
            if (icon.indexOf("&") > -1) {
                icon = icon.replace("&", "&amp;");
            }
            operate.setIcon(icon);
            operate.setText(buttonName + expendContext);
            //    if(!isshowlist.equals("1")){//只展示在右侧三个...里面的自定义按钮
            ls.add(operate);
            //    }
            if ("1".equals(hreftype)) {
                this.jsBody.append(jsmethodbody);
                this.jsBody.append(';');
                if (!StringHelper.isEmpty(jsParameter)) {
                    StringBuffer fieldBuf = new StringBuffer();
                    ArrayList<String> fieldList = Util.TokenizerString(jsParameter, ",");
                    for (int fieldIdx = 0; fieldIdx < fieldList.size(); fieldIdx++) {
                        if (!StringHelper.isEmpty(fieldList.get(fieldIdx))) {
                            fieldBuf.append("column:" + fieldList.get(fieldIdx) + "+");
                            customButtonParameter.add(fieldList.get(fieldIdx));
                        }
                    }
                    if (!StringHelper.isEmpty(fieldBuf.toString())) {
                        jsParameter = fieldBuf.substring(0, fieldBuf.length() - 1);
                    }
                    operate.setOtherpara(jsParameter);
                }
                Pattern pat = Pattern.compile("javascript\\s*:");
                if (!pat.matcher(jsmethodname).find()) {//错误格式添加上javascript:防止查询列表报错
                    jsmethodname = "javascript:" + jsmethodname;
                }
                operate.setHref(jsmethodname);
            } else if ("2".equals(hreftype)) {
                if (!StringHelper.isEmpty(hreftargetParval)) {
                    StringBuffer fieldBuf = new StringBuffer();
                    ArrayList<String> fieldList = Util.TokenizerString(hreftargetParval, ",");
                    for (int fieldIdx = 0; fieldIdx < fieldList.size(); fieldIdx++) {
                        if (!StringHelper.isEmpty(fieldList.get(fieldIdx))) {
                            fieldBuf.append("column:" + fieldList.get(fieldIdx) + "+");
                            customButtonParameter.add(fieldList.get(fieldIdx));
                        }
                    }
                    if (!StringHelper.isEmpty(fieldBuf.toString())) {
                        jsParameter = fieldBuf.substring(0, fieldBuf.length() - 1);
                    }
                    operate.setOtherpara(jsParameter);
                }

                String _blank = "_blank";
                if ("2".equals(Util.null2String(rs.getString("hreftargetOpenWay"))))
                    _blank = "_fullwindow";
                hreftarget = hreftarget.replaceAll("&", "&amp;");  //避免&符号在xml解析是报错
                operate.setHref("javascript:modeOpenUrl('" + hreftarget + "','" + _blank + "')");
                operate.setIsalwaysshow("true");
                operate.setLinkkey(hreftargetParid);
                operate.setLinkvaluecolumn(hreftargetParval);
            } else if ("3".equals(hreftype)) {
                String jsAction = this.analyzePageExpandJsAction(pageExpandId);
                if ("".equals(jsAction)) {
                    jsAction = "void(0)";
                }
                operate.setHref("javascript:" + jsAction);
            } else if ("4".equals(hreftype)) {

            } else if ("5".equals(hreftype)) {
                operate.setHref("javascript:likes");
            } else if ("6".equals(hreftype)) {
                operate.setHref("javascript:dislikes");
            }
            index++;
        }
        operateBean.setOperate(ls);
        operateBean.setPopedom(popedom);
        return operateBean;
    }

    public String analyzePageExpandJsAction(int pageExpandId) {
        ModeExpandPageComInfo expandPageComInfo = new ModeExpandPageComInfo();
        String jsAction = "";
        int issystem = Util.getIntValue(expandPageComInfo.getIsSystem("" + pageExpandId), 0);
        int issystemflag = Util.getIntValue(expandPageComInfo.getIsSystemFlag("" + pageExpandId), 0);
        int opentype = Util.getIntValue(expandPageComInfo.getOpentype("" + pageExpandId), 0);
        if (issystem == 1) {//系统菜单
            if (issystemflag == 3) {//编辑
                jsAction = "toEdit(" + pageExpandId + ")";
            } else if (issystemflag == 4) {//共享
                jsAction = "doShare(" + pageExpandId + ")";
            } else if (issystemflag == 5) {//查看删除
                jsAction = "toDel(" + pageExpandId + ")";
            } else if (issystemflag == 7) {//打印
                jsAction = "doprint(" + pageExpandId + ")";
            } else if (issystemflag == 9) {//日志
                jsAction = "viewLog(" + pageExpandId + ")";
            } else if (issystemflag == 11) { //生成二维码
                jsAction = "createQRCode(" + pageExpandId + ")";
            } else if (issystemflag == 170) { //生成条形码
                jsAction = "createBARCode(" + pageExpandId + ")";
            } else if (issystemflag == 14) { //生成条形码
                jsAction = "expCard(" + pageExpandId + ")";
            }
        } else {//用户自定义菜单
            jsAction = "openPageExpandLinkUrl(" + pageExpandId + "," + opentype + ")";
        }
        return jsAction;
    }

    public String getOrderBy() {
        StringBuffer orderby = new StringBuffer();
        //档案自定义排序 返回结果格式 t1.wb1 asc,t1.wb2 desc ....
        String customOrderBy = Util.null2String(ArchivesFModeUtil.getCustomOrderBy(customId + "", user), "");
        if (customOrderBy != "") {
            return customOrderBy;
        }
        RecordSet rs = new RecordSet();
        String sql = " select b.fieldid, a.fieldname, a.viewtype, b.ordertype,a.fielddbtype" +
                "    from  mode_customdspfield b left join workflow_billfield a on a.id = b.fieldid " +
                "   where b.customid = ? and b.isorder = 1 order by b.ordernum asc ";
        rs.executeQuery(sql, customId);
        while (rs.next()) {
            String fieldid = rs.getString("fieldid");
            String ordertype = rs.getString("ordertype");
            String fieldname = Util.null2String(rs.getString("fieldname"));
            String fielddbtype = Util.null2String(rs.getString("fielddbtype"));
            int viewtype = rs.getInt("viewtype");
            if ("-3".equals(fieldid)) {
                fieldname = "id";
                viewtype = 0;
            } else if ("".equals(fieldname) && !"-1".equals(fieldid) && !"-2".equals(fieldid) && !"-7".equals(fieldid) && !"-8".equals(fieldid))
                continue;
            if ("a".equalsIgnoreCase(ordertype)) { //默认升序
                ordertype = "asc";
            } else if ("d".equalsIgnoreCase(ordertype)) { //默认降序
                ordertype = "desc";
            } else {
                continue;
            }

            fieldname = (viewtype == 1 ? DETAIL_TABLE_ALIAS : MAIN_TABLE_ALIAS) + "." + fieldname;
            if ("sqlserver".equalsIgnoreCase(this.dataSourceDBType) && "text".equalsIgnoreCase(fielddbtype)) {
                fieldname = "convert(varchar, " + fieldname + ")";
            }
            if (fieldid.equals("-1")) {
                orderby.append(MAIN_TABLE_ALIAS).append(".modedatacreatedate  ").append(ordertype).append(",")
                        .append(MAIN_TABLE_ALIAS).append(".modedatacreatetime ").append(ordertype).append(",");
            } else if (fieldid.equals("-2")) {
                orderby.append(MAIN_TABLE_ALIAS).append(".modedatacreater  ").append(ordertype).append(",");
            } else if (fieldid.equals("-7")) {
                orderby.append(MAIN_TABLE_ALIAS).append(".modedatamodifier  ").append(ordertype).append(",");
            } else if (fieldid.equals("-8")) {
                orderby.append(MAIN_TABLE_ALIAS).append(".modedatamodifydatetime  ").append(ordertype).append(",");
            } else {
                orderby.append(fieldname).append(" ").append(ordertype).append(",");
            }
        }
        if (orderby.toString().toLowerCase().indexOf(MAIN_TABLE_ALIAS + "." + primaryKey.toLowerCase()) == -1) {
            if (orderby.length() > 0 && !orderby.toString().endsWith(",")) {
                orderby.append(",");
            }
            if ("1".equals(this.sort1141500)) {
                orderby.append(MAIN_TABLE_ALIAS).append(".").append("praise").append(" desc ").append(",");
            } else if ("2".equals(this.sort1141500)) {
                orderby.append(MAIN_TABLE_ALIAS).append(".").append("reply").append(" desc ").append(",");
            }
            orderby.append(MAIN_TABLE_ALIAS).append(".").append(primaryKey).append(" desc ");
        }
        if (!StringHelper.isEmpty(detailTable)) {
            orderby.append(",").append(DETAIL_TABLE_ALIAS).append(".id desc ");
        }
        return orderby.toString();
    }

    private String getSqlWhere(HttpServletRequest request, HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        StringBuffer sqlWhere = new StringBuffer();
        RecordSet rs = new RecordSet();
        RecordSet rs1 = new RecordSet();
        String treesqlwhere = Util.null2String(request.getParameter("treesqlwhere"));
        String expanderrorid = Util.null2String(request.getParameter("expanderrorid"));
        String treehrmtype = Util.null2String(request.getParameter("treehrmtype"));
        String linkFields = Util.null2String(request.getParameter("linkFields"));
        if (!isChexkAdvanedDate) {
            sqlWhere.append(" where 1=2 ");
        } else {
            sqlWhere.append(" where 1=1 ");
        }
        if (!"".equals(expanderrorid)) {
            sqlWhere.append(" and ").append(this.primaryKey).append(" in (").append(expanderrorid).append(") ");
        }
        XssUtil xssUtil = new XssUtil();
        treesqlwhere = xssUtil.get(treesqlwhere);
        //try{
        //    BASE64Decoder decoder = new BASE64Decoder();
        //    treesqlwhere = new String(decoder.decodeBuffer(treesqlwhere), "UTF-8");
        //
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        if (!treesqlwhere.equals("")) {//来自树形关联字段
            int index1 = treesqlwhere.indexOf("=");
            String dbtype = this.dataSourceDBType;
            if (index1 != -1) {
                String where = treesqlwhere.substring(index1 + 1);
                String[] vals = where.split(",");
                treesqlwhere = treesqlwhere.substring(0, index1);
                treesqlwhere = "t1." + treesqlwhere;
                String fieldname = treesqlwhere;
                StringBuilder sb = new StringBuilder();
                sb.append("(");
                if (!"".equals(treehrmtype)) {
                    String hrmWhere = this.getHrmWhere(treehrmtype, request);
                    sb.append(fieldname).append(" in (").append(hrmWhere).append(")");
                } else {
                    for (String val : vals) {
                        val = "'%," + val + ",%'";
                        if (dbtype.toLowerCase().contains("mysql")) {
                            sb.append(" CONCAT(\',\',").append(fieldname).append(",\',\')");
                        } else if (dbtype.indexOf("sqlserver") > -1) {
                            sb.append(" (\',\'+" + "cast(").append(fieldname).append(" as varchar(8000))" + "+\',\')");
                        } else if ("oracle".equalsIgnoreCase(dbtype) || "db2".equalsIgnoreCase(dbtype) || dbtype.indexOf("oracle") > -1 || "postgresql".equalsIgnoreCase(dbtype)) {
                            sb.append(" \',\'||").append(fieldname).append("||\',\'");
                        }
                        sb.append(" like ").append(val).append(" or");
                    }
                }

                treesqlwhere = sb.toString();
                if (treesqlwhere.endsWith("or")) {
                    treesqlwhere = treesqlwhere.replaceAll("or$", "");
                }
                treesqlwhere = treesqlwhere + ")";
            }
            sqlWhere.append(" and ").append(treesqlwhere);
        }

        if (!isVirtualForm) {
            if (modeId > 0) {
                sqlWhere.append(" and ").append(MAIN_TABLE_ALIAS).append(".formmodeid = ").append(modeId);
            }
            if (viewType != 3 && !noRightList && !isVirtualForm && !hasUserRight) {//不是监控、无权限列表、不是虚拟表单
                sqlWhere.append(" and ").append(MAIN_TABLE_ALIAS).append(".").append(primaryKey).append(" = ")
                        .append(RIGHT_TABLE_ALIAS).append(".sourceid");
            }
        }
        if (isVirtualForm && !noRightList && virtualHasRightSql) {
            sqlWhere.append(" and ").append(MAIN_TABLE_ALIAS).append(".").append(primaryKey).append(" = ")
                    .append(RIGHT_TABLE_ALIAS).append(".sourceid");
        }

        //快捷搜索 1本周,2本月,3本季,4本年
        String thisdate = Util.null2String(request.getParameter("thisdate"));
        //快捷搜索 -1本部门,-2本部门(包含下级部门),-3本分部,-4本分部(包含下级分部)
        String thisorg = Util.null2String(request.getParameter("thisorg"));
        //快捷搜索 1未读,2反馈,3已读
        String enabled = Util.null2String(request.getParameter("enabled"));
        if (!StringHelper.isEmpty(thisdate)) {
            sqlWhere.append(this.getDateCondition("t1.modedatacreatedate", thisdate + ",,"));
        }
        if (!StringHelper.isEmpty(thisorg)) {
            sqlWhere.append(this.getHrmCondition("t1.modedatacreater", thisorg, user, "0"));
        }

        if (!StringHelper.isEmpty(enabled) && !"0".equals(enabled)) {
            sqlWhere.append(CubeSearchTransMethod.INSTANCE.getEnabledSql(user, enabled, modeId));
        }

        Map<String, Object> param = ParamUtil.request2Map(request);
        this.defaulSql = handleDefaultSql(param, this.defaulSql, this.user);
        if (!StringHelper.isEmpty(this.defaulSql) && !"2".equals(this.searchconditiontype)) {
            sqlWhere.append(" and ").append("(" + this.defaulSql + ")").append(" ");
        }
        if ("2".equals(this.searchconditiontype)) {
            if (!javafileaddress.equals("")) {
                String classFullName = javafileaddress;
                param.put("user", user);
                String javacondition = Util.null2String(CustomJavaCodeRun.run(classFullName, param)).trim();
                if (!StringHelper.isEmpty(javacondition)) {
                    sqlWhere.append(" and ").append(javacondition).append(" ");
                }
            } else if (!javafilename.equals("")) {
                Map<String, String> sourceCodePackageNameMap = CommonConstant.SOURCECODE_PACKAGENAME_MAP;
                String sourceCodePackageName = sourceCodePackageNameMap.get("2");
                String classFullName = sourceCodePackageName + "." + javafilename;
                param.put("user", user);
                String javacondition = Util.null2String(CustomJavaCodeRun.run(classFullName, param)).trim();
                if (!StringHelper.isEmpty(javacondition)) {
                    sqlWhere.append(" and ").append(javacondition).append(" ");
                }
            }
        }

        //快捷搜索
        String quickSearchFieldid = "";
        String quickSearchValue = Util.null2String(request.getParameter("quickSearchValue"));
        if (!StringHelper.isEmpty(quickSearchValue)) {
            String sql = " select fieldname, viewtype, fielddbtype, fieldhtmltype, type typeTemp,a.id from workflow_billfield a , mode_customdspfield b where a.id = b.fieldid and b.iskey = '1' and b.customid = ? ";
            rs.executeQuery(sql, customId);
            EncryptFieldEntity encryptFieldEntity = null;
            String encrypttable = "";
            if (rs.getCounts() > 0) {
                sqlWhere.append(" and (");
                while (rs.next()) {
                    String fieldname = rs.getString(1);
                    int viewtype = Util.getIntValue(rs.getString(2));
                    encrypttable = tableName;
                    if ("1".equals(viewtype)) {
                        encrypttable = detailTable;
                    }
                    encryptFieldEntity = new EncryptFieldConfigComInfo().getFieldEncryptConfig(encrypttable, fieldname);
                    if (encryptFieldEntity != null && ("1".equals(encryptFieldEntity.getDesensitization()) || "1".equals(encryptFieldEntity.getIsEncrypt()))) {
                        continue;//加密,脱敏字段 过滤
                    }
                    String fielddbtype = rs.getString(3);
                    String fieldhtmltype = rs.getString(4);
                    int type = rs.getInt(5);
                    quickSearchFieldid = Util.null2String(rs.getString("id"));
                    String fieldAlias = viewtype == 1 ? DETAIL_TABLE_ALIAS : MAIN_TABLE_ALIAS;
                    if (fieldhtmltype.equals("2")) {
                        fieldname = fieldAlias + "." + fieldname;
                        sqlWhere.append(this.getTextCondition2(fieldname, quickSearchValue));
                    } else {
                        //工具栏搜索A+B格式支持
                        String tmpvalue = Util.StringReplace(quickSearchValue, "%", "/%");
                        tmpvalue = Util.StringReplace(Util.StringReplace(tmpvalue, "+", "%"), "＋", "%");
                        tmpvalue = Util.StringReplace(tmpvalue, "\\", "/\\");//把\替换为/\
                        if (fieldhtmltype.equals("1") && type == 5) {//金额转换
                            String replace = "replace(" + fieldAlias + "." + fieldname + ",',','')";
                            sqlWhere.append(" LOWER(").append(replace).append(")");
                        } else {
                            sqlWhere.append(" LOWER(").append(fieldAlias).append(".").append(fieldname).append(")");
                        }
                        if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                            if (fielddbtype.contains("number") && fieldhtmltype.equals("1") && (type == 3 || type == 4)) {
                                if (tmpvalue.contains(".")) {
                                    String tmpvalueArry = tmpvalue.substring(0, tmpvalue.indexOf("."));
                                    if (tmpvalueArry.equals("0")) {
                                        if ("jc".equalsIgnoreCase(this.dataSourceDBTypeOrgin)) {
                                            //金仓数据库 与 oracle数据库不同
                                        } else {
                                            tmpvalue = tmpvalue.substring(tmpvalue.indexOf("."));
                                        }
                                        sqlWhere.append(" like LOWER('").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]"), "%", "/%"))
                                                .append("%') ESCAPE '/'  ");
                                    } else {
                                        sqlWhere.append(" like LOWER('%").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]"), "%", "/%"))
                                                .append("%') ESCAPE '/'  ");
                                    }
                                } else {
                                    sqlWhere.append(" like LOWER('%").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]"), "%", "/%"))
                                            .append("%') ESCAPE '/'  ");
                                }
                            } else {
                                sqlWhere.append(" like LOWER('%").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]"), "%", "/%"))
                                        .append("%')  ESCAPE '/' ");
                            }
                        } else {
                            sqlWhere.append(" like LOWER('%").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]"))
                                    .append("%')  ESCAPE '/' ");
                        }
                    }
                    sqlWhere.append(" or ");
                }
                sqlWhere.append(" 1=2 ) ");
            }
        }

        String con__1value = request.getParameter("con_1");
        if (!StringHelper.isEmpty(con__1value)) {
            sqlWhere.append(this.getDateCondition("t1.modedatacreatedate", con__1value));
        }
        String con__8value = Util.null2String(request.getParameter("con_8"));
        if (!StringHelper.isEmpty(con__8value) && !con__8value.trim().equals(",")) {
            //sqlWhere.append(this.getDateCondition("t1.modedatamodifydatetime", con__8value));
            String[] time = con__8value.split(",");
            if (time.length == 1) {
                // 单一值
                if (!"".equals(time[0])) {
                    sqlWhere.append(" and ").append("t1.modedatamodifydatetime").append(" = '").append(new DateTransformer().getServerDateTime(time[0])).append("' ");
                }
            } else if (time.length != 0) {

                if ("".equals(time[0]) && !"".equals(time[1])) {
                    sqlWhere.append(" and ").append("t1.modedatamodifydatetime").append(" <= '").append(new DateTransformer().getServerDateTime(time[1])).append("' ");
                } else if (!"".equals(time[0]) && "".equals(time[1])) {
                    sqlWhere.append(" and ").append("t1.modedatamodifydatetime").append(" >= '").append(new DateTransformer().getServerDateTime(time[0])).append("' ");
                } else if (!"".equals(time[0]) && !"".equals(time[1])) {
                    //保证时间大小关系
                    if (time[0].compareTo(time[1]) > 0) {
                        String temp = time[1];
                        time[1] = time[0];
                        time[0] = temp;
                    }
                    sqlWhere.append(" and ").append("t1.modedatamodifydatetime").append(" >= '").append(new DateTransformer().getServerDateTime(time[0])).append("' and ").append("t1.modedatamodifydatetime").append(" <= '").append(new DateTransformer().getServerDateTime(time[1])).append("' ");
                }
            }
        }
        String con__2value = request.getParameter("con_2");
        if (!StringHelper.isEmpty(con__2value)) {
            sqlWhere.append(this.getHrmCondition("t1.modedatacreater", con__2value, user, "0"));
        }
        String con__7value = request.getParameter("con_7");
        if (!StringHelper.isEmpty(con__7value)) {
            sqlWhere.append(this.getHrmCondition("t1.modedatamodifier", con__7value, user, "0"));
        }
        String con__9value = request.getParameter("con_9");
        if (!StringHelper.isEmpty(con__9value)) {
            if (con__9value.indexOf(",") == -1) {
                sqlWhere.append(" and  t1.seclevel in (" + con__9value + ")  ");
            } else {
                if (con__9value.endsWith(",")) {
                    con__9value = con__9value.substring(0, con__9value.length() - 1);
                }
                sqlWhere.append(" and t1.seclevel in (" + con__9value + ")  ");
            }

        }
        String con__3value = request.getParameter("con_3");
        rs.executeQuery("select isquery from mode_customdspfield where customid=? and fieldid=-3", customId);
        boolean idisquery = false;
        if (rs.next()) {
            idisquery = "1".equalsIgnoreCase(Util.null2String(rs.getString("isquery")));
        }
        if (!StringHelper.isEmpty(con__3value) && idisquery) {
            sqlWhere.append(" and t1.id = '").append(con__3value).append("'");
        }
        String con__4value = request.getParameter("con_4");
        if (!StringHelper.isEmpty(con__4value)) {
            sqlWhere.append(" and (");
            String[] tabidArr = con__4value.split(",");
            for (int i = 0; i < tabidArr.length; i++) {
                if (i != 0) {
                    sqlWhere.append(" or ");
                }
                if ("-1".equals(tabidArr[i])) {//未关联标签
                    sqlWhere.append(" (t1.modelableid is null or t1.modelableid = '')");
                } else {
                    if ("oracle".equals(rs.getDBType())) {
                        sqlWhere.append(" ','||t1.modelableid||',' like '%,'||'").append(tabidArr[i]).append("'||',%' ");
                    } else if (rs.getDBType().toLowerCase().contains("mysql")) {
                        sqlWhere.append(" concat(',',modelableid,',') like concat('%,','").append(tabidArr[i]).append("',',%') ");
                    } else if ("postgresql".equals(rs.getDBType())) {
                        sqlWhere.append(" ','||modelableid||',' like '%,'||'").append(tabidArr[i]).append("'||',%' ");
                    } else {
                        sqlWhere.append(" ','+modelableid+',' like '%,'+'").append(tabidArr[i]).append("'+',%' ");
                    }
                }
            }
            sqlWhere.append(") ");
        }

        String con__5value = request.getParameter("con_5");
        if (!StringHelper.isEmpty(con__5value)) {
            sqlWhere.append(" and t1.modedatastatus = ").append(con__5value).append("");
        }

        //自定义条件
        String sql = " select b.fieldid, a.fieldname, a.viewtype, a.fieldhtmltype, a.type typeTemp, b.conditiontransition,b.isquery,b.isadvancedquery " +
                "   from mode_customdspfield b left join  workflow_billfield a on a.id = b.fieldid "
                + " where  b.customid = ? and (a.detailtable is null  or a.detailtable = '' or a.detailtable = ?)  and (fieldid > 0 or fieldid=-3)";
        rs.executeQuery(sql, customId, detailTable);
        EncryptFieldEntity encryptFieldEntity = null;
        //        用于判断字段是否开启多选
        Map<String, String> multChoice = new HashMap<>();
        String encrypttable = "";
        while (rs.next()) {
            int fieldid = Util.getIntValue(rs.getString("fieldid"));
            String con_fieldid = "con_" + fieldid;
            String con_fieldvalue = Util.null2String(request.getParameter(con_fieldid)).replace("'", "''");
            String fieldname = Util.null2String(rs.getString("fieldname"));
            multChoice.put(rs.getString("fieldid"), rs.getString("conditiontransition"));
            boolean isquery = "1".equalsIgnoreCase(Util.null2String(rs.getString("isquery")));
            boolean isadvancedquery = "1".equalsIgnoreCase(Util.null2String(rs.getString("isadvancedquery")));
            if (StringHelper.isEmpty(con_fieldvalue)) {//&&StringHelper.isEmpty(con_fieldvalue2)) {
                continue;
            }
            if (!"".equals(quickSearchFieldid) && quickSearchFieldid.equals(fieldid + "")) {//如果是关键字搜索,那么高级搜索中这个关键字对应的条件就跳过,以关键字搜索那里的为准
                continue;
            }
            encrypttable = tableName;
            String viewtype = Util.null2String(rs.getString("viewtype"));
            if ("1".equals(viewtype)) {
                encrypttable = detailTable;
            }
            encryptFieldEntity = new EncryptFieldConfigComInfo().getFieldEncryptConfig(encrypttable, fieldname);
            if (encryptFieldEntity != null && "1".equals(encryptFieldEntity.getIsEncrypt())) {//加密字段不要返回数据.
                con_fieldvalue = "encryption__not_data";//加密的不让查出数据
            }
            String fieldhtmltype = Util.null2String(rs.getString("fieldhtmltype"));
            boolean isfromTab = Util.null2String(request.getParameter("isfromTab")).equals("1");//页面扩展来自tab页
            boolean isBoardData = Util.null2String(request.getParameter("isBoardData")).equals("1");//是否是看板数据
            boolean isfromRight = Util.null2String(request.getParameter("isfromRight")).equals("1");//页面扩展来自右键菜单
            String type = Util.null2String(rs.getString("typeTemp"));
            fieldname = ("1".equals(viewtype) ? DETAIL_TABLE_ALIAS : MAIN_TABLE_ALIAS) + "." + fieldname;
            boolean isquicksearch = false;
            String sql1 = "select isquicksearch from mode_quicksearch_setting where customid=?";//查询列表是否开启快捷查询
            rs1.executeQuery(sql1, customId);
            while (rs1.next()) {
                isquicksearch = "1".equals(rs1.getString("isquicksearch"));
            }
            boolean flg = false;
            float num = 0;
            float num2 = 0;
            if (isquicksearch) {//查询列表开启快捷查询后，判断该字段是否设置快捷查询
                int id = 0;
                rs1.executeQuery("select id from mode_quicksearch_condition where fieldid=?", fieldid);
                while (rs1.next()) {
                    id = rs1.getInt("id");
                }
                if (id != 0) {
                    if ("2".equals(type) || "3".equals(type)) {//2为整数，3为浮点数，进行区间判断。
                        String sql2 = "select id,minnum,maxnum from mode_quicksearch_detail  where customid=? and fieldid=?";
                        RecordSet rs2 = new RecordSet();
                        rs2.executeQuery(sql2, customId, fieldid);
                        while (rs2.next()) {
                            String minnum = Util.null2String(rs2.getString("minnum"));
                            String maxnum = Util.null2String(rs2.getString("maxnum"));
                            num = Util.getFloatValue(minnum);
                            num2 = Util.getFloatValue(maxnum);
                            String[] con_fieldvalueArray1 = Util.splitString(con_fieldvalue, ",");
                            if (con_fieldvalueArray1.length > 1 && "".equals(con_fieldvalueArray1[1])) {
                                if (num <= Util.getFloatValue(con_fieldvalueArray1[0]) && num2 >= Util.getFloatValue(con_fieldvalueArray1[0]) && !flg) {//该条件参数的值符合设置区间，参数才生效
                                    flg = true;
                                }
                            } else if (con_fieldvalueArray1.length > 1) {
                                flg = true;
                            }
                        }
                    } else {
                        flg = true;
                    }
                }
            }
            linkFields = "," + linkFields + ",";
            if (isquery || flg || isadvancedquery || isfromTab || isfromRight) {//当该字段为查询条件或快捷查询或高级查询条件时，且开启快捷查询，且该字段设置为快捷查询，且参数值属于设置的区间内，参数生效。
                if ("1".equals(fieldhtmltype)) {
                    String fieldname1 = fieldname;
                    if ("1".equals(type)) { //文本字段如果来自页面扩展做的右键、tab页，则精
                        if ((linkFields.indexOf("," + con_fieldid + ",") > -1) && !isBoardData) {//确匹配
                            sqlWhere.append(" and ").append(fieldname).append(" = '").append(con_fieldvalue).append("'");
                        } else {
                            sqlWhere.append(this.getTextCondition(fieldname, con_fieldvalue));
                            //查询、看板高级搜索（看板高级搜索不用像查询高级搜索一样在 tablestring 拼 xml，所以不用转义）
                            //sqlWhere.append(Util.StringReplace(this.getTextCondition(fieldname, con_fieldvalue), "\\\\","\\"));
                        }
                    } else {
                        String[] con_fieldvalueArray = Util.splitString(con_fieldvalue, ",");
                        if ("5".equals(type)) {//金额转换
                            if ("oracle".equals(rs.getOrgindbtype())) {
                                fieldname = " cast((CASE WHEN ( " + fieldname + "||'') is null THEN '0' WHEN (" + fieldname +
                                        "||'') =' ' THEN '0' ELSE Replace((" + fieldname + "||''), ',', '') END) as number(30,6)) ";

                            } else if (rs.getOrgindbtype().toLowerCase().contains("mysql")) {
                                fieldname = "  cast((case when ifnull(( " + fieldname + " REGEXP '[0-9.,]'),0)=1 then replace(" +
                                        fieldname + " ,',','')  else '0' end) as  decimal(30,6) ) ";
                            } else if ("postgresql".equals(rs.getOrgindbtype())) {
                                fieldname = " cast((CASE WHEN ( " + fieldname + "||'') is null THEN '0' WHEN (" + fieldname +
                                        "||'') =' ' THEN '0' ELSE Replace((" + fieldname + "||''), ',', '') END) as  decimal(30,6) ) ";

                            } else {//金额千分位是varchar类型
                                fieldname = "  cast((CASE isnumeric ( " + fieldname + ") WHEN 0 THEN '0' WHEN 1 THEN replace(" +
                                        fieldname + ",',','') ELSE '0' END) as decimal(30,6))";
                            }
                        }
                        if ((linkFields.indexOf("," + con_fieldid + ",") > -1) && !isBoardData &&
                                ((con_fieldvalueArray.length == 2 && StringHelper.isEmpty(con_fieldvalueArray[1])) || con_fieldvalueArray.length == 1)) {
                            //如果来自页面扩展做的右键、tab页，则精确匹配，qc798633
                            sqlWhere.append(" and ").append(fieldname1 + " is not null and ").append(fieldname).append(" = ").append(con_fieldvalueArray[0]);
                        } else if (con_fieldvalueArray.length >= 2 && !"rowClick".equals(Util.null2String(request.getParameter("viewFrom")))) {
                            if (!StringHelper.isEmpty(con_fieldvalueArray[0])) {
                                sqlWhere.append(" and ").append(fieldname1 + " is not null and ").append(fieldname).append(" >= ").append(con_fieldvalueArray[0]);
                            }
                            if (!StringHelper.isEmpty(con_fieldvalueArray[1])) {
                                sqlWhere.append(" and ").append(fieldname1 + " is not null and ").append(fieldname).append(" <= ").append(con_fieldvalueArray[1]);
                            }
                        } else if (con_fieldvalueArray.length == 1) {
                            sqlWhere.append(" and ").append(fieldname1 + " is not null and ").append(fieldname).append(" = ").append(con_fieldvalueArray[0]);
                        }
                    }
                } else if ("2".equals(fieldhtmltype)) {
                    sqlWhere.append(" and ");
                    sqlWhere.append(this.getTextCondition2(fieldname, con_fieldvalue));
                } else if ("3".equals(fieldhtmltype)) {

                    if ("2".equals(type)) {//日期
                        if (linkFields.indexOf("," + con_fieldid + ",") > -1) {//链接字段
                            String[] con_fieldvalueArray = Util.splitString(con_fieldvalue, ",");
                            if (con_fieldvalueArray.length > 1) {
                                sqlWhere.append(" and ").append(fieldname).append(" = '").append(con_fieldvalueArray[1]).append("'");
                            }
                        } else {
                            sqlWhere.append(this.getDateCondition(fieldname, con_fieldvalue));
                        }
                    } else if ("19".equals(type)) {//时间
                        if (con_fieldvalue.contains("，")) {//支持条件参数中的中文逗号
                            con_fieldvalue = con_fieldvalue.replace("，", ",");
                        }
                        con_fieldvalue = con_fieldvalue.contains("：") ? con_fieldvalue.replace("：", ":") : con_fieldvalue; //支持条件参数中时间格式中有中文冒号
                        if (con_fieldvalue.contains(",") && con_fieldvalue.split(",").length > 1) {//时间范围     ;  填写两个时间点或者只填写后面的时间点
                            String[] time = con_fieldvalue.split(",");
                            for (int i = 0; i < time.length; i++) { //支持条件参数中时间范围是  0:00,9:00 的情况
                                if (time[i] != null && "" != time[i]) {
                                    time[i] = time[i].length() < 5 ? "0" + time[i] : time[i];
                                }
                            }

                            if (time[0].compareTo(time[1]) > 0) {//前者时间大
                                sqlWhere.append(" and ").append(fieldname).append(" >= '").append(time[1]).append("' and ").append(fieldname).append(" <= '").append(time[0]).append("' ");
                            } else {
                                sqlWhere.append(" and ").append(fieldname).append(" >= '").append(time[0]).append("' and ").append(fieldname).append(" <= '").append(time[1]).append("' ");
                            }
                        } else {//  QC503899，将其改为时间段
                            //只填写第一个时间点

                            con_fieldvalue = con_fieldvalue.contains(",") ? con_fieldvalue.substring(0, con_fieldvalue.length() - 1) : con_fieldvalue;

                            if (con_fieldvalue.length() < 5) {
                                con_fieldvalue = "0" + con_fieldvalue;
                            }
                            //如果来自页面扩展做的右键、tab页，则精确匹配
                            if ((linkFields.indexOf("," + con_fieldid + ",") > -1) && !isBoardData) {
                                sqlWhere.append(" and ").append(fieldname).append(" = '").append(con_fieldvalue).append("' ");
                            } else {
                                sqlWhere.append(" and ").append(fieldname).append(" >= '").append(con_fieldvalue).append("' ");
                            }
                        }
                    } else if ("290".equalsIgnoreCase(type)) {//日期时间
                        //防止条件参数出现中文逗号
                        //if(con_fieldvalue.contains("，")){
                        //    con_fieldvalue=con_fieldvalue.replace("，", ",");
                        //}
                        String[] time = con_fieldvalue.split(",");
                        if (time.length > 0) {
                            if (time.length == 1) {
                                // 单一值
                                if (!"".equals(time[0])) {
                                    sqlWhere.append(" and ").append(fieldname).append(" = '").append(new DateTransformer().getServerDateTime(time[0])).append("' ");
                                }
                            } else {

                                if ("".equals(time[0]) && !"".equals(time[1])) {
                                    sqlWhere.append(" and ").append(fieldname).append(" <= '").append(new DateTransformer().getServerDateTime(time[1])).append("' ");
                                } else if (!"".equals(time[0]) && "".equals(time[1])) {
                                    sqlWhere.append(" and ").append(fieldname).append(" >= '").append(new DateTransformer().getServerDateTime(time[0])).append("' ");
                                } else if (!"".equals(time[0]) && !"".equals(time[1])) {
                                    //保证时间大小关系
                                    if (time[0].compareTo(time[1]) > 0) {
                                        String temp = time[1];
                                        time[1] = time[0];
                                        time[0] = temp;
                                    }
                                    sqlWhere.append(" and ").append(fieldname).append(" >= '").append(new DateTransformer().getServerDateTime(time[0])).append("' and ").append(fieldname).append(" <= '").append(new DateTransformer().getServerDateTime(time[1])).append("' ");
                                }
                            }
                        }
                    } else if ("1".equalsIgnoreCase(type)) {
                        sqlWhere.append(this.getHrmCondition(fieldname, con_fieldvalue, user, rs.getString("conditiontransition")));

                    } else if ("141".equalsIgnoreCase(type)) {
                        sqlWhere.append(this.getResourceBrowserCondition(fieldname, con_fieldvalue));

                    } else if (BrowserHelper.isSingleBrowserField(type) && "1".equals(rs.getString("conditiontransition"))) {
                        sqlWhere.append(" and ").append(fieldname).append(" in (").append("'" + con_fieldvalue.replaceAll(",", "','") + "'").append(") ");
                    } else {
                        sqlWhere.append(this.getBrowserCondition(fieldname, con_fieldvalue));
                    }
                } else if ("4".equals(fieldhtmltype)) {
                    if ("1".equals(con_fieldvalue)) {
                        sqlWhere.append(" and ").append(fieldname).append(" = '1' ");
                    } else if ("2".equals(con_fieldvalue)) {
                        sqlWhere.append(" and (").append(fieldname).append(" = '0' or ").append(fieldname).append(" is null ) ");
                    } else if ("0".equals(con_fieldvalue)) {//全部
                        continue;
                    }
                } else if ("5".equals(fieldhtmltype)) {
                    if ("1".equals(rs.getString("conditiontransition"))) {
                        //选择框多选
                        //sqlWhere.append(" and ").append(fieldname).append(" in (").append(con_fieldvalue).append(") ");
                        sqlWhere.append(this.getBrowserCondition(fieldname, con_fieldvalue));
                    } else {
                        if (type.equals("2")) {
                            sqlWhere.append(" and ");
                            String[] con_fieldvalueArray = con_fieldvalue.split(",");
                            sqlWhere.append("(");
                            for (int m = 0; m < con_fieldvalueArray.length; m++) {
                                String convertfieldname = "";
                                String tmpfieldvalue = con_fieldvalueArray[m];
                                if (tmpfieldvalue.length() == 0) {
                                    continue;
                                }
                                if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                                    convertfieldname = "  ','||to_char(" + fieldname + ")||',' ";
                                } else if ("postgresql".equalsIgnoreCase(dataSourceDBType)) {
                                    convertfieldname = "  ','||to_char(" + fieldname + ")||',' ";
                                } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                                    convertfieldname = " concat(',',convert(" + fieldname + ",char),',') ";
                                } else {
                                    convertfieldname = "  ','+convert(varchar(max)," + fieldname + ")+',' ";
                                }
                                sqlWhere.append(convertfieldname).append(" like '%,").append(tmpfieldvalue).append(",%' ");
                                if (m != con_fieldvalueArray.length - 1) {
                                    sqlWhere.append(" or ");
                                }
                            }
                            sqlWhere.append(")");
                        } else {
                            //2025-07-07增加逻辑 QC3780013
                            //某些查询下拉框字段，例如下拉选择某个值，需要同时查询出null值
                            String absFieldName = fieldname;
                            if (fieldname.contains(".")) {
                                absFieldName = fieldname.split("\\.")[1];//这里要做转义
                            }
                            boolean flag = checkSelectNullValue(absFieldName, con_fieldvalue, Util.null2String(customId));
                            bb.writeLog("自定义条件-校验下拉框字段校验包含空值:" + absFieldName + "," + con_fieldvalue + "," + customId + "," + flag);
                            if (flag) {
                                sqlWhere.append(" and (").append(fieldname).append(" = '").append(con_fieldvalue).append("' ").append(" or ").append(fieldname).append(" is null ) ");
                            } else {
                                sqlWhere.append(" and ").append(fieldname).append(" = '").append(con_fieldvalue).append("' ");
                            }


                        }
                    }
                } else if ("6".equals(fieldhtmltype)) {
                    sqlWhere.append(this.getBrowserCondition(fieldname, con_fieldvalue));
                }
            }
        }


        //快捷搜索条件
        String isQuickSearch = Util.null2String(request.getParameter("isQuickSearch"));
        String isOnlyQuick = Util.null2String(request.getParameter("isOnlyQuick"));
        String canIsQuickSearch = Util.null2String(request.getParameter("canIsQuickSearch"));
        if ("1".equals(canIsQuickSearch)) {
            isQuickSearch = "1";
        }
        if ("1".equals(isQuickSearch)) {//是快捷搜索条件搜索
            sql = "select fieldname,detailtable,fieldhtmltype,s.fieldid,bill.type fieldtype from mode_quicksearch_condition s left join workflow_billfield bill on s.fieldid=bill.id where s.customid=? and s.fieldid>0 ";

            List args = new ArrayList();
            args.add(this.customId);
            if (!this.isVirtualForm) {

                // 加上创建日期、创建人
                sql += "UNION ALL ";
                sql += "SELECT case when fieldid=-1 then 'modedatacreatedate' when fieldid=-9 then 'seclevel' when fieldid=-2 then 'modedatacreater' when fieldid=-7 then 'modedatamodifier' else 'modedatamodifydatetime' end fieldname,NULL,'3' fieldhtmltype,";
                sql += "case when fieldid=-1 then 1 when fieldid=-2 then 2 when fieldid=-7 then 7  when fieldid=-9 then 9  else 8 end fieldid,";
                sql += "case when fieldid=-1 then 2 when fieldid=-8 then 290 else 1 end fieldtype ";
                sql += "FROM mode_quicksearch_condition WHERE customid=? AND fieldid in(-1,-2,-7,-8,-9) ";

                args.add(this.customId);
            }

            rs.executeQuery(sql, args);
            while (rs.next()) {
                String fieldhtmltype = Util.null2String(rs.getString("fieldhtmltype"));
                String fieldtype = Util.null2String(rs.getString("fieldtype"));
                String fieldid = Util.null2String(rs.getString("fieldid"));
                if ("9".equals(fieldid)) {
                    fieldhtmltype = "5";
                }
                String fieldname = Util.null2String(rs.getString("fieldname"));
                String detailtable = Util.null2String(rs.getString("detailtable"));
                fieldname = ("".equals(detailtable) ? MAIN_TABLE_ALIAS : DETAIL_TABLE_ALIAS) + "." + fieldname;
                String value = Util.null2String(request.getParameter(fieldid)).replace("'", "''");
                RecordSet rs12 = new RecordSet();
                String sql12 = " select b.fieldid, a.fieldname, a.viewtype, a.fieldhtmltype, a.type typeTemp, b.conditiontransition,b.isquery,b.isadvancedquery " +
                        "   from mode_customdspfield b left join  workflow_billfield a on a.id = b.fieldid "
                        + " where  b.customid = ? and (a.detailtable is null  or a.detailtable = '' or a.detailtable = ?)  and fieldid > 0 ";

                args = new ArrayList();
                args.add(customId);
                args.add(detailTable);
                if (!this.isVirtualForm) {
                    // 创建日期、创建人
                    sql12 += "UNION ALL ";
                    sql12 += "select b.fieldid, ";
                    sql12 += "case when fieldid=-1 then 'modedatacreatedate' when fieldid=-9 then 'seclevel'  when fieldid=-2 then 'modedatacreater' when fieldid=-7 then 'modedatamodifier' else 'modedatamodifydatetime' end fieldname, 0 viewtype, '3' fieldhtmltype, ";
                    sql12 += "case when fieldid=-1 then 2 when fieldid=-8 then 290 else 1 end typeTemp, b.conditiontransition,b.isquery,b.isadvancedquery ";
                    sql12 += "from mode_customdspfield b where b.customid = ? and fieldid in(-1,-2,-7,-8,-9) ";

                    args.add(customId);
                }

                rs12.executeQuery(sql12, args);
                boolean isqc = false;
                while (rs12.next()) {
                    String fieldname12 = Util.null2String(rs12.getString("fieldname"));
                    boolean isquery12 = "1".equalsIgnoreCase(Util.null2String(rs12.getString("isquery")));
                    if (fieldname.indexOf(fieldname12) > -1 && isquery12 && !"1".equals(isOnlyQuick)) {
                        isqc = true;
                    }
                }
                if ("".equals(value) || ",".equals(value)) {
                    continue;
                }
                if (isqc) {
                    continue;
                }
                if ("1".equals(fieldhtmltype)) {
                    if ("1".equals(fieldtype)) {//单行文本
                        sqlWhere.append(this.getTextCondition(fieldname, value));
                    } else {//整数或浮点数
                        sql = "select minnum,maxnum from mode_quicksearch_detail where id=?";
                        rs1.executeQuery(sql, value);
                        if (rs1.next()) {
                            String minnum = Util.null2String(rs1.getString("minnum"));
                            String maxnum = Util.null2String(rs1.getString("maxnum"));
                            if ("5".equals(fieldtype)) {//金额千分位
                                if ("oracle".equals(dataSourceDBType)) {
                                    fieldname = fieldname + " is not null and cast((CASE WHEN ( " + fieldname + "||'') is null THEN '0' WHEN (" + fieldname +
                                            "||'') =' ' THEN '0' ELSE Replace((" + fieldname + "||''), ',', '') END) as number(30,6)) ";

                                } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                                    fieldname = fieldname + " is not null and  cast((case when ifnull(( " + fieldname + " REGEXP '[0-9.,]'),0)=1 then replace(" +
                                            fieldname + " ,',','')  else '0' end) as  decimal(30,6) ) ";
                                } else {//金额千分位是varchar类型
                                    fieldname = fieldname + " is not null and cast((CASE isnumeric ( " + fieldname + ") WHEN 0 THEN '0' WHEN 1 THEN replace(" +
                                            fieldname + ",',','') ELSE '0' END) as decimal(30,6))";
                                }
                            }

                            if (!"".equals(minnum)) {
                                double num = Util.getDoubleValue(minnum);
                                sqlWhere.append(" and ").append(fieldname).append(" >=").append(num);
                            }
                            if (!"".equals(maxnum)) {
                                double num = Util.getDoubleValue(maxnum);
                                sqlWhere.append(" and ").append(fieldname).append(" <=").append(num);
                            }
                        }
                    }
                } else if ("2".equals(fieldhtmltype)) {
                    sqlWhere.append(" and ");
                    sqlWhere.append(this.getTextCondition2(fieldname, value));
                } else if ("4".equals(fieldhtmltype)) {
                    if ("1".equals(value)) {
                        sqlWhere.append(" and ").append(fieldname).append(" = '1' ");
                    } else if ("2".equals(value)) {
                        sqlWhere.append(" and (").append(fieldname).append(" = '0' or ").append(fieldname).append(" is null ) ");
                    }
                } else if ("5".equals(fieldhtmltype)) {//选择框
                    if ("1".equals(fieldtype)) {
                        //2025-07-07增加逻辑 QC3780013
                        //某些查询单选下拉框字段，例如下拉选择某个值，需要同时查询出null值
                        String absFieldName = fieldname;
                        if (fieldname.contains(".")) {
                            absFieldName = fieldname.split("\\.")[1];//这里要做转义
                        }
                        boolean flag = checkSelectNullValue(absFieldName, value, Util.null2String(customId));
                        bb.writeLog("快捷搜索-校验下拉框字段校验包含空值:" + absFieldName + "," + value + "," + customId + "," + flag);
                        if (flag) {
                            sqlWhere.append(" and (").append(fieldname).append(" = '").append(value).append("' ").append(" or ").append(fieldname).append(" is null) ");
                        } else {
                            String querysql = "select t.* from mode_CustomDspField t where t.customid=? and t.fieldid=?";
                            String conditiontransition = "";
                            rs1.executeQuery(querysql, this.customId, fieldid);
                            if (rs1.next()) {
                                conditiontransition = Util.null2String(rs1.getString("conditiontransition"));
                            }
                            if (conditiontransition.equalsIgnoreCase("1") || "t1.seclevel".equals(fieldname)) {
//                        if(sqlWhere.indexOf(fieldname)>-1){
//                            continue;
//                        }
                                sqlWhere.append(" and ").append(fieldname).append(" in (").append(value).append(") ");
                            } else {
//                        if(sqlWhere.indexOf(fieldname)>-1){
//                            continue;
//                        }
                                sqlWhere.append(" and ").append(fieldname).append(" = '").append(value).append("' ");
                            }
                        }
                    } else {
                        if (fieldtype.equals("2")) {
                            sqlWhere.append(" and ");
                            String[] con_fieldvalueArray = value.split(",");
                            sqlWhere.append("(");
                            for (int m = 0; m < con_fieldvalueArray.length; m++) {
                                String convertfieldname = "";
                                String tmpfieldvalue = con_fieldvalueArray[m];
                                if (tmpfieldvalue.length() == 0) {
                                    continue;
                                }
                                if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                                    convertfieldname = "  ','||to_char(" + fieldname + ")||',' ";
                                } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                                    convertfieldname = " concat(',',convert(" + fieldname + ",char),',') ";
                                } else if ("postgresql".equalsIgnoreCase(dataSourceDBType)) {
                                    convertfieldname = "  ','||to_char(" + fieldname + ")||',' ";
                                } else {
                                    convertfieldname = "  ','+convert(varchar(max)," + fieldname + ")+',' ";
                                }
                                sqlWhere.append(convertfieldname).append(" like '%,").append(tmpfieldvalue).append(",%' ");
                                if (m != con_fieldvalueArray.length - 1) {
                                    sqlWhere.append(" or ");
                                }
                            }
                            sqlWhere.append(")");
                        } else {
                            sqlWhere.append(" and ").append(fieldname).append(" = '").append(value).append("' ");
                        }
                    }

                } else if ("3".equals(fieldhtmltype)) {//日期
                    //  判断开启多选且有值 人力资源字段开启多选
                    String[] hrmTempVa = value.split(",");
                    if ("1".equals(multChoice.get(fieldid)) && hrmTempVa.length > 1) {
                        fieldtype = "161";
                    }
                    if ("1".equals(fieldtype)) {  //单人力资源
                        String querysql = "select t.* from mode_CustomDspField t where t.customid=? and t.fieldid=?";
                        String conditiontransition = "";
                        rs1.executeQuery(querysql, this.customId, fieldid);
                        if (rs1.next()) {
                            int isNum = 0;
                            conditiontransition = Util.null2String(rs1.getString("conditiontransition"));
                            if ("1".equals(conditiontransition)) {
                                if (value.indexOf(",[") > -1) {
                                    String copeValue = value;
                                    isNum = copeValue.split("\\[").length;
                                    value = value.replace("-5,", "");
                                    String[] hrmVa = value.split(",");
                                    String deValue = "";
                                    for (int i = 0; i < isNum - 1; i++) {
                                        deValue += hrmVa[i] + ",";
                                    }
                                    value = deValue.substring(0, deValue.length() - 1);
                                }
                            }
                        }
                        if (!"".equals(value) && !"-5".equals(value)) {
                            sqlWhere.append(this.getHrmCondition(fieldname, value, user, conditiontransition));
                        }
                    } else if ("2".equals(fieldtype)) { //日期 日期时间
                        if ("6".equals(value)) {
                            value = "0";
                        }
                        if (!"".equals(value)) {
                            sqlWhere.append(this.getDateCondition(fieldname, value));
                        }
                    } else if ("19".equals(fieldtype)) {// 时间
                        String values[] = value.split(",");
                        if (values.length == 1) {//只有起始时间有值
                            sqlWhere.append(" and ").append(fieldname).append(" >= '").append(values[0]).append("' ");
                        } else {
                            if ("".equals(values[0])) {//只有截止时间有值
                                sqlWhere.append(" and ").append(fieldname).append(" <= '").append(values[1]).append("' ");
                            } else {//两个都有值
                                sqlWhere.append(" and ").append(fieldname).append(" >= '").append(values[0]).append("' ");
                                sqlWhere.append(" and ").append(fieldname).append(" <= '").append(values[1]).append("' ");
                            }
                        }
                    } else if ("290".equals(fieldtype)) {
                        String values[] = value.split(",");
                        if (values.length != 2) {
                            continue;
                        }
                        sqlWhere.append(" and ").append(fieldname).append(" >= '").append(new DateTransformer().getServerDateTime(values[0])).append("' ");
                        sqlWhere.append(" and ").append(fieldname).append(" <= '").append(new DateTransformer().getServerDateTime(values[1])).append("' ");
                    } else if ("141".equalsIgnoreCase(fieldtype)) {

                        sqlWhere.append(this.getResourceBrowserCondition(fieldname, value));

                    } else if (BrowserHelper.isSingleBrowserField(fieldtype)) {
                        sqlWhere.append(" and ");
                        String[] valueArray = value.split(",");
                        sqlWhere.append("(");
                        for (int m = 0; m < valueArray.length; m++) {
                            String convertfieldname = "";
                            String tmpfieldvalue = valueArray[m];
                            if (tmpfieldvalue.length() == 0) {
                                continue;
                            }
                            if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                                convertfieldname = "  ','||to_char(" + fieldname + ")||',' ";
                            } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                                convertfieldname = " concat(',',convert(" + fieldname + ",char),',') ";
                            } else if ("postgresql".equalsIgnoreCase(dataSourceDBType)) {
                                convertfieldname = "  ','||to_char(" + fieldname + ")||',' ";
                            } else {
                                convertfieldname = "  ','+convert(varchar(max)," + fieldname + ")+',' ";
                            }
                            sqlWhere.append(convertfieldname).append(" like '%,").append(tmpfieldvalue).append(",%' ");
                            if (m != valueArray.length - 1) {
                                sqlWhere.append(" or ");
                            }
                        }
                        sqlWhere.append(")");
                        //sqlWhere.append(" and ").append(fieldname).append(" in (").append("'"+value.replaceAll(",","','")+"'").append(") ");
                    } else {
                        sqlWhere.append(this.getBrowserCondition(fieldname, value));
                    }
                }
            }
        }
        if (this.isEdit) {
            ModeRightInfo modeRightInfo = new ModeRightInfo();
            Map<String, Object> preCheckisEdit = modeRightInfo.preCheckUserRightIsEdit(user, modeId);
            boolean hasUserRightIsEdit = (boolean) preCheckisEdit.get("isedit");
            if (!hasUserRight && !hasUserRightIsEdit) {
                sqlWhere.append(" and t2.sharelevel>1 ");
            }
            String selectedRowKeysStr = Util.null2String(request.getParameter("selectedRowKeysStr"));
            if (!"".equals(selectedRowKeysStr)) {
                if (!"".equals(this.detailTable)) {
                    String[] selectedRowKeysStrs = Util.splitString(Util.null2String(request.getParameter("selectedRowKeysStr")), ",");
                    String idStr = "";
                    String idStr_ = "";
                    for (String id : selectedRowKeysStrs) {
                        if (id.endsWith("_'")) {
                            idStr += id.substring(0, id.length() - 2) + "',";
                        } else {
                            idStr_ += id + ",";
                        }
                    }
                    if (idStr.endsWith(",")) idStr = idStr.substring(0, idStr.length() - 1);
                    if (idStr_.endsWith(",")) idStr_ = idStr_.substring(0, idStr_.length() - 1);
                    if (!"".equals(idStr)) {
                        if (!"".equals(idStr_)) {
                            sqlWhere.append(" and (" + CommonConstant.getConcatSql(CommonConstant.toChar("t1.id"), "'_'", CommonConstant.DB_ISNULL_FUN + "(" + CommonConstant.toChar("d1.id") + ",'')") + "in (" + idStr_ + ") or t1.id in (" + idStr + "))");
                        } else {
                            sqlWhere.append(" and t1.id in (" + idStr + ")");
                        }
                    } else {
                        sqlWhere.append(" and (" + CommonConstant.getConcatSql(CommonConstant.toChar("t1.id"), "'_'", CommonConstant.DB_ISNULL_FUN + "(" + CommonConstant.toChar("d1.id") + ",'')") + "in (" + selectedRowKeysStr + "))");
                    }
                } else {
                    sqlWhere.append(" and " + CommonConstant.toChar("t1.id") + " in (" + selectedRowKeysStr + ") ");
                }
            }
        }

        if (this.isVirtualForm) {//避免缓存sql问题
            String timeStr = DateHelper.getCurDateTime();
            sqlWhere.append(" and '" + timeStr + "'='" + timeStr + "' ");
        }
        //判断是否有密级搜索，如果关联的模块开启了分级保护，而搜索条件中没有传入 那么默认展示用户能看到资源等级的数据
        //  this.customId;
        AddSeclevelUtil addSeclevelUtil = new AddSeclevelUtil();
        Map<String, Object> param_ = new HashMap<>();
        param_.put("customId", this.customId);
        param_.put("modeid", this.modeId);
        boolean isOpenClassProtectFlag = addSeclevelUtil.modeOpenClassProtect(param_);
        if (isOpenClassProtectFlag) {
            String tempStr = sqlWhere.toString();
            if (tempStr.indexOf("t1.seclevel") == -1) {
                String hrmSeclevelString = addSeclevelUtil.getSeclevelString(param_, user);
                if ("".equals(hrmSeclevelString)) {
                    hrmSeclevelString = "4";
                }
                sqlWhere.append(" and ").append("t1.seclevel").append(" in (").append(hrmSeclevelString).append(") ");
            }
        }
        bb.writeLog("final sqlwhere :" + sqlWhere);

        return sqlWhere.toString();
    }


    private String getHrmWhere(String hrmtype, HttpServletRequest request) {
        String treenodeid = Util.null2String(request.getParameter("treenodeid"));
        RecordSet rs = new RecordSet();
        String sql = "select sourcefrom,iscontainssub from mode_customtreedetail where id=?";
        rs.executeQuery(sql, treenodeid);
        String sourcefrom = "";
        boolean iscontainssub = false;
        if (rs.next()) {
            sourcefrom = Util.null2String(rs.getString("sourcefrom"));
            iscontainssub = "1".equals(Util.null2String(rs.getString("iscontainssub")));
        }
        String subid = Util.null2String(request.getParameter("subid"));
        String deptid = Util.null2String(request.getParameter("deptid"));
        String hrmid = Util.null2String(request.getParameter("hrmid"));
        String sqlwhere = "";
        if ("3".equals(sourcefrom)) {//我的下属
            sqlwhere = "select id from hrmresource where  id=" + hrmid;
            if (iscontainssub) {
                sqlwhere += " or managerstr like '%," + hrmid + ",%' ";
            }
        } else if ("4".equals(sourcefrom)) {//组织结构
            sqlwhere = "select id from hrmresource where  1=1 " + HrmTreeDataUtil.hrmWhere + " and ";
            if (iscontainssub) {
                if ("subcom".equalsIgnoreCase(hrmtype)) {
                    String subids = SubCompanyComInfo.getAllChildSubcompanyId(subid, subid);
                    if (!"".equals(subids)) {
                        sqlwhere += "subcompanyid1 in (" + subids + ")";
                    }
                } else if ("dept".equalsIgnoreCase(hrmtype)) {
                    try {
                        String depids = DepartmentComInfo.getAllChildDepartId(deptid, deptid);
                        if (!"".equals(depids)) {
                            sqlwhere += "departmentid in (" + depids + ")";
                        }
                    } catch (Exception e) {
                        sqlwhere += "1=2";
                    }
                } else if ("resource".equalsIgnoreCase(hrmtype)) {
                    sqlwhere += " id=" + hrmid;
                } else if ("com".equalsIgnoreCase(hrmtype)) {//总部根节点,查询所有人力资源
                    sqlwhere += " 1=1 ";
                } else {
                    sqlwhere += " 1=2 ";
                }
            } else {
                sqlwhere += " id=" + hrmid;
            }

        } else if ("5".equals(sourcefrom)) {//组织分部
            sqlwhere = "select id from hrmsubcompany where 1=1  ";
            if ("subcom".equalsIgnoreCase(hrmtype)) {
                if (iscontainssub) {
                    String subids = SubCompanyComInfo.getAllChildSubcompanyId(subid, subid);
                    sqlwhere += " and id in (" + subids + ")";
                } else {
                    sqlwhere += " and id =" + subid;
                }
            } else if ("com".equalsIgnoreCase(hrmtype)) {//总部根节点,查询所有人力资源
                if (!iscontainssub) {
                    sqlwhere += " and 1=2 ";
                }
            } else {
                sqlwhere += " and 1=2 ";
            }
        } else if ("6".equals(sourcefrom)) {//组织部门
            sqlwhere = "select id from hrmdepartment where 1=1  ";
            if (iscontainssub) {
                if ("subcom".equalsIgnoreCase(hrmtype)) {
                    String subids = SubCompanyComInfo.getAllChildSubcompanyId(subid, subid);
                    sqlwhere += " and subcompanyid1 in (" + subids + ")";
                } else if ("dept".equalsIgnoreCase(hrmtype)) {
                    try {
                        String depids = DepartmentComInfo.getAllChildDepartId(deptid, deptid);
                        if (!"".equals(depids)) {
                            sqlwhere += " and id in (" + depids + ")";
                        }
                    } catch (Exception e) {
                        sqlwhere += " and 1=2";
                    }
                } else if ("com".equalsIgnoreCase(hrmtype)) {//总部根节点,查询所有部门

                } else {
                    sqlwhere += " and 1=2 ";
                }
            } else {
                sqlwhere += " and id =" + deptid;
            }
        }

        return sqlwhere;
    }

    /**
     * 处理查询列表自定义查询条件
     *
     * @param
     */
    public String handleDefaultSql(Map<String, Object> parms, String defaulSql, User user) {
        defaulSql = CubeSearchTransMethod.INSTANCE.getDefaultSql(user, defaulSql);
        //固定查询条件
        //替换查询url，request传参字段
        if (defaulSql.indexOf("PARM(") > -1) {
            int beginIndex = defaulSql.indexOf("PARM(");
            while (beginIndex > -1) {
                int endIndex = defaulSql.indexOf(")", beginIndex + 5);
                int nextIndex = 0;
                if (endIndex > -1) {
                    String substring = defaulSql.substring(beginIndex + 5, endIndex);
                    if (parms.get(substring) == null) {
                        beginIndex = defaulSql.indexOf("PARM(", endIndex - nextIndex + 1);
                    } else {
                        String paramvalue = Util.null2String(parms.get(substring));
                        defaulSql = defaulSql.replace("PARM(" + substring + ")", paramvalue);
                        if (paramvalue.length() < substring.length()) {
                            nextIndex = substring.length() - paramvalue.length();
                        }
                        beginIndex = defaulSql.indexOf("PARM(", endIndex - nextIndex + 1);
                    }
                } else {
                    break;
                }
            }
        }
        return defaulSql;
    }

    public String getDepartCondition(String fieldname, String con_fieldvalue, String drl1) {
        StringBuffer sqlwhere = new StringBuffer();
        String[] values = Util.splitString(con_fieldvalue, ",");

        if (values.length < 1) {
            return "";
        } else {
            sqlwhere.append(" and (1=2"); // sql左括号
            if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                fieldname = "  ','||" + fieldname + "||',' ";
            } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                fieldname = " concat(','," + fieldname + ",',') ";
            } else if ("postgresql".equalsIgnoreCase(dataSourceDBType)) {
                fieldname = "  ','||" + fieldname + "||',' ";
            } else {
                fieldname = " ',' + convert(varchar(max)," + fieldname + ")+ ',' ";
            }
            for (int i = 0; i < values.length; i++) {
                String[] DepVa = drl1.split(",");//从快捷搜索过来的drl1 是截取过的，0表示包含，-1表示不包含。但从查询条件中过来的drl未截取，导致条件过滤失败，所以在此截取。
                if (DepVa.length > 0) {
                    drl1 = DepVa[0];
                }
                //rs.executeQuery("select departmentname from hrmdepartment where id = ?",Util.getIntValue(values[i]));
                if ("0".equals(drl1)) {
                    sqlwhere.append(" or ").append(fieldname).append(" like '%,").append(values[i]).append(",%'");
                } else if ("-1".equals(drl1)) {
                    sqlwhere.append(" and ").append(fieldname).append(" not like '%,").append(values[i]).append(",%' ");
                } else {
                    return "";
                }
            }
        }
        return sqlwhere.append(") ").toString();
    }

    public String getHrmCondition(String fieldname, String con_fieldvalue, User user, String conditiontransition) {
        RecordSet rs = new RecordSet();
        StringBuffer sqlwhere = new StringBuffer();
        String[] values = Util.splitString(con_fieldvalue, ",");
        if (values.length < 1) {
            return "";
        }
        if ("1".equals(conditiontransition) && Util.getIntValue(values[0]) > 0) {//第一个大于0的肯定是人员id
            sqlwhere.append(" and ").append(fieldname).append(" in (").append("'" + con_fieldvalue.replaceAll(",", "','") + "'").append(") ");
        } else {
            if (values.length == 1) {
                if (Util.getIntValue(values[0]) > 0) {
                    sqlwhere.append(" and ").append(fieldname).append(" = ").append(values[0]);
                    return sqlwhere.toString();
                }
            }
            try {
                ResourceComInfo resourceComInfo = new ResourceComInfo();
                if ("-1".equals(values[0])) {
                    if (isVirtualForm) {
                        ArrayList billids = new ArrayList<String>();
                        String sqlrlzy = "select id from hrmresource where departmentid =" + resourceComInfo.getDepartmentID(user.getUID() + "");
                        rs.executeSql(sqlrlzy);
//                        String rlzyval = "";
                        while (rs.next()) {
//                            rlzyval += "," + rs.getString(1);
                            billids.add(rs.getString(1));
                        }
//                        if(!rlzyval.isEmpty()){
//                            rlzyval = rlzyval.substring(1);
//                        }
//                        sqlwhere.append(" and ").append(fieldname).append(" in ( ").append(rlzyval).append(" ) ");
                        sqlwhere = splitBigInSqlwhere(sqlwhere, fieldname, billids);
                    } else {
                        sqlwhere.append(" and ").append(fieldname).append(" in ( select id from hrmresource where departmentid = ")
                                .append(resourceComInfo.getDepartmentID(user.getUID() + "")).append(" ) ");
                    }
                } else if ("-2".equals(values[0])) {
                    DepartmentComInfo departmentComInfo = new DepartmentComInfo();
                    String departid = resourceComInfo.getDepartmentID(user.getUID() + "");
                    if (isVirtualForm) {
                        ArrayList billids = new ArrayList<String>();
                        String sqlrlzy = "select id from hrmresource where departmentid in(" + departmentComInfo.getAllChildDepartId(departid, departid) + ")";
                        rs.executeSql(sqlrlzy);
//                        String rlzyval = "";
                        while (rs.next()) {
//                            rlzyval += "," + rs.getString(1);
                            billids.add(rs.getString(1));
                        }
//                        if(!rlzyval.isEmpty()){
//                            rlzyval = rlzyval.substring(1);
//                        }
//                        sqlwhere.append(" and ").append(fieldname).append(" in ( ").append(rlzyval).append(" ) ");
                        sqlwhere = splitBigInSqlwhere(sqlwhere, fieldname, billids);
                    } else {
                        sqlwhere.append(" and ").append(fieldname).append(" in (select id from hrmresource where departmentid in (")
                                .append(departmentComInfo.getAllChildDepartId(departid, departid)).append(" )) ");
                    }
                } else if ("-3".equals(values[0])) {
                    String subcomid = resourceComInfo.getSubCompanyID(user.getUID() + "");
                    if (isVirtualForm) {
                        ArrayList billids = new ArrayList<String>();
                        String sqlrlzy = "select id from hrmresource where subcompanyid1 =" + subcomid;
                        rs.executeSql(sqlrlzy);
//                        String rlzyval = "";
                        while (rs.next()) {
//                            rlzyval += "," + rs.getString(1);
                            billids.add(rs.getString(1));
                        }
//                        if(!rlzyval.isEmpty()){
//                            rlzyval = rlzyval.substring(1);
//                        }
//                        sqlwhere.append(" and ").append(fieldname).append(" in ( ").append(rlzyval).append(" ) ");
                        sqlwhere = splitBigInSqlwhere(sqlwhere, fieldname, billids);
                    } else {
                        sqlwhere.append(" and ").append(fieldname).append(" in ( select id from hrmresource where subcompanyid1 = ")
                                .append(subcomid).append(" ) ");
                    }
                } else if ("-4".equals(values[0])) {
                    SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
                    String subcomid = resourceComInfo.getSubCompanyID(user.getUID() + "");
                    if (isVirtualForm) {
                        ArrayList billids = new ArrayList<String>();
                        String sqlrlzy = "select id from hrmresource where subcompanyid1 in(select id from hrmsubcompany where supsubcomid=" + subcomid + " or id= " + subcomid + " )";
                        rs.executeSql(sqlrlzy);
//                        String rlzyval = "";
                        while (rs.next()) {
//                          rlzyval += "," + rs.getString(1);
                            billids.add(rs.getString(1));
                        }
//                        if(!rlzyval.isEmpty()){
//                            rlzyval = rlzyval.substring(1);
//                        }
//                        sqlwhere.append(" and ").append(fieldname).append(" in ( ").append(rlzyval).append(" ) ");
                        sqlwhere = splitBigInSqlwhere(sqlwhere, fieldname, billids);
                    } else {
                        sqlwhere.append(" and ").append(fieldname).append(" in (select id from hrmresource where subcompanyid1 in (select id from hrmsubcompany where supsubcomid=" + subcomid + " or id= " + subcomid + " )) ");
                    }
                } else if ("-5".equals(values[0])) {
                    if (values.length > 1) {
                        if (!StringHelper.isEmpty(values[1])) {
                            //当设置人力资源为多选的时候会存在 -5,18,3,19,杨光,杨文元,吴韦,,[object Object],[object Object],[object Object] 数据格式，目前看是存在问题的在人力资源设置多人力查询的时候，需要对数据进行处理
                            String valuesStr = CubeSearchService.getParam(values);
                            if ("".equals(valuesStr)) {
                                sqlwhere.append(" and ").append(fieldname).append(" = ").append(values[1]);
                            } else {
                                sqlwhere.append(" and ").append(fieldname).append(" in ( ").append(valuesStr).append(" ) ");
                            }

                        }
                    }
                } else if ("-6".equals(values[0])) {//本人
                    sqlwhere.append(" and ").append(fieldname).append(" = ").append(user.getUID());
                } else if ("-7".equals(values[0])) {//直属上级
                    sqlwhere.append(" and ").append(fieldname).append(" = ").append(user.getManagerid());
                } else if ("-8".equals(values[0])) {//直属下级
                    rs.executeQuery("select * from hrmresource where managerid = ? ", user.getUID());
                    if (rs.next()) {
                        sqlwhere.append(" and ").append(fieldname).append(" in (select id  from hrmresource where managerid = " + user.getUID() + " ) ");
                    } else {
                        sqlwhere.append(" and ").append(fieldname).append(" in (0) ");
                    }


                }
            } catch (Exception e) {

            }
        }

        return sqlwhere.toString();
    }

    private StringBuffer splitBigInSqlwhere(StringBuffer sqlwhere, String fieldname, ArrayList billids) {
        if (billids.size() < 1) return sqlwhere;
        //数据数量每逢1000进行分开存放进数组，拼接时每1000个拼接一次
        int sqlSize = 1000;
        int num = billids.size() / sqlSize + 1;
        String[] idsSql = new String[num];
        for (int n = 0; n < num; n++) {
            String ids = "";
            for (int i = sqlSize * n; i < (sqlSize * n + sqlSize) && i < billids.size(); i++) {
                if (i % sqlSize == 0) {
                    ids += "'" + billids.get(i) + "'";
                } else {
                    ids += ",'" + billids.get(i) + "'";
                }
            }
            idsSql[n] = "".equals(ids) ? "" : " in (" + ids + ") ";
        }
        if (idsSql.length > 0) {
            sqlwhere.append(" and ( "); //sqlwhere.append("".equals(sqlwhere.toString().trim())?"(":" and ( ");  sqlwhere.append(" and ( ");
            for (int i = 0; i < idsSql.length; i++) {
                if (!"".equals(idsSql[i])) {
                    if (i == 0) sqlwhere.append(fieldname).append(idsSql[i]);
                    else sqlwhere.append(" or ").append(fieldname).append(idsSql[i]);
                }
            }
            sqlwhere.append(" ) ");
        }
        return sqlwhere;
    }

    private String getResourceBrowserCondition(String fieldname, String con_fieldvalue) {
        StringBuffer sqlwhere = new StringBuffer();
        String[] con_fieldvalueArray = Util.splitString(con_fieldvalue, ",");
        if (con_fieldvalueArray.length > 0) {
            sqlwhere.append(" and ( ");
            if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                //fieldname = fieldname+"||',' ";
            } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                //fieldname = " concat(',',"+fieldname+",',') ";
            } else {
                fieldname = " convert(varchar(max)," + fieldname + ")";
            }
            for (String tmpvalue : con_fieldvalueArray) {
                sqlwhere.append(fieldname).append(" like '%").append(tmpvalue).append("%' or ");
            }
            sqlwhere.append(" 1=2 ) ");
        }
        return sqlwhere.toString();
    }

    public String getBrowserCondition(String fieldname, String con_fieldvalue) {
        StringBuffer sqlwhere = new StringBuffer();
        String[] con_fieldvalueArray = Util.splitString(con_fieldvalue, ",");
        if (con_fieldvalueArray.length > 0) {
            sqlwhere.append(" and ( ");
            if (dataSourceDBType.toLowerCase().contains("oracle") || "db2".equalsIgnoreCase(dataSourceDBType)) {
                fieldname = "  ','||" + fieldname + "||',' ";
            } else if (dataSourceDBType.toLowerCase().contains("mysql")) {
                fieldname = " concat(','," + fieldname + ",',') ";
            } else if (dataSourceDBType.toLowerCase().contains("postgresql")) {
                fieldname = "  ','||" + fieldname + "||',' ";
            } else {
                fieldname = " ',' + convert(varchar(max)," + fieldname + ")+ ',' ";
            }
            if (dataSourceDBType.toLowerCase().contains("oracle") || "db2".equalsIgnoreCase(dataSourceDBType)) {
                for (String tmpvalue : con_fieldvalueArray) {
                    sqlwhere.append(fieldname).append(" like '%,").append(tmpvalue).append(",%' or ");
                }
            } else {
                for (String tmpvalue : con_fieldvalueArray) {
                    sqlwhere.append(fieldname).append(" like '%,").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]")).append(",%' ESCAPE '/'  or ");
                }
            }
            sqlwhere.append(" 1=2 ) ");
        }
        return sqlwhere.toString();
    }

    public String getDateCondition(String fieldname, String con_fieldvalue) {
        StringBuffer sqlwhere = new StringBuffer();
        if (con_fieldvalue.indexOf(",") < 0 && con_fieldvalue.length() > 1 && !"10".equals(con_fieldvalue) && !"11".equals(con_fieldvalue) && !"12".equals(con_fieldvalue)) { //单一值
            sqlwhere.append(" and ").append(fieldname).append(" = '").append(con_fieldvalue).append("' ");
        } else {
            String[] con_fieldvalueArray = Util.splitString(con_fieldvalue, ",");
            Date date = new Date();
//            if(con_fieldvalueArray.length == 3) {
            int type = Util.getIntValue(con_fieldvalueArray[0]);
            switch (type) {
                case 0:
                    break;//全部
                case 1: //今天
                    sqlwhere.append(" and ").append(fieldname).append(" >= '").append(DateHelper.getCurrentDate()).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" < '").append(DateHelper.getTomorrow()).append("' ");
                    break;
                case 2: //本周（周一~周日）
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(DateHelper.getFirstDayOfWeek(date)).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(DateHelper.getLastDayOfWeek(date)).append("' ");
                    break;
                case 3://本月
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(DateHelper.getFirstDayOfMonthWeek(date)).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(DateHelper.getLastDayOfMonthWeek(date)).append("' ");
                    break;
                case 4://本季
                    Calendar today = Calendar.getInstance();
                    int month = today.get(Calendar.MONTH) + 1;    //获取到的mouth是比当前月少一个月月份的
                    int season = (month - 1) / 3 + 1;
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(DateHelper.getSeasonStart(season)).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(DateHelper.getSeasonend(season)).append("' ");
                    break;
                case 5://本年
                    String year = DateHelper.getCurrentYear();
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(year).append("-01-01' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(year).append("-12-31' ");
                    break;
                case 6:
                    if (con_fieldvalueArray.length > 1) {
                        if (!StringHelper.isEmpty(con_fieldvalueArray[1])) {
                            sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(con_fieldvalueArray[1]).append("' ");
                        }
                        if (con_fieldvalueArray.length > 2) {
                            if (!StringHelper.isEmpty(con_fieldvalueArray[2])) {
                                sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(con_fieldvalueArray[2]).append("' ");
                            }
                        }
                    }
                    break;
                case 7://上个月
                    Calendar last = Calendar.getInstance();
                    last.add(Calendar.MONTH, -1);
                    Date d = last.getTime();
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(DateHelper.getFirstDayOfMonthWeek(d)).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(DateHelper.getLastDayOfMonthWeek(d)).append("' ");
                    break;
                case 8://上年
                    int y = Util.getIntValue(DateHelper.getCurrentYear()) - 1;
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(y).append("-01-01' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(y).append("-12-31' ");
                    break;
                case 9://昨天
                    sqlwhere.append(" and ").append(fieldname).append(" = '").append(DateHelper.getYesterday()).append("' ");
                    break;
                case 10://明天
                    sqlwhere.append(" and ").append(fieldname).append(" = '").append(DateHelper.getTomorrow()).append("' ");
                    break;
                case 11: //上周（周一~周日）
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(DateHelper.getLastWeek().split(",")[0]).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(DateHelper.getLastWeek().split(",")[1]).append("' ");
                    break;
                case 12: //下周（周一~周日）
                    sqlwhere.append(" and ").append(fieldname).append(" >= ").append(" '").append(DateHelper.getNextWeek().split(",")[0]).append("' ");
                    sqlwhere.append(" and ").append(fieldname).append(" <= ").append(" '").append(DateHelper.getNextWeek().split(",")[1]).append("' ");
                    break;


            }
//            }
        }
        return sqlwhere.toString();
    }


    //单行文本	不区分大小写
    public String getTextCondition(String fieldname, String con_fieldvalue) {
        String searchsettings = "1";
        RecordSet rs = new RecordSet();
        rs.executeQuery("select * from formEngineSet t where t.isdelete=0");
        if (rs.next()) {
            searchsettings = Util.null2String(rs.getString("searchsettings"));
        }
        StringBuffer sqlwhere = new StringBuffer();
        if ("1".equals(searchsettings)) {
            ArrayList tempvalues = Util.TokenizerString(Util.StringReplace(con_fieldvalue, "　", " "), " ");
            sqlwhere.append(" and (");
            for (int k = 0; k < tempvalues.size(); k++) {
                if ("postgresql".equalsIgnoreCase(dataSourceDBType)) {
                    if (k == 0) {
                        sqlwhere.append(" cast(").append(fieldname).append(" as varchar) ");
                    } else {
                        sqlwhere.append(" or cast(").append(fieldname).append(" as varchar) ");
                    }
                } else {
                    if (k == 0) {
                        sqlwhere.append(" LOWER(").append(fieldname).append(") ");
                    } else {
                        sqlwhere.append(" or LOWER(").append(fieldname).append(") ");
                    }
                }
                String tmpvalue = (String) tempvalues.get(k);
                if (!this.noEscape) {
                    tmpvalue = Util.StringReplace(tmpvalue, "\\", "\\\\");
                }
                tmpvalue = Util.StringReplace(Util.StringReplace(tmpvalue, "+", "%"), "＋", "%");
                if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                    sqlwhere.append(" like LOWER('%").append(Util.StringReplace(tmpvalue, "/", "//"))
                            .append("%') ESCAPE '/'  ");
                } else if ("postgresql".equalsIgnoreCase(dataSourceDBType)) {
                    sqlwhere.append(" like '%").append(Util.StringReplace(tmpvalue, "/", "//")).append("%' ESCAPE '/' ");
                } else {
                    sqlwhere.append(" like LOWER('%").append(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/[")).append("%') ESCAPE '/' ");
                }
            }
            sqlwhere.append(")");
        } else {
            sqlwhere.append(" and LOWER(").append(fieldname).append(") ");
            String tmpvalue = con_fieldvalue;
            if (!this.noEscape) {
                tmpvalue = Util.StringReplace(tmpvalue, "\\", "\\\\");
            }
            if ("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType)) {
                sqlwhere.append(" like LOWER('%").append(Util.StringReplace(tmpvalue, "/", "//"))
                        .append("%') ESCAPE '/'  ");
            } else {
                sqlwhere.append(" like LOWER('%/").append(tmpvalue).append("%') ESCAPE '/' ");
            }
        }
        return sqlwhere.toString();
    }

    //多行文本
    public String getTextCondition2(String fieldname, String con_fieldvalue) {
        StringBuffer sqlwhere = new StringBuffer();
        ArrayList tempvalues = Util.TokenizerString(Util.StringReplace(con_fieldvalue, "　", " "), " ");
        sqlwhere.append("(");
        if ("sqlserver".equals(dataSourceDBType)) {
            fieldname = "convert(varchar(max)," + fieldname + ")";
        }
        for (int k = 0; k < tempvalues.size(); k++) {
            if (k == 0) {
                sqlwhere.append(" lower(").append(fieldname).append(") ");
            } else {
                sqlwhere.append(" or lower(").append(fieldname).append(") ");
            }
            String tmpvalue = Util.StringReplace(Util.StringReplace((String) tempvalues.get(k), "+", "%"), "＋", "%");
            tmpvalue = tmpvalue.toLowerCase();
            if (!("oracle".equalsIgnoreCase(dataSourceDBType) || "db2".equalsIgnoreCase(dataSourceDBType))) {
                sqlwhere.append(" like '%").append(Util.StringReplace(Util.StringReplace(Util.StringReplace(Util.StringReplace(tmpvalue, "/", "//"), "[", "/["), "]", "/]"), "%", "/%"))
                        .append("%' ESCAPE '/'  ");
            } else {
                sqlwhere.append(" like '%").append(Util.StringReplace(tmpvalue, "%", "/%")).append("%' ");
            }
        }
        sqlwhere.append(")");
        return sqlwhere.toString();
    }

    private String getSqlFrom() {
        StringBuffer sqlFrom = new StringBuffer();
        sqlFrom.append(" from ").append(tableName).append(" ").append(MAIN_TABLE_ALIAS);
        if (detailTable != null && !"".equals(detailTable)) {
            sqlFrom.append(" left join ").append(detailTable).append(" ").append(DETAIL_TABLE_ALIAS)
                    .append("  on  ").append(DETAIL_TABLE_ALIAS).append(".").append(DETAIL_MAINID).append(" = ")
                    .append(MAIN_TABLE_ALIAS).append(".").append(primaryKey);
        }
        if ((viewType != 3 && !noRightList && !isVirtualForm && !hasUserRight) ||
                (isVirtualForm && !noRightList)) {//不是监控、无权限列表、不是虚拟表单
            String rightsql = this.getRightSql();
            if (!"".equals(rightsql)) {
                sqlFrom.append(" , ").append(rightsql).append(" ").append(RIGHT_TABLE_ALIAS);
            }
        }
        return StringEscapeUtils.unescapeHtml4(sqlFrom.toString());
    }

    private String getRightSql() {
        RecordSet rs = new RecordSet();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        ModeShareManager modeShareManager = new ModeShareManager();
        List<User> lsUser = modeRightInfo.getAllUserCountList(user);
        //未配置模块时重新解析权限
        String rightsql = "";
        if (isVirtualForm) {
            if (!ListUtil.haveFormmodeidAndID(formId + "")) {
                return "";
            }
            ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
            String vdatasource = modeFormComInfo.getVDataSource(formId + "");
            String tablename = "";
            String vformtype = modeFormComInfo.getVformtype(this.formId);
            if ("2".equals(vformtype)) {
                tablename = modeFormComInfo.getVsql(this.formId);
            } else {
                tablename = VirtualFormHandler.getRealFromName(tableName);
            }
            String sql = "select distinct formmodeid from " + tablename;
            if ("2".equals(vformtype)) {
                sql += " t ";
            }
            rs.executeQueryWithDatasource(sql, vdatasource);
            while (rs.next()) {
                this.virtualHasRightSql = true;
                String mid = rs.getString("formmodeid");
                modeShareManager.setModeId(Util.getIntValue(mid, 0));
                for (int i = 0; i < lsUser.size(); i++) {
                    User tempUser = lsUser.get(i);
                    String tempRightStr = modeShareManager.getShareDetailTableByUser("formmode", tempUser);
                    if (rightsql.isEmpty()) {
                        rightsql += tempRightStr;
                    } else {
                        if (rs.getDBType().toLowerCase().contains("mysql")) {//mysql两个大结果集直接union all会报错
                            rightsql += " union  all SELECT  sourceid,sharelevel from " + tempRightStr + " as t" + i;
                        } else {
                            rightsql += " union  all " + tempRightStr;
                        }
                    }
                    String roleLimitedRightsql = getRoleLimitedRightsql(mid + "", tempUser, tableName);
                    if (roleLimitedRightsql.isEmpty()) {
                        rightsql += roleLimitedRightsql;
                    } else {
                        if (rs.getDBType().toLowerCase().contains("mysql")) {//mysql两个大结果集直接union all会报错
                            rightsql += " union  all SELECT  sourceid,sharelevel from (" + roleLimitedRightsql + ") as t" + i;
                        } else {
                            rightsql += " union  all " + roleLimitedRightsql;
                        }
                    }
//                    //针对字段相关的权限
//                    CubeFieldRightInfo cubeFieldRightInfo=new CubeFieldRightInfo(Util.getIntValue(mid),this.formId,tempUser);
//            		String fieldRightSql=cubeFieldRightInfo.getWhereSqlForRolelimited(this.isEdit);
//            		rightsql=cubeFieldRightInfo.plusRightSql(rightsql,fieldRightSql);
                }
                /*String roleLimitedRightsql = getRoleLimitedRightsql(mid+"",lsUser);
                if(!roleLimitedRightsql.equals("")){
                    if (rs.getDBType().equals("mysql")) {//mysql两个大结果集直接union all会报错
                        rightsql += " union  all SELECT  sourceid,sharelevel from "+ roleLimitedRightsql+" as t";
                    }else {
                        rightsql += " union  all "+ roleLimitedRightsql;
                    }
                }*/
            }
        } else {
            if (modeId == 0) {//查询中没有设置模块
                String sqlStr1 = "select id,modename from modeinfo where formid=? order by id";
                rs.executeQuery(sqlStr1, formId);
                int tempNum = 1;
                while (rs.next()) {
                    String mid = rs.getString("id");
                    modeShareManager.setModeId(Util.getIntValue(mid, 0));
                    for (int i = 0; i < lsUser.size(); i++) {
                        User tempUser = lsUser.get(i);
                        String tempRightStr = modeShareManager.getShareDetailTableByUser("formmode", tempUser);

                        if (rightsql.isEmpty()) {
                            rightsql += tempRightStr;
                        } else {
                            if (rs.getDBType().toLowerCase().contains("mysql")) {//mysql两个大结果集直接union all会报错
                                rightsql += " union  all SELECT  sourceid,sharelevel from " + tempRightStr + " as t" + i;
                            } else {
                                rightsql += " union  all " + tempRightStr;
                            }
                        }

                        String roleLimitedRightsql = getRoleLimitedRightsql(mid + "", tempUser, tableName);
                        if (roleLimitedRightsql.isEmpty()) {
                            rightsql += roleLimitedRightsql;
                        } else {
                            if (rs.getDBType().toLowerCase().contains("mysql")) {//mysql两个大结果集直接union all会报错
                                rightsql += " union  all SELECT  sourceid,sharelevel from (" + roleLimitedRightsql + ") as t" + i;
                            } else {
                                rightsql += " union  all " + roleLimitedRightsql;
                            }
                        }
//                        //针对字段相关的权限
//                        CubeFieldRightInfo cubeFieldRightInfo=new CubeFieldRightInfo(Util.getIntValue(mid),this.formId,tempUser);
//                		String fieldRightSql=cubeFieldRightInfo.getWhereSqlForRolelimited(this.isEdit);
//                		rightsql=cubeFieldRightInfo.plusRightSql(rightsql,fieldRightSql);
                    }

                    /*String roleLimitedRightsql = getRoleLimitedRightsql(mid,lsUser);
                    if(!roleLimitedRightsql.equals("")){
                        if (rs.getDBType().equals("mysql")) {//mysql两个大结果集直接union all会报错
                            rightsql += " union  all SELECT  sourceid,sharelevel from "+ roleLimitedRightsql+" as r"+tempNum;
                        }else {
                            rightsql += " union  all "+ roleLimitedRightsql;
                        }
                    }*/
                    tempNum++;
                }
            } else {
                modeShareManager.setModeId(modeId);
                for (int i = 0; i < lsUser.size(); i++) {
                    User tempUser = (User) lsUser.get(i);
                    String tempRightStr = modeShareManager.getShareDetailTableByUser("formmode", tempUser);
                    if (rightsql.isEmpty()) {
                        rightsql += tempRightStr;
                    } else {
                        rightsql += " union  all " + tempRightStr;
                    }

                    String roleLimitedRightsql = getRoleLimitedRightsql(modeId + "", tempUser, tableName);
                    if (roleLimitedRightsql.isEmpty()) {
                        rightsql += roleLimitedRightsql;
                    } else {
                        if (rs.getDBType().toLowerCase().contains("mysql")) {//mysql两个大结果集直接union all会报错
                            rightsql += " union  all SELECT  sourceid,sharelevel from (" + roleLimitedRightsql + ") as t" + i;
                        } else {
                            rightsql += " union  all " + roleLimitedRightsql;
                        }
                    }
//                    //针对字段相关的权限
//                    CubeFieldRightInfo cubeFieldRightInfo=new CubeFieldRightInfo(this.modeId,this.formId,tempUser);
//            		String fieldRightSql=cubeFieldRightInfo.getWhereSqlForRolelimited(this.isEdit);
//            		rightsql=cubeFieldRightInfo.plusRightSql(rightsql,fieldRightSql);
                }
                /*String roleLimitedRightsql = getRoleLimitedRightsql(modeId+"",lsUser);
                if(!roleLimitedRightsql.equals("")){
                    if (rs.getDBType().equals("mysql")) {//mysql两个大结果集直接union all会报错
                        rightsql += " union  all SELECT  sourceid,sharelevel from "+ roleLimitedRightsql+" as t";
                    }else {
                        rightsql += " union  all "+ roleLimitedRightsql;
                    }
                }*/
            }
        }
        if (!rightsql.isEmpty()) {
            rightsql = " (SELECT  sourceid,MAX(sharelevel) AS sharelevel from ( " + rightsql + " ) temptable group by temptable.sourceid) ";
        }
        return rightsql;
    }

    public Map<String, String> getFieldinfo(String fieldid) {
        Map<String, String> fieldInfo = new HashMap<>();
        RecordSet rs = new RecordSet();
        rs.executeQuery("select fieldname,type typeTemp,detailtable from workflow_billfield where id=?", fieldid);
        if (rs.next()) {
            fieldInfo.put("fieldname", rs.getString("fieldname"));
            fieldInfo.put("detailtable", rs.getString("detailtable"));
            fieldInfo.put("type", rs.getString("typeTemp"));
        }
        return fieldInfo;
    }

    public boolean isMultBrowser(String type) {
        if ("17".equals(type) || "57".equals(type) || "168".equals(type) || "166".equals(type) || "170".equals(type) || "194".equals(type)) {
            return true;
        } else {
            return false;
        }
    }

    public boolean isFitRole(User user, int roleId, int rolelevel, int showlevel, int showlevel2) {
        HrmCommonService hrmCommonService = new HrmCommonServiceImpl();
        List<Object> roleList = hrmCommonService.getRoleInfo(user.getUID());
        int userS = Util.getIntValue(user.getSeclevel(), -9999);
        for (Object obj : roleList) {
            Map<String, Object> m = (Map<String, Object>) obj;
            if (Util.getIntValue(Util.null2String(m.get("roleid")), 0) == roleId
                    && userS >= showlevel && userS <= showlevel2) {
                return true;
            }
        }

        return false;
    }

    public String getRoleLimitedRightsql(String modeid, User currentUser, String zbTableName) {
        String roleLimitedRightsql = "";
        RecordSet rs = new RecordSet();
        String tablename = "modeDataShare_" + modeid + "_set";
        String sql = "select * from moderightinfo  where sharetype=4 and isrolelimited=1 and  modeid=" + modeid;
        rs.executeQuery(sql);
        int count = 0;
        while (rs.next()) {
            String tempSql = "";
            String rolefield = rs.getString("rolefield");//字段详细   -101：模块创建人  -102：模块创建人部门  -103：模块创建人分部  其他与workflow_billfield表中的id对应
            if (StringHelper.isEmpty(rolefield)) {
                continue;
            }
            int rightid = rs.getInt("id");
            int rolefieldtype = rs.getInt("rolefieldtype");//字段类型  1：人员  2：部门  3：分部
            int relatedid = rs.getInt("relatedid");        //ID
            int rolelevel = rs.getInt("rolelevel");        //角色级别
            int showlevel = rs.getInt("showlevel");        //安全级别

            String showlevel2Str = Util.null2String(rs.getString("showlevel2"));
            int showlevel2 = Util.getIntValue(showlevel2Str, 9999);
            Map<String, String> fieldinfo = getFieldinfo(rolefield);
            String roleFieldName = fieldinfo.get("fieldname");
            String detailtable = fieldinfo.get("detailtable");
            String conditionsql = rs.getString("conditionsql").trim();
            String conditionsqlWhere = "";
            if (!StringHelper.isEmpty(conditionsql)) {
                conditionsqlWhere = " and zb.id in (select sourceid from " + tablename + " where rightid='" + rightid + "')";
            }
            String tempTableName = zbTableName;
            if (roleFieldName != null) {
                //明细字段
                if (!StringHelper.isEmpty(detailtable)) {
                    tempTableName = " ( select zb.id as id,zb.modedatacreater as modedatacreater,mx." + roleFieldName + " as " + roleFieldName + " from " + tempTableName + " zb," + detailtable + " mx where zb.id=mx.mainid " + conditionsqlWhere + " and  zb.formmodeid=" + modeid + ") ";

                } else {
                    tempTableName = " ( select zb.id as id,zb.modedatacreater as modedatacreater,zb." + roleFieldName + " as " + roleFieldName + " from " + tempTableName + " zb where 1=1 " + conditionsqlWhere + " and  zb.formmodeid=" + modeid + ") ";
                }
            } else {
                tempTableName = "(select * from " + zbTableName + " zb where 1=1 " + conditionsqlWhere + ")";
            }

            String type = fieldinfo.get("type");
            if (count > 0) {
                if (rs.getDBType().toLowerCase().contains("mysql")) {
                    tempSql = " union  all select sourceid,sharelevel from (select distinct tt.id as sourceid,1 as sharelevel from " + tempTableName + " tt ";
                } else {
                    tempSql = " union  all select distinct tt.id as sourceid,1 as sharelevel from " + tempTableName + " tt ";
                }
            } else {
                tempSql = " select distinct tt.id as sourceid,1 as sharelevel from " + tempTableName + " tt ";
            }
            int id = rs.getInt("id");


            if (!isFitRole(currentUser, relatedid, rolelevel, showlevel, showlevel2)) {
                continue;
            }
            String sqlwhere = "";
            if (rolefield.equals("-101")) {
                if (rolelevel == 0) {
                    sqlwhere = " where " + currentUser.getUID() + " in (select id from hrmresource where departmentid in (select departmentid from hrmresource where id=tt.modedatacreater)) ";
                } else if (rolelevel == 1) {
                    sqlwhere = " where " + currentUser.getUID() + " in (select id from hrmresource where subcompanyid1 in (select subcompanyid1 from hrmresource where id=tt.modedatacreater)) ";
                } else if (rolelevel == 2) {
                    sqlwhere = " where " + currentUser.getUID() + " = tt.modedatacreater";
                }
                //sqlwhere = " where tt.modedatacreater = "+currentUser.getUID();
            } else if (rolefield.equals("-102")) {
                if (currentUser.getUID() == 1) {
                    continue;
                }
                sqlwhere = " where " + currentUser.getUserDepartment() + " in (select departmentid from hrmresource where id=tt.modedatacreater) ";
            } else if (rolefield.equals("-103")) {
                if (currentUser.getUID() == 1) {
                    continue;
                }
                sqlwhere = " where " + currentUser.getUserSubCompany1() + " in (select subcompanyid1 from hrmresource where id=tt.modedatacreater) ";
            } else {
                boolean isMultBrowser = isMultBrowser(type);
                if (rolefieldtype == 1) {//人力资源
                    String tempSqlWhere = "";
                    if (isMultBrowser) {
                        if (rs.getDBType().toLowerCase().contains("mysql")) {
                            tempSqlWhere = " where CONCAT(',',ifNULL(tt." + roleFieldName + ",''),',')  like CONCAT('%,',ifNULL(id,''),',%') ";
                        } else if (rs.getDBType().equals("oracle") || "postgresql".equals(rs.getDBType().toLowerCase())) {
                            tempSqlWhere = " where ','||tt." + roleFieldName + "||',' like '%,'||id||',%' ";
                        } else {
                            tempSqlWhere = " where ','+isnull(cast(tt." + roleFieldName + " as varchar(4000)),'')+',' like '%,'+isnull(cast(id  as varchar(4000)),'')+',%' ";
                        }
                    } else {
                        tempSqlWhere = " where id   = tt." + roleFieldName;
                    }
                    if (rolelevel == 0) {
                        sqlwhere = " where " + currentUser.getUID() + " in (select id from hrmresource where departmentid in (select departmentid from hrmresource " + tempSqlWhere + ")) ";
                    } else if (rolelevel == 1) {
                        sqlwhere = " where " + currentUser.getUID() + " in (select id from hrmresource where subcompanyid1 in (select subcompanyid1 from hrmresource " + tempSqlWhere + ")) ";
                    } else if (rolelevel == 2) {
                        if (isMultBrowser) {
                            if (rs.getDBType().toLowerCase().contains("mysql")) {
                                sqlwhere = " where CONCAT(',',ifNULL(tt." + roleFieldName + ",''),',') like '%," + currentUser.getUID() + ",%'";
                            } else if (rs.getDBType().equals("oracle")) {
                                sqlwhere = " where ','||tt." + roleFieldName + "||',' like '%," + currentUser.getUID() + ",%'";
                            } else {
                                sqlwhere = " where ','+isnull(cast(tt." + roleFieldName + " as varchar(4000)),'')+',' like '%," + currentUser.getUID() + ",%' ";
                            }
                        } else {
                            sqlwhere = " where tt." + roleFieldName + "=" + currentUser.getUID();
                        }
                    }

                } else if (rolefieldtype == 2) {//部门
                    if (isMultBrowser) {
                        if (rs.getDBType().toLowerCase().contains("mysql")) {
                            sqlwhere = " where CONCAT(',',ifNULL(tt." + roleFieldName + ",''),',') like '%," + currentUser.getUserDepartment() + ",%'";
                        } else if (rs.getDBType().equals("oracle")) {
                            sqlwhere = " where ','||tt." + roleFieldName + "||',' like '%," + currentUser.getUserDepartment() + ",%'";
                        } else {
                            sqlwhere = " where ','+isnull(cast(tt." + roleFieldName + " as varchar(4000)),'')+',' like '%," + currentUser.getUserDepartment() + ",%' ";
                        }
                    } else {
                        sqlwhere = " where tt." + roleFieldName + "=" + currentUser.getUserDepartment();
                    }
                    //sqlwhere = " where "+currentUser.getUserDepartment()+" in (tt."+roleFieldName+")";
                } else if (rolefieldtype == 3) {//分部
                    if (isMultBrowser) {
                        if (rs.getDBType().toLowerCase().contains("mysql")) {
                            sqlwhere = " where CONCAT(',',ifNULL(tt." + roleFieldName + ",''),',') like '%," + currentUser.getUserSubCompany1() + ",%'";
                        } else if (rs.getDBType().equals("oracle")) {
                            sqlwhere = " where ','||tt." + roleFieldName + "||',' like '%," + currentUser.getUserSubCompany1() + ",%'";
                        } else {
                            sqlwhere = " where ','+isnull(cast(tt." + roleFieldName + " as varchar(4000)),'')+',' like '%," + currentUser.getUserSubCompany1() + ",%' ";
                        }
                    } else {
                        sqlwhere = " where tt." + roleFieldName + "=" + currentUser.getUserSubCompany1();
                    }
                    //sqlwhere = " where "+currentUser.getUserSubCompany1()+" in (tt."+roleFieldName+")";
                }
            }

            if (!StringHelper.isEmpty(sqlwhere)) {
                tempSql += sqlwhere;
                if (count > 0 && rs.getDBType().toLowerCase().contains("mysql")) {
                    tempSql += ") as temp_" + count;
                }
                roleLimitedRightsql += tempSql;
                count++;
            }

        }
        /*ModeRightForPage modeRightForPage = new ModeRightForPage();
        Set<String> rightids = new HashSet<String>();
        while(rs.next()){
            String rightid = Util.null2String(rs.getString("id"));
            String sourceid = Util.null2String(rs.getString("sourceid"));
            String content = Util.null2String(rs.getString("relatedid"));
            String showlevelValue = Util.null2String(rs.getString("showlevel"));
            String rolelevelValue = Util.null2String(rs.getString("rolelevel"));
            String showlevel2 = Util.null2String(rs.getString("showlevel2"));
            int rolefieldtype=Util.getIntValue(rs.getString("rolefieldtype"));
            //int righttype=Util.getIntValue(rs.getString("righttype"));
            String rolefield=Util.null2String(rs.getString("rolefield"));
            String hrmCompanyVirtualType = Util.null2String(rs.getString("hrmCompanyVirtualType"));
            Set<String> users = modeRightForPage.getRightUsersByRolelimited(sourceid,formId,
                    Util.getIntValue(content),Util.getIntValue(showlevelValue),Util.getIntValue(rolelevelValue),showlevel2,
                    rolefieldtype,rolefield,hrmCompanyVirtualType);
            for(User user : lsUser){
                if(users.contains(user.getUID()+"")){
                    rightids.add(rightid);
                    break;
                }
            }
        }
        if(rightids.size()>0){
            roleLimitedRightsql = "(select sourceid,MAX(righttype) as sharelevel from "+tablename+" where id in("+String.join(",",rightids)+") GROUP BY sourceid)";
        }*/
        return roleLimitedRightsql;
    }

    private String getBackFields(RecordSet rs) {
        Map<String, String> oriFieldMap = new CaseInsensitiveMap();
        StringBuffer showfield = new StringBuffer();
        //判断表单中是否有创建人、创建日期字段。（流程表）
        if (isVirtualForm) {
            rs.executeSqlWithDataSource("select * from " + VirtualFormHandler.getRealFromName(tableName) + " tempName where 1=2", dataSource);
        } else {
            rs.executeSql("select * from " + tableName + " where 1=2");
        }
        String colfieldname[] = rs.getColumnName();
        if (!StringHelper.containsIgnoreCase(colfieldname, "modedatacreater") && !StringHelper.containsIgnoreCase(colfieldname, "modedatacreatedate")) {
            showfield.append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append(primaryKey);
            oriFieldMap.put(primaryKey.toLowerCase(), primaryKey.toLowerCase());
            this.customButtonParameter.remove(primaryKey);
        } else {
            if (isVirtualForm) {
                showfield.append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append(primaryKey);
                oriFieldMap.put(primaryKey.toLowerCase(), primaryKey.toLowerCase());
                this.customButtonParameter.remove(primaryKey);
            } else {
                showfield.append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append(primaryKey).append(",")
                        .append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("formmodeid,")
                        .append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatacreater,")
                        .append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatacreatertype,")
                        .append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatacreatedate,")
                        .append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatacreatetime");

                if (StringHelper.containsIgnoreCase(colfieldname, "modedatamodifier")) {
                    showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatamodifier");
                    this.customButtonParameter.remove("modedatamodifier");
                }
                if (StringHelper.containsIgnoreCase(colfieldname, "modedatamodifydatetime")) {
                    showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatamodifydatetime ");
                    this.customButtonParameter.remove("modedatamodifydatetime");
                }
                if (StringHelper.containsIgnoreCase(colfieldname, "seclevel")) {//密级字段
                    showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("seclevel ");
                    this.customButtonParameter.remove("seclevel");
                }
                if (StringHelper.containsIgnoreCase(colfieldname, "modesecrettime")) {//密级时间长字段
                    showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modesecrettime ");
                    this.customButtonParameter.remove("modesecrettime");
                }

                if (StringHelper.containsIgnoreCase(colfieldname, "modeuuid")) {
                    showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modeuuid ");
                }

                this.customButtonParameter.remove(primaryKey);
                this.customButtonParameter.remove("formmodeid");
                this.customButtonParameter.remove("modedatacreater");
                this.customButtonParameter.remove("modedatacreatertype");
                this.customButtonParameter.remove("modedatacreatedate");
                this.customButtonParameter.remove("modedatacreatetime");
                ModeSetUtil modeSetUtil = new ModeSetUtil();
                if (modeSetUtil.isHaveModedatastatusByformid(formId)) {
                    showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modedatastatus ");
                    this.customButtonParameter.remove("modedatastatus");
                }
            }
            String sql = "select istagset from modeinfo where id=?";
            String istagset = "0";
            rs.executeQuery(sql, modeId);
            if (rs.next()) {
                istagset = Util.null2String(rs.getString("istagset"));
            }
            if ("1".equals(istagset)) {
                showfield.append(",").append(SearchConstant.MAIN_TABLE_ALIAS).append(".").append("modelableid ");
                this.customButtonParameter.remove("modelableid");
            }
        }
        this.sqlPrimaryKey = MAIN_TABLE_ALIAS + "." + primaryKey;
        if (!StringHelper.isEmpty(detailTable)) {
            this.sqlPrimaryKey += ",d_id";
            showfield.append(",").append(SearchConstant.DETAIL_TABLE_ALIAS).append(".id").append(" d_id ");
        }
        String sql = "select b.isorder,b.ColWidth,a.id as id,a.fieldname as name,a.fieldlabel as label" +
                ",a.fielddbtype as dbtype ,a.fieldhtmltype as httype, a.type as typeTemp" +
                ",b.showorder,b.istitle,b.isstat,b.showmethod, b.isgroup" +
                ",a.viewtype,a.detailtable,b.ismaplocation,b.shownamelabel,b.editable,b.ALIGNMENT,a.qfws,b.hreflink" +
                " from workflow_billfield a ,Mode_CustomDspField b" +
                " where a.id=b.fieldid and b.customid= ?  and b.isshow='1' and a.billid= ? " +
                " union all select a.isorder,a.ColWidth,a.fieldid as id,'1' as name,0 as label,'3' as dbtype, '4' as httype,5 as typeTemp " +
                ",a.showorder,a.istitle,a.isstat,a.showmethod, a.isgroup" +
                ",0 as viewtype,'' as detailtable,a.ismaplocation,a.shownamelabel,a.editable,a.ALIGNMENT,'2' qfws ,a.hreflink" +
                " from Mode_CustomDspField a " +
                " where a.customid=? and a.isshow='1'  and a.fieldid<0" +
                " order by showorder asc,viewtype,id asc";
        //根据customId查询是否是分组查询
        RecordSet rsForTree = new RecordSet();
        int istreesearch = 0;
        rsForTree.executeQuery("select appid,fixednumberforth,fixednumberback,istreesearch from mode_customsearch where id = ?", customId);
        if (rsForTree.next()) {
            istreesearch = Util.getIntValue(rsForTree.getString("istreesearch"), 0);
        }
        if (istreesearch == 1) {
            //查询mode_customSearchButton 中isshow=1的条数
            int btnCounts = 0;
            rsForTree.executeQuery(" select  count(1) as count from mode_customSearchButton where objid=?", customId);
            if (rsForTree.next()) {
                btnCounts = Util.getIntValue(rsForTree.getString("count"), 0);
                if (btnCounts > 0) {
                    sql = "select b.isorder,b.ColWidth,a.id as id,a.fieldname as name,a.fieldlabel as label" +
                            ",a.fielddbtype as dbtype ,a.fieldhtmltype as httype, a.type as typeTemp" +
                            ",b.showorder,b.istitle,b.isstat,b.showmethod, b.isgroup" +
                            ",a.viewtype,a.detailtable,b.ismaplocation,b.shownamelabel,b.editable,b.ALIGNMENT,a.qfws,b.hreflink" +
                            " from workflow_billfield a ,Mode_CustomDspField b" +
                            " where a.id=b.fieldid and b.customid= ?  and b.isshow='1' and a.billid= ? " +
                            " union all select a.isorder,a.ColWidth,a.fieldid as id,'1' as name,0 as label,'3' as dbtype, '4' as httype,5 as typeTemp " +
                            ",a.showorder,a.istitle,a.isstat,a.showmethod, a.isgroup" +
                            ",0 as viewtype,'' as detailtable,a.ismaplocation,a.shownamelabel,a.editable,a.ALIGNMENT,'2' qfws ,a.hreflink" +
                            " from Mode_CustomDspField a " +
                            " where a.customid=? and a.isshow='1'  and a.fieldid<0 and a.fieldid!=-6 " +
                            " union all select a.isorder,a.ColWidth,a.fieldid as id,'1' as name,0 as label,'3' as dbtype, '4' as httype,5 as typeTemp " +
                            ",a.showorder,a.istitle,a.isstat,a.showmethod, a.isgroup" +
                            ",0 as viewtype,'' as detailtable,a.ismaplocation,a.shownamelabel,a.editable,a.ALIGNMENT,'2' qfws ,a.hreflink" +
                            " from Mode_CustomDspField a " +
                            " where a.customid=? and a.fieldid=-6" +
                            " order by showorder asc,viewtype,id asc";
                    rs.executeQuery(sql, customId, formId, customId, customId);
                } else {
                    rs.executeQuery(sql, customId, formId, customId);
                }
            }
        } else {
            rs.executeQuery(sql, customId, formId, customId);
        }

        Set<String> showFieldSet = new HashSet<String>();
        while (rs.next()) {
            if (rs.getInt("id") > 0) {
                String tempname = Util.null2String(rs.getString("name"));
                if (tempname.equalsIgnoreCase(primaryKey)) {
                    continue;
                }
                String dbtype = Util.null2String(rs.getString("dbtype"));
                String isgroup = Util.null2String(rs.getString("isgourp"));

                String fieldAlias = tempname;
                String tableAlias = SearchConstant.MAIN_TABLE_ALIAS;
                int field_viewtype = rs.getInt("viewtype");
                if (field_viewtype == 1) {
                    tableAlias = SearchConstant.DETAIL_TABLE_ALIAS;
                    fieldAlias = SearchConstant.DETAIL_FIELD_ALIAS + tempname;
                }
                oriFieldMap.put(fieldAlias, fieldAlias);
                if (isBackUpData) {
                    if (!backupFields.contains(fieldAlias.toLowerCase())) {
                        showFieldSet.add(", null " + fieldAlias);
                        continue;
                    }
                }
                if (this.customButtonParameter.contains(fieldAlias)) {
                    this.customButtonParameter.remove(fieldAlias);
                }
                showFieldSet.add(this.getShowFieldName(dbtype, tableAlias, tempname, fieldAlias, field_viewtype));
            }
        }
        for (String field : showFieldSet) {
            showfield.append(field);
        }
        //配置了自定义按钮参数，单未在返回字段中
        Set<String> allFields = new HashSet<String>();
        if (this.customButtonParameter.size() > 0) {
            RecordSet rs_c = new RecordSet();
            rs_c.executeQuery("select fieldname,detailtable from workflow_billfield where (detailtable is null or detailtable='' or detailtable=?) and billid=?", this.detailTable, this.formId);
            while (rs_c.next()) {
                String fieldname = Util.null2String(rs_c.getString("fieldname"));
                String detailtable = Util.null2String(rs_c.getString("detailtable"));
                if (detailtable.equals("")) {
                    allFields.add(fieldname);
                } else {
                    allFields.add("d_" + fieldname);
                }
            }
        }
        for (String param : this.customButtonParameter) {
            if (param.startsWith(SearchConstant.DETAIL_FIELD_ALIAS)) {//明细字段
                if (allFields.contains(param)) {
                    showfield.append("," + SearchConstant.DETAIL_TABLE_ALIAS + "." + param.substring(2) + " as " + param);
                    oriFieldMap.put(param, param);
                }
            } else {
                String newshowfield = showfield.toString().toLowerCase();
                String newparam = SearchConstant.MAIN_TABLE_ALIAS + "." + param;
                if (allFields.contains(param) && newshowfield.indexOf(newparam.toLowerCase()) == -1) {
                    showfield.append("," + SearchConstant.MAIN_TABLE_ALIAS + "." + param);
                    oriFieldMap.put(param, param);
                }
            }
        }
        RecordSet rs2 = new RecordSet();
        rs2.executeQuery("select * from mode_customSearchButton where objid=? and isshow=1  order by showorder asc,id desc", customId);
        if (rs2.getCounts() > 0) {
            showfield.append("," + SearchConstant.MAIN_TABLE_ALIAS).append(".").append(primaryKey + " as operatesshow");
            showfield.append("," + SearchConstant.MAIN_TABLE_ALIAS).append(".").append(primaryKey + " as likesinfo");
        }
        //CubeSearchTransMethod.getothers需要查处所有列,这里添加没有查出来的列
        if (StringHelper.containsIgnoreCase(colfieldname, "requestid") && !oriFieldMap.containsKey("requestid")) {
            showfield.append("," + SearchConstant.MAIN_TABLE_ALIAS).append(".requestid");
            oriFieldMap.put("requestid", "requestid");
        }
        addHideField(oriFieldMap, showfield);
        return showfield.toString();
    }

    private void addHideField(Map<String, String> oriFieldMap, StringBuffer showfield) {
        RecordSet rs = new RecordSet();
        String sql = "select fieldname,viewtype,fielddbtype from workflow_billfield where billid=? ";
        boolean haveDetail = !StringHelper.isEmpty(detailTable);
        if (haveDetail) {
            sql += " and ((viewtype=1 and detailtable=?) or viewtype=0)";
            rs.executeQuery(sql, formId, detailTable);
        } else {
            sql += " and viewtype=0 ";
            rs.executeQuery(sql, formId);
        }
        while (rs.next()) {
            int viewtype = Util.getIntValue(rs.getString("viewtype"), 0);
            String fieldname = Util.null2String(rs.getString("fieldname"));
            String fielddbtype = Util.null2String(rs.getString("fielddbtype"));
            String fieldAlias = fieldname;
            String tableAlias = SearchConstant.MAIN_TABLE_ALIAS;
            if (viewtype == 1) {
                tableAlias = SearchConstant.DETAIL_TABLE_ALIAS;
                fieldAlias = SearchConstant.DETAIL_FIELD_ALIAS + fieldname;
            }
            if (oriFieldMap.containsKey(fieldAlias)) {
                continue;
            }
            String showFieldName = this.getShowFieldName(fielddbtype, tableAlias, fieldname, fieldAlias, viewtype);
            showfield.append(showFieldName);
        }
    }

    private String getShowFieldName(String dbtype, String tableAlias, String tempname, String fieldAlias, int field_viewtype) {
        String showfield = "";
        if (dbtype != null && "text".equalsIgnoreCase(dbtype)) {
            if ("oracle".equals(this.dataSourceDBType)) {
                showfield = "," + "to_char(" + tableAlias + "." + tempname + ") as " + fieldAlias;
            } else if (this.dataSourceDBType.toLowerCase().contains("mysql")) {
                showfield = "," + "convert(" + tableAlias + "." + tempname + ",char) as " + fieldAlias;
            } else if ("postgresql".equals(this.dataSourceDBType)) {
                showfield = "," + "to_char(" + tableAlias + "." + tempname + ") as " + fieldAlias;
            } else {
                showfield = "," + "convert(varchar(max)," + tableAlias + "." + tempname + ") as " + fieldAlias;
            }
        } else {
            if (field_viewtype == 1) {
                showfield = "," + tableAlias + "." + tempname + " as " + fieldAlias;
            } else {
                showfield = "," + tableAlias + "." + tempname;
            }
        }
        return showfield;
    }


    public Map<String, Object> getMapTitles(HttpServletRequest request, HttpServletResponse response) {
        String customId = Util.null2String(request.getParameter("customid"));
        String id = Util.null2String(request.getParameter("billids"));
        //针对前端的id数据有3_1这种，需要做判断，进行字符串截取。
        String[] idArry = "".equals(id) ? new String[0] : id.split("\\,");
        StringBuffer idStr = new StringBuffer();
        for (int i = 0; i < idArry.length; i++) {
            int indexof = idArry[i].indexOf("_");
            //如果没有“_”，则说明当前查询列表没有引入子表
            if (indexof == -1) {
                break;
            } else if (i == 0) {
                idStr.append(idArry[i].substring(0, indexof));
                id = idStr.toString();
                continue;
            }
            idStr.append("," + idArry[i].substring(0, indexof));
            if (i == idArry.length - 1) {
                id = idStr.toString();
            }
        }
        this.user = HrmUserVarify.getUser(request, response);
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        String formId = Util.null2String(customSearchComInfo.getFormId(customId));
        this.modeId = Util.getIntValue(customSearchComInfo.getModeId(customId));
        this.noRightList = "1".equals(customSearchComInfo.getNoRightList(customId));
        this.viewType = Util.getIntValue(request.getParameter("viewtype"), 0);
        this.formId = Util.getIntValue(customSearchComInfo.getFormId(customId), 0);
        String tableName = modeFormComInfo.getTableName(formId);
        boolean isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        String primaryKey = "id";
        String dataSource = "";
        if (isVirtualForm) {
            primaryKey = modeFormComInfo.getVPrimaryKey(formId);
            dataSource = modeFormComInfo.getVDataSource(formId);
            tableName = VirtualFormHandler.getRealFromName(tableName);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        String sql = " select a.fieldid, b.fieldname, b.detailtable " +
                "  from mode_customdspfield a " +
                "  left join workflow_billfield b " +
                "    on a.fieldid = b.id " +
                " where a.customid = ? " +
                "   and a.ismaplocation = 1 " +
                "   and isshow = 1";
        rs.executeQuery(sql, customId);
        String detailTable = "";
        StringBuffer _sql = new StringBuffer();
        _sql.append(" select  distinct ");
        while (rs.next()) {
            String fieldname = rs.getString(2);
            String _detailTable = rs.getString(3);
            if (StringHelper.isEmpty(_detailTable)) {
                _sql.append(" t.");
            } else {
                _sql.append(" d.");
                //明细表不为空，则将明细表名称赋值给detailTable
                detailTable = _detailTable;
            }
            _sql.append(fieldname).append(",");
        }
        _sql.append("t.id from ").append(tableName).append(" t ");
        if (!StringHelper.isEmpty(detailTable)) {
            _sql.append(" left join ").append(detailTable).append(" d on t.id = d.mainid ");
        }
        if (id != null && !"".equals(id)) {
            _sql.append("where t.id in (" + id + ")  and formmodeid=" + modeId);
        } else {
            //针对未勾选无权限列表，需要做数据过滤（参照 列表数据查询的 getSplitBase 方法中调用 的 sqlfrom方法，）
            if (viewType != 3 && !noRightList && !isVirtualForm) {//不是监控、无权限列表、不是虚拟表单
                _sql.append(" , ").append(this.getRightSql()).append(" ").append(RIGHT_TABLE_ALIAS);
            }

            _sql.append(" where 1=1 ");
            //针对未勾选无权限列表，需要做数据过滤（参照 列表查询的 getSplitBase 方法中调用 的 sqlwhere方法，）
            if (!isVirtualForm) {
                if (modeId > 0) {
                    _sql.append(" and ").append("t").append(".formmodeid = ").append(modeId);
                }
                if (viewType != 3 && !noRightList && !isVirtualForm) {//不是监控、无权限列表、不是虚拟表单
                    _sql.append(" and ").append("t").append(".").append(primaryKey).append(" = ")
                            .append(RIGHT_TABLE_ALIAS).append(".sourceid");
                }
            }

        }
        List<String> list = new ArrayList<String>();
        rs.executeQuery(_sql.toString());
        int colCounts = rs.getColCounts();
        while (rs.next()) {
            for (int i = 0; i < colCounts - 1; i++) {
                list.add(CubeCipherUitl.decrypt(rs.getString(i + 1)));
            }
        }


//        String[] strArr=new String[list.size()];
        List<String> strArr = new ArrayList<String>();
        for (int i = 0; i < list.size(); i++) {
            //过滤无效的地址名称
            if ("".equals(list.get(i)) || null == list.get(i)) {
                continue;
            }
            strArr.add(list.get(i));
        }

        result.put("titles", strArr);
        return result;
    }

    public Map<String, Object> getTemplate(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        String customId = Util.null2String(request.getParameter("customid"));
        String type = Util.null2String(request.getParameter("type"));
        User user = HrmUserVarify.getUser(request, response);
        RecordSet rs = new RecordSet();
        rs.executeQuery(" select id, templatename,isdefault from mode_templateinfo where customid = " + customId + " and sourcetype in (" + type + ") and createrid =" + user.getUID() + "  ORDER BY displayorder asc,id asc");
        List<HashMap<String, Object>> templates = new ArrayList<HashMap<String, Object>>();
        while (rs.next()) {
            HashMap<String, Object> template = new HashMap<String, Object>();
            template.put("key", rs.getString(1));
            template.put("name", rs.getString(2));
            template.put("selected", "1".equals(rs.getString(3)));
            templates.add(template);
        }
        //rs.executeQuery("")
        result.put("templates", templates);
        return result;
    }


    public Map<String, Object> saveTempalte(HttpServletRequest request, HttpServletResponse response) {
        String action = Util.null2String(request.getParameter("action"));
        String sourcetype = Util.null2String(request.getParameter("sourcetype"));
        String customid = Util.null2String(request.getParameter("customid"));
        String values = Util.null2String(request.getParameter("values"));
        String templatename = Util.null2String(request.getParameter("templatename"));
        String templatetype = Util.null2String(request.getParameter("templatetype"));
        float fieldorder = Util.getFloatValue(request.getParameter("fieldorder"));
        String templateid = Util.null2String(request.getParameter("templateid"));
        int isdefault = Util.getIntValue(request.getParameter("isdefault"), 0);
        RecordSet rs = new RecordSet();
        User user = HrmUserVarify.getUser(request, response);
        if (isdefault == 1) {//将其他的模板设置成非默认
            String sql = "update mode_TemplateInfo set isdefault=0 where customid=? and sourcetype=? and createrid=?";
            rs.executeUpdate(sql, customid, sourcetype, user.getUID());
        }
        JSONObject fields = JSONObject.parseObject(values);
        if ("add".equalsIgnoreCase(action)) {
            String modeuuid = UUID.randomUUID().toString();
            String sql = " insert into mode_templateinfo ( customid, templatename, templatetype,displayorder,isdefault,createrid,createdate,sourcetype,modeuuid ) " +
                    "values (?,?,?,?,?,?,?,?,?)";
            rs.executeUpdate(sql, customid, templatename, templatetype, fieldorder, isdefault, user.getUID(), DateHelper.getCurDateTime(), sourcetype, modeuuid);
            rs.executeQuery("select id from mode_templateinfo where MODEUUID=?", modeuuid);
            templateid = null;
            if (rs.next()) {
                templateid = rs.getString(1);
            }
            this.saveFieldTemplate(fields, templateid, customid);
            return this.getTemplate(request, response);
        } else if ("save".equals(action)) {
            this.saveFieldTemplate(fields, templateid, customid);
        }
        return new HashMap<String, Object>();

    }

    private void saveFieldTemplate(JSONObject fields, String templateid, String customid) {
        RecordSet rs0 = new RecordSet();
        String sql0 = "select id,fieldid from mode_templatedspfield where templateid=?";
        rs0.executeQuery(sql0, templateid);
        Map<String, String> map = new HashMap<String, String>();
        while (rs0.next()) {
            String fieldid = rs0.getString("fieldid");
            String id = rs0.getString("id");
            map.put(fieldid, id);
        }
        RecordSet rs = new RecordSet();
        if (fields != null && templateid != null) {
            Iterator iter = fields.keySet().iterator();
            JSONArray indexs = fields.getJSONArray("rs");
            JSONArray values = fields.getJSONArray("vs");
            for (int i = 0; i < indexs.size(); i++) {
                String key = indexs.getString(i);
                String value = values.getString(i);
                String fieldid = key.replace("con_", "").replace("_", "-");
                String[] tvalues = getFieldTemplate(fieldid, value, customid);
                String tvalues0 = tvalues[0];
                String tvalues1 = tvalues[1];
                String tvalues2 = tvalues[2];
                if (fieldid.equals("thisdate") || fieldid.equals("thisorg")) continue;
                if ("1".equals(fieldid) || "2".equals(fieldid) || "3".equals(fieldid) || "4".equals(fieldid) || "5".equals(fieldid) || "7".equals(fieldid) || "8".equals(fieldid) || "9".equals(fieldid)) {
                    fieldid = "-" + fieldid;
                    if ("-4".equals(fieldid) || "-9".equals(fieldid)) {//标签
                        tvalues0 = value;
                    } else {
                        String[] tvs = value.split(",");
                        if (null != tvs && tvs.length > 0) {
                            tvalues0 = tvs[0];
                            if (tvs.length > 1) {
                                tvalues1 = tvs[1];
                            }
                            if (tvs.length > 2) {
                                tvalues2 = tvs[2];
                            }
                        }
                    }
                }
                if (map.containsKey(fieldid)) {//已经存在则更新
                    String id = map.get(fieldid);
                    String sql = " update mode_templatedspfield set templateid=?, fieldid=?, isshow=?,fieldorder=?,Topt=?, topt1=?, tvalue=?, tvalue1=?, tname=? " +
                            " where id=?";
                    rs.executeUpdate(sql, templateid, fieldid, "1", i, null, null, tvalues0, tvalues1, tvalues2, id);
                } else {//不存在则插入
                    String sql = " insert into mode_templatedspfield (templateid, fieldid, isshow,fieldorder,Topt, topt1, tvalue, tvalue1, tname)" +
                            " values (?,?,?,?,?,?,?,?,?)";
                    rs.executeUpdate(sql, templateid, fieldid, "1", i, null, null, tvalues0, tvalues1, tvalues2);

                }
            }

        }
    }

    private String[] getFieldTemplate(String fieldid, String value, String customid) {
        RecordSet rs = new RecordSet();
        String tvalue = null, tvalue1 = null, tname = null;
        rs.executeQuery("select a.fieldhtmltype, a.fielddbtype, a.type typeTemp,b.conditionTransition from workflow_billfield a left join mode_CustomDspField b on a.id=b.fieldid where a.id = ? and b.customid=? ", fieldid, customid);
        if (rs.next()) {
            String fieldhtmltype = rs.getString("fieldhtmltype");
            String fielddbtype = rs.getString("fielddbtype");
            String type = rs.getString("typeTemp");
            String conditionTransition = Util.null2String(rs.getString("conditionTransition"));
            if ("1".equals(fieldhtmltype)) {
                if ("1".equals(type)) {
                    tvalue = value;
                } else {
                    String[] arr = Util.splitString(value, ",");
                    if (arr.length > 0) {
                        tvalue = arr[0];
                        if (arr.length > 1) {
                            tvalue1 = arr[1];
                        }
                    }

                }
            } else if ("3".equals(fieldhtmltype)) {
                if ("1".equals(type)) {
                    String[] arr = Util.splitString(value, ",");
                    if ("1".equals(conditionTransition) && Util.getIntValue(arr[0]) == -5) {
                        String tv = "";
                        for (int i = 0; i < (arr.length - 1) / 3; i++) {
                            tv += arr[i + 1] + ",";
                        }
                        if (tv.endsWith(",")) {
                            tv = tv.substring(0, tv.length() - 1);
                        }
                        tvalue = arr[0];
                        tvalue1 = tv;
                    } else {
                        if (arr.length > 0) {
                            if (Util.getIntValue(arr[0]) > -5) {
                                tvalue = arr[0];
                            } else {
                                if (arr.length >= 3) {
                                    tvalue = arr[0];
                                    tvalue1 = arr[1];
                                    tname = arr[2];
                                } else {
                                    tvalue = arr[0];
                                    tvalue1 = "";
                                    tname = "";
                                }

                            }
                        }
                    }
                } else if ("2".equals(type)) {
                    String[] arr = Util.splitString(value, ",");
                    if (arr.length > 0) {
                        if (Util.getIntValue(arr[0]) != 6) {
                            tvalue = arr[0];
                            if (tvalue.equals("0")) {
                                tvalue = "";
                                tvalue1 = "-1";//时间日期选项重构,原来的设计 全部和 指定日期范围数据存储数据  tvalue 和 tvalue1 都存的空，查询模板的时候无法区分
                            }
                        } else {
                            if (arr.length > 1) {
                                tvalue = arr[1];
                                if (arr.length > 2) {
                                    tvalue1 = arr[2];
                                }
                            }
                        }
                    }
                } else if ("19".equals(type)) {
                    String[] arr = Util.splitString(value, ",");
                    if (arr.length > 0) {
                        tvalue = arr[0];
                        if (arr.length > 1) {
                            tvalue1 = arr[1];
                        }
                    }
                } else if ("290".equals(type)) {
                    String[] arr = Util.splitString(value, ",");
                    if (arr.length > 0) {
                        tvalue = new DateTransformer().getServerDateTime(arr[0]);
                        if (arr.length > 1) {
                            tvalue1 = new DateTransformer().getServerDateTime(arr[1]);
                        }
                    }
                } else {
//                    String [] arr = Util.splitString(value,",");
//                    if(arr.length>0) {
//                        tvalue = arr[0];
//                        if(arr.length>1) {
//                            tname = arr[1];
//                        }
//                    }
                    tvalue = value;
                }
            } else if ("6".equals(fielddbtype)) {
                String[] arr = Util.splitString(value, ",");
                if (arr.length > 0) {
                    tvalue = arr[0];
                    if (arr.length > 1) {
                        tname = arr[1];
                    }
                }
            } else {
                tvalue = value;
            }
        }
        return new String[]{tvalue, tvalue1, tname};
    }

    //------------------------------------------------------------------------------------------------------看板相关
    public void initProperties(String customid, String formId, int modeId, User user) {
        this.modeId = modeId;
        this.formId = Util.getIntValue(formId);
        this.customId = Util.getIntValue(customid);
        this.user = user;
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        this.noRightList = "1".equals(customSearchComInfo.getNoRightList(customid));
        this.isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        this.tableName = modeFormComInfo.getTableName(formId);
        this.detailTable = customSearchComInfo.getDetailTable(customid);
        this.pageNum = Util.getIntValue(customSearchComInfo.getPageNumber(customid));
        this.isCustom = Util.getIntValue(customSearchComInfo.getisCustom(customid));
        this.openType = customSearchComInfo.getOpenType(customid);
        this.defaulSql = Util.toScreenToEdit(customSearchComInfo.getDefaultSql(customid), user.getLanguage());
        this.searchconditiontype = customSearchComInfo.getSearchConditionType(customid);
        this.javafilename = customSearchComInfo.getJavaFileName(customid);
        this.javafileaddress = customSearchComInfo.getJavaFileAddress(customid);
        if (isVirtualForm) {
            this.dataSource = modeFormComInfo.getVDataSource(formId);
            DataSourceXML dataSourceXML = new DataSourceXML();
            this.dataSourceDBType = dataSourceXML.getDataSourceDBType(dataSource);
            this.primaryKey = modeFormComInfo.getVPrimaryKey(formId);
            if ("2".equals(modeFormComInfo.getVformtype(this.formId))) {
                this.tableName = modeFormComInfo.getVsql(this.formId);
            } else {
                this.tableName = VirtualFormHandler.getRealFromName(tableName);
            }
        } else {
            this.dataSource = null;
            this.dataSourceDBType = new RecordSet().getDBType();
            this.primaryKey = "id";
        }
    }

    public String getBoardSqlFrom(String tablename, String boardid) {
        StringBuffer sqlFrom = new StringBuffer();
        sqlFrom.append(" from ").append(tablename).append(" ").append(MAIN_TABLE_ALIAS);
        if (detailTable != null && !"".equals(detailTable)) {
            sqlFrom.append(" left join ").append(detailTable).append(" ").append(DETAIL_TABLE_ALIAS)
                    .append("  on  ").append(DETAIL_TABLE_ALIAS).append(".").append(DETAIL_MAINID).append(" = ")
                    .append(MAIN_TABLE_ALIAS).append(".").append(primaryKey);
            sqlFrom.append(" left join mode_boardItemOrder_" + this.customId + " t3 on " + CommonConstant.getConcatSql("t3.dataid", "'_'", CommonConstant.toChar("t3.boardid")) + "=" + CommonConstant.getConcatSql(CommonConstant.toChar("t1.id"), "'_'", CommonConstant.DB_ISNULL_FUN + "(" + CommonConstant.toChar("d1.id") + ",'')", "'_'", "'" + boardid + "'") + " ");
        } else {
            sqlFrom.append(" left join mode_boardItemOrder_" + this.customId + " t3 on " + CommonConstant.getConcatSql("t3.dataid", "'_'", CommonConstant.toChar("t3.boardid")) + "=" + CommonConstant.getConcatSql(CommonConstant.toChar("t1.id"), "'_'", "'" + boardid + "'") + " ");
        }
        if (!noRightList && !isVirtualForm) {//不是监控、无权限列表、不是虚拟表单
            sqlFrom.append(" , ").append(this.getRightSql()).append(" ").append(RIGHT_TABLE_ALIAS);
        }
        return sqlFrom.toString();
    }

    /**
     * 初始化查询页面的自定义列宽以及显示列定制
     *
     * @param request
     * @param response
     */
    public void cleanCol(HttpServletRequest request, HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        this.customId = Util.getIntValue(request.getParameter("customid"));
        String customId = "" + this.customId;
        String cleanSql = "delete from cloudstore_defcol where pageuid='mode_customsearch:" + customId + "' and userid=" + user.getUID();
        RecordSet rs = new RecordSet();
        rs.executeSql(cleanSql);
    }

    public String getBoardSqlWhere(HttpServletRequest request) {
        this.noEscape = true;//看板高级搜索不需要拼 查询高级搜索 tablestring 的 xml，故不需要转义
        return getSqlWhere(request, null);
    }

    public Map<String, Object> doExcelExpost(HttpServletRequest request,
                                             HttpServletResponse response) {
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> apidatas = new HashMap<String, Object>();
        String key = weaver.formmode.exttools.impexp.common.StringUtils.getUnquieID();
        String dataKey = Util.null2String(request.getParameter("dataKey"));
        String exptype = Util.null2String(request.getParameter("exptype"));
        Map<String, Object> canExpObj = new HashMap<String, Object>();
        if ("card".equals(exptype)) {//批量导出文件
        }
        if (!"".equals(dataKey)) {
            SplitPageBean bean = null;
            try {
//				if (!"card".equals(exptype)) {//批量导出文件
                bean = new SplitPageBean(request, dataKey, "RootMap", "head", "sql");
//				}
                Map<String, Object> parmMap = ParamUtil.request2Map(request);
                parmMap.put("basePath", basePath);
                BASE64Decoder decoder = new BASE64Decoder();
                String modeCustomid = Util.null2String(parmMap.get("modeCustomid"));
                modeCustomid = new String(decoder.decodeBuffer(modeCustomid), "UTF-8");
                parmMap.put("modeCustomid", modeCustomid);
                //判断是否有导出权限
                String errorMsg = ExpExcelUtil.checkRightExpExcel(modeCustomid, user);
                if ("".equals(errorMsg)) {
                    errorMsg = ExpExcelUtil.checkCanExpExcel(user);
                }
                //添加置顶逻辑 排序字段规则 mode_top_modeCustomid 例:mode_top_369
                Map<String, Object> otherparam = new HashMap<>();
                otherparam.put("user", user);
                getCompleteSql(modeCustomid, bean, otherparam);

                //导出的时候需要校验文件名
                String excelName = Util.null2String(parmMap.get("excelName"));
                boolean regexFlag = excelName.matches(".*(/|\\\\|:|\\*|\\?|\"|<|>|\\||\\[|\\]).*");
                //07excel命名长度可超过31位，先不判断超长问题
                //if (excelName.length()>30) {errorMsg = "请修改excel导出名称不多于31个字符";}
                if (regexFlag) {
                    //系统input框保存会把< > "转全角，提示先不写了
                    errorMsg = SystemEnv.getHtmlLabelName(514845, user.getLanguage());//请修改excel导出名称不包含: \ / : * ? [ ] 或 |
                }
                if ("".equals(errorMsg)) {
                    ExpExcelUtil.refreshExpUserStateNew(user.getUID());
                    canExpObj.put("canExp", true);
                    parmMap.put("key", key);
                    parmMap.put("isReturnDecryptData", true);
                    ExecutorService executorService = ExpExcelUtil.getInstance().getExppool();
                    if ("card".equals(exptype)) {//批量导出文件
                        executorService.submit(new ExpCardTask(parmMap, user, bean));
                    } else {
                        executorService.submit(new ListExceloutTask(parmMap, bean, user));
                    }
                    request.getSession().setAttribute(key, parmMap.get("excelName"));
                    apidatas.put("status", "1");
                    apidatas.put("key", key);
                } else {
                    canExpObj.put("canExp", false);
                    canExpObj.put("canExpMsg", errorMsg);
                    apidatas.put("status", "3");
                    apidatas.put("canExpObj", canExpObj);
                }
            } catch (Exception e) {
                apidatas.put("status", "2");
                //列表未操作时间过久，请刷新页面
                apidatas.put("error", SystemEnv.getHtmlLabelName(508217, user.getLanguage()));
            }
        }
        return apidatas;
    }

    public SplitPageBean getCompleteSql(String modeCustomid, SplitPageBean bean, Map<String, Object> params) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
        JSONObject sqlObj = bean.getSql();
        String defaultsqltop = "";
        CubeSearchService cubeSearchService = new CubeSearchService();
        //获取mode_customsearch id
        int id = Util.getIntValue(modeCustomid, 0);
        int modeid = 0;
        //置顶标识
        int enabledtop = 0;
        //设定排序的表的过滤条件
        String sqlwhere = "";
        String tOrder = Util.null2String(sqlObj.getString("sqlorderby"));//bean 里面的排序语句，如果当前表在mode_customsearch设置了置顶条件，那么需要将置顶条件字段拼接在前面
        String sqlFrom = Util.null2String(sqlObj.getString("sqlform"));
        String beanSqlWhere = Util.null2String(sqlObj.getString("sqlwhere"));
        if (!("".equals(sqlFrom))) {
            //获取表名
            String tableName = sqlFrom.substring(4, sqlFrom.indexOf("t1")).trim();
            if (id != 0) {
                String topColumn = "mode_top_" + id;
                //查询 mode_customsearch 表里面的信息，enabledtop  defaultsqltop
                String sqlForModeCustomsearch = "select enabledtop,defaultsqltop,modeid  from mode_customsearch where id = " + id;

                RecordSet rs = new RecordSet();
                rs.execute(sqlForModeCustomsearch);
                while (rs.next()) {
                    enabledtop = Util.getIntValue(Util.null2String(rs.getString("enabledtop"), "0"));
                    defaultsqltop = Util.null2String(rs.getString("defaultsqltop"), "");
                    modeid = Util.getIntValue(Util.null2String(rs.getString("modeid"), "0"));
                    bean.getSql().put("modeid", modeid);
                    params.put("modeId", modeid);
                }
                //添加密级处理
//                AddSeclevelUtil addSeclevelUtil = new AddSeclevelUtil();
//                String secLevelSqlWhere =  addSeclevelUtil.getBrowserOrSearchSecLevelSqlWhere(params);
//                if(!"".equals(secLevelSqlWhere)){
//                    beanSqlWhere+= secLevelSqlWhere;
//                    sqlObj.put("sqlwhere",beanSqlWhere);
//                }

                if (enabledtop == 1) {
                    //查询设置表单的表 存不存在标记置顶的字段
                    try {
                        String sqlGetTopCol = "select id, " + topColumn + " from " + tableName + " where 1=2 ";
                        rs.execute(sqlGetTopCol);
                        if ("".equals(rs.getExceptionMsg())) {//rs对象内部处理了异常，只能根据msg 来判断是否有异常，先保留try catch
                            String nullFun = CommonConstant.DB_ISNULL_FUN;
                            //orderby 中包含置顶字段由于数据库对于null的排序处理不一致 在处理null转空的时候例子 nvl(mode_top,0) 改造成nvl(mode_top-modetop0)
                            //在excel中需要对排序进行转换，原来的转换方式是根据逗号进行切割 会造成   order by nvl(t1.mode_top_369 desc,0) desc ,t1.id desc 语法错误
                            tOrder = !"".equals(tOrder) ? nullFun + "(t1." + topColumn + "-modetop0) desc," + tOrder : nullFun + "(t1." + topColumn + ",0) desc";
                            //  bean.getSql().put("topColumn", "mode_top_" + id);
                            bean.getSql().put("sqlorderby", tOrder);
                        }
                    } catch (Exception e) {
                        //打印日志，列不存在
                    }
                }
            }
        }

        return bean;

    }

    public Map<String, Object> getExcelExpProgress(HttpServletRequest request,
                                                   HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        String dataKey = Util.null2String(request.getParameter("key"));
        JSONObject progress = new JSONObject();
        if (!"".equals(dataKey)) {
            String progressstr = StringCacheMap.get(dataKey);
            if (progressstr != null && !"".equals(progressstr)) {
                progress = JSONObject.parseObject(progressstr);
                progress.put("filename", "");
                if ("end".equals(progress.get("step"))) {
                    StringCacheMap.remove(dataKey);//导出结束移除进度条对象
                } else if ("expSheet".equals(progress.get("step")) || "expcard".equals(progress.get("step"))) {
                    int current = Util.getIntValue(progress.get("current") + "", 0) * 100;
                    int total = Util.getIntValue(progress.get("total") + "", 0);
                    int percent = 0;
                    if (total == 0) {
                        percent = 100;
                    } else {
                        percent = current / total;
                    }
                    progress.put("percent", percent);
                }
            }
        }
        apidatas.put("progress", progress);
        return apidatas;
    }

    public Map<String, Object> cancelExcelExp(HttpServletRequest request,
                                              HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        String dataKey = Util.null2String(request.getParameter("key"));
        JSONObject progress = new JSONObject();
        if (!"".equals(dataKey)) {
            String progressstr = StringCacheMap.get(dataKey);
            if (progressstr != null && !"".equals(progress)) {
                StringCacheMap.put(dataKey + "_cancel", "true");
            }
        }
        return apidatas;
    }

    // 自定义按钮是否直接显示在列表上
    public int getIsCustomButtonShowList(int customid) {
        int num = 0;
        RecordSet RecordSet = new RecordSet();
        String cbsql = "select  * from mode_customSearchButton  where objid=? and isshow=1 and isshowlist=1";
        RecordSet.executeQuery(cbsql, customid);
        num = RecordSet.getCounts();
        return num;
    }

    public int getIsCustomButtonShowListForGroup(int customid) {
        int num = 0;
        RecordSet RecordSet = new RecordSet();
        String cbsql = "select  * from mode_customSearchButton  where objid=? and isshow=1 ";
        RecordSet.executeQuery(cbsql, customid);
        num = RecordSet.getCounts();
        return num;
    }

    public Map<String, Object> getOriginalTableWidth(HttpServletRequest request,
                                                     HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        User user = HrmUserVarify.getUser(request, response);
        String customid = Util.null2String(request.getParameter("customid"));
        double originalTableWidth = getOriginalTableWidth(customid, user.getUID());
        apidatas.put("originalTableWidth", originalTableWidth + "%");
        return apidatas;
    }


    public String getDataSource() {
        return this.dataSource;
    }

    public String getPrimaryKey() {
        return this.primaryKey;
    }

    public String getDetailTable() {
        return this.detailTable;
    }

    public boolean getIsVirtualForm() {
        return this.isVirtualForm;
    }
    //------------------------------------------------------------------------------------------------------看板相关

    /**
     * @param columns    :
     * @param backfields :
     * @return : java.lang.String
     * @Method : setFilterColumn
     * @Description : 设置数据筛选隐藏列用于前端判断该行是否禁用check框
     * <AUTHOR> xk
     * @CreateDate : 2019-10-21 10:48:07
     */
    public String setFilterColumn(List<SplitTableColBean> columns, String backfields, String sqlfrom, String sqlWh, String formID) {
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        String sql = "";
        String sqlwhere = "";

        //1.找出要数据筛选的按钮
        if (modeId == 0) {
            sqlwhere = " and a.modeid  is null ";
        } else {
            sqlwhere = " and a.modeid = ? ";
        }
        sql = "select t1.customervalue,t1.fieldid,t1.expandid,t2.fieldname from mode_checkcondition t1,workflow_billfield t2 " +
                " where t1.customid= ? and t1.expandid in ( " +
                " select a.id from mode_pageexpand a left join mode_batchset b on a.id = b.expandid and b.customsearchid =  ? " +
                " where a.isbatch in(1,2) and b.isuse = 1 and b.isfilter = 1 " + sqlwhere + "  and a.isshow=1 ) " +
                " and t2.id = t1.fieldid order by t1.expandid "
        ;

        if (modeId == 0) {
            rs.executeQuery(sql, customId, customId);
        } else {
            rs.executeQuery(sql, customId, customId, modeId);
        }

        //2.给表格增加多个隐藏列 用于判断本行check框是否禁用
        StringBuffer names = new StringBuffer();
        StringBuffer values = new StringBuffer();
        StringBuffer column = new StringBuffer();
        Map<String, Map<String, String>> conditions = new HashMap<>();
        while (rs.next()) {
            Map<String, String> entity = new HashMap<>();
            String expandid = rs.getString("expandid");
            String fieldname = rs.getString("fieldname");
            String customervalue = rs.getString("customervalue");
            if (!conditions.containsKey(expandid)) {
                names = new StringBuffer();
                values = new StringBuffer();
            }
            names.append("column:" + fieldname + "+");
            values.append(customervalue + "+");
            entity.put(names.toString(), values.toString());
            conditions.put("0," + expandid, entity);
        }

        //采用新的数据筛选逻辑 qc 	770532
        sql = " select a.id,b.conditionsql from mode_pageexpand a left join mode_batchset b on a.id = b.expandid and b.customsearchid =  ? " +
                " where a.isbatch in(1,2) and b.isuse = 1 and b.isfilter = 1 and b.conditionsql is not null " + sqlwhere + "  and a.isshow=1 ";
        if (modeId == 0) {
            rs.executeQuery(sql, customId);
        } else {
            rs.executeQuery(sql, customId, modeId);
        }
        String backfieldsTmep = "t1.id ";
        boolean isVirtualForm = VirtualFormHandler.isVirtualForm(formId);
        String pkfieldname = "id";
        String vdatasource = "";
        if (isVirtualForm) {
            FormInfoDao formInfoDao = new FormInfoDao();
            Map<String, Object> formInfo = formInfoDao.getFormInfoById(formId);
            pkfieldname = Util.null2String(formInfo.get("vprimarykey"));
            vdatasource = Util.null2String(formInfo.get("vdatasource"));
            backfieldsTmep = "t1." + pkfieldname;
        }
        CustomSearchComInfo CustomSearchComInfo = new CustomSearchComInfo();
        String detailtable = CustomSearchComInfo.getDetailTable(customId + "");
        if (!StringHelper.isEmpty(detailtable) && sqlfrom.contains(" d1")) {
            backfieldsTmep = "d1.id ";
        }
        while (rs.next()) {
            Map<String, String> entity = new HashMap<>();
            String expandid = rs.getString("id");
            String conditionsql = rs.getString("conditionsql");
            CustomTreeData customTreeData = new CustomTreeData();
            customTreeData.setUser(user);
            conditionsql = customTreeData.replaceParam(conditionsql);//替换变量
            String checksqlwhere = sqlWh + " and ( " + conditionsql + " )";
            if (!checksqlwhere.contains("d1")) {
                if (isVirtualForm) {
                    backfieldsTmep = "t1." + pkfieldname + " ";
                } else {
                    backfieldsTmep = "t1.id ";
                }
            }
            if (isVirtualForm) {//vdatasource
                rs2.executeQueryWithDatasource("select " + backfieldsTmep + sqlfrom + checksqlwhere, vdatasource);
            } else {
                rs2.executeQuery("select " + backfieldsTmep + sqlfrom + checksqlwhere);//不要写在转换方法里
            }
            names = new StringBuffer();
            while (rs2.next()) {
                if (isVirtualForm) {
                    names.append(rs2.getString(pkfieldname));
                } else {
                    names.append(rs2.getString("id"));
                }
                names.append(",");
            }
            if (names.length() > 0) names.deleteCharAt(names.length() - 1);
            entity.put("new", names.toString());
            conditions.put("1," + expandid + "," + backfieldsTmep, entity);
        }

        conditions.forEach((String k, Map<String, String> v) -> {
            SplitTableColBean splitTableColBean = new SplitTableColBean();
            String expandid = k.split(",")[1];
            boolean isnew = "1".equals(k.split(",")[0]);
            String backfieldstmep = "t1.id";
            String pkfieldname2 = "id";
            if (isVirtualForm) {
                FormInfoDao formInfoDao = new FormInfoDao();
                Map<String, Object> formInfo = formInfoDao.getFormInfoById(formId);
                pkfieldname2 = Util.null2String(formInfo.get("vprimarykey"));
                backfieldstmep = "t1." + pkfieldname2;
            }
            if (isnew) {
                backfieldstmep = k.split(",")[2];
            }
            String transmethod = "com.api.cube.util.FieldUtil.isFilter";
            column.append(" ,");
            column.append(backfieldstmep);
            column.append(" expandid_");
            column.append(expandid);

            v.forEach((para1, para2) -> {
                if (para1.length() > 0 && !isnew) {
                    para1 = para1.substring(0, para1.length() - 1);
                    para2 = para2.substring(0, para2.length() - 1);
                }
                splitTableColBean.setOtherpara(para1);
                splitTableColBean.setOtherpara2(para2);
            });

            splitTableColBean.setColumn("expandid_" + expandid);
            splitTableColBean.setHide("true");
            splitTableColBean.setTransmethod(transmethod);
            splitTableColBean.setTransMethodForce("true");
            columns.add(splitTableColBean);
        });

        if (column.length() > 0) {
            this.isFilter = true;
            backfields = backfields + column.toString();
        }
        return backfields;
    }


    public double getOriginalTableWidth(String customid, int userid) {
        RecordSet rs = new RecordSet();
        String pageuid = "mode_customsearch:" + customid;
        String sql = "select dataindex,display from cloudstore_defcol where pageUid = ? and userid = ? ";//and display=0
        rs.executeQuery(sql, pageuid, userid);
        Map<String, String> defcols = new HashMap<>();
        while (rs.next()) {
            String filename = Util.null2String(rs.getString("dataindex"));
            String display = Util.null2String(rs.getString("display"));
            defcols.put(filename, display);
        }
        double originalTableWidth = 0;
        rs.executeQuery("select colwidth,w.fieldname,m.fieldid,w.viewtype from mode_CustomDspField m left join workflow_billfield w on w.id=m.fieldid  where customid=? and isshow=1", customid);
        while (rs.next()) {
            if (defcols.size() > 0) {
                String fieldid = Util.null2String(rs.getString("fieldid"));
                String fieldname = "";
                if ("-1".equals(fieldid)) {//创建日期
                    fieldname = "modedatacreatedate";
                } else if ("-2".equals(fieldid)) {//创建人
                    fieldname = "modedatacreater";
                } else if ("-3".equals(fieldid)) {//id
                    fieldname = "id";
                } else if ("-4".equals(fieldid)) {
                    fieldname = "modelableid";
                } else if ("-5".equals(fieldid)) {
                    fieldname = "modedatastatus";
                } else if ("-6".equals(fieldid)) {//操作列
                    fieldname = "isshowlist";
                } else if ("-7".equals(fieldid)) {
                    fieldname = "modedatamodifier";
                } else if ("-8".equals(fieldid)) {
                    fieldname = "modedatamodifydatetime";
                } else if ("-9".equals(fieldid)) {
                    fieldname = "seclevel";
                } else {
                    fieldname = Util.null2String(rs.getString("fieldname"));
                    String viewtype = Util.null2String(rs.getString("viewtype"));
                    fieldname = "1".equals(viewtype) ? "d_" + fieldname : fieldname;
                }
                if (defcols.containsKey(fieldname) && "1".equals(defcols.get(fieldname))) {
                    continue;
                }
            }
            double colwidth = rs.getDouble("colwidth");
            if (colwidth <= 0) {
                colwidth = isBackUpData ? 7 : 5;
            }
            originalTableWidth += colwidth;
        }
        return originalTableWidth;
    }

    /**
     * 是否有批量导入数据还原权限
     *
     * @param customid
     * @return
     */
    public void setBackUpRight(User user, String customid, String backuplogid) {
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        String modeid = customSearchComInfo.getModeId(customid);
        String userRightStr = "ModeSetting:All";
        if (modeid != null && !"".equals(modeid)) {
            int subCompanyId = -1;
            RecordSet rs = new RecordSet();
            String sql = "select subcompanyid from modeinfo where id=?";
            rs.executeQuery(sql, modeid);
            if (rs.next()) {
                subCompanyId = Util.getIntValue(Util.null2String(rs.getString("subcompanyid")), -1);
            }
            int operatelevel = DetachHelper.getUserDeatchOperateLevel(user, subCompanyId, userRightStr);
            this.isBackUpData = operatelevel == 2;
            sql = "select operatetype,backindex from mode_batchimp_log where id=?";
            rs.executeQuery(sql, backuplogid);
            if (rs.next()) {
                this.backuplogid = backuplogid;
                int backindex = Util.getIntValue(rs.getString("backindex"));
                int operatetype = Util.getIntValue(rs.getString("operatetype"));
                this.isCoverType = operatetype == 2;
                this.suffix += backindex;
            }
            this.detailTable = customSearchComInfo.getDetailTable(customid);
            ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
            this.tableName = modeFormComInfo.getTableName(customSearchComInfo.getFormId(customid));
            sql = "select * from " + tableName + suffix + " where 1=2";
            rs.executeQuery(sql);
            String[] columnName = rs.getColumnName();
            backupFields = Arrays.stream(columnName).map(String::toLowerCase).collect(Collectors.toList());
            if (!StringHelper.isEmpty(detailTable)) {
                sql = "select * from " + detailTable + suffix + " where 1=2";
                rs.executeQuery(sql);
                columnName = rs.getColumnName();
                backupFields.addAll(Arrays.stream(columnName).map(col -> "d_" + col.toLowerCase()).collect(Collectors.toList()));
            }
        }
    }

    public Map<String, Object> doDelLoadingpost(HttpServletRequest request,
                                                HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        String key = weaver.formmode.exttools.impexp.common.StringUtils.getUnquieID();
        apidatas.put("status", "1");
        apidatas.put("key", key);
        return apidatas;
    }

    public Map<String, Object> getDelLoadingProgress(HttpServletRequest request,
                                                     HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        String dataKey = Util.null2String(request.getParameter("key"));
        JSONObject progress = new JSONObject();
        if (!"".equals(dataKey)) {
            String progressstr = StringCacheMap.get(dataKey);
            if (progressstr != null && !"".equals(progressstr)) {
                progress = JSONObject.parseObject(progressstr);
                if ("end".equals(progress.get("step"))) {
                    StringCacheMap.remove(dataKey);//导出结束移除进度条对象
                } else if ("processing".equals(progress.get("step"))) {
                    int current = Util.getIntValue(progress.get("current") + "", 0) * 100;
                    int total = Util.getIntValue(progress.get("total") + "", 0);
                    int percent = current / total;
                    progress.put("percent", percent);
                }
            }
        }
        apidatas.put("progress", progress);
        return apidatas;
    }


    //检测是否需要二次密码验证。
    public Map<String, Object> modeSecondaryVerify(HttpServletRequest request,
                                                   HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        boolean isNeedSecondPwdVerify = true;
        try {
            User user = HrmUserVarify.getUser(request, response);
            String customid = Util.null2String(request.getParameter("customid"));//查询列表id
            String layoutid = Util.null2String(request.getParameter("layoutid"));//布局id
            String viewtype = Util.null2String(request.getParameter("viewtype"));// 0查询列表 1 布局 2 移动建模 3 监控布局。
            RecordSet rs = new RecordSet();
            viewtype = "".equals(viewtype) ? "0" : viewtype;
//            int freeMins=15;
//            //判断若免密时间为0 则每次都进行二次密码验证。
//            String id=customid;
//            String sctype="".equals(viewtype)||"3".equals(viewtype)?"0":viewtype;//监控布局于查询列表用同一个免密时间
//            if("".equals(customid)) {//布局
//                sctype="1";
//                id=layoutid;
//            }
//            rs.executeQuery("select freeMins from mode_secondPasswordMin where type=? and customid=?",sctype,id);
//            if(rs.next()){
//                freeMins=Util.getIntValue(rs.getString("freeMins"),15);
//            }
//
            String currentTime = TimeUtil.getCurrentTimeString();
            int userid = user.getUID();
            String sql = "";
            if (!"".equals(customid)) {
                sql = "select freesecrettime from mode_secondpassword where userid=? and customid=? and  viewtype=?";
                rs.executeQuery(sql, userid, customid, viewtype);
            } else if (!"".equals(layoutid)) {
                sql = "select freesecrettime from mode_secondpassword where userid=? and layoutid=?";
                rs.executeQuery(sql, userid, layoutid);
            }
            while (rs.next()) {
                String freesecrettime = Util.null2String(rs.getString("freesecrettime"));
                if (freesecrettime.compareTo(currentTime) > 0) {//说明仍然在免密时间内,免密时间内，不需要二次验证
                    isNeedSecondPwdVerify = false;
                }
            }
            apidatas.put("isNeedSecondPwdVerify", isNeedSecondPwdVerify);
            apidatas.put("status", "1");
        } catch (Exception e) {
            apidatas.put("status", "-1");
            apidatas.put("message", SystemEnv.getHtmlLabelName(382661, user.getLanguage()));
            writeLog(e);
        }
        return apidatas;
    }

    //更新二次密码验证 验证时间
    public Map<String, Object> updateFreeSecretTime(HttpServletRequest request,
                                                    HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        int customid = Util.getIntValue(Util.null2String(request.getParameter("customid")), 0);//查询列表id
        int layoutid = Util.getIntValue(Util.null2String(request.getParameter("layoutid")), 0);//布局id
        int viewtype = Util.getIntValue(Util.null2String(request.getParameter("viewtype")), 0);//查询列表类型
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            int userId = user.getUID();
            String deletesql = "";
            int freeMins = 15;//默认十五分钟免密时间
            if (customid != 0) {
                deletesql = "delete  from mode_secondpassword where userid=? and customid=? and  viewtype=?";
                rs.executeUpdate(deletesql, userId, customid, viewtype);
                rs.executeQuery("select freeMins from mode_secondPasswordMin where type=? and customid=?", viewtype, customid);
                if (rs.next()) {
                    freeMins = Util.getIntValue(rs.getString("freeMins"), 15);
                }
            } else if (layoutid != 0) {
                deletesql = "delete  from mode_secondpassword where userid=? and layoutid=?";
                rs.executeUpdate(deletesql, userId, layoutid);
                rs.executeQuery("select freeMins from mode_secondPasswordMin where type=1 and customid=?", layoutid);
                if (rs.next()) {
                    freeMins = Util.getIntValue(rs.getString("freeMins"), 15);
                }
            }
//            ChgPasswdReminder reminder = new ChgPasswdReminder();
//            RemindSettings settings = reminder.getRemindSettings();
            String currentTime = TimeUtil.getCurrentTimeString();
//            int freeMins = Util.getIntValue(Util.null2String(settings.getSecondPasswordMin()));
            //if (freeMins > 0) {
            //保存免密时间
            String freeSecretTime = TimeUtil.timeAdd(currentTime, freeMins * 60);
            String sql = "insert into mode_secondpassword (userId, customid, viewtype, layoutid,freeSecretTime) values (?, ?, ?, ?, ?)";
            boolean result = rs.executeUpdate(sql, userId, customid, viewtype, layoutid, freeSecretTime);
            apidatas.put("result", result);
            //}
//            else{
//                apidatas.put("message",SystemEnv.getHtmlLabelName(517243, user.getLanguage())) ;
//            }
        }
        return apidatas;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    /**
     * 针对人力字段多选传入的值进行修复 返回用于 in 查询的字符串
     *
     * @param strArr
     * @return
     */
    public static String getParam(String[] strArr) {
        String res = "";
        int tempCount = 0;
        if (strArr.length > 1) {
            for (int i = 0; i < strArr.length; i++) {
                if ("[object Object]".equals(strArr[i])) {
                    tempCount += 1;
                }
            }
            if (tempCount > 0) {
                for (int i = 1; i <= tempCount; i++) {
                    res += "'" + strArr[i] + "'" + ",";

                }
            }
            if (res.length() > 1) {
                res = res.substring(0, res.length() - 1);
            }

        }
        return res;
    }

    private List<SearchConditionOption> getCheckOptions(int language) {
        List<SearchConditionOption> options = new ArrayList<SearchConditionOption>();
        options.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(332, language)));   //全部
        options.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(30631, language)));   //勾选
        options.add(new SearchConditionOption("2", SystemEnv.getHtmlLabelName(385734, language)));  //未勾选
        return options;
    }

    //单独分离出 统计数据接口。
    public Map<String, Object> getCountData(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        RecordSet rs3 = new RecordSet();
        RecordSet rs4 = new RecordSet();
        this.isTreeType = Util.null2String(request.getParameter("isTreeType"));
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        //int pageIndex =  Util.getIntValue(request.getParameter("current"),1);
        //int treePageSize = Util.getIntValue(request.getParameter("pagesize"),10);
        this.viewType = Util.getIntValue(request.getParameter("viewtype"), 0);
        this.customId = Util.getIntValue(request.getParameter("customid"));
        this.isEdit = Util.null2String(request.getParameter("isEdit")).equals("true") ? true : false;
        this.user = HrmUserVarify.getUser(request, response);
        String customId = "" + this.customId;
        this.formId = Util.getIntValue(customSearchComInfo.getFormId(customId), 0);
        String formId = "" + this.formId;
        this.tableName = modeFormComInfo.getTableName(formId);
        this.modeId = Util.getIntValue(customSearchComInfo.getModeId(customId), 0);
        this.detailTable = getRealDetailTable(customSearchComInfo.getDetailTable(customId));
        this.isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        this.noRightList = isBackUpData || "1".equals(customSearchComInfo.getNoRightList(customId));
        this.grouptype = Util.null2String(request.getParameter("grouptype"));
        this.defaulSql = Util.toScreenToEdit(customSearchComInfo.getDefaultSql(customId), user.getLanguage());
        this.searchconditiontype = customSearchComInfo.getSearchConditionType(customId);
        this.javafilename = customSearchComInfo.getJavaFileName(customId);
        this.javafileaddress = customSearchComInfo.getJavaFileAddress(customId);


        if (isVirtualForm) {
            this.dataSource = modeFormComInfo.getVDataSource(formId);
            String confirmSql = "select id  from datasourcesetting where pointid=\'" + dataSource + "\'";
            rs.execute(confirmSql);
            if (rs.getCounts() > 0 || "$ECOLOGY_SYS_LOCAL_POOLNAME".equals(dataSource)) {
                DataSourceXML dataSourceXML = new DataSourceXML();
                this.dataSourceDBType = dataSourceXML.getDataSourceDBType(dataSource);
                this.primaryKey = modeFormComInfo.getVPrimaryKey(formId);
                if ("2".equals(modeFormComInfo.getVformtype(this.formId))) {
                    String tempRealName = modeFormComInfo.getVsql(this.formId);
                    this.tableName = replaceParamBefUse(tempRealName);
                } else {
                    this.tableName = VirtualFormHandler.getRealFromName(tableName);
                }
            } else {
                result.put("error", SystemEnv.getHtmlLabelName(389199, user.getLanguage()));
                return result;
            }
        } else {
            this.dataSource = null;
            this.dataSourceDBType = rs.getDBType();
            this.primaryKey = "id";
        }


        ModeRightInfo modeRightInfo = new ModeRightInfo();
        Map<String, Object> preCheck = modeRightInfo.preCheckUserRight(user, modeId);
        this.hasUserRight = isBackUpData || (boolean) preCheck.get("isview");
        if (isEdit) {//批量修改判断修改权限
            this.hasUserRight = (boolean) preCheck.get("isedit");
        }
        String sqlWhere = this.getSqlWhere(request, response);
        if (hasUserRight && viewType != 3 && !noRightList && !isVirtualForm) {
            sqlWhere += ModeRightInfo.getStatusSqlWhere(SearchConstant.MAIN_TABLE_ALIAS, tableName, user.getUID());
        }
        String orderby = this.getOrderBy();
        String countColumnsDbType = "";
        BoolAttr counttransmethod = BoolAttr.TRUE;
        String groupSqlWhere = this.getGroupSqlWhere(request, response);
        String backfields = "";
        backfields = this.getBackFields(rs);
        String sqlFrom = this.getSqlFrom();
        Map<String, Object> groupCount = null;

        String datasqlwhere = Util.null2String(request.getParameter("datasqlwhere"));
        if (!datasqlwhere.equals("")) {
            try {
                BASE64Decoder decoder = new BASE64Decoder();
                datasqlwhere = new String(decoder.decodeBuffer(datasqlwhere), "UTF-8");
                datasqlwhere = URLDecoder.decode(datasqlwhere, "utf-8");
            } catch (Exception e) {
                // TODO: handle exception
            }
            sqlWhere += datasqlwhere.trim().startsWith("and") ? datasqlwhere : " and " + datasqlwhere;
        }
        if (!StringHelper.isEmpty(this.groupName) && !isBackUpData) {
            groupCount = this.getGroupCount(sqlWhere, sqlFrom, dataSource, this.user);
        }
        if (!StringHelper.isEmpty(groupSqlWhere)) {
            sqlWhere += " and " + groupSqlWhere;
        }
        //如果是批量修改只查询出正式数据
        if (this.isEdit && backfields.indexOf(SearchConstant.MAIN_TABLE_ALIAS + ".modedatastatus") > -1) {
            sqlWhere += " and (" + SearchConstant.MAIN_TABLE_ALIAS + ".modedatastatus is null or " + SearchConstant.MAIN_TABLE_ALIAS + " .modedatastatus<>1) ";
        }


        //统计数据查询
        ArrayList<Map<String, String>> countData = new ArrayList<Map<String, String>>();
        if (!isBackUpData) {
            String numberstr = "decimal";
            String split = "";
            String DB_ISNULL_FUN = "isnull";
            if (null != this.dataSourceDBType) {
                if (this.dataSourceDBType.contains("oracle")) {
                    DB_ISNULL_FUN = "nvl";
                } else if (this.dataSourceDBType.contains("mysql")) {
                    DB_ISNULL_FUN = "ifnull";
                } else {
                    DB_ISNULL_FUN = "isnull";
                }

            } else {
                DB_ISNULL_FUN = CommonConstant.DB_ISNULL_FUN;

            }
            //数据库加密的字段不允许合计
            String sql2 = "SELECT distinct a.*,c.fieldhtmltype,c.type typeTemp,c.qfws,c.detailtable,c.fielddbtype FROM mode_customcountset a LEFT JOIN mode_customsearch b ON a.customid=b.id " +
                    " LEFT JOIN workflow_billfield c ON a.countfield=c.fieldname AND c.billid=b.formid  where customid=? and a.isenable=1 " +
                    "  and  not exists (select  1  from  enc_field_config_info  m where  upper( m.tablename )= ? and  upper(m.fieldname) =upper(a.countfield)  and m.isencrypt =1) " +
                    "  order by a.orderid ,a.id asc";
            rs2.executeQuery(sql2, customId, this.tableName.toUpperCase());
            Map<String, String> temMap = new HashMap<String, String>();
            // #783597 原先sql 查询存在关联问题 方法根据关联其他表单的必填字段判断是否是其他表单类型，然后根据 formid 和   countfield 去查询千分位等信息
            String vdatasource = "";//虚拟表单数据源
            while (rs2.next()) {
                String id = Util.null2String(rs2.getString("id"));
                String formtype = Util.null2String(rs2.getString("formtype"));
                String temformid = Util.null2String(rs2.getString("formid"));
                String defaultsql = Util.null2String(rs2.getString("defaultsql"));
                String formname = Util.null2String(rs2.getString("formname"));
                String countfield = Util.null2String(rs2.getString("countfield"));
                String detailtable = Util.null2String(rs2.getString("detailtable"));
                String fieldhtmltype = "";
                String fieldtype = "";
                String temValue = Util.null2String(temMap.get(id));
                String fielddbtype = Util.null2String(rs2.getString("fielddbtype"));
                if (temValue.equals(id)) {
                    continue;
                } else {
                    temMap.put(id, id);
                }
                int qfws = 0;
                String formid = Util.null2String(rs2.getString("formid"));
                if ("".equals(defaultsql) || "".equals(formid)) {
                    qfws = Util.getIntValue(Util.null2String(rs2.getString("qfws")), 0);
                    fieldhtmltype = Util.null2String(rs2.getString("fieldhtmltype"));
                    fieldtype = Util.null2String(rs2.getString("typeTemp"));
                    if ("1".equals(fieldhtmltype) && "4".equals(fieldtype)) {
                        qfws = 2;
                    }
                } else {
                    //单独查询  formid -52_1
                    if (formid.contains("_")) {
                        rs4.execute("SELECT c.fieldhtmltype,c.type typeTemp,c.qfws FROM workflow_billfield c " +
                                " where c.fieldname ='" + countfield + "' and  c.billid=(select billid from Workflow_billdetailtable where tablename='" + formname + "')  and c.detailtable='" + formname + "'");
                    } else {
                        String sqlParam = "SELECT c.fieldhtmltype,c.type typeTemp,c.qfws FROM workflow_billfield c where c.fieldname ='" + countfield + "' and  c.billid=" + formid;
                        rs4.execute(sqlParam);
                    }
                    while (rs4.next()) {
                        qfws = Util.getIntValue(Util.null2String(rs4.getString("qfws")), 0);
                        fieldhtmltype = Util.null2String(rs4.getString("fieldhtmltype"));
                        fieldtype = Util.null2String(rs4.getString("typeTemp"));
                        if ("1".equals(fieldhtmltype) && "4".equals(fieldtype)) {
                            qfws = 2;
                        }
                    }

                }
                //String formid=Util.null2String(rs2.getString("formid"));
                String statisticaltype = Util.null2String(rs2.getString("statisticaltype"));//统计类型：0：求和统计，1：总数统计，2：平均数统计 3：最大值统计 4：最小值统计
                String conditionsql = Util.null2String(rs2.getString("conditionsql"));
                if (!"".equals(conditionsql)) {
                    conditionsql = " and " + conditionsql;
                }
                String tempSqlwhere = sqlWhere + conditionsql;//新定义tempSqlwhere 用于统计计算
                String countnum = "";
                int fielddbtypes = 0;
                try {
                    if (fielddbtype.indexOf("decimal") > -1) {
                        fielddbtype = fielddbtype.toLowerCase().replace("decimal", "").replace(")", "");
                        fielddbtype = Util.null2String(fielddbtype.split(",")[1]);
                        fielddbtypes = Util.getIntValue(fielddbtype);
                        qfws = fielddbtypes;
                    }
                } catch (Exception e) {
                    qfws = qfws;

                }
                Map<String, String> newList = new HashMap<String, String>();
                newList.put("id", Util.null2String(rs2.getString("id")));
                /*newList.put("name",TextUtil.toBase64ForMultilang(rs2.getString("name")));
                newList.put("icon",Util.null2String(rs2.getString("icon")));
                newList.put("setdesc",TextUtil.toBase64ForMultilang(rs2.getString("setdesc")));*/
                newList.put("name", Util.null2String(rs2.getString("name")));
                newList.put("icon", Util.null2String(rs2.getString("icon")));
                newList.put("setdesc", Util.null2String(rs2.getString("setdesc")));
                String sqlcount = "";
                if ("0".equals(formtype)) {
                    if ("1".equals(fieldhtmltype) && "5".equals(fieldtype)) {
                        //	fieldtype 没找到相关说明
                        // fieldhtmltype  1：单行文本框<br> 2：多行文本框<br> 3：浏览按钮<br> 4：check框<br> 5：选择框
                        if (null != this.dataSourceDBType && this.dataSourceDBType.contains("sqlserver")) {
                            if ("0".equals(statisticaltype)) {
                                //sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT sum(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0) ) as  " + countfield + sqlFrom + tempSqlwhere;

                            } else if ("1".equals(statisticaltype)) {
                                countfield = "id";
                                // sqlcount="SELECT count(1) as "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT count(1) as " + countfield + sqlFrom + tempSqlwhere;

                            } else if ("2".equals(statisticaltype)) {
                                //  sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT avg(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            } else if ("3".equals(statisticaltype)) {
                                //   sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT max(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            } else {
                                // sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT min(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            }
                        } else {
                            if ("0".equals(statisticaltype)) {
                                //sqlcount="SELECT sum("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT sum(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            } else if ("1".equals(statisticaltype)) {
                                countfield = "id";
                                // sqlcount="SELECT count(1) as "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT count(1) as " + countfield + sqlFrom + tempSqlwhere;

                            } else if ("2".equals(statisticaltype)) {
                                //  sqlcount="SELECT avg("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT avg(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            } else if ("3".equals(statisticaltype)) {
                                //   sqlcount="SELECT max("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT max(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            } else {
                                // sqlcount="SELECT min("+DB_ISNULL_FUN+"(cast( REPLACE(t1."+countfield+",',','') as "+numberstr+"(38,"+qfws+")),0)) as  "+countfield+sqlFrom+tempSqlwhere+" and "+DB_ISNULL_FUN+"(t1."+countfield+",'"+split+"')<>'"+split+"'";
                                sqlcount = "SELECT min(" + DB_ISNULL_FUN + "(cast( REPLACE(t1." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + sqlFrom + tempSqlwhere;

                            }
                        }


                    } else {
                        if (null != this.dataSourceDBType && this.dataSourceDBType.contains("sqlserver")) {
                            if ("0".equals(statisticaltype)) {
                                sqlcount = "select sum(cast(" + DB_ISNULL_FUN + "(t1." + countfield + ",0) as " + numberstr + "(38," + qfws + "))) " + sqlFrom + tempSqlwhere;
                            } else if ("1".equals(statisticaltype)) {
                                sqlcount = "select count(1) " + sqlFrom + tempSqlwhere;
                            } else if ("2".equals(statisticaltype)) {
                                sqlcount = "select avg(cast(" + DB_ISNULL_FUN + "(t1." + countfield + ",0) as " + numberstr + "(38," + qfws + "))) " + sqlFrom + tempSqlwhere;
                            } else if ("3".equals(statisticaltype)) {
                                sqlcount = "select max(" + DB_ISNULL_FUN + "(t1." + countfield + ",0)) " + sqlFrom + tempSqlwhere;
                            } else {
                                sqlcount = "select min(" + DB_ISNULL_FUN + "(t1." + countfield + ",0)) " + sqlFrom + tempSqlwhere;
                            }
                        } else {
                            if ("0".equals(statisticaltype)) {
                                sqlcount = "select sum(" + DB_ISNULL_FUN + "(t1." + countfield + ",0) ) " + sqlFrom + tempSqlwhere;
                            } else if ("1".equals(statisticaltype)) {
                                sqlcount = "select count(1) " + sqlFrom + tempSqlwhere;
                            } else if ("2".equals(statisticaltype)) {
                                sqlcount = "select avg(" + DB_ISNULL_FUN + "(t1." + countfield + ",0) ) " + sqlFrom + tempSqlwhere;
                            } else if ("3".equals(statisticaltype)) {
                                sqlcount = "select max(" + DB_ISNULL_FUN + "(t1." + countfield + ",0)) " + sqlFrom + tempSqlwhere;
                            } else {
                                sqlcount = "select min(" + DB_ISNULL_FUN + "(t1." + countfield + ",0)) " + sqlFrom + tempSqlwhere;
                            }
                        }

                    }
                } else {


                    String otherFormKey = ",b1.id as bid ";
                    String distinctfield = countfield;
                    boolean hasVprimarykey = true;
                    //虚拟表主键处理
                    String otherFormKeysql = "select isvirtualform,vprimarykey from Modeformextend a , workflow_bill b where a.formid = b.id and a.formid =" + temformid;
                    String vsqlFormSql = " select vsql , vdatasource from ModeFormExtend where formid =  " + temformid;
                    boolean isvirtualform = false;
                    rs3.executeSql(otherFormKeysql);
                    while (rs3.next()) {
                        isvirtualform = "1".equals(Util.null2String(rs3.getString("isvirtualform")));
                        if (!StringHelper.isEmpty(Util.null2String(rs3.getString("vprimarykey")))) {
                            otherFormKey = ",b1." + Util.null2String(rs3.getString("vprimarykey")).toLowerCase() + " as bid ";
                            hasVprimarykey = false;
                        }
                    }
                    //视图且Modeformextend查不到主键的情况，置为空
                    if ("1".equals(formtype) && StringHelper.isEmpty(formid) && hasVprimarykey && isvirtualform) {
                        otherFormKey = " ";
                        distinctfield = "*";
                    }
                    String sqlWhere1 = sqlWhere;
                    //String sqlWhere1= tempSqlwhere;
                    if (!StringHelper.isEmpty(detailTable) && formname.equalsIgnoreCase(detailTable)) {
                        sqlWhere1 = sqlWhere1.replace("d1.", "b1.");
                    }
                    rs3.execute(vsqlFormSql);

                    while (rs3.next()) {
                        if (!"".equals(Util.null2String(rs3.getString("vsql")))) {
                            formname = "(" + Util.null2String(rs3.getString("vsql")) + ")";
                        }
                        vdatasource = Util.null2String(rs3.getString("vdatasource"));
                    }
                    if ("1".equals(fieldhtmltype) && "5".equals(fieldtype)) {
                        //sqlcount="SELECT sum(cast( REPLACE(m."+countfield+",',','') as "+numberstr+"(38,"+qfws+"))) as  "+countfield+" from ( SELECT DISTINCT t1.id,b1."+countfield+","+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m "; //改造前的sql
                        if (null != this.dataSourceDBType && this.dataSourceDBType.contains("sqlserver")) {
                            if ("0".equals(statisticaltype)) {
                                sqlcount = "SELECT sum(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0) ) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("1".equals(statisticaltype)) {
                                sqlcount = "SELECT c  from ( SELECT DISTINCT count(1) c " + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("2".equals(statisticaltype)) {
                                sqlcount = "SELECT avg(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0) ) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("3".equals(statisticaltype)) {
                                sqlcount = "SELECT max(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else {
                                sqlcount = "SELECT min(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            }

                        } else {
                            if ("0".equals(statisticaltype)) {
                                sqlcount = "SELECT sum(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("1".equals(statisticaltype)) {
                                sqlcount = "SELECT c  from ( SELECT DISTINCT count(1) c " + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("2".equals(statisticaltype)) {
                                sqlcount = "SELECT avg(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("3".equals(statisticaltype)) {
                                sqlcount = "SELECT max(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else {
                                sqlcount = "SELECT min(" + DB_ISNULL_FUN + "(cast( REPLACE(m." + countfield + ",',','') as " + numberstr + "(38," + qfws + ")),0)) as  " + countfield + " from ( SELECT DISTINCT t1.id,b1." + countfield + " " + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            }
                        }


                    } else {
                        //sqlcount="SELECT sum(m."+countfield+") from ( SELECT DISTINCT t1.id,b1."+countfield+otherFormKey+sqlFrom+","+formname+" b1 "+sqlWhere1+" and "+defaultsql +" ) m ";  //改造前的统计函数sql
                        if (null != this.dataSourceDBType && this.dataSourceDBType.contains("sqlserver")) {
                            if ("0".equals(statisticaltype)) {
                                sqlcount = "select sum(cast(" + DB_ISNULL_FUN + "(m." + countfield + ",0) as " + numberstr + "(38," + qfws + "))) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("1".equals(statisticaltype)) {
                                sqlcount = "select c from ( SELECT DISTINCT count(1) c " + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("2".equals(statisticaltype)) {
                                sqlcount = "select avg(cast(" + DB_ISNULL_FUN + "(m." + countfield + ",0) as " + numberstr + "(38," + qfws + "))) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("3".equals(statisticaltype)) {
                                sqlcount = "select max(" + DB_ISNULL_FUN + "(m." + countfield + ",0)) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else {
                                sqlcount = "select min(" + DB_ISNULL_FUN + "(m." + countfield + ",0)) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            }

                        } else {
                            if ("0".equals(statisticaltype)) {
                                sqlcount = "select sum(" + DB_ISNULL_FUN + "(m." + countfield + ",0)) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("1".equals(statisticaltype)) {
                                sqlcount = "select c from ( SELECT DISTINCT count(1) c " + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("2".equals(statisticaltype)) {
                                sqlcount = "select avg(" + DB_ISNULL_FUN + "(m." + countfield + ",0)) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else if ("3".equals(statisticaltype)) {
                                sqlcount = "select max(" + DB_ISNULL_FUN + "(m." + countfield + ",0)) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            } else {
                                sqlcount = "select min(" + DB_ISNULL_FUN + "(m." + countfield + ",0)) from ( SELECT DISTINCT b1." + distinctfield + otherFormKey + sqlFrom + "," + formname + " b1 " + sqlWhere1 + " and " + defaultsql + " ) m ";
                            }
                        }

                    }

                }
                sqlcount = sqlcount.replace("\\'", "'");
                sqlcount = sqlcount.replace("&lt;", "<").replace("&gt;", ">");
                writeLog("countsql" + sqlcount);
                if (!"".equals(vdatasource) && !"$ECOLOGY_SYS_LOCAL_POOLNAME".equals(vdatasource)) {
                    rs3.executeQueryWithDatasource(sqlcount, vdatasource);
                } else {
                    rs3.executeSql(sqlcount, this.dataSource);
                }
                if (rs3.next()) {
                    countnum = Util.null2String(rs3.getString(1));
                }
                boolean flag = true;
                if ("".equals(countnum)) {
                    countnum = "0";
                } else if (countnum.indexOf("-") > -1) {
                    countnum = countnum.replace("-", "");
                    flag = false;
                }
                try {
                    if (!("1".equals(statisticaltype))) {//statisticaltype = 1 是统计总条数不需要进行数据格式化
                        //countnum=NumberHelper.moneyAddCommaSub(countnum);
                        //countnum=Util.toDecimalDigits(countnum,qfws);
                        if ("1".equals(fieldhtmltype) && "4".equals(fieldtype)) {
                            countnum = Util.toDecimalDigits(countnum, 2);//金额转换单独处理
                        } else {
                            if (!formid.equals("") || "0".equals(formtype)) {
                                countnum = Util.toDecimalDigits(countnum, qfws);//为数字统计补该字段的小数位
                            } else {
                                countnum = Util.toDecimalDigits(countnum, 2);//没有选表单时默认两位小数
                            }
                        }
                    }
                    countnum = NumberHelper.moneyAddCommaSub(countnum);
                } catch (Exception e) {
                }
                if (!flag) {
                    countnum = "-" + countnum;
                }
                newList.put("countnum", countnum);
                countData.add(newList);
            }
        }
        result.put("countData", countData);
        return result;
    }

    public Map<String, Object> getGroupData(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> result = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
        ModeFormComInfo modeFormComInfo = new ModeFormComInfo();
        this.viewType = Util.getIntValue(request.getParameter("viewtype"), 0);
        String menuIds = Util.null2String(request.getParameter("menuIds"));
        this.displayType = Util.null2String(request.getParameter("displayType"));
        boolean isExcel = this.displayType.equalsIgnoreCase("excel");
        this.user = HrmUserVarify.getUser(request, response);
        this.isEdit = Util.null2String(request.getParameter("isEdit")).equals("true") ? true : false;
        this.customId = Util.getIntValue(request.getParameter("customid"));
        this.isTreeType = Util.null2String(request.getParameter("isTreeType"));
        String customId = "" + this.customId;
        this.formId = Util.getIntValue(customSearchComInfo.getFormId(customId), 0);
        String formId = "" + this.formId;
        this.modeId = Util.getIntValue(customSearchComInfo.getModeId(customId), 0);
        this.noRightList = isBackUpData || "1".equals(customSearchComInfo.getNoRightList(customId));
        this.isVirtualForm = "1".equals(modeFormComInfo.getIsVirtualForm(formId));
        this.tableName = modeFormComInfo.getTableName(formId);
        this.detailTable = getRealDetailTable(customSearchComInfo.getDetailTable(customId));
        this.defaulSql = Util.toScreenToEdit(customSearchComInfo.getDefaultSql(customId), user.getLanguage());
        this.searchconditiontype = customSearchComInfo.getSearchConditionType(customId);
        this.javafilename = customSearchComInfo.getJavaFileName(customId);
        this.javafileaddress = customSearchComInfo.getJavaFileAddress(customId);
        this.grouptype = Util.null2String(request.getParameter("grouptype"));
        if (isVirtualForm) {
            this.dataSource = modeFormComInfo.getVDataSource(formId);
            String confirmSql = "select id  from datasourcesetting where pointid=\'" + dataSource + "\'";
            rs.execute(confirmSql);
            if (rs.getCounts() > 0 || "$ECOLOGY_SYS_LOCAL_POOLNAME".equals(dataSource)) {
                DataSourceXML dataSourceXML = new DataSourceXML();
                this.dataSourceDBType = dataSourceXML.getDataSourceDBType(dataSource);
                this.primaryKey = modeFormComInfo.getVPrimaryKey(formId);
                if ("2".equals(modeFormComInfo.getVformtype(this.formId))) {
                    this.tableName = modeFormComInfo.getVsql(this.formId);
                } else {
                    this.tableName = VirtualFormHandler.getRealFromName(tableName);
                }
            } else {
                result.put("error", SystemEnv.getHtmlLabelName(389199, user.getLanguage()));
                return result;
            }
        } else {
            this.dataSource = null;
            this.dataSourceDBType = rs.getDBType();
            this.primaryKey = "id";
        }

        ModeRightInfo modeRightInfo = new ModeRightInfo();
        Map<String, Object> preCheck = modeRightInfo.preCheckUserRight(user, modeId);
        this.hasUserRight = isBackUpData || (boolean) preCheck.get("isview");
        if (isEdit) {//批量修改判断修改权限
            this.hasUserRight = (boolean) preCheck.get("isedit");
        }
        String backfields = "";
        backfields = this.getBackFields(rs);
        String sqlFrom = this.getSqlFrom();
        String sqlWhere = this.getSqlWhere(request, response);
        if (hasUserRight && viewType != 3 && !noRightList && !isVirtualForm) {
            sqlWhere += ModeRightInfo.getStatusSqlWhere(SearchConstant.MAIN_TABLE_ALIAS, tableName, user.getUID());
        }
        String orderby = this.getOrderBy();
        String groupSqlWhere = this.getGroupSqlWhere(request, response);

        Map<String, Object> groupCount = null;

        String datasqlwhere = Util.null2String(request.getParameter("datasqlwhere"));
        if (!datasqlwhere.equals("")) {
            try {
                BASE64Decoder decoder = new BASE64Decoder();
                datasqlwhere = new String(decoder.decodeBuffer(datasqlwhere), "UTF-8");
                datasqlwhere = URLDecoder.decode(datasqlwhere, "utf-8");
            } catch (Exception e) {
                // TODO: handle exception
            }
            sqlWhere += datasqlwhere.trim().startsWith("and") ? datasqlwhere : " and " + datasqlwhere;
        }
        if (!StringHelper.isEmpty(this.groupName) && !isBackUpData) {
            groupCount = this.getGroupCount(sqlWhere, sqlFrom, dataSource, this.user);
        }
        result.put("groupCount", groupCount);
        return result;
    }

    public void setDataSourceDBType(String dataSourceDBType) {
        this.dataSourceDBType = dataSourceDBType;
    }


    /**
     * 检查当前建模查询中，下拉框字段对应的值，是否需要查询包含null值
     *
     * @param selectField 查询下拉框字段名
     * @param fieldValue  需要包含空值的下拉值
     * @param customid    建模查询id
     * @return
     */
    private boolean checkSelectNullValue(String selectField, String fieldValue, String customid) {
        BaseBean bb = new BaseBean();
        boolean flag = false;
        if (StringUtils.isNotBlank(selectField) && StringUtils.isNotBlank(fieldValue) && StringUtils.isNotBlank(customid)) {
            String sql = "select id from uf_jmcx_xlckz where jmcxid = ? and cxxlkzdm = ? and xybhkzdxlz = ?";
            RecordSet rs = new RecordSet();
            if (rs.executeQuery(sql, customid, selectField, fieldValue)) {
                if (rs.next()) {
                    flag = true;
                }
            } else {
                bb.writeLog("查询checkSelectNullValue出错:" + rs.getExceptionMsg());
            }
        }
        return flag;
    }
}
