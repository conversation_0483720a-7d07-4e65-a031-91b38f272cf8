package com.engine.visualReport.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.visualReport.service.VisualReportService;
import com.engine.visualReport.service.impl.VisualReportServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * FileName: VisualReportWeb.java
 * 可视化报表接口web
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/6/29
 */
public class VisualReportWeb {

    private VisualReportService getService(User user) {
        return ServiceUtil.getService(VisualReportServiceImpl.class, user);
    }

    /**
     * 客户信息
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/customerInfo")
    @Produces(MediaType.TEXT_PLAIN)
    public String customerInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).customerInfo(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }

}
