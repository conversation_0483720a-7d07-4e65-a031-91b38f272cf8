package com.engine.job;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.workflow.control.dto.WfConfigDto;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.workflow.invoice.action.BWInvoiceRedQueryFileAction;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName CompensateRedInvoiceJob.java
 * @Description 定时任务补偿百望红冲发票
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/22
 */
@Getter
@Setter
public class CompensateRedInvoiceJob extends BaseCronJob {
    //----定时任务参数-----//
    private String functionId;

    //基类
    private BaseBean bb;

    /**
     * 执行
     */
    @Override
    public void execute() {
        bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            bb.writeLog("functionId:" + Util.null2String(functionId));
            List<WfConfigDto> allConfig = WfConfigUtil.getAllWfConfig(functionId);
            bb.writeLog("allConfig:" + allConfig);
            if (!allConfig.isEmpty()) {
                for (WfConfigDto config : allConfig) {
                    process(config);
                }
            } else {
                bb.writeLog("未查到配置，跳过执行");
            }
        } catch (Exception e) {
            bb.writeLog(SDUtil.getExceptionDetail(e));
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

    /**
     * 执行每个配置
     *
     * @param config
     */
    private void process(WfConfigDto config) {
        //每个流程的配置
        String wfid = Util.null2String(config.getWfid());
        String nodeids = String.join(",", config.getNodeIds());
        JSONObject param = config.getParamObject();
        bb.writeLog("wfid:" + wfid);
        bb.writeLog("nodeids:" + nodeids);
        bb.writeLog("param:" + param);

        String fileFunctionId = Util.null2String(param.get("cfg_invoice_red_file_id"));
        //获取流程节点上所有附件为空的流程数据
        List<String> needFixData = getAllWfData(wfid, nodeids, param);
        bb.writeLog("needFixData:" + needFixData);
        if (!needFixData.isEmpty() && !fileFunctionId.isEmpty()) {
            ExecutorService executorService = Executors.newFixedThreadPool(5);
            //线程池执行
            CountDownLatch latch = new CountDownLatch(needFixData.size());
            for (String requestid : needFixData) {
                executorService.submit(() -> {
                    try {
                        // 处理每一组数据
                        processBuchang(requestid, fileFunctionId);
                    } finally {
                        latch.countDown(); // 线程完成任务后减少计数器
                    }
                });
            }
            try {
                // 等待所有线程完成任务，超时30分钟
                if (!latch.await(30, TimeUnit.MINUTES)) {
                    bb.writeLog("超时30分钟未执行完所有线程任务");
                }
            } catch (InterruptedException e) {
                bb.writeLog("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
            } finally {
                executorService.shutdown();
            }
        }
    }

    /**
     * 获取所有流程数据请求id
     * 匹配节点，附件为空的,并且推送flag为0(代表该单是全电票) 的
     */
    private List<String> getAllWfData(String wfid, String nodeids, JSONObject param) {
        String fieldFlag = Util.null2String(param.get("fieldFlag"));
        String fieldAttachLink = Util.null2String(param.get("fieldAttachLink"));
        String fieldQueryStatus = Util.null2String(param.get("fieldQueryStatus"));

        String dtIndex = Util.null2String(param.get("dtIndex"));

        bb.writeLog("param:" + param);
        bb.writeLog("fieldFlag:" + fieldFlag);
        bb.writeLog("fieldAttachLink:" + fieldAttachLink);
        bb.writeLog("fieldQueryStatus:" + fieldQueryStatus);

        String[] fieldAttachLinkArray = fieldAttachLink.split(CommonCst.COMMA_EN);
        String fieldAttachLinkValue;
        boolean needRun;

        List<String> result = new ArrayList<>();
        RecordSet rs = new RecordSet();
        String tableName = WfUtil.getWfInfoByWfId(wfid).getFormtableName();
        String detailTableName = tableName + "_dt" + dtIndex;
        String sql = "select a.* from " + tableName + " a " +
                " left join workflow_requestbase b on (a.requestid = b.requestid) " +
                " where 1=1 " +
//                //明细表附件字段为空
//                " and ( d." + fieldAttach + " is null or DATALENGTH(a." + fieldAttach + ") = 0 ) " +
                //回写状态为空、01、02、03、04
                " and (a." + fieldQueryStatus + " is null or a." + fieldQueryStatus + " in ('','01','02','03','04') )" +
                //主表字段为0 全电票
                " and a." + fieldFlag + " = '0' " +
                //在配置的节点上
                " and b.currentnodeid in (" + nodeids + ") ";
        bb.writeLog("getAllWfData sql :" + sql);
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                needRun = false;
                for (String str : fieldAttachLinkArray) {
                    fieldAttachLinkValue = Util.null2String(rs.getString(str));
                    if (fieldAttachLinkValue.isEmpty()) {
                        needRun = true;
                        break;
                    }
                }
                if (needRun) {
                    result.add(Util.null2String(rs.getString("requestid")));
                }

            }
        }
        return result;

    }

    /**
     * 执行
     *
     * @param requestid
     * @param functionId
     */
    private void processBuchang(String requestid, String functionId) {
        bb.writeLog("process each");
        bb.writeLog("requestid:" + requestid);
        bb.writeLog("functionId:" + functionId);
        try {
            BWInvoiceRedQueryFileAction action = new BWInvoiceRedQueryFileAction();
            String errorMsg = action.executeData(requestid, functionId, 1, true);
            if (!errorMsg.isEmpty()) {
                bb.writeLog("process each errorMsg:" + errorMsg);
            }
        } catch (Exception e) {
            bb.writeLog("process each 异常:" + SDUtil.getExceptionDetail(e));
        }

    }
}
