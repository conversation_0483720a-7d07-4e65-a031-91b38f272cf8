package com.engine.report.job;

import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName YearMonthUpdateJob
 * @Description 年月更新定时任务
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/5
 */
@Getter
@Setter
public class YearMonthUpdateJob extends BaseCronJob {
    //年月表的建模id
    private String moduleId;
    //年月的建模名
    private String moduleTableName;

    @Override
    public void execute() {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            //当前日期
            String currentDate = TimeUtil.getCurrentDateString();
            String currentYear = currentDate.substring(0, 4);
            String currentMonth = currentDate.substring(5, 7);
            String currentYearMonth = currentDate.substring(0, 7);
            //现将已有的数据删除
            if (delCurrentData(currentYear, currentMonth, currentYearMonth)) {
                //插入当前年月数据
                ModuleInsertBean mb = new ModuleInsertBean();
                List<String> fields = new ArrayList<>();
                List<Object> value = new ArrayList<>();
                fields.add("nd");
                fields.add("yf");
                fields.add("ny");

                value.add(currentYear);
                value.add(currentMonth);
                value.add(currentYearMonth);
                mb.setFields(fields);
                mb.setValue(value);
                mb.setModuleId(Integer.parseInt(moduleId));
                mb.setTableName(moduleTableName);
                bb.writeLog("insertOne mb :" + mb);
                ModuleDataUtil.insertOne(mb);
            }
        } catch (Exception e) {
            bb.writeLog(this.getClass().getName() + "---FAIL END");
        }
        bb.writeLog(this.getClass().getName() + "---SUCCESS END");
    }

    /**
     * 删除当前年月的数据
     *
     * @param currentYear
     * @param currentMonth
     * @param currentYearMonth
     * @return
     */
    private boolean delCurrentData(String currentYear, String currentMonth, String currentYearMonth) {
        RecordSet rs = new RecordSet();
        return rs.executeUpdate("delete from uf_bbtbny where nd = ? and yf = ? and ny = ?",
                currentYear, currentMonth, currentYearMonth);
    }
}
