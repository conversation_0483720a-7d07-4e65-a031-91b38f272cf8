package com.engine.report.job;

import com.engine.parent.common.util.SDUtil;
import com.engine.report.util.ReportDataUtil;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * FileName: OutPutValueReportJob.java
 * 产值信息汇总表（明细）表
 * 通过存储过程查询数据，并将数据插入指定建模表中
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/18
 */
public class OutPutValueReportJob extends BaseCronJob {

    /**
     * 存储过程名称
     */
    private String procedureName;
    /**
     * 合同生效开始日期
     */
    private String startDate;
    /**
     * 插入结果表名
     */
    private String moduleTableName;
    /**
     * 插入结果表建模id
     */
    private String moduleId;
    /**
     * 插入建模的字段名(多条逗号隔开)
     */
    private String insertFields;
    /**
     * 基类
     */
    private BaseBean bb;

    /**
     * 初始化
     */
    private void _init() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _init();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            String procedureParam = startDate;
            int creatId = SDUtil.getSystemMangerByLoginId();
            ReportDataUtil.procedureAndInsertData(procedureName,
                    procedureParam,
                    insertFields,
                    moduleTableName,
                    Integer.parseInt(moduleId),
                    creatId);
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expt:" + e.getMessage());
            bb.writeLog(this.getClass().getName() + "---FAIL END");
        }
        bb.writeLog(this.getClass().getName() + "---SUCCESS END");
    }

    public String getProcedureName() {
        return procedureName;
    }

    public void setProcedureName(String procedureName) {
        this.procedureName = procedureName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getModuleTableName() {
        return moduleTableName;
    }

    public void setModuleTableName(String moduleTableName) {
        this.moduleTableName = moduleTableName;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getInsertFields() {
        return insertFields;
    }

    public void setInsertFields(String insertFields) {
        this.insertFields = insertFields;
    }
}
