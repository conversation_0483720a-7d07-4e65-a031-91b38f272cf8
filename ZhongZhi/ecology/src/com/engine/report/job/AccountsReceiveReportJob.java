package com.engine.report.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.engine.report.util.ReportDataUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.List;

/**
 * FileName: AcrossSaleReportJob.java
 * 应收账款报表（汇总）
 * 部门-年月
 * 通过存储过程查询数据，并将数据插入指定建模表中
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/22
 */
public class AccountsReceiveReportJob extends BaseCronJob {

    /**
     * 存储过程名称
     */
    private String procedureName;
    /**
     * 合同生效开始日期
     */
    private String startDate;
    /**
     * 插入结果表名
     */
    private String moduleTableName;
    /**
     * 插入结果表建模id
     */
    private String moduleId;
    /**
     * 插入建模的字段名(多条逗号隔开)
     */
    private String insertFields;
    /**
     * 需要统计所有下级部门的字段
     */
    private String sumDepFields;
    /**
     * 基类
     */
    private BaseBean bb;

    /**
     * 初始化
     */
    private void _init() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _init();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            String procedureParam = startDate;
            int creatId = SDUtil.getSystemMangerByLoginId();
            String depId, depIdJunior, year, yearJunior;
            List<String> allJuniorDeptList;
            JSONObject jo, joJunior;
            //查询存储过程数据
            RecordSet rs = new RecordSet();
            boolean flag = rs.executeProc(procedureName, procedureParam);
            if (flag) {
                JSONArray jaList = QueryResultUtil.getJSONArrayList(rs);
                //jaList复制一份，作为原始数据
                JSONArray jaOrigin = JSONArray.parseArray(jaList.toJSONString());
                for (int i = 0; i < jaList.size(); i++) {
                    jo = jaList.getJSONObject(i);
                    depId = Util.null2String(jo.get("ssbm"));
                    year = Util.null2String(jo.get("ny"));
                    if (StringUtils.isNotBlank(depId)) {
                        //找到所有下级部门
                        allJuniorDeptList = ReportDataUtil.queryAllJuniorDept(depId);
                        for (String id : allJuniorDeptList) {
                            //对每个下级部门，进行数据合计，汇总统计到主节点上
                            for (int j = 0; j < jaOrigin.size(); j++) {
                                joJunior = jaOrigin.getJSONObject(j);
                                depIdJunior = Util.null2String(joJunior.get("ssbm"));
                                yearJunior = Util.null2String(joJunior.get("ny"));
                                //匹配相同部门id和年份
                                if (id.equals(depIdJunior) && year.equals(yearJunior)) {
                                    //将该下级数据合计到主节点上
                                    ReportDataUtil.sumJuinordataToTop(sumDepFields, jo, joJunior);
                                }
                            }
                        }
                    }
                }
                //此时jaList为处理好的最终结果集
                bb.writeLog("jaList:" + jaList);
                ReportDataUtil.insertData(jaList, insertFields, moduleTableName, Integer.parseInt(moduleId), creatId);

            } else {
                bb.writeLog("执行存储过程" + procedureName + "出错：" + rs.getExceptionMsg() + "," + rs.getMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expt:" + e.getMessage());
            bb.writeLog(this.getClass().getName() + "---FAIL END");
        }
        bb.writeLog(this.getClass().getName() + "---SUCCESS END");
    }

    public String getProcedureName() {
        return procedureName;
    }

    public void setProcedureName(String procedureName) {
        this.procedureName = procedureName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getModuleTableName() {
        return moduleTableName;
    }

    public void setModuleTableName(String moduleTableName) {
        this.moduleTableName = moduleTableName;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getInsertFields() {
        return insertFields;
    }

    public void setInsertFields(String insertFields) {
        this.insertFields = insertFields;
    }

    public String getSumDepFields() {
        return sumDepFields;
    }

    public void setSumDepFields(String sumDepFields) {
        this.sumDepFields = sumDepFields;
    }
}
