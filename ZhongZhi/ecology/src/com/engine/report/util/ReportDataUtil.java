package com.engine.report.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryResultUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * FileName: ProcDataUtil.java
 * 执行存储过程工具类
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/21
 */
public class ReportDataUtil {
    /**
     * 执行存储过程，并将查询结果数据插入建模表中
     * 注意：存储过程结果的字段名，必须和【insertFields】参数名一致
     *
     * @param procedureName   存储过程名称
     * @param procedureParam  存储过程参数
     * @param insertFields    建模插入的字段集合
     * @param moduleTableName 建模表名
     * @param moduleId        建模id
     * @param creatId         创建人id
     * @return
     */
    public static String procedureAndInsertData(String procedureName,
                                                String procedureParam,
                                                String insertFields,
                                                String moduleTableName,
                                                int moduleId,
                                                int creatId) throws Exception {
        String erroMsg = "";
        BaseBean bb = new BaseBean();
        bb.writeLog("ProcDataUtil -- procedureAndInsertData ---START");
        List<Object> values;
        RecordSet rs = new RecordSet();
        boolean flag = rs.executeProc(procedureName, procedureParam);
        if (!flag) {
            erroMsg = rs.getExceptionMsg() + "," + rs.getMsg();
        } else {
            JSONArray jaList = QueryResultUtil.getJSONArrayList(rs);
            insertData(jaList, insertFields, moduleTableName, moduleId, creatId);
//            String[] fieldArray = insertFields.split(CommonCst.COMMA_EN);
//            //先删除数据
//            clearModuleData(moduleTableName);
//            while (rs.next()) {
//                //添加value集合值
//                values = new ArrayList<>();
//                for (String field : fieldArray) {
//                    //根据配置的insertField 直接获取sql结果的值
//                    values.add(Util.null2String(rs.getString(field)));
//                }
//                //插入建模
//                InsertModuleUtil.ModuleInsert(moduleTableName, fieldArray, values, creatId, moduleId, null);
//            }
        }
        if (StringUtils.isNotBlank(erroMsg)) {
            bb.writeLog("错误信息：" + erroMsg);
        }
        bb.writeLog("ProcDataUtil -- procedureAndInsertData ---END");
        return erroMsg;
    }

    /**
     * 将JSONArray 结果数据插入建模表中
     * 注意：存储过程结果的字段名，必须和【insertFields】参数名一致
     *
     * @param ja              数据集合（字段都是小写的）
     * @param insertFields    建模插入的字段集合
     * @param moduleTableName 建模表名
     * @param moduleId        建模id
     * @param creatId         创建人id
     * @return
     */
    public static String insertData(JSONArray ja,
                                    String insertFields,
                                    String moduleTableName,
                                    int moduleId,
                                    int creatId) {
        String erroMsg = "";
        BaseBean bb = new BaseBean();
        bb.writeLog("ProcDataUtil -- insertData ---START");

        JSONObject jo;

        String[] fieldArray = insertFields.split(CommonCst.COMMA_EN);
        //先删除数据
        clearModuleData(moduleTableName);
        ModuleInsertBean mb = new ModuleInsertBean();
        mb.setFields(Arrays.asList(fieldArray));
        mb.setModuleId(moduleId);
        mb.setCreatorId(creatId);
        mb.setTableName(moduleTableName);
        List<List<Object>> values = new ArrayList<>();
        List<Object> eachValue;
        try {
            for (int i = 0; i < ja.size(); i++) {
                eachValue = new ArrayList<>();
                jo = ja.getJSONObject(i);
                for (String field : fieldArray) {
                    //根据配置的insertField 直接获取sql结果的值
                    eachValue.add(jo.get(field));
                }
                values.add(eachValue);
            }
            mb.setValues(values);
            ModuleDataUtil.insertAc(mb);
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("ProcDataUtil -- insertData expt :" + e.getMessage());
        }

        bb.writeLog("ProcDataUtil -- insertData ---END");
        return erroMsg;
    }

    /**
     * 清除建模表数据
     *
     * @param moduleTableName 建模表名
     */
    private static void clearModuleData(String moduleTableName) {
        RecordSet rs = new RecordSet();
        rs.executeUpdate("truncate table " + moduleTableName);
    }

    /**
     * 查询所有该部门所有的下级部门id
     * 排除查询的父级部门
     *
     * @param parentDeptId 部门id
     * @return
     */
    public static List<String> queryAllJuniorDept(String parentDeptId) {
        List<String> allJuniorDep = new ArrayList<>();
        RecordSet rs = new RecordSet();
        String sql = "with temp " +
                " as " +
                " ( " +
                " select * from HrmDepartment " +
                " where id= " + parentDeptId +
                " union all " +
                " select a.* from HrmDepartment a inner join temp on a.supdepid = temp.id " +
                " ) " +
                " " +
                " select * from temp " +
                " where id !=" + parentDeptId;
        boolean flag = rs.executeQuery(sql);
        if (flag) {
            while (rs.next()) {
                allJuniorDep.add(Util.null2String(rs.getString("id")));
            }
        }
        return allJuniorDep;
    }


    /**
     * 将下级的数据合计到顶级节点上
     *
     * @param fields
     * @param joTop
     * @param joJunior
     */
    public static void sumJuinordataToTop(String fields, JSONObject joTop, JSONObject joJunior) {
        if (StringUtils.isNotBlank(fields)) {
            String[] array = fields.split(CommonCst.COMMA_EN);
            for (String str : array) {
                joTop.put(str, SDUtil.getBigDecimalValue(joTop.get(str)).add(SDUtil.getBigDecimalValue(joJunior.get(str))));
            }
        }
    }


    /**
     * 查询所有该部门所有的上级部门id
     * 排除查询的父级部门
     *
     * @param parentDeptId 部门id
     * @return
     */
    public static List<String> getALLParentsub(String parentDeptId) {
        List<String> allJuniorDep = new ArrayList<>();
        RecordSet rs = new RecordSet();
        String sql = "with temp " +
                " as " +
                " ( " +
                " select * from HrmDepartment " +
                " where id= " + parentDeptId +
                " union all " +
                " select a.* from HrmDepartment a inner join temp on a.id = temp.supdepid " +
                " ) " +
                " " +
                " select * from temp " +
                " where id !=" + parentDeptId;
        boolean flag = rs.executeQuery(sql);
        if (flag) {
            while (rs.next()) {
                allJuniorDep.add(Util.null2String(rs.getString("id")));
            }
        }
        return allJuniorDep;
    }

}
