package com.engine.contract.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.workflow.biz.freeNode.FreeNodeBiz;
import com.engine.workflow.entity.publicApi.PAResponseEntity;
import com.engine.workflow.entity.publicApi.ReqOperateRequestEntity;
import com.engine.workflow.entity.publicApi.WorkflowDetailTableInfoEntity;
import com.engine.workflow.publicApi.WorkflowRequestOperatePA;
import com.engine.workflow.publicApi.impl.WorkflowRequestOperatePAImpl;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightForPage;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.MainTableInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.webservices.WorkflowRequestTableField;

import java.util.*;

@Getter
@Setter
public class AddRoleAction extends BaseBean implements Action {


    private String manager;

    private String contractdtid;

    private String contractcol;

    private String contracthrm;

    private String prijdtid;

    private String prjcol;

    private String prjhrm;

    //明细表 比如明细1 则dtid的值为1
    private String dtid;
    //转移到字段名
    private String zyd;
    //项目名称字段名
    private String xmmc;
    //要更新的表名 三张表之间用逗号连接
    private String tableNames;


    @Override
    public String execute(RequestInfo requestInfo) {
        try {
            writeLog("requestid:" + requestInfo.getRequestid());
            RecordSet recordSet = new RecordSet();
            RequestManager rm = requestInfo.getRequestManager();
            MainTableInfo mainTableInfo = requestInfo.getMainTableInfo();
            Property[] propertys = mainTableInfo.getProperty();
            Map<String, String> map = new HashMap<>();
            for (Property property : propertys) {
                String str = property.getName();
                String value = property.getValue();
                map.put(str, value);
            }

            //处理proj
            String managerid = Util.null2String(map.get(manager));
            writeLog("manager----------" + managerid);
            ArrayList<HashMap<String, String>> dtproj = ActionUtil.getDetailMap(requestInfo, Integer.parseInt(prijdtid));
            if (dtproj != null) {
                for (HashMap<String, String> dtcol : dtproj
                ) {
                    String prjids = dtcol.get(prjcol);
                    String prjhrms = dtcol.get(prjhrm);
                    String selectprj = " SELECT * FROM  prj_projectinfo where id = '" + prjids + "'";
                    recordSet.execute(selectprj);
                    writeLog("sql:" + selectprj);
                    while (recordSet.next()) {
                        String menber = Util.null2String(recordSet.getString("members")).trim();
                        writeLog("menber" + menber);
                        writeLog("prjhrms" + prjhrms);
                        if (menber.length() > 0 && prjhrms.length() > 0) {
                            menber = menber + "," + prjhrms;
                        } else {
                            menber = prjhrms;
                        }
                        RecordSet update = new RecordSet();
                        String updatesql = " UPDATE prj_projectinfo set manager = '" + prjhrms + "', members = '" + menber + "' where id = '" + prjids + "'";
                        writeLog("updatesql" + updatesql);
                        update.execute(updatesql);
                        RecordSet updatenex = new RecordSet();
                        String updatenext = " UPDATE uf_ysxx set xmjl = '" + prjhrms + "'  where xmmc = '" + prjids + "'";
                        writeLog("updatesql" + updatenext);
                        updatenex.execute(updatenext);

                        String sqlss = "SELECT * FROM uf_ysxx where xmmc = '" + prjids + "'";
                        RecordSet record = new RecordSet();
                        record.execute(sqlss);
                        while (record.next()) {
                            String billid = Util.null2String(record.getString("id"));
                            UpdateMode(billid, prjhrms, 34);
                        }
                    }
                }
            }

            //项目经理权限批量移交功能
            writeLog("项目经理权限批量移交功能---START");
            HashMap<String, List<String>> rebuildModeMap = new HashMap<>();
            ArrayList<HashMap<String, String>> details1 = ActionUtil.getDetailMap(requestInfo, Integer.parseInt(dtid));
            if (details1 != null && StringUtils.isNotBlank(tableNames)) {
                writeLog("项目经理权限批量移交功能dtproj集合对象的值===>" + JSONObject.toJSONString(details1));
                RecordSet rs = new RecordSet();
                for (HashMap<String, String> dtcol : details1) {
                    String zydValue = Util.null2String(dtcol.get(zyd));
                    String xmmcValue = Util.null2String(dtcol.get(xmmc));
                    if (StringUtils.isNotBlank(zydValue) && StringUtils.isNotBlank(xmmcValue)) {
                        //更新三张表的信息
                        String[] tables = tableNames.split(",");
                        for (String tableName : tables) {
                            ArrayList<String> list = new ArrayList<>();
                            String sql1 = " UPDATE " + tableName + " set xmjl = '" + zydValue + "'  where xmmc = '" + xmmcValue + "'";
                            writeLog("项目经理权限批量移交功能-updatesql===>" + sql1);
                            rs.executeUpdate(sql1);
                            String sql2 = "select id from " + tableName + " where xmmc = '" + xmmcValue + "'";
                            writeLog("项目经理权限批量移交功能-querysql===>" + sql2);
                            rs.executeQuery(sql2);
                            while (rs.next()) {
                                list.add(rs.getString("id"));
                            }
                            writeLog("将要进行项目重构的tableName:" +tableName+",list:"+list );
                            if (rebuildModeMap.containsKey(tableName)) {
                                List<String> billIds = rebuildModeMap.get(tableName);
                                billIds.addAll(list);
                                rebuildModeMap.put(tableName, billIds);
                            } else {
                                rebuildModeMap.put(tableName, list);
                            }
                        }
                    }
                }
            }
            writeLog("项目经理权限批量移交功能rebuildModeMap===>" + JSONObject.toJSONString(rebuildModeMap));
            if (!rebuildModeMap.isEmpty()) {
                for (Map.Entry<String, List<String>> entry : rebuildModeMap.entrySet()) {
                    String tableName = entry.getKey();
                    List<String> billIds = entry.getValue();
                    if (billIds != null && !billIds.isEmpty()) {
                        writeLog("项目经理权限批量移交功能将要更新的-tableName===>" + tableName);
                        writeLog("项目经理权限批量移交功能将要更新的-billIds===>" + billIds);
                        ModuleDataUtil.resetModShare(ModuleDataUtil.getModuleIdByName(tableName), billIds);
                    }
                }
            }

            writeLog("项目经理权限批量移交功能---END");
            writeLog("处理合同---START");
            //处理合同
            ArrayList<HashMap<String, String>> dtcontract = ActionUtil.getDetailMap(requestInfo, Integer.parseInt(contractdtid));
            if (dtcontract != null) {
                for (HashMap<String, String> dtcol : dtcontract) {
                    String contractid = dtcol.get(contractcol);
                    String contracthrms = dtcol.get(contracthrm);
                    String sqlcon = " SELECT * FROM uf_ywhtxx where id = '" + contractid + "'";

                    recordSet.execute(sqlcon);
                    while (recordSet.next()) {
                        String menber = Util.null2String(recordSet.getString("htgxfw")).trim();
                        if (menber.length() > 0 && contracthrms.length() > 0) {
                            List<String> list = new ArrayList<>(Arrays.asList(menber.split(",")));
                            if (!"".equals(managerid)) {
                                list.removeIf(element -> element.equals(managerid));
                            }
                            menber = String.join(",", list) + "," + contracthrms;
                        } else {
                            menber = contracthrms;
                        }
                        RecordSet update = new RecordSet();
                        String updatesql = " UPDATE uf_ywhtxx set htgxfw = '" + menber + "' where id = '" + contractid + "'";
                        writeLog("updatesqlcontract" + updatesql);
                        update.execute(updatesql);
                        //更新开票信息权限
                        HashMap<String, Integer> updat = new HashMap<>();
                        updat.put("uf_xkpjh", 15);
                        updat.put("uf_kpss", 14);
                        updat.put("uf_tpsq", 32);
                        updat.put("uf_hkjh", 31);
                        UpdateMode(contractid, contracthrms, 6);
                        try {
                            forwardsql(contractid, contracthrms);
                        } catch (Exception e) {
                            writeLog("EX_________" + e);
                        }
//                    if (ans != null && ans.getCode() != PAResponseCode.SUCCESS) {
//                        rm.setMessagecontent(ans.getErrMsg().toString() + "_" + ans);
//                        return FAILURE_AND_CONTINUE;
//                    }
                        Set<Map.Entry<String, Integer>> sets = updat.entrySet();
                        for (Map.Entry<String, Integer> set :
                                sets) {
                            UpdateMode(contractid, contracthrms, set.getKey(), set.getValue());
                        }
                    }
                }
            }
            writeLog("处理合同---END");
        } catch (Exception e) {
            writeLog("异常：" + SDUtil.getExceptionDetail(e));
        }
        return SUCCESS;
    }


    public void forwardsql(String htid, String hrmid) {
        RecordSet recordSet = new RecordSet();
        String sql = "select \n" +
                "taba.REQUESTID, \n" +
                "taba.sqr \n" +
                "from \n" +
                "formtable_main_28 taba\n" +
                "left join \n" +
                "workflow_requestbase base on taba.requestid = base.REQUESTID\n" +
                "where taba.htmc = '" + htid + "'";
        recordSet.execute(sql);
        writeLog("sql" + sql);
        while (recordSet.next()) {
            String REQUESTID = Util.null2String(recordSet.getString("REQUESTID"));
            int sqr = Integer.parseInt(recordSet.getString("sqr"));
            WorkflowRequestOperatePA workflowRequestOperatePA = this.getRequestOperatePA();
            User user = new User(sqr);
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("requestId", REQUESTID);
            hashMap.put("forwardFlag", "1");
            hashMap.put("forwardResourceIds", hrmid);
            writeLog("hashMap" + hashMap);
            PAResponseEntity ans = workflowRequestOperatePA.forwardRequest(user, hashMap2Entity(hashMap));
            writeLog("ans-------------" + ans.toString());
//            if (ans.getCode() != PAResponseCode.SUCCESS) {
//            return ans;
//            }
        }
    }

    private WorkflowRequestOperatePA getRequestOperatePA() {
        return (WorkflowRequestOperatePAImpl) ServiceUtil.getService(WorkflowRequestOperatePAImpl.class);
    }


    public static ReqOperateRequestEntity hashMap2Entity(HashMap<String, String> var0) {
        ReqOperateRequestEntity var1 = new ReqOperateRequestEntity();

        try {
            int var2 = Util.getIntValue(var0.get("workflowId"));
            int var3 = Util.getIntValue(var0.get("requestId"));
            String var4 = Util.null2String(var0.get("requestName"));
            int var5 = Util.getIntValue(var0.get("userId"));
            int var6 = Util.getIntValue(var0.get("forwardFlag"));
            String var7 = Util.null2String(var0.get("forwardResourceIds"));
            String var8 = Util.null2String(var0.get("mainData"));
            int var9 = Util.getIntValue(Util.null2String(var0.get("isremind")), 1);
            if (!"".equals(var8)) {
                try {
                    List var10 = JSONObject.parseArray(var8, WorkflowRequestTableField.class);
                    var1.setMainData(var10);
                } catch (Exception var17) {
                    var17.printStackTrace();
                }
            }

            String var19 = Util.null2String(var0.get("detailData"));
            if (!"".equals(var19)) {
                try {
                    List var11 = JSONObject.parseArray(var19, WorkflowDetailTableInfoEntity.class);
                    var1.setDetailData(var11);
                } catch (Exception var16) {
                    var1.setIsDetailParseError("1");
                    var16.printStackTrace();
                }
            }
            String var20 = Util.null2String(var0.get("remark"));
            String var12 = Util.null2String(var0.get("requestLevel"));
            String var13 = Util.null2String(var0.get("otherParams"));
            if (!"".equals(var13)) {
                try {
                    Map var14 = (Map) JSONObject.parseObject(var13, Map.class);
                    var1.setOtherParams(var14);
                } catch (Exception var15) {
                    var15.printStackTrace();
                }
            }
            var1.setWorkflowId(var2);
            var1.setRequestId(var3);
            var1.setRequestName(var4);
            var1.setUserId(var5);
            var1.setRemark(var20);
            var1.setRequestLevel(var12);
            var1.setForwardFlag(var6);
            var1.setForwardResourceIds(var7);
            var1.setIsremind(var9);
            int var21 = Util.getIntValue(Util.null2String(var0.get("submitNodeId")));
            if (var21 > 0 || FreeNodeBiz.isFreeNode(var21)) {
                var1.setSubmitNodeId(var21);
                var1.setEnableIntervenor("1".equals(Util.null2s(var0.get("enableIntervenor"), "1")));
                var1.setSignType(Util.getIntValue(Util.null2String(var0.get("SignType")), 0));
                var1.setIntervenorid(Util.null2String(var0.get("Intervenorid")));
            }
        } catch (Exception var18) {
            var18.printStackTrace();
        }

        return var1;
    }

    public void UpdateMode(String contractid, String hrmid, String table, Integer modeid) {
        RecordSet recordSet = new RecordSet();
        String sql = " SELECT * FROM " + table + " where htmc = '" + contractid + "'";
        writeLog("sql" + sql);
        recordSet.execute(sql);
        while (recordSet.next()) {
            String id = recordSet.getString("id");
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("relatedid", hrmid);
            hashMap.put("HrmCompanyVirtual", "0");
            hashMap.put("isRoleLimited", "0");
            hashMap.put("joblevel", "2");
            hashMap.put("rolelevel", "0");
            hashMap.put("rightType", "1");
            hashMap.put("shareType", "1");
            hashMap.put("relatedid1value", hrmid);
            hashMap.put("showlevel", "0");
            hashMap.put("rolefieldtype", "1");
            addrole(modeid, id, hashMap);
        }
    }

    public void UpdateMode(String billid, String hrmid, Integer modeid) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("relatedid", hrmid);
        hashMap.put("HrmCompanyVirtual", "0");
        hashMap.put("isRoleLimited", "0");
        hashMap.put("joblevel", "2");
        hashMap.put("rolelevel", "0");
        hashMap.put("rightType", "1");
        hashMap.put("shareType", "1");
        hashMap.put("relatedid1value", hrmid);
        hashMap.put("showlevel", "0");
        hashMap.put("rolefieldtype", "1");
        addrole(modeid, billid, hashMap);
    }

    //    uf_ywhtxx htgxfw
    public void addrole(Integer modeId, String billid, HashMap<String, String> hashMap) {
        writeLog("in----add share" + modeId + "_" + billid + "_" + hashMap.toString());
        ModeRightForPage var1 = new ModeRightForPage();
        ModeRightInfo var2 = new ModeRightInfo();
        int var3 = Util.getIntValue(hashMap.get("shareType"), 0);
        String var4 = Util.null2String(hashMap.get("relatedid"));
        int var5 = Util.getIntValue(hashMap.get("rolelevel"), 0);
        int var6 = Util.getIntValue(hashMap.get("showlevel"), 0);
        int var7 = Util.getIntValue(hashMap.get("showlevel2"), var6 - 2);
        int var8 = Util.getIntValue(hashMap.get("rightType"), 0);
        int var9 = Util.getIntValue(hashMap.get("isRoleLimited"), 0);
        int var10 = Util.getIntValue(hashMap.get("rolefieldtype"), 0);
        int var11 = Util.getIntValue(hashMap.get("orgrelation"), 0);
        String var12 = Util.null2String(hashMap.get("rolefield"));
        String var13 = Util.null2String(hashMap.get("HrmCompanyVirtual"));
        int var14 = Util.getIntValue(hashMap.get("joblevel"), 0);
        String var15 = Util.null2String(hashMap.get("jobleveltext"));
        var1.addNewRight(Util.getIntValue(billid), modeId, var3, var4, var5, var6, var8, var9, var10, var12, var7, var13, var11, var14, var15);
        var2.editDocShareWithUser(Util.getIntValue(billid), modeId, var3, 0, var5, var6, var8);
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getContractdtid() {
        return contractdtid;
    }

    public void setContractdtid(String contractdtid) {
        this.contractdtid = contractdtid;
    }

    public String getContractcol() {
        return contractcol;
    }

    public void setContractcol(String contractcol) {
        this.contractcol = contractcol;
    }

    public String getContracthrm() {
        return contracthrm;
    }

    public void setContracthrm(String contracthrm) {
        this.contracthrm = contracthrm;
    }

    public String getPrijdtid() {
        return prijdtid;
    }

    public void setPrijdtid(String prijdtid) {
        this.prijdtid = prijdtid;
    }

    public String getPrjcol() {
        return prjcol;
    }

    public void setPrjcol(String prjcol) {
        this.prjcol = prjcol;
    }

    public String getPrjhrm() {
        return prjhrm;
    }

    public void setPrjhrm(String prjhrm) {
        this.prjhrm = prjhrm;
    }
}
