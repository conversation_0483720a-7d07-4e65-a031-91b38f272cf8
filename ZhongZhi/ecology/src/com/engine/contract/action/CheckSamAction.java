package com.engine.contract.action;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;

public class CheckSamAction extends BaseBean implements Action {

    private String colname; //浏览按钮字段

    private String table; //校验表

    private String tablecol; //校验表字段名

    private String needshowword;//展示文字

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        RecordSet recordSet = new RecordSet();
        String sql = " SELECT * FROM " + table;
        if(!"".equals(map.get(colname))){
            recordSet.execute(sql + " WHERE "+tablecol+" = '"+map.get(colname)+"'");
            if(recordSet.next()){
                rm.setMessagecontent(needshowword);
                return Action.FAILURE_AND_CONTINUE;
            }
        }else {
            rm.setMessagecontent("校验字段无值");
            return Action.FAILURE_AND_CONTINUE;
        }

        return Action.SUCCESS;
    }

    public String getColname() {
        return colname;
    }

    public void setColname(String colname) {
        this.colname = colname;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getTablecol() {
        return tablecol;
    }

    public void setTablecol(String tablecol) {
        this.tablecol = tablecol;
    }

    public String getNeedshowword() {
        return needshowword;
    }

    public void setNeedshowword(String needshowword) {
        this.needshowword = needshowword;
    }
}
