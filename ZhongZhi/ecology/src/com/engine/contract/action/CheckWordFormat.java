package com.engine.contract.action;

import com.engine.contract.utils.formatcheck.Checkformat;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;

public class CheckWordFormat extends BaseBean implements Action {

    private String  colname;

    private String  needshowword;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        String value = map.get(colname);
        if(!"".equals(value)){
            for (int i = 0; i < value.length(); i++) {
                if(Checkformat.CheckASC(value.charAt(i),48,57)||
                        Checkformat.CheckASC(value.charAt(i),65,90)||
                        Checkformat.CheckASC(value.charAt(i),97,122)){
                }else {
                    rm.setMessagecontent(needshowword);
                    return Action.FAILURE_AND_CONTINUE;
                }
            }
        }else {
            rm.setMessagecontent("校验字段无值");
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    public String getColname() {
        return colname;
    }

    public void setColname(String colname) {
        this.colname = colname;
    }

    public String getNeedshowword() {
        return needshowword;
    }

    public void setNeedshowword(String needshowword) {
        this.needshowword = needshowword;
    }
}
