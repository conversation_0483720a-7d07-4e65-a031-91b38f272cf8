package com.engine.contract.action;

import com.api.prj.util.PrjFieldManager;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.proj.Maint.ProjectInfoComInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.*;

public class UpdateProaction extends BaseBean implements Action {


    private String col; //字段名称

    private String projectid;//项目id

    private String valuecol; //值

    private String xmlx;//项目类型

    @Override
    public String execute(RequestInfo requestInfo) {
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        RequestManager rm = requestInfo.getRequestManager();
        Map<String, String> mainmap = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            mainmap.put(str, value);
        }
        String fieldid = "";
        RecordSet record = new RecordSet();
        record.execute("select id from PrjFieldAllview where fieldname = '"+col+"' AND prjtype = '"+mainmap.get(xmlx)+"'");
        if(record.next()){
            fieldid = Util.null2String(record.getString("id"));
        }
        String field;
        if("".equals(mainmap.get(xmlx))){
            return Action.FAILURE_AND_CONTINUE;
        }else if("1".equals(mainmap.get(xmlx))){
            field = "field";
        }else if("2".equals(mainmap.get(xmlx))){
            field = "customfield";
        }else{
            return Action.FAILURE_AND_CONTINUE;
        }
        writeLog("field = "+field+fieldid);
        writeLog("value = " +mainmap.get(valuecol));
        writeLog("prjtype = "+mainmap.get(xmlx));
        writeLog("prjid = "+mainmap.get(projectid));
        User user =  requestInfo.getRequestManager().getUser();
        Map<String, Object> map = new HashMap<>();
        map.put("prjtype",mainmap.get(xmlx));
        map.put(field+fieldid,mainmap.get(valuecol));
        map.put("method","edit");
        writeLog("here = 1");
        PrjFieldManager prjFieldManager = new PrjFieldManager();
        writeLog("here = 2");
        prjFieldManager.updateCusfieldValue(mainmap.get(projectid),map,user,-1);
        writeLog("here = 3");
        ProjectInfoComInfo pic = new ProjectInfoComInfo();
        writeLog("here = 4");
        pic.removeProjectInfoCache();
        return Action.SUCCESS;
    }

    public String getCol() {
        return col;
    }

    public void setCol(String col) {
        this.col = col;
    }

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }

    public String getValuecol() {
        return valuecol;
    }

    public void setValuecol(String valuecol) {
        this.valuecol = valuecol;
    }

    public String getXmlx() {
        return xmlx;
    }

    public void setXmlx(String xmlx) {
        this.xmlx = xmlx;
    }
}
