package com.engine.contract.action;

import com.api.formmode.cache.ModeComInfo;
import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.hrm.User;
import weaver.soa.workflow.request.MainTableInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.workflow.WorkflowBillComInfo;

import java.util.HashMap;
import java.util.Map;

public class CheckSam4ModeAction  extends AbstractModeExpandJavaCodeNew {

    public CheckSam4ModeAction() {
    }

    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<String, String>();
        new BaseBean().writeLog("ModeExpandTemplatetest");
        try {
            User user = (User)param.get("user");
            RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
            MainTableInfo mainTableInfo = requestInfo.getMainTableInfo();
            Map<String,String> map = new HashMap<>();
            Property[] propertys = mainTableInfo.getProperty();
            for( Property property : propertys){
                String str = property.getName();
                String value = property.getValue();
                map.put(str,value);
            }
            ModeComInfo modeComInfo = new ModeComInfo();
            String formId = modeComInfo.getFormId(requestInfo.getWorkflowid());
            WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
            String tablename = workflowBillComInfo.getTablename(formId);
            String col = new BaseBean().getPropValue("CheckProps","col");
            RecordSet recordSet = new RecordSet();
            if("".equals(map.get(col))){
                result.put("errmsg","未填写必选字段");
                result.put("flag", "false");
                return result;
            }
            recordSet.execute("SELECT * FROM "+tablename+" WHERE "+col+" = "+map.get(col));
            if(recordSet.next()){
                result.put("errmsg","存在重复字段");
                result.put("flag", "false");
                return result;
            }
        } catch (Exception e) {
            result.put("errmsg","接口出错");
            result.put("flag", "false");
        }
        return result;
    }

}
