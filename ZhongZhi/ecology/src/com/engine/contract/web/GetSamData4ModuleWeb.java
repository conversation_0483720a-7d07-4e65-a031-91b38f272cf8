package com.engine.contract.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.contract.service.GetSamDataService;
import com.engine.contract.service.GetisHavingSamService;
import com.engine.contract.service.impl.GetSamDataServiceImpl;
import com.engine.contract.service.impl.GetisHavingSamServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class GetSamData4ModuleWeb {

    private GetSamDataService getService(User user) {
        return ServiceUtil.getService(GetSamDataServiceImpl.class, user);
    }

    @POST
    @Path("/getisHavingSamData")
    @Produces(MediaType.TEXT_PLAIN)
    public String getisHavingSamData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getSamData(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }
}
