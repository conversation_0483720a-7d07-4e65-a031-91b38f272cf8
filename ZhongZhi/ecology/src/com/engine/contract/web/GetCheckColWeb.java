package com.engine.contract.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.contract.service.GetCheckColService;
import com.engine.contract.service.impl.GetCheckColServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class GetCheckColWeb {

    private GetCheckColService getService(User user) {
        return ServiceUtil.getService(GetCheckColServiceImpl.class, user);
    }

    @POST
    @Path("/getCheckCol")
    @Produces(MediaType.TEXT_PLAIN)
    public String getCheckCol(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        new BaseBean().writeLog("进入接口");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).getCheckCol(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }

}
