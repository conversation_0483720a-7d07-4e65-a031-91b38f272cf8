package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.utils.fileutils.FileUtils;
import com.engine.core.interceptor.CommandContext;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.toolbox.doc.DownloadManager;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class GetisHavingSamCmd extends AbstractCommonCommand<Map<String, Object>> {



    public GetisHavingSamCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String id = Util.null2String(params.get("fileid"));
        String colname = Util.null2String(params.get("colname"));
        String colvalue = Util.null2String(params.get("colvalue"));

        File file = null;
        try {
            file = new File(DownloadManager.getFileByImageFileId(Integer.parseInt(id)));
        } catch (IOException e) {
            result.put("ans",false);
            result.put("dec","获取文件出错"+e.getMessage());
            return result;
        }

        try {
            return FileUtils.searchFilecolSam4excel(colname,colvalue,file,result);
        } catch (Exception e) {
            result.put("ans",false);
            result.put("dec",e.getMessage());
            return result;
        }
    }

}
