package com.engine.contract.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.pojo.SamDataBean;
import com.engine.core.interceptor.CommandContext;
import com.weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

public class GetSamDataCmd extends AbstractCommonCommand<Map<String, Object>> {


    public GetSamDataCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        RecordSet recordSet = new RecordSet();
        String table = weaver.general.Util.null2String(params.get("table"));
        String colname = weaver.general.Util.null2String(params.get("colname"));
        String colamount = weaver.general.Util.null2String(params.get("colamount"));
        String where = weaver.general.Util.null2String(params.get("where"));
        String sql = "SELECT * FROM "+table+" "+where;
        recordSet.execute(sql);
        HashSet<SamDataBean> set = new HashSet<>();
        HashSet<SamDataBean> returnset = new HashSet<>();
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        while (recordSet.next()){
            String amount = Util.null2String(recordSet.getString(colamount)).trim();
            String name = Util.null2String(recordSet.getString(colname)).trim();
            if(!"".equals(name)&&!"".equals(amount)){
                SamDataBean samDataBean = new SamDataBean(name,amount);
                if(set.contains(samDataBean)){
                    if(!returnset.contains(samDataBean)){
                        JSONObject bean =new JSONObject();
                        bean.put("name", samDataBean.getName());
                        bean.put("amount", samDataBean.getAmount());
                        jsonArray.add(bean);
                        returnset.add(samDataBean);
                    }
                }else{
                    set.add(samDataBean);
                }
            }
        }
        jsonObject.put("samarray",jsonArray);
        result.put("status","1");
        result.put("jsondata",jsonObject);
        return result;
    }

}
