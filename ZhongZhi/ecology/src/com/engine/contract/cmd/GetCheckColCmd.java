package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.utils.formatcheck.Checkformat;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;


import java.util.HashMap;
import java.util.Map;

public class GetCheckColCmd  extends AbstractCommonCommand<Map<String, Object>> {


    public GetCheckColCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        new BaseBean().writeLog("进入cmd");
        Map<String, Object> result = new HashMap<>();
        String colname = Util.null2String(params.get("colname"));
        String value = Util.null2String(params.get("value"));
        String isnumber = Util.null2String(params.get("isnumber"));
        String table = Util.null2String(params.get("table"));
        String outprint = Util.null2String(params.get("outprint"));
        String outprintsam = Util.null2String(params.get("outprintsam"));
        String addnew = Util.null2String(params.get("addnew"));

        RecordSet recordSet = new RecordSet();
        new BaseBean().writeLog("获取参数 "+colname+" 和" + value);

        if("true".equals(isnumber)){
            for (int i = 0; i < value.length(); i++) {
                if(Checkformat.CheckASC(value.charAt(i),48,57)||
                        Checkformat.CheckASC(value.charAt(i),65,90)||
                        Checkformat.CheckASC(value.charAt(i),97,122)){
                }else {
                    result.put("ans",false);
                    result.put("dec",outprint);//统一社会信用代码/手机号格式不规范
                    return result;
                }
            }
        }
        String sql = "SELECT * FROM "+table+" WHERE "+colname+" = '"+value+"'";
        if("true".equals(addnew)){
            sql="SELECT * FROM "+table+" WHERE "+colname+" = '"+value+"' and  deleted <> '1'";
        }
        new BaseBean().writeLog("执行sql "+sql);
        recordSet.execute(sql);
        if(recordSet.next()){
            new BaseBean().writeLog("查找成功");
            result.put("ans",false);
            if("".equals(outprintsam)){
                result.put("dec",outprint);
            }else{
                result.put("dec",outprintsam);
            }
            return result;
        }
        result.put("ans",true);
        new BaseBean().writeLog("完成返回"+result.toString());
        return result;
    }
}
