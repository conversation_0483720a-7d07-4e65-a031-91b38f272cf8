package com.engine.contract.service.impl;

import com.engine.contract.cmd.GetSamDataCmd;
import com.engine.contract.service.GetSamDataService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.Map;

public class GetSamDataServiceImpl extends Service implements GetSamDataService {
    @Override
    public Map<String, Object> getSamData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSamDataCmd(params, user));
    }
}
