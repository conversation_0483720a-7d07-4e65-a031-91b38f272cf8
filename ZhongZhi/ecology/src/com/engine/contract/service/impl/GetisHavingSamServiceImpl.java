package com.engine.contract.service.impl;

import com.engine.contract.cmd.GetisHavingSamCmd;
import com.engine.contract.service.GetisHavingSamService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.Map;

public class GetisHavingSamServiceImpl extends Service implements GetisHavingSamService {
    @Override
    public Map<String, Object> getisHavingSamService(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetisHavingSamCmd(params, user));
    }
}
