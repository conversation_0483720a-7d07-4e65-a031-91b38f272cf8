package com.engine.contract.service.impl;

import com.engine.contract.cmd.GetCheckColCmd;
import com.engine.contract.service.GetCheckColService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.Map;

public class GetCheckColServiceImpl extends Service  implements GetCheckColService {
    @Override
    public Map<String, Object> getCheckCol(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetCheckColCmd(params, user));
    }
}
