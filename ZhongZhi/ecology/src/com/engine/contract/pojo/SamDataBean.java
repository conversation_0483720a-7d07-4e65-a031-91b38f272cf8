package com.engine.contract.pojo;

public class SamDataBean {

    private String name;

    private String amount;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public SamDataBean(String name, String amount) {
        this.name = name;
        this.amount = amount;
    }

    @Override
    public int hashCode() {
        int result = name.hashCode();
        result = 17 * result + amount.hashCode();
        return result;

    }

    @Override
    public boolean equals(Object obj) {
        if(!(obj instanceof SamDataBean)) {
            return false;
        }
        SamDataBean tatObj = (SamDataBean) obj;
        if (this == tatObj) {
            return true;
        }

        if (tatObj.amount.equals(this.amount)&&tatObj.name.equals(this.name)) {
            return true;
        } else {
            return false;
        }
    }
}
