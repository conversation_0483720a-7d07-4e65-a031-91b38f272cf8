package com.engine.contract.utils.reporttable;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.contract.utils.moduleutil.InsertModuleUtil4samtable;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;

/**
 * sql server
 */
public class Sql2Module {

    public static HashMap<String,Object> viewinsetmodule(String moduleid,String table,String view){
        JSONArray datajson = executeview(view);
        return JSONArrayinsetmodule(moduleid,table,datajson);
    }

    public static HashMap<String,Object> sqlinsetmodule(String moduleid,String table,String sql){
        JSONArray datajson = executesql(sql);
        return JSONArrayinsetmodule(moduleid,table,datajson);
    }

    public static HashMap<String,Object> JSONArrayinsetmodule(String moduleid,String table,JSONArray datajson){
        HashMap<String,Object> result = new HashMap<>();
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
        if("".equals(table)|| "".equals(moduleid)){
            result.put("flag","false");
            result.put("dec","module或table为空");
            return result;
        }
        ArrayList<String> list = getmodulecolnostand(table);
        baseBean.writeLog("显示基础数据-"+table,"-"+moduleid+"-");
        baseBean.writeLog("查询条数"+datajson.size());
        baseBean.writeLog("获取到字段数"+list.size());
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        try{
            recordSetTrans.execute("delete from "+table);
            for (int i = 0; i < datajson.size(); i++) {
                HashMap<String,String> map=new HashMap<>();
                JSONObject jsonObject = datajson.getJSONObject(i);
                for (String col : list) {
                    if (jsonObject.getString(col) != null && !"".equals(jsonObject.getString(col))) {
                        map.put(col, jsonObject.getString(col));
                    }
                }
                int mainid = InsertModuleUtil4samtable.insert(table,map,1,Integer.parseInt(moduleid),recordSetTrans);
                if(mainid<0){
                    result.put("flag",false);
                    recordSetTrans.execute("delete from "+table+" where id = "+(-1)*mainid);
                    result.put("dec","插入第"+i+"条时出现了错误停止插入"+jsonObject.toJSONString());
                    baseBean.writeLog("插入第"+i+"条时出现了错误停止插入"+jsonObject.toJSONString());
                    return result;
                }
            }
            result.put("flag","true");
        }catch (Exception e){
            result.put("flag","false");
            result.put("dec",e.getMessage());
            baseBean.writeLog(e.getMessage());
        }
        return result;
    }



    // sql server 适用
    public static ArrayList<String> getmodulecolnostand(String table){
        RecordSet recordSet = new RecordSet();
        recordSet.execute("select name from sys.columns where object_id=object_id('"+table+"')");
        ArrayList<String> list = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        set.add("id");
        set.add("MODEUUID");
        set.add("requestId");
        set.add("modedatamodifydatetime");
        set.add("modedatamodifier");
        set.add("modedatacreatetime");
        set.add("modedatacreatertype");
        set.add("modedatacreater");
        set.add("modedatacreatedate");
        set.add("formmodeid");
        while (recordSet.next()){
            String index =  recordSet.getString(1);
            if(!"".equals(Util.null2String(index))
                    &&!set.contains(index)){
                list.add(index);
            }
        }
        return list;
    }

    public static JSONArray executesql(String sql){
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(SDUtil.toDBC(sql));
        return QueryResultUtil.getJSONArrayList(recordSet);
    }

    public static JSONArray executeview(String view){
        String sql = "select * from "+view;
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(SDUtil.toDBC(sql));
        return QueryResultUtil.getJSONArrayList(recordSet);
    }
}
