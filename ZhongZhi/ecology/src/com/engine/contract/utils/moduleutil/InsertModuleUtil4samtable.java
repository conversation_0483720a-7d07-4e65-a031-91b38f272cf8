package com.engine.contract.utils.moduleutil;

import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.setup.ModeRightInfo;
import weaver.hrm.User;

import java.util.Map;


/**
 * 建模表工具列
 */
public class InsertModuleUtil4samtable {

    /**
     * 插入建模表一条数据
     *
     * @param table       表名uf_xxxx
     * @param insertField 插入字段的id数组
     * @param value       插入字段的id对应的值数组(和字段id顺序对应)
     * @param Create      创建人id
     * @param module      模块id
     *
     */

    public static int insert(String table, String[] insertField, String[] value, int Create, int module, RecordSetTrans recordSetTrans) {
//        ModeDataIdUpdate modeDataIdUpdate = new ModeDataIdUpdate();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        User user = new User(Create);   //用户id
// 初始化建模数据返回id
        int billid = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                table, module, user.getUID(), "1".equals(user.getLogintype()) ? 0 : 1, DateHelper.getCurrentDate(), DateHelper.getCurrentTime());
//将数据的其他内容更新到表中
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < insertField.length; i++) {
            stringBuffer.append(insertField[i]).append("=").append("?");
            if(i!=insertField.length-1){
                stringBuffer.append(", ");
            }
        }
        try {
            recordSetTrans.executeUpdate("update "+ table +" set "+stringBuffer.toString()+" where id= '"+billid+"'",value);
        } catch (Exception e) {
            return -1*billid;
        }
          modeRightInfo.setNewRight(true);
          modeRightInfo.editModeDataShare(Create, module,billid);//新建的时候添加共享

////添加对应数据的权限
//        new Thread(() -> {
//        }).start();

        return billid;
    }
    public static int insert(String table, String[] insertField, String[] value, int Create, int module) {
//        ModeDataIdUpdate modeDataIdUpdate = new ModeDataIdUpdate();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        User user = new User(Create);   //用户id
// 初始化建模数据返回id
        int billid = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                table, module, user.getUID(), "1".equals(user.getLogintype()) ? 0 : 1, DateHelper.getCurrentDate(), DateHelper.getCurrentTime());
//将数据的其他内容更新到表中
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < insertField.length; i++) {
            stringBuffer.append(insertField[i]).append("=").append("?");
            if(i!=insertField.length-1){
                stringBuffer.append(", ");
            }
        }
        try {
            RecordSet recordSet = new RecordSet();
            recordSet.executeUpdate("update "+ table +" set "+stringBuffer.toString()+" where id="+billid,value);
        } catch (Exception e) {
            return -1*billid;
        }
//添加对应数据的权限
            modeRightInfo.setNewRight(true);
            modeRightInfo.editModeDataShare(Create, module,billid);//新建的时候添加共享

//        new Thread(() -> {
//        }).start();

        return billid;
    }

    public static int insert(String table, Map<String,String> map, int Create, int module, RecordSetTrans recordSetTrans){
            
       String[] col =new String[map.size()];     
       String[] value = new String[map.size()];
       int i = 0;
        for (Map.Entry<String,String> entry: map.entrySet()) {
            col[i]=entry.getKey();
            value[i] = entry.getValue();
            i++;
        }
       return insert(table,col,value,Create,module,recordSetTrans);
    }

    public static int insertmodule(String table, Map<String,String> map, int Create, int module){

        String[] col =new String[map.size()];
        String[] value = new String[map.size()];
        int i = 0;
        for (Map.Entry<String,String> entry: map.entrySet()) {
            col[i]=entry.getKey();
            value[i] = entry.getValue();
            i++;
        }
        return insert(table,col,value,Create,module);
    }

    public static int insert(String table, Map<String,String> map, int Create, int module){

        String[] col =new String[map.size()];
        String[] value = new String[map.size()];
        int i = 0;
        for (Map.Entry<String,String> entry: map.entrySet()) {
            col[i]=entry.getKey();
            value[i] = entry.getValue();
            i++;
        }
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        return insert(table,col,value,Create,module,recordSetTrans);
    }

}
