package com.engine.contract.utils.fileutils;
import com.engine.contract.utils.formatcheck.Checkformat;
import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import weaver.conn.RecordSet;

import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.Map;

public class FileUtils {

    public static Map<String, Object> searchFilecolSam4excel(String cols,String name, File file,Map<String, Object> result) throws IOException, BiffException {
       String filename =  file.getName();
       String [] filenamearray=  filename.split("\\.");
       if(!"xls".equals(filenamearray[filenamearray.length-1])){
           result.put("dec","检测不是excel文件");
           result.put("ans",false);
           return result;
       }
        RecordSet recordSet = new RecordSet();
        Workbook workBook = Workbook.getWorkbook(file);
        //2、获取工作表中的第一页（sheet1）
        Sheet sheet1 = workBook.getSheet(0);
        HashSet<String> set = new HashSet<>();
        int colindex = -1;
        for (int i = 0; i < sheet1.getColumns(); i++) {
            Cell cell  = sheet1.getCell(i, 0);
            String value = cell.getContents().trim();
            name = name.trim();
            if(value.equals(name)){
                colindex = i;
            }
        }
        if(colindex == -1){
            result.put("dec","未获取到配置字段");
            result.put("ans",false);
            return result;
        }
        for(int i = 1; i < sheet1.getRows(); i++) {
                Cell cell = sheet1.getCell(colindex, i);
                for (int j = 0; j < cell.getContents().length(); j++) {
                    if(Checkformat.CheckASC(cell.getContents().charAt(j),48,57)||
                            Checkformat.CheckASC(cell.getContents().charAt(j),65,90)||
                            Checkformat.CheckASC(cell.getContents().charAt(j),97,122)){
                    }else {
                        result.put("ans",false);
                        result.put("dec","统一社会信用代码/手机号格式不规范");
                        return result;
                    }
                }
                if(set.contains(cell.getContents())){
                    result.put("dec","excel表中存在字段重复");
                    result.put("ans",false);
                    return result;
                }
                recordSet.execute("SELECT * FROM crm_customerinfo WHERE deleted <> '1' and "+cols+" = '"+cell.getContents()+"'");
                if(recordSet.next()){
                    result.put("dec","当前客户中已存在重复统一社会信用代码");
                    result.put("ans",false);
                    return result;
                }
               set.add(cell.getContents());
        }
        result.put("ans",true);
        return result;
    }

}
