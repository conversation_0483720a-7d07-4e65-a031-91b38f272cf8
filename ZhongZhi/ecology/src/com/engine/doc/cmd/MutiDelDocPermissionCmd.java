package com.engine.doc.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.doc.bean.DocPermissonChange;
import com.engine.doc.util.DocPermissionUtil;
import com.engine.parent.common.constant.CommonCst;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * FileName: MutiDelDocPermissionCmd.java
 * 批量删除文档权限
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/14
 */
public class MutiDelDocPermissionCmd extends AbstractCommonCommand<Map<String, Object>> {
    public MutiDelDocPermissionCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        BaseBean bb = new BaseBean();
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog(this.getClass().getName() + "---START");
        String userIds = Util.null2String(params.get("userIds"));
        result.put("status", 1);
        result.put("msg", "执行成功");
        try {
            RecordSet rs = new RecordSet();
            StringBuilder sb = new StringBuilder();
            //删除文档共享权限
            if (StringUtils.isNotBlank(userIds)) {
                sb.append(" delete from docshare where 1=1 ");
                String[] ary = userIds.split(CommonCst.COMMA_EN);
                sb.append(" and ( ");
                for (int i = 0; i < ary.length; i++) {
                    if (i > 0) {
                        sb.append(" or ");
                    }
                    sb.append(" (userid = ").append(ary[i]).append(") ");
                }
                sb.append("  ) ");
                bb.writeLog("del docshare sql:" + sb);
                rs.executeUpdate(sb.toString());
                //执行更新建模数据
                sb = new StringBuilder();
                sb.append(" update uf_wdzlqd set zlsqqx = 1 where 1=1 ");
                sb.append(" and ( ");
                for (int i = 0; i < ary.length; i++) {
                    if (i > 0) {
                        sb.append(" or ");
                    }
                    sb.append(" (ygxm = ").append(ary[i]).append(") ");
                }
                sb.append("  ) ");
                bb.writeLog("update uf_wdzlqd sql:" + sb);
                rs.executeUpdate(sb.toString());
                //插入变更日志
                sb = new StringBuilder();
                sb.append(" select a.ygxm,a.wdmc, ");
                sb.append(" b.departmentid,b.subcompanyid1, ");
                sb.append(" c.xgmk,d.projectid ");
                sb.append("  from uf_wdzlqd a ");
                sb.append(" left join hrmresource b on (a.ygxm = b.id)  ");
                sb.append(" left join ");
                sb.append(" (select id,xgmk,xmm from cus_fielddata ");
                sb.append("  where scope='DocCustomFieldBySecCategory') c on (a.wdmc = c.id)");
                sb.append(" left join docdetail d on (d.id = a.wdmc)  ");
                sb.append(" where 1=1 ");
                sb.append(" and ( ");
                for (int i = 0; i < ary.length; i++) {
                    if (i > 0) {
                        sb.append(" or ");
                    }
                    sb.append(" (a.ygxm = ").append(ary[i]).append(") ");
                }
                sb.append("  ) ");
                sb.append(" ");
                bb.writeLog("query uf_wdzlqd sql:" + sb);
                rs.executeQuery(sb.toString());
                while (rs.next()) {
                    //插入变更日志
                    DocPermissonChange bean = new DocPermissonChange();
                    bean.setYgxm(Util.null2String(rs.getString("ygxm")));
                    bean.setWdmc(Util.null2String(rs.getString("wdmc")));
                    bean.setXgxm(Util.null2String(rs.getString("projectid")));
                    bean.setXgmk(Util.null2String(rs.getString("xgmk")));
                    bean.setBdsj(TimeUtil.getToday());
                    bean.setBdlx("2");
                    bean.setSzbm(Util.null2String(rs.getString("departmentid")));
                    bean.setSzzx(Util.null2String(rs.getString("subcompanyid1")));
                    DocPermissionUtil.addPermissionLog(bean);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expt:" + e.getMessage());
            result.put("status", -1);
            result.put("msg", e.getMessage());
        }
        return result;
    }
}
