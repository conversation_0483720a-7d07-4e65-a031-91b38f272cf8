package com.engine.doc.util;

import com.engine.doc.bean.DocPermissonChange;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import com.weaver.general.BaseBean;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.List;

/**
 * FileName: DocPermissionUtil.java
 * 文档权限util
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/14
 */
public class DocPermissionUtil {
    /**
     * 添加权限变更日志
     *
     * @param bean
     */
    public static void addPermissionLog(DocPermissonChange bean) {
        BaseBean bb = new BaseBean();
        bb.writeLog("DocPermissionUtil-addPermissionLog---START");
        try {
            String tableName = Util.null2String(bb.getPropValue("ciic_module", "DocPermissionLog_moduleName"));
            int moduleId = Util.getIntValue(bb.getPropValue("ciic_module", "DocPermissionLog_moduleId"));
            int creatId = SDUtil.getSystemMangerByLoginId();


            List<String> insertFields = new ArrayList<>();
            insertFields.add("ygxm");
            insertFields.add("wdmc");
            insertFields.add("xgxm");
            insertFields.add("xgmk");
            insertFields.add("bdsj");
            insertFields.add("bdlx");
            insertFields.add("szbm");
            insertFields.add("szzx");

            List<Object> values = new ArrayList<>();
            values.add(bean.getYgxm());
            values.add(bean.getWdmc());
            values.add(bean.getXgxm());
            values.add(bean.getXgmk());
            values.add(bean.getBdsj());
            values.add(bean.getBdlx());
            values.add(bean.getSzbm());
            values.add(bean.getSzzx());
            InsertModuleUtil.ModuleInsert(tableName, insertFields, values, creatId, moduleId, null);
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expt:" + e.getMessage());
            bb.writeLog("DocPermissionUtil-addPermissionLog FAIL END");
        }
        bb.writeLog("DocPermissionUtil-addPermissionLog SUCCESS END");
    }
}
