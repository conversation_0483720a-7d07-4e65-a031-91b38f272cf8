package com.engine.doc.util;

import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;

public class Calculator {
    // 存储函数和变量的名称及其对应的值
    private Map<String, BigDecimal> variableMap = new HashMap<>();
    private Map<String, Function> functionMap = new HashMap<>();
    private Map<String, Queue<Integer>> functionargslen = new HashMap<>();

    // 定义一个函数
    public interface Function {
        BigDecimal eval(BigDecimal... args);
    }

    public Calculator() {
        // 添加内置函数
        functionMap.put("sin", args -> {
            if (args.length != 1) {
                throw new IllegalArgumentException("sin function takes 1 argument");
            }
            return BigDecimal.valueOf(Math.sin(args[0].doubleValue()));
        });
        functionMap.put("cos", args -> {
            if (args.length != 1) {
                throw new IllegalArgumentException("cos function takes 1 argument");
            }
            return BigDecimal.valueOf(Math.cos(args[0].doubleValue()));
        });
        functionMap.put("pow", args -> {
            if (args.length != 2) {
                throw new IllegalArgumentException("pow function takes 2 arguments");
            }
            return args[0].pow(args[1].intValue());
        });
    }

    // 解析表达式中的函数和变量
    private BigDecimal resolveIdentifier(String identifier, Stack<BigDecimal> stack) {
        if (variableMap.containsKey(identifier)) {
            return variableMap.get(identifier);
        }
        if (functionMap.containsKey(identifier)) {
            Queue<Integer> funclen = this.functionargslen.get(identifier);
            if (funclen.isEmpty()) {
                throw new IllegalArgumentException("Error functionlenStack get");
            }
            Integer len = funclen.poll();
            if (len != null) {
                BigDecimal[] args = new BigDecimal[len];
                for (int i = args.length - 1; i >= 0; i--) {
                    if (stack.isEmpty()) {
                        throw new IllegalArgumentException("Error args get");
                    }
                    args[i] = stack.pop();
                }
                return functionMap.get(identifier).eval(args);
            } else {
                throw new IllegalArgumentException("Error functionargs get");
            }
        }
        throw new IllegalArgumentException("Undefined identifier: " + identifier);
    }

    // 计算中缀表达式的值
    private BigDecimal evaluateInfix(List<String> infix) {
        // 转换为后缀表达式
        List<String> postfix = infixToPostfix(infix);
        // 计算后缀表达式的值
        Stack<BigDecimal> stack = new Stack<>();
        for (String token : postfix) {
            if (isNumber(token)) {
                stack.push(new BigDecimal(token));
            } else if (isOperator(token)) {
                BigDecimal operand2 = stack.pop();
                BigDecimal operand1 = stack.pop();
                stack.push(applyOperator(token, operand1, operand2));
            } else {
                stack.push(resolveIdentifier(token, stack));
            }
        }
        return stack.pop();
    }

    // 转换为后缀表达式
    private List<String> infixToPostfix(List<String> infix) {
        List<String> postfix = new ArrayList<>();
        Stack<String> stack = new Stack<>();
        Stack<Integer> paren = new Stack<>();
        for (int index = 0; index < infix.size(); index++) {
            String token = infix.get(index);
            if (isComma(token)) {
                stack.push(token);
                if (!paren.isEmpty()) {
                    Integer len = paren.pop();
                    paren.push(len + 1);
                }
            } else if (isNumber(token)) {
                postfix.add(token);
                if (!paren.isEmpty()) {
                    Integer len = paren.pop();
                    if (len > 1) {
                        paren.push(len);
                    } else {
                        paren.push(1);
                    }
                }
            } else if (isLeftParen(token)) {
                stack.push(token);
                paren.push(0);
            } else if (isRightParen(token)) {
                if (paren.isEmpty()) {
                    throw new IllegalArgumentException("leftparen error ");
                }
                Integer length = paren.pop();
//                if(!paren.isEmpty()){
//                    Integer lastnum =  paren.pop();
//                    paren.push(lastnum-length);
//                }
                while (!stack.isEmpty() && !isLeftParen(stack.peek())) {
//                    if(isOperator(stack.peek())){
//                    }
                    if (!isComma(stack.peek())) {
                        postfix.add(stack.pop());
                    } else {
                        stack.pop();
                    }
                }
                if (!stack.isEmpty() && isLeftParen(stack.peek())) {
                    stack.pop();
                } else {
                    throw new IllegalArgumentException("Mismatched parentheses");
                }
                if (!stack.isEmpty() && isFunc(stack.peek())) {
                    String funcname = stack.pop();
                    postfix.add(funcname);
                    if (!this.functionargslen.containsKey(funcname)) {
                        Queue<Integer> funclen = new ArrayDeque<>();
                        funclen.offer(length);
                        this.functionargslen.put(funcname, funclen);
                    } else {
                        Queue<Integer> funclen = this.functionargslen.get(funcname);
                        funclen.offer(length);
                    }
                }
            } else if (isOperator(token)) {
                while (!stack.isEmpty() && isOperator(stack.peek()) && hasHigherPrecedence(stack.peek(), token)) {
                    postfix.add(stack.pop());
                }
                stack.push(token);
            } else if (isFunc(token)) {
                stack.push(token);
                if (!paren.isEmpty()) {
                    Integer len = paren.pop();
                    if (len > 1) {
                        paren.push(len);
                    } else {
                        paren.push(1);
                    }
                }
            } else {
                postfix.add(token);
            }
        }
        while (!stack.isEmpty()) {
            String token = stack.pop();
            if (isLeftParen(token) || isRightParen(token)) {
                throw new IllegalArgumentException("Mismatched parentheses");
            }
            postfix.add(token);
        }
        return postfix;
    }

    // 应用运算符
    private BigDecimal applyOperator(String operator, BigDecimal operand1, BigDecimal operand2) {
        switch (operator) {
            case "+":
                return operand1.add(operand2);
            case "-":
                return operand1.subtract(operand2);
            case "*":
                return operand1.multiply(operand2);
            case "/":
                if (operand2.compareTo(BigDecimal.ZERO) == 0) {
                    return new BigDecimal("0.00"); // 更改计算机除法逻辑
                }
                return operand1.divide(operand2, 4, RoundingMode.DOWN);
            case "%":
                return operand1.remainder(operand2);
            default:
                throw new IllegalArgumentException("Invalid operator: " + operator);
        }
    }

    // 检查是否为数字
    private boolean isNumber(String token) {
        Pattern pattern = Pattern.compile("^-?\\d+(\\.\\d+)?$");
        return pattern.matcher(token).matches();
    }

    // 检查是否为运算符
    private boolean isOperator(String token) {
        return token.equals("+") || token.equals("-") || token.equals("*") || token.equals("/") || token.equals("%");
    }

    // 检查是否为左括号
    private boolean isLeftParen(String token) {
        return token.equals("(");
    }


    // 检查是否为逗号
    private boolean isComma(String token) {
        return token.equals(",");
    }

    // 检查是否为函数
    private boolean isFunc(String token) {
        return this.functionMap.containsKey(token);
    }


    // 检查是否为右括号
    private boolean isRightParen(String token) {
        return token.equals(")");
    }

    // 比较运算符的优先级
    private int comparePrecedence(String operator1, String operator2) {
        if (operator1.equals("*") || operator1.equals("/") || operator1.equals("%")) {
            if (operator2.equals("+") || operator2.equals("-")) {
                return 1;
            } else {
                return 0;
            }
        } else {
            if (operator2.equals("*") || operator2.equals("/") || operator2.equals("%")) {
                return -1;
            } else {
                return 0;
            }
        }
    }

    // 检查运算符的优先级是否高于栈顶运算符
    private boolean hasHigherPrecedence(String operator1, String operator2) {
        return comparePrecedence(operator1, operator2) > 0;
    }

    // 计算表达式的值
    public BigDecimal evaluate(String expression) {
        List<String> infix = tokenize(expression);
        return evaluateInfix(infix);
    }

    // 将表达式分解为符号和数字的列表
    private List<String> tokenize(String expression) {
        List<String> tokens = new ArrayList<>();
        String[] parts = expression.split("(?<=op)|(?=op)".replace("op", "[,+\\-*/()%]"));
        for (String part : parts) {
            if (!isBlank(part)) {
                tokens.add(part.trim());
            }
        }
        return tokens;
    }

    // 设置变量的值
    public void setVariable(String name, BigDecimal value) {
        variableMap.put(name, value);
    }

    // 获取变量的值
    public BigDecimal getVariable(String name) {
        if (!variableMap.containsKey(name)) {
            throw new IllegalArgumentException("Undefined variable: " + name);
        }
        return variableMap.get(name);
    }

    public boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static boolean getBigDecimal(String value) {
        return NumberUtils.isParsable(value);
    }

    public void setFunctionMap(String key, Function func) {
        this.functionMap.put(key, func);
    }

}