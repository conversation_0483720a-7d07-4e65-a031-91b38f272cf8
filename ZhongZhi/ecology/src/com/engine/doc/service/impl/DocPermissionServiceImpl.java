package com.engine.doc.service.impl;

import com.engine.core.impl.Service;
import com.engine.doc.cmd.MutiDelDocPermissionCmd;
import com.engine.doc.service.DocPermissionService;
import weaver.hrm.User;

import java.util.Map;

public class DocPermissionServiceImpl extends Service implements DocPermissionService {
    @Override
    public Map<String, Object> mutiDel(Map<String, Object> params, User user) {
        return commandExecutor.execute(new MutiDelDocPermissionCmd(params, user));
    }
}
