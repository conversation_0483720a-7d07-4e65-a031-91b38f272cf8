package com.engine.doc.action;

import com.api.doc.search.service.DocShareService;
import com.engine.parent.common.constant.CommonCst;
import weaver.conn.RecordSet;
import weaver.docs.docs.DocViewer;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * FileName: UpdateDocAllPermissionAction.java
 * 为该文档赋予所有人的查看可下载权限
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/11
 */
public class UpdateDocAllPermissionAction extends BaseBean implements Action {

    /**
     * 文档字段名
     */
    private String docField;

    /**
     * 选择框字段
     */
    private String permissionTypeField;

    @Override

    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //请求id
        String requestId = requestInfo.getRequestid();
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();

        //是否为单据(1为是)
        int isBill = rm.getIsbill();
        //获取数据库主表名(如果 不为1，流程数据是存在"workflow_form"表中)
        String tableName = isBill == 1 ? rm.getBillTableName() : "workflow_form";
        //获取主表信息
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        try {
            String docId, permissionType;
            StringBuilder allDocId = new StringBuilder();
            RecordSet rs = new RecordSet();
            //执行查询，通过?占位符防止sql注入问题
            rs.executeQuery("select d.* from " + tableName + "_dt1 d " +
                    " left join " + tableName + " m on (d.mainid = m.id) where m.requestid = ? ", requestId);
            while (rs.next()) {
                docId = Util.null2String(rs.getString(docField));
                permissionType = Util.null2String(rs.getString(permissionTypeField));
                //判断权限字段
                if ("0".equals(permissionType)) {
                    allDocId.append(docId).append(",");
                }
            }
            writeLog("allDocId:" + allDocId);
            if (allDocId.length() > 0) {
                //给文档赋予下载权限
                givePermission(allDocId.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            writeLog("catch exception:" + e.getMessage());
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent("catch exception:" + e.getMessage());
            writeLog("FAIL END");
            return Action.FAILURE_AND_CONTINUE;
        }
        writeLog("SUCCESS END");
        return Action.SUCCESS;
    }

    /**
     * 赋予文档权限
     */
    private void givePermission(String allDocId) throws Exception {
        RecordSet rs = new RecordSet();
        User user = new User(1);
        //权限（1-查看；2-编辑，3-完全控制）
        int seclevel = 1;
        //下载
        boolean download = true;

        DocShareService ds = new DocShareService();
        for (String s : allDocId.split(CommonCst.COMMA_EN)) {
            //查看是否已经有所有人的权限
            rs.executeQuery("select id,downloadlevel from docshare " +
                    " where docid = ? and sharetype = 5", s);
            if (!rs.next()) {
                //插入所有人的权限
                ds.addShareForAll(user, Integer.parseInt(s), seclevel, 0, 100, download);
                DocViewer dv = new DocViewer();
                dv.setDocShareByDoc(s);
            }
        }
    }
    

    public String getDocField() {
        return docField;
    }

    public void setDocField(String docField) {
        this.docField = docField;
    }

    public String getPermissionTypeField() {
        return permissionTypeField;
    }

    public void setPermissionTypeField(String permissionTypeField) {
        this.permissionTypeField = permissionTypeField;
    }
}
