package com.engine.doc.action;

import com.api.doc.search.service.DocShareService;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.module.util.InsertModuleUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.docs.docs.DocViewer;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FileName: UpdateDocDownPermissionAction.java
 * 插入或更新文档下载权限信息
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/11
 */
public class UpdateDocDownPermissionAction extends BaseBean implements Action {
    /**
     * 赋予权限的有效天数
     */
    private String permissonDays;
    /**
     * 申请人字段名
     */
    private String sqrField;
    /**
     * 文档字段名
     */
    private String docField;
    /**
     * 下载权限建模表名
     */
    private String modeTableName;
    /**
     * 下载权限建模id
     */
    private String modeId;
    //明细的过滤字段
    private String filterField;
    //明细的过滤字段对应的过滤值
    private String filterValue;

    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //请求id
        String requestId = requestInfo.getRequestid();
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();

        //是否为单据(1为是)
        int isBill = rm.getIsbill();
        //获取数据库主表名(如果 不为1，流程数据是存在"workflow_form"表中)
        String tableName = isBill == 1 ? rm.getBillTableName() : "workflow_form";
        //获取主表信息
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        try {
            int sqr = Integer.parseInt(map.get(sqrField));
            String docId;
            StringBuilder allDocId = new StringBuilder();
            RecordSet rs = new RecordSet();
            //执行查询，通过?占位符防止sql注入问题

            String sql = "select d.* from " + tableName + "_dt1 d " +
                    " left join " + tableName + " m on (d.mainid = m.id) where m.requestid = ? " +
                    " and d.qrff = '0' ";
            if (filterField != null && filterValue != null) {
                sql += " and d." + filterField + " in(" + filterValue + ")";
            }
            writeLog("sql:" + sql);
            writeLog("docField:" + docField);
            rs.executeQuery(sql, requestId);
            while (rs.next()) {
                docId = Util.null2String(rs.getString(docField));
                allDocId.append(docId).append(",");
            }
            writeLog("allDocId:" + allDocId);
            if (allDocId.length() > 0) {
                //给文档赋予下载权限
                givePermission(allDocId.toString(), sqr);
                //需求变更，建模数据由流程转建模实现
//                //删除建模数据
//                delModeData(allDocId.toString(), sqr);
//                //插入建模数据
//                insertData(allDocId.toString(), sqr);
            }
        } catch (Exception e) {
            e.printStackTrace();
            writeLog("catch exception:" + e.getMessage());
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent("catch exception:" + e.getMessage());
            writeLog("FAIL END");
            return Action.FAILURE_AND_CONTINUE;
        }
        writeLog("SUCCESS END");
        return Action.SUCCESS;
    }

    /**
     * 赋予文档权限
     */
    private void givePermission(String allDocId, int sqr) throws Exception {
        RecordSet rs = new RecordSet();
        User user = new User(sqr);
        //权限（1-查看；2-编辑，3-完全控制）
        int seclevel = 1;
        //下载
        boolean download = true;
        StringBuilder shareIds = new StringBuilder();
        DocShareService ds = new DocShareService();
        for (String s : allDocId.split(CommonCst.COMMA_EN)) {

            //查看是否已经有下载权限
            rs.executeQuery("select id,downloadlevel from docshare " +
                    " where docid = ? and userid = ? and downloadlevel = '1' ", s, sqr);
            while (rs.next()) {
                shareIds.append(Util.null2String(rs.getString("id"))).append(CommonCst.COMMA_EN);
            }
            //先删除已经的有的权限
            writeLog("shareIds:" + shareIds);
            if (StringUtils.isNotBlank(shareIds.toString())) {
                String shareIdsStr = shareIds.substring(0, shareIds.length() - 1);
                ds.deleteShare(user, Integer.parseInt(s), shareIdsStr);
            }
            //插入新的权限
            ds.addShareForUser(user, Integer.parseInt(s), String.valueOf(sqr), seclevel, download);
            DocViewer dv = new DocViewer();
            dv.setDocShareByDoc(s);
        }
    }

    /**
     * 删除建模数据
     *
     * @param allDocId
     * @param sqr
     */
    private void delModeData(String allDocId, int sqr) {
        StringBuilder sb = new StringBuilder();
        sb.append(" delete from ").append(modeTableName);
        sb.append(" where yyr = ? ");

        String[] array = allDocId.split(CommonCst.COMMA_EN);
        sb.append(" and ( ");
        for (int i = 0; i < array.length; i++) {
            if (i > 0) {
                sb.append(" or ");
            }
            sb.append(" (wd = ").append(array[i]).append(") ");
        }
        sb.append("  ) ");
        writeLog("delSql" + sb);
        RecordSet rs = new RecordSet();
        rs.executeUpdate(sb.toString(), sqr);
    }

    /**
     * 插入建模数据
     *
     * @param allDocId
     * @param sqr
     * @throws Exception
     */
    private void insertData(String allDocId, int sqr) throws Exception {
        String today = TimeUtil.getToday();
        String lastDay = TimeUtil.dateAdd(today, Integer.parseInt(permissonDays));
        List<String> insertFields = new ArrayList<>();
        insertFields.add("yyr");
        insertFields.add("wd");
        insertFields.add("fyrq");
        insertFields.add("dqrq");
        List<Object> values;
        String[] array = allDocId.split(CommonCst.COMMA_EN);
        for (String s : array) {
            values = new ArrayList<>();
            values.add(sqr);
            values.add(s);
            values.add(today);
            values.add(lastDay);
            InsertModuleUtil.ModuleInsert(modeTableName, insertFields, values, sqr, Integer.parseInt(modeId), null);
        }
    }

    public String getPermissonDays() {
        return permissonDays;
    }

    public void setPermissonDays(String permissonDays) {
        this.permissonDays = permissonDays;
    }

    public String getSqrField() {
        return sqrField;
    }

    public void setSqrField(String sqrField) {
        this.sqrField = sqrField;
    }

    public String getDocField() {
        return docField;
    }

    public void setDocField(String docField) {
        this.docField = docField;
    }

    public String getModeTableName() {
        return modeTableName;
    }

    public void setModeTableName(String modeTableName) {
        this.modeTableName = modeTableName;
    }

    public String getModeId() {
        return modeId;
    }

    public void setModeId(String modeId) {
        this.modeId = modeId;
    }

    public String getFilterField() {
        return filterField;
    }

    public void setFilterField(String filterField) {
        this.filterField = filterField;
    }

    public String getFilterValue() {
        return filterValue;
    }

    public void setFilterValue(String filterValue) {
        this.filterValue = filterValue;
    }
}
