package com.engine.doc.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.engine.contract.utils.moduleutil.InsertModuleUtil4samtable;
import com.engine.contract.utils.reporttable.Sql2Module;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.workflow.request.RequestManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InsertAndUpdateSplitAmountAction extends BaseBean implements Action {


    //下拉框类型字段选择
    private String selecttype;
    //对应关系json数据表
    private String tables;
    //第几明细表
    private String detailid;
    //复制过去的建模表字段
    private String copycol;
    //复制过去的明细表字段
    private String detailcol;

    private String copycol2;
    //复制过去的明细表字段
    private String detailcol2;
    @Override
    public String execute(RequestInfo requestInfo) {
            RequestManager rm = requestInfo.getRequestManager();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        String SelectType = Util.null2String(map.get(selecttype));
        // 获取所有明细表
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        DetailTable dt;
        JSONObject tablemap;
        try {
            tablemap = JSONObject.parseObject(tables);
            dt = detailtable[Integer.parseInt(detailid)];
        }catch (Exception e){
            rm.setMessagecontent("配置文件不正确" + e.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
        ArrayList<HashMap<String,String>> detaillist = new ArrayList<>();
        Row[] s = dt.getRow();
        for (Row r : s) {
            Cell[] c = r.getCell();
            HashMap<String, String> rowmap = new HashMap<>();
            for (Cell c1 : c) {
                rowmap.put(c1.getName(),c1.getValue());
            }
            detaillist.add(rowmap);
        }
        if(!"".equals(Util.null2String(tablemap.getString(SelectType)))){
            JSONObject jsonObject = tablemap.getJSONObject(SelectType);
            String table = jsonObject.getString("table");
            String moduleid =  jsonObject.getString("moduleid");
            String selectcol =  jsonObject.getString("selectcol");
            ArrayList<String> list =  Sql2Module.getmodulecolnostand(table);
            String cols = parseListToStr2(list);
            JSONArray result =  Sql2Module.executesql("SELECT "+cols+" from "+table+" where id = '"+ map.get(selectcol)+"'");
            Map<String, String> copmap = new HashMap<>();
            for (int i = 0; i < result.size(); i++) {
                JSONObject obj =result.getJSONObject(i);
                copmap = JSONObject.parseObject(obj.toJSONString(), new TypeReference<Map<String, String>>(){});
            }
            writeLog("复制数据：" +copmap);
            boolean ret = true;
            for (HashMap<String, String> ans : detaillist) {
                copmap.put(copycol, Util.null2String(ans.get(detailcol)));
                copmap.put(copycol2, Util.null2String(ans.get(detailcol2)));
                    int flag = InsertModuleUtil4samtable.insert(table, copmap, 1, Integer.parseInt(moduleid));
                if (flag < 0) {
                    ret = false;
                }
            }
            if(ret){
                RecordSet deletesql = new RecordSet();
                writeLog("delete sql :"+"DELETE FROM "+ table +" where id = '"+map.get(selectcol)+"'");
                deletesql.execute("DELETE FROM "+ table +" where id = '"+map.get(selectcol)+"'");
            }else{
                rm.setMessagecontent("插入数据出错");
                return Action.FAILURE_AND_CONTINUE;
            }
        }
        return Action.SUCCESS;
    }

    public <T> String parseListToStr2(List<T> list){
        StringBuilder sb = new StringBuilder();
        list.forEach(str->{
            sb.append(str).append(",");
        });
        sb.deleteCharAt(sb.length()-1);
        return sb.toString();
    }

    public String getSelecttype() {
        return selecttype;
    }

    public void setSelecttype(String selecttype) {
        this.selecttype = selecttype;
    }

    public String getTables() {
        return tables;
    }

    public void setTables(String tables) {
        this.tables = tables;
    }

    public String getDetailid() {
        return detailid;
    }

    public void setDetailid(String detailid) {
        this.detailid = detailid;
    }

    public String getCopycol() {
        return copycol;
    }

    public void setCopycol(String copycol) {
        this.copycol = copycol;
    }

    public String getDetailcol() {
        return detailcol;
    }

    public void setDetailcol(String detailcol) {
        this.detailcol = detailcol;
    }

    public String getCopycol2() {
        return copycol2;
    }

    public void setCopycol2(String copycol2) {
        this.copycol2 = copycol2;
    }

    public String getDetailcol2() {
        return detailcol2;
    }

    public void setDetailcol2(String detailcol2) {
        this.detailcol2 = detailcol2;
    }
}
