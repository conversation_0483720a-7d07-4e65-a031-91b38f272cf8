package com.engine.doc.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.doc.service.DocPermissionService;
import com.engine.doc.service.impl.DocPermissionServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * FileName: DocPermissionWeb.java
 * 文档权限接口
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/14
 */
public class DocPermissionWeb {

    private DocPermissionService getService(User user) {
        return ServiceUtil.getService(DocPermissionServiceImpl.class, user);
    }

    /**
     * 批量删除文档权限
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/mutiDel")
    @Produces(MediaType.TEXT_PLAIN)
    public String mutiDel(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).mutiDel(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }

}
