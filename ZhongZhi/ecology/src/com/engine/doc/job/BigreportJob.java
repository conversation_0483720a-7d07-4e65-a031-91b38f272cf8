package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.contract.utils.moduleutil.InsertModuleUtil4samtable;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;

public class BigreportJob extends BaseCronJob {

    private BaseBean baseBean;

    @Override
    public void execute() {
        baseBean= new BaseBean();
        baseBean.writeLog("start--");
        String table = Util.null2String(baseBean.getPropValue("interface4module","TableName"));
        String moduleid = Util.null2String(baseBean.getPropValue("interface4module","ModuleId"));
        if("".equals(table)|| "".equals(moduleid)){
            baseBean.writeLog("配置出错");
            return;
        }
        ArrayList<String> list = getmodulecolnostand(table);
        JSONArray datajson = executesql();
        baseBean.writeLog("显示基础数据-"+table,"-"+moduleid+"-");
        baseBean.writeLog("查询条数"+datajson.size());
        baseBean.writeLog("获取到字段数"+list.size());
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        try{
            recordSetTrans.execute("delete from "+table);
            for (int i = 0; i < datajson.size(); i++) {
                HashMap<String,String> map=new HashMap<>();
                JSONObject jsonObject = datajson.getJSONObject(i);
                for (String col : list) {
                    if (jsonObject.getString(col) != null && !"".equals(jsonObject.getString(col))) {
                        map.put(col, jsonObject.getString(col));
                    }
                }
                int mainid = InsertModuleUtil4samtable.insert(table,map,1,Integer.parseInt(moduleid),recordSetTrans);
                if(mainid<0){
//                    new RecordSet().execute("delete from "+table+" where id = '"+(-1)*mainid+"'");
                    baseBean.writeLog("插入第"+i+"条时出现了错误停止插入"+jsonObject.toJSONString());
                    break;
                }
            }
        }catch (Exception e){
            baseBean.writeLog(e.getMessage());
        }
    }

    // sql server 适用
    public ArrayList<String> getmodulecolnostand(String table){
        RecordSet recordSet = new RecordSet();
        recordSet.execute("select name from sys.columns where object_id=object_id('"+table+"')");
        ArrayList<String> list = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        set.add("id");
        set.add("MODEUUID");
        set.add("requestId");
        set.add("modedatamodifydatetime");
        set.add("modedatamodifier");
        set.add("modedatacreatetime");
        set.add("modedatacreatertype");
        set.add("modedatacreater");
        set.add("modedatacreatedate");
        set.add("formmodeid");
        while (recordSet.next()){
            String index =  recordSet.getString(1);
            if(!"".equals(Util.null2String(index))
                    &&!set.contains(index)){
                list.add(index);
            }
        }
        return list;
    }

    public JSONArray executesql(){
        String sql = "select * from View_Bigreport";
        RecordSet recordSet = new RecordSet();
        recordSet.executeQuery(SDUtil.toDBC(sql));
        return QueryResultUtil.getJSONArrayList(recordSet);
    }
}
