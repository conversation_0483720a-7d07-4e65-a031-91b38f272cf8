package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.contract.utils.reporttable.Sql2Module;
import com.engine.parent.common.constant.CommonCst;
import com.engine.report.util.ReportDataUtil;
import com.weaver.general.BaseBean;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class ProjectALLyearReportJob extends BaseCronJob {

    @Override
    public void execute() {
        BaseBean bb = new BaseBean();
        bb.writeLog("start--");
        String table = Util.null2String(bb.getPropValue("interface4module","TableName3"));
        String moduleid = Util.null2String(bb.getPropValue("interface4module","ModuleId3"));
        String depId, depIdJunior,yearJunior,year;
        List<String> allJuniorDeptList;
        JSONObject jo, joJunior;
        JSONArray jaList = Sql2Module.executeview("ProjectALLView");
        JSONArray ans = new JSONArray();
        for (int i = 0; i < jaList.size(); i++) {
            jo = jaList.getJSONObject(i);
            JSONObject copyjo = new JSONObject();
            copyjo.putAll(jo);
            bb.writeLog("jo :" + jo);
            depId = jo.getString("ssbm");
            year = jo.getString("nd");
            if (StringUtils.isNotBlank(depId)) {
                //找到所有下级部门
                allJuniorDeptList = ReportDataUtil.queryAllJuniorDept(depId);
                bb.writeLog("start--bm"+allJuniorDeptList);
//                for (String id : allJuniorDeptList) {
//                    //对每个下级部门，进行数据合计，汇总统计到主节点上
//                    for (int j = 0; j < jaList.size(); j++) {
//                        joJunior =  jaList.getJSONObject(j);
//                        depIdJunior = joJunior.getString("ssbm");
//                        yearJunior = joJunior.getString("nd");
//                        //匹配相同部门id和年份
//                        bb.writeLog("start--depIdJunior"+depIdJunior);
//                        bb.writeLog("start--yearJunior"+yearJunior);
//                        if (id.equals(depIdJunior) && year.equals(yearJunior)) {
//                            String fields = "xqxms,dnxqhte,gbxms,gbhte,zcjxxms,xmjshke,yhkhj,htqdd1nhk,htqdd2nhk,htqdd3nhk,clxms,hjje,wkpe,yszk";
//                            ReportDataUtil.sumJuinordataToTop(fields, copyjo, joJunior);
//                        }
//                    }
//                }
                this.add(copyjo);
                ans.add(copyjo);
            }
        }
        //此时jaList为处理好的最终结果集
        bb.writeLog("jaList:" + jaList);
       HashMap<String,Object> result = Sql2Module.JSONArrayinsetmodule(moduleid,table,ans);
        if("true".equals(result.get("flag"))){
            bb.writeLog("同步成功");
        }else {
            bb.writeLog("同步失败"+ result.get("dec"));
        }
    }


    public void add(JSONObject jsonObject) {
        BigDecimal dnxqhte = jsonObject.getBigDecimal("dnxqhte");
        BigDecimal xmjshke = jsonObject.getBigDecimal("dnxqhte");
        BigDecimal yhkhj = jsonObject.getBigDecimal("dnxqhte");
        if(dnxqhte.compareTo(new BigDecimal("0.00"))==0){
            jsonObject.put("sjhkxml","0.00");
            jsonObject.put("sjhkl","0.00");
        }else{
            jsonObject.put("sjhkxml",xmjshke.divide(dnxqhte,2, RoundingMode.HALF_UP));
            jsonObject.put("sjhkl",yhkhj.divide(dnxqhte,2, RoundingMode.HALF_UP));
        }
    }

}
