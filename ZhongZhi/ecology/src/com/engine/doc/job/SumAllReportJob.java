package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.engine.contract.utils.reporttable.Sql2Module;
import com.weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.*;

public class SumAllReportJob extends BaseCronJob {

    private String date;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
        String table = Util.null2String(baseBean.getPropValue("interface4module","TableName8"));
        String moduleid = Util.null2String(baseBean.getPropValue("interface4module","ModuleId8"));
        JSONArray projs = Sql2Module.executesql("select \n" +
                "tabb.id as xmmc,\n" +
                "tabb.procode as xmbh,\n" +
                "tabb.subcompany as ssfb,\n" +
                "tabb.glbm as ssbm,\n" +
                "taba.years as nf,\n" +
                "taba.months as yf,\n" +
                "isnull(viewone.num,0) as bxcb,\n" +
                "isnull(viewtwo.num,0) as hzfy,\n" +
                "(isnull(viewone.num,0)+isnull(viewtwo.num,0)) as hj\n" +
                "from\n" +
                "(\n" +
                "select DATEPART(yyyy, dateadd(MM,v.number,d.xmkssj)) years,DATEPART(mm, dateadd(MM,v.number,d.xmkssj)) months, id\n" +
                "  from Prj_ProjectInfo d\n" +
                "  join master..spt_values v on v.type='P'\n" +
                "       and v.number between 0 and datediff(MM, xmkssj, xmjssj)\n" +
                ") taba right join\n" +
                "(select xmkssj,xmjssj,prjtab.id,procode,glbm,deptab.subcompanyid1 as subcompany from Prj_ProjectInfo prjtab left join HrmDepartment deptab on prjtab.glbmd = deptab.id) tabb on tabb.id = taba.id\n" +
                "left join SumOnetab viewone on viewone.xmmc = taba.id and taba.years =  viewone.years and taba.months = viewone.months\n" +
                "left join SumTwotab viewtwo on viewtwo.xmmc = taba.id and taba.years =  viewtwo.years and taba.months = viewtwo.months where taba.years is not null and taba.years >1900");
        HashMap<String,Object> result = Sql2Module.JSONArrayinsetmodule(moduleid,table,projs);
        if("true".equals(result.get("flag"))){
            baseBean.writeLog("同步成功");
        }else {
            baseBean.writeLog("同步失败"+ result.get("dec"));
        }
    }


}
