package com.engine.doc.job;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.engine.parent.workflow.util.WorkFlowUtil;
import com.wbi.util.Util;
import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;

public class ContractModule2Workflow extends BaseCronJob {



    private String col;

    private String colvalue;

    private String workflowid;

    private String remark;

    private String requestName;

    private String isNextFlow;

    private String workflowcreator;

    @Override
    public void execute() {
        Calendar instance = Calendar.getInstance();
        int thisyear = instance.get(Calendar.YEAR);
        //3、获取系统当前月
        int thismonth = instance.get(Calendar.MONTH) + 1;
        JSONObject baseobject = new JSONObject();
        baseobject.set("workflowId",workflowid);
        baseobject.set("remark",remark);
        baseobject.set("requestName",requestName);
        baseobject.set("isNextFlow",isNextFlow);
        RecordSet recordSet = new RecordSet();
        String sql = "select * from uf_ywhtxx where DATENAME(YEAR,htksrq)<='"+thisyear+"' and DATENAME(MONTH,htksrq)<='"+thismonth+"' and  DATENAME(YEAR,htjsrq)>='"+thisyear+"' and DATENAME(MONTH,htjsrq)>='"+thismonth+"' AND "+col+" = '"+colvalue+"'";
        new BaseBean().writeLog("sql : " +sql);
        recordSet.execute(sql);
        while (recordSet.next()){
            JSONArray mainjsonarray = new JSONArray();
            JSONArray detail = new JSONArray();
            String mainid = Util.null2String(recordSet.getString("id"));
            String htcjr =  Util.null2String(recordSet.getString("htcjr"));
            String sqbm =  Util.null2String(recordSet.getString("sqbm"));
            String sqfb =  Util.null2String(recordSet.getString("sqfb"));
            String startdate = Util.null2String(recordSet.getString("htksrq"));
            String enddate = Util.null2String(recordSet.getString("htjsrq"));
            String monthnum = Integer.toString(getRoughlyMonth(startdate,enddate));
            JSONObject col1 = new JSONObject();
            col1.set("name","sqr");
            col1.set("value",htcjr);
            col1.set("htmlType","1");
            col1.set("type","1");

            JSONObject col2 = new JSONObject();
            col2.set("name","sqbm");
            col2.set("value",sqbm);
            col2.set("htmlType","3");
            col2.set("type","4");

            JSONObject col3 = new JSONObject();
            col3.set("name","sqfb");
            col3.set("value",sqfb);
            col3.set("htmlType","3");
            col3.set("type","164");

            mainjsonarray.add(col1);
            mainjsonarray.add(col2);
            mainjsonarray.add(col3);

            RecordSet rs = new RecordSet();
                String sqldetail = "select * from uf_ywhtxx_dt1 where mainid = '"+mainid+"'";
            rs.execute(sqldetail);
            while (rs.next()){
                JSONArray array = new JSONArray();
                String hte =  Util.null2String(recordSet.getString("hte"));
                String ljqrsr =  Util.null2String(recordSet.getString("ljqrsr"));
                String htbh =  Util.null2String(recordSet.getString("htbh"));
                String xmbh =  Util.null2String(recordSet.getString("xmbh"));
                String htgsbm1 =  Util.null2String(recordSet.getString("htgsbm1"));
                String ldssjsrq =  Util.null2String(recordSet.getString("ldssjsrq"));

                JSONObject detailcol1 = new JSONObject();
                detailcol1.set("name","htze");
                detailcol1.set("value",hte);
                detailcol1.set("htmlType","1");
                detailcol1.set("type","3");

                JSONObject detailcol2 = new JSONObject();
                detailcol2.set("name","lsqrsr");
                detailcol2.set("value",ljqrsr);
                detailcol2.set("htmlType","1");
                detailcol2.set("type","1");

                JSONObject detailcol3 = new JSONObject();
                detailcol3.set("name","htbh");
                detailcol3.set("value",htbh);
                detailcol3.set("htmlType","1");
                detailcol3.set("type","1");

                JSONObject detailcol4 = new JSONObject();
                detailcol4.set("name","xmbh");
                detailcol4.set("value",xmbh);
                detailcol4.set("htmlType","1");
                detailcol4.set("type","1");

                JSONObject detailcol5 = new JSONObject();
                detailcol5.set("name","qrsrbm");
                detailcol5.set("value",htgsbm1);
                detailcol5.set("htmlType","3");
                detailcol5.set("type","4");

                JSONObject detailcol6 = new JSONObject();
                detailcol6.set("name","jfrq");
                detailcol6.set("value",ldssjsrq);
                detailcol6.set("htmlType","3");
                detailcol6.set("type","2");

                String je = "".equals(hte)||"0".equals(hte)||Integer.parseInt(monthnum)<1?
                        "0"
                        :
                        new BigDecimal(hte).divide(new BigDecimal(monthnum),2, RoundingMode.HALF_UP).toString() ;
                JSONObject detailcol7 = new JSONObject();
                detailcol7.set("name","je");
                detailcol7.set("value",je);
                detailcol7.set("htmlType","1");
                detailcol7.set("type","3");

                array.add(detailcol1);
                array.add(detailcol2);
                array.add(detailcol3);
                array.add(detailcol4);
                array.add(detailcol5);
                array.add(detailcol6);
                array.add(detailcol7);
                detail.add(array);
            }
            new BaseBean().writeLog("maindata : " +mainjsonarray);
            new BaseBean().writeLog("baseobject : " +baseobject);
            new BaseBean().writeLog("detail : " +detail);
            String ans = WorkFlowUtil.createWorkflowRequest(
                    Integer.parseInt(workflowcreator),
                    mainjsonarray,
                    baseobject,detail);
            if(Integer.parseInt(ans)>0){
                new BaseBean().writeLog("返回成功");
            }else{
                new BaseBean().writeLog("执行失败在创建流程处");
            }
        }
    }

    public int getRoughlyMonth(String startdate ,String enddate){
        Calendar instance = Calendar.getInstance();
        Date start = DateHelper.parseDate(startdate);
        instance.setTime(start);
        int startyear = instance.get(Calendar.YEAR);
        //3、获取系统当前月
        int startmonth = instance.get(Calendar.MONTH) + 1;
        Date end = DateHelper.parseDate(enddate);
        instance.setTime(end);
        int endyear = instance.get(Calendar.YEAR);
        //3、获取系统当前月
        int endmonth = instance.get(Calendar.MONTH) + 1;

        return (endyear-startyear)*12 + endmonth-startmonth +1;
    }


    public String getCol() {
        return col;
    }

    public void setCol(String col) {
        this.col = col;
    }

    public String getColvalue() {
        return colvalue;
    }

    public void setColvalue(String colvalue) {
        this.colvalue = colvalue;
    }

    public String getWorkflowid() {
        return workflowid;
    }

    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRequestName() {
        return requestName;
    }

    public void setRequestName(String requestName) {
        this.requestName = requestName;
    }

    public String getIsNextFlow() {
        return isNextFlow;
    }

    public void setIsNextFlow(String isNextFlow) {
        this.isNextFlow = isNextFlow;
    }

    public String getWorkflowcreator() {
        return workflowcreator;
    }

    public void setWorkflowcreator(String workflowcreator) {
        this.workflowcreator = workflowcreator;
    }
}
