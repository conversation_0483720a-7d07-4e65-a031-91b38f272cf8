package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.contract.utils.reporttable.Sql2Module;
import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class HrmOutPutreportJob extends BaseCronJob {



    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
        String table = Util.null2String(baseBean.getPropValue("interface4module","TableName4"));
        String moduleid = Util.null2String(baseBean.getPropValue("interface4module","ModuleId4"));
        JSONArray jsonArray =  Sql2Module.executeview("HrmOutPutView");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            List<String> deps = getallsub(jsonObject.getString("ssbm"));
            for (String dep: deps) {
                JSONObject jsonObjectnew = JSONObject.parseObject(jsonObject.toJSONString());
                jsonObjectnew.put("ssbm",dep);
                jsonArray.add(jsonObjectnew);
            }
        }
        HashMap<String,Object> result = Sql2Module.JSONArrayinsetmodule(moduleid,table,jsonArray);
          if("true".equals(result.get("flag"))){
              baseBean.writeLog("同步成功");
          }else {
              baseBean.writeLog("同步失败"+ result.get("dec"));
        }
    }

    public List<String> getallsub(String dep){
        List<String> list =new ArrayList<>();
        String sql = "WITH NODES AS (\n" +
                "\tSELECT\n" +
                "\t\t* \n" +
                "\tFROM\n" +
                "\t\thrmdepartment C \n" +
                "\tWHERE\n" +
                "\t\tC.id = "+dep+"\n" +
                "\t\tUNION ALL\n" +
                "\tSELECT\n" +
                "\t\tCSC.* \n" +
                "\tFROM\n" +
                "\t\thrmdepartment AS CSC\n" +
                "\t\tINNER JOIN NODES AS RC ON CSC.id = RC.SUPDEPID \n" +
                "\t) \n" +
                "\n" +
                "\tSELECT\n" +
                "\tSUPDEPID as SUPDEPID \n" +
                "\tFROM\n" +
                "\tNODES N ";
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        while(recordSet.next()){
            list.add(Util.null2String(recordSet.getString("SUPDEPID")));
        }
        return list;
    }

}
