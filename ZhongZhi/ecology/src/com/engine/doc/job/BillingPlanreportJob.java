package com.engine.doc.job;

import com.engine.contract.utils.reporttable.Sql2Module;
import com.weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;

/**
 * 开票计划
 */
public class BillingPlanreportJob extends BaseCronJob {

    private String Date;

    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
        String table = Util.null2String(baseBean.getPropValue("interface4module","TableName1"));
        String moduleid = Util.null2String(baseBean.getPropValue("interface4module","ModuleId1"));
        String sql = "select * from  BillingPlan where rq <= GETDATE() AND rq >= '"+Date+"'";
          HashMap<String,Object> result = Sql2Module.sqlinsetmodule(moduleid,table,sql);
          if("true".equals(result.get("flag"))){
              baseBean.writeLog("同步成功");
          }else {
              baseBean.writeLog("同步失败"+ result.get("dec"));
          }
    }

    public String getDate() {
        return Date;
    }

    public void setDate(String date) {
        Date = date;
    }
}
