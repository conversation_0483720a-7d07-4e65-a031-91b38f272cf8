package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.doc.multi.service.DocCopyMoveService;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 定时任务，复制指定目录文档到目标目录
 *
 * <AUTHOR>
 */
public class MoveDocJob extends BaseCronJob {

    /**
     * 配置目录的建模表
     */
    private String configTable;
    /**
     * 源目录字段名
     */
    private String sourceField;
    /**
     * 目标目录字段名
     */
    private String destField;
    /**
     * 记录复制过的文档id表
     */
    private String recordIdTable;
    /**
     * 记录复制过的文档id字段
     */
    private String recordIdField;
    /**
     * 记录复制过的文档名称字段
     */
    private String recordNameField;
    /**
     * 记录复制过的模块id
     */
    private String recordModule;

    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            RecordSet rs = new RecordSet();
            int sourceId, destId;
            StringBuilder allDocIds = new StringBuilder();
            rs.executeQuery("select * from " + configTable);
            if (rs.next()) {
                if (!"".equals(Util.null2String(rs.getString(sourceField))) && !"".equals(Util.null2String(rs.getString(destField)))) {
                    sourceId = rs.getInt(sourceField);
                    destId = rs.getInt(destField);
                    //根据源目录id下的文档，其中文档不在目标目录下
                    rs = new RecordSet();

                    String sql = "select a.id,a.docsubject from docdetail a where a.seccategory = ? " +
                            " and NOT EXISTS ( select b." + recordIdField + " from " + recordIdTable +
                            " b where b." + recordIdField + " = a.id )";
                    bb.writeLog("sql:" + sql);
                    rs.executeQuery(sql, sourceId);
                    JSONArray ja = QueryResultUtil.getJSONArrayList(rs);
                    while (rs.next()) {
                        allDocIds.append(Util.null2String(rs.getString("id"))).append(",");
                    }
                    if (!"".equals(allDocIds.toString())) {
                        allDocIds = new StringBuilder(allDocIds.substring(0, allDocIds.length() - 1));
                        Map<String, Object> map;
                        User user = new User(1);
                        String[] docIdArray = allDocIds.toString().split(",");
                        String ipAdd = "0:0:0:0:0:0:0:1";
                        map = DocCopyMoveService.doCopyOrMove(user, docIdArray, sourceId, destId, null, ipAdd, "copy");
                        bb.writeLog("map:" + map);
                        if ("1".equals(Util.null2String(map.get("status")))) {
                            //插入成功之后，将文档id，插入到记录表中
                            insertRecord(ja);
                        } else {
                            bb.writeLog("复制出错：" + map.get("msg"));
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog(e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "-----END");
    }

    /**
     * 插入已复制完成的文档id至记录表
     *
     * @param ja 文档列表数据
     */
    private void insertRecord(JSONArray ja) {
        JSONObject obj;
        List<String> insertFields = new ArrayList<>();
        insertFields.add(recordIdField);
        insertFields.add(recordNameField);
        List<Object> values;
        int creatId = SDUtil.getSystemMangerByLoginId();
        int moduleId = Util.getIntValue(recordModule);
        //循环所有文档数据
        for (int i = 0; i < ja.size(); i++) {
            obj = ja.getJSONObject(i);
            values = new ArrayList<>();
            values.add(obj.get("id"));
            values.add(obj.get("docsubject"));
            try {
                InsertModuleUtil.ModuleInsert(recordIdTable, insertFields, values, creatId, moduleId, null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public String getConfigTable() {
        return configTable;
    }

    public void setConfigTable(String configTable) {
        this.configTable = configTable;
    }

    public String getSourceField() {
        return sourceField;
    }

    public void setSourceField(String sourceField) {
        this.sourceField = sourceField;
    }

    public String getDestField() {
        return destField;
    }

    public void setDestField(String destField) {
        this.destField = destField;
    }

    public String getRecordIdTable() {
        return recordIdTable;
    }

    public void setRecordIdTable(String recordIdTable) {
        this.recordIdTable = recordIdTable;
    }

    public String getRecordIdField() {
        return recordIdField;
    }

    public void setRecordIdField(String recordIdField) {
        this.recordIdField = recordIdField;
    }

    public String getRecordModule() {
        return recordModule;
    }

    public void setRecordModule(String recordModule) {
        this.recordModule = recordModule;
    }

    public String getRecordNameField() {
        return recordNameField;
    }

    public void setRecordNameField(String recordNameField) {
        this.recordNameField = recordNameField;
    }
}
