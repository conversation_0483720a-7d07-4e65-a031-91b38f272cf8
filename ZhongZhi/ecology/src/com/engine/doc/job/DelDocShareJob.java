package com.engine.doc.job;

import com.engine.doc.bean.DocPermissonChange;
import com.engine.doc.util.DocPermissionUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.docs.docs.DocViewer;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * FileName: DelDocShareJob.java
 * 删除文档权限job
 * 定期删除到期的文档权限
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/11
 */
public class DelDocShareJob extends BaseCronJob {
    /**
     * 查看权限建模表名
     */
    private String viewModeTableName;
    /**
     * 下载权限建模表名
     */
    private String downModeTableName;
    /**
     * 文档资料建模表名
     */
    private String fileModeTableName;
    /**
     * 文档资料权限字段
     */
    private String hasPermissionField;

    private BaseBean bb;

    private void _init() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _init();
        bb.writeLog(this.getClass().getName() + "---START");
        RecordSet rs = new RecordSet();
        String today = TimeUtil.getToday();
        String userId, docId, downloadlevel;
        rs.executeQuery("select distinct TBA.*,b.departmentid,b.subcompanyid1, " +
                " TBC.glmk,d.projectid " +
                " from (select yyr,wd,'0' as downloadlevel from " + viewModeTableName + " where dqrq = ? " +
                " union all " +
                " select yyr,wd,'1' as downloadlevel from " + downModeTableName + " where dqrq = ? ) TBA " +
                " left join hrmresource b on (TBA.yyr = b.id) " +
                " left join " +
                " (select id,glmk,xmm from cus_fielddata " +
                " where scope='DocCustomFieldBySecCategory') TBC on (TBA.wd = TBC.id) " +
                " left join docdetail d on (d.id = TBA.wd) ", today, today);
        while (rs.next()) {
            userId = Util.null2String(rs.getString("yyr"));
            docId = Util.null2String(rs.getString("wd"));
            downloadlevel = Util.null2String(rs.getString("downloadlevel"));
            if (StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(docId)) {
                bb.writeLog("删除权限：userid:" + userId + ",docid :" + docId + ",downloadlevel:" + downloadlevel);
                //删除文档的权限(制定用户，文档，1下载/0查看)
                rs.executeUpdate("delete from docshare where  userid = ? and docid = ? and downloadlevel = ? ",
                        userId, docId, downloadlevel);
                DocViewer dv = new DocViewer();
                try {
                    dv.setDocShareByDoc(docId);
                } catch (Exception e) {
                    e.printStackTrace();
                    bb.writeLog("DocViewer出错：" + e.getMessage());
                }
                //更新建模数据
                rs.executeUpdate("update " + fileModeTableName + " set " + hasPermissionField +
                        " = 1 where  ygxm = ? and wdmc = ?", userId, docId);
                //插入变更日志
                DocPermissonChange bean = new DocPermissonChange();
                bean.setYgxm(userId);
                bean.setWdmc(docId);
                bean.setXgxm(Util.null2String(rs.getString("projectid")));
                //相关模块
                bean.setXgmk(Util.null2String(rs.getString("glmk")));
                bean.setBdsj(TimeUtil.getToday());
                bean.setBdlx("2");
                bean.setSzbm(Util.null2String(rs.getString("departmentid")));
                bean.setSzzx(Util.null2String(rs.getString("subcompanyid1")));
                DocPermissionUtil.addPermissionLog(bean);

            }
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

    public String getViewModeTableName() {
        return viewModeTableName;
    }

    public void setViewModeTableName(String viewModeTableName) {
        this.viewModeTableName = viewModeTableName;
    }

    public String getDownModeTableName() {
        return downModeTableName;
    }

    public void setDownModeTableName(String downModeTableName) {
        this.downModeTableName = downModeTableName;
    }

    public String getFileModeTableName() {
        return fileModeTableName;
    }

    public void setFileModeTableName(String fileModeTableName) {
        this.fileModeTableName = fileModeTableName;
    }

    public String getHasPermissionField() {
        return hasPermissionField;
    }

    public void setHasPermissionField(String hasPermissionField) {
        this.hasPermissionField = hasPermissionField;
    }
}
