package com.engine.doc.job;

import com.engine.contract.utils.reporttable.Sql2Module;
import com.weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;

/**
 * 案例公海
 */
public class CaseOfSeaReportJob extends BaseCronJob {

    private String Date;

    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
        String table = Util.null2String(baseBean.getPropValue("interface4module", "TableName0"));
        String moduleid = Util.null2String(baseBean.getPropValue("interface4module", "ModuleId0"));
        String sql = "SELECT * from SeaReport ";
        HashMap<String, Object> result = Sql2Module.sqlinsetmodule(moduleid, table, sql);
        if ("true".equals(result.get("flag"))) {
            baseBean.writeLog("同步成功");
        } else {
            baseBean.writeLog("同步失败" + result.get("dec"));
        }
    }

    public String getDate() {
        return Date;
    }

    public void setDate(String date) {
        Date = date;
    }

}
