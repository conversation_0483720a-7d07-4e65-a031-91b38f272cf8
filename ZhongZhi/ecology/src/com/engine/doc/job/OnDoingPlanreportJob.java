package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.contract.utils.reporttable.Sql2Module;
import com.engine.report.util.ReportDataUtil;
import com.weaver.general.BaseBean;
import org.apache.commons.lang.StringUtils;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;

public class OnDoingPlanreportJob extends BaseCronJob {

    private String sumDepFields;

    @Override
    public void execute() {
        BaseBean bb = new BaseBean();
        bb.writeLog("start--");
        String table = Util.null2String(bb.getPropValue("interface4module","TableName5"));
        String moduleid = Util.null2String(bb.getPropValue("interface4module","ModuleId5"));
        String depId, depIdJunior;
        List<String> allJuniorDeptList;
        JSONObject jo, joJunior;
        JSONArray jaList = Sql2Module.executeview("OnDoingReprot");
        for (int i = 0; i < jaList.size(); i++) {
            jo = jaList.getJSONObject(i);
            bb.writeLog("jo :" + jo);
            depId = jo.getString("ssbm");
            if (StringUtils.isNotBlank(depId)) {
                //找到所有下级部门
                allJuniorDeptList = ReportDataUtil.queryAllJuniorDept(depId);
                for (String id : allJuniorDeptList) {
                    //对每个下级部门，进行数据合计，汇总统计到主节点上
                    for (int j = 0; j < jaList.size(); j++) {
                        joJunior = jaList.getJSONObject(i);
                        depIdJunior = joJunior.getString("ssbm");
                        //匹配相同部门id和年份
                        if (id.equals(depIdJunior)) {
                            //将该下级数据合计到主节点上
                            ReportDataUtil.sumJuinordataToTop(sumDepFields, jo, joJunior);
                        }
                        add(jo);
                    }
                }
            }
        }
        //此时jaList为处理好的最终结果集
        bb.writeLog("jaList:" + jaList);
        HashMap<String,Object> result = Sql2Module.JSONArrayinsetmodule(moduleid,table,jaList);
        if("true".equals(result.get("flag"))){
            bb.writeLog("同步成功");
        }else {
            bb.writeLog("同步失败"+ result.get("dec"));
        }
    }

    public void add(JSONObject top){

        BigDecimal hte = new BigDecimal(Util.null2o(top.getString("hte")));
        BigDecimal aqxms = new BigDecimal(Util.null2o(top.getString("aqxms")));
        BigDecimal wjxms = new BigDecimal(Util.null2o(top.getString("wjxms")));
        BigDecimal yq50ynxnms = new BigDecimal(Util.null2o(top.getString("yq50ynxnms")));
        BigDecimal yq50100xms = new BigDecimal(Util.null2o(top.getString("yq50100xms")));
        BigDecimal yq100jysxms = new BigDecimal(Util.null2o(top.getString("yq100jysxms")));
        BigDecimal xmzxsjcg2nxms = new BigDecimal(Util.null2o(top.getString("xmzxsjcg2nxms")));
        if(hte.compareTo(new BigDecimal("350000"))<0){
            top.put("tjlb","0");
        }else if(hte.compareTo(new BigDecimal("900000"))<0){
            top.put("tjlb","1");
        }else{
            top.put("tjlb","2");
        }
        if(wjxms.compareTo(new BigDecimal("0"))==0){
            top.put("aqxmzb","0.00");
            top.put("yq50ynxmzb","0.00");
            top.put("yq50100xmzb","0.00");
            top.put("yq100jysxmzb","0.00");
            top.put("xmzxsjcg2nxmzb","0.00");
        }else{
            top.put("aqxmzb",aqxms.divide(wjxms,2, RoundingMode.UP).toString());
            top.put("yq50ynxmzb",yq50ynxnms.divide(wjxms,2, RoundingMode.UP).toString());
            top.put("yq50100xmzb",yq50100xms.divide(wjxms,2, RoundingMode.UP).toString());
            top.put("yq100jysxmzb",yq100jysxms.divide(wjxms,2, RoundingMode.UP).toString());
            top.put("xmzxsjcg2nxmzb",xmzxsjcg2nxms.divide(wjxms,2, RoundingMode.UP).toString());
        }
    }

    public String getSumDepFields() {
        return sumDepFields;
    }

    public void setSumDepFields(String sumDepFields) {
        this.sumDepFields = sumDepFields;
    }
}
