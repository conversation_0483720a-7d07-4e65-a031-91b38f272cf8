package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.contract.utils.reporttable.Sql2Module;
import com.engine.doc.util.Calculator;
import com.engine.report.util.ReportDataUtil;
import com.weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class One4allSumJob extends BaseCronJob {

    private String view;
    private String table;
    private String moduleid;
    private String hrmcol;
    private String nexuscol;
    private String sumcol;
    private String addcol;
    private String mathString;

    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
        JSONArray jsonArray = Sql2Module.executeview(view);
        String[] sumcolstring = sumcol.split(",");
        String[] addcolstring = addcol.split(",");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonobj = jsonArray.getJSONObject(i);
            String hrmstring = jsonobj.getString(hrmcol);
            String[] nexustringcol = nexuscol.split(",");
            String[] nexuxvalue = new String[nexustringcol.length];
            for (int j = 0; j < nexustringcol.length; j++) {
                nexuxvalue[j] = Util.null2String(jsonobj.getString(nexustringcol[j]));
            }
            //找到所有上级部门向上累加不会重复
            List<String> supdeps = ReportDataUtil.getALLParentsub(hrmstring);
            for (String subdep : supdeps) {
                for (int j = 0; j < jsonArray.size(); j++) {
                    JSONObject thisjsonobj = jsonArray.getJSONObject(j);
                    String thishrmstring = thisjsonobj.getString(hrmcol);
                    String[] thisnexuxvalue = new String[nexustringcol.length];
                    for (int k = 0; k < nexustringcol.length; k++) {
                        thisnexuxvalue[k] = Util.null2String(thisjsonobj.getString(nexustringcol[k]));
                    }
                    if (thishrmstring.equals(subdep) && isequals(nexuxvalue, thisnexuxvalue)) {
                        for (String s : sumcolstring) {
                            String thstsum = Util.null2o(thisjsonobj.getString(s));
                            String thattsum = Util.null2o(jsonobj.getString(s));
                            BigDecimal thissum = new BigDecimal(thstsum).add(new BigDecimal(thattsum));
                            thisjsonobj.put(s, thissum.toString());
                        }
                        for (String s : addcolstring) {
                            String thstsum = Util.null2String(thisjsonobj.getString(s));
                            String thattsum = Util.null2String(jsonobj.getString(s));
                            if ("".equals(thattsum)) {
                                continue;
                            }
                            if ("".equals(thstsum)) {
                                thisjsonobj.put(s, thattsum);
                            } else {
                                thisjsonobj.put(s, thstsum + "," + thattsum);
                            }
                        }
                    }
                }
            }
        }
        Mathchange(jsonArray, mathString);
        Sql2Module.JSONArrayinsetmodule(moduleid, table, jsonArray);
    }


    public static boolean isequals(String[] a, String[] b) {
        int length = Math.min(a.length, b.length);
        for (int i = 0; i < length; i++) {
            if (!a[i].equals(b[i])) {
                return false;
            }
        }
        return true;
    }

    public static void Mathchange(JSONArray jsonArray, String str) {
        JSONArray matharray;
        try {
            matharray = JSONArray.parseArray(str);
        } catch (Exception e) {
            new BaseBean().writeLog("数学解析出错" + str);
            return;
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
//            Set<Map.Entry<String, Object>> entries= jsonObject.entrySet();
            Calculator calculator = new Calculator();
            calculator.setFunctionMap("GETSIZE", args1 -> BigDecimal.valueOf(args1.length));
            calculator.setFunctionMap("TOZERO", args1 -> {
                if (args1.length != 1) {
                    throw new IllegalArgumentException("cos function takes 1 argument");
                }

                return args1[0].compareTo(new BigDecimal("0.00")) < 0 ? new BigDecimal("0.00") : args1[0];
            });
//            for (Map.Entry<String, Object> entry:
//                 entries) {
//                String key = entry.getKey();
//                String value = Util.null2String(entry.getValue());
//                if(Calculator.getBigDecimal(value)) {
//                    calculator.setVariable("$" + key + "$", new BigDecimal(value));
//                }
//            }
            for (int j = 0; j < matharray.size(); j++) {
                JSONObject mathobj = matharray.getJSONObject(j);
                String col = mathobj.getString("col");
                String mathstr = mathobj.getString("mathstring");
                String mathstring = getmathString(jsonObject, mathstr);
                if (!"".equals(mathstring)) {
                    if (!"".equals(col) && !"".equals(mathstr)) {
                        try {
                            BigDecimal ans = calculator.evaluate(mathstring);
                            jsonObject.put(col, ans.toString());
                        } catch (Exception e) {
                            new BaseBean().writeLog(e + "-" + mathstring + "-" + jsonObject.toJSONString());
                            jsonObject.put(col, "");
                        }
                    }
                } else {
                    new BaseBean().writeLog("正则匹配出错：" + mathstring + "-" + mathstr + "-" + jsonObject.toJSONString());
                }
            }
        }
    }

    public static String getmathString(JSONObject jsonObject, String str) {
        String s = str;
        try {
            Pattern p = Pattern.compile("\\$\\w+\\$");
            Matcher m = p.matcher(s);
            while (m.find()) {
                String sub = s.substring(m.start(), m.end());
                String subcontent = sub.substring(1, sub.length() - 1);
                String ans = Util.null2String(jsonObject.getString(subcontent));
                s = s.replace(sub, ans);
                m = p.matcher(s);
            }
        } catch (Exception e) {
            new BaseBean().writeLog("公式分析出错" + str);
            return "";
        }
        return s;
    }

    public String getView() {
        return view;
    }

    public void setView(String view) {
        this.view = view;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getModuleid() {
        return moduleid;
    }

    public void setModuleid(String moduleid) {
        this.moduleid = moduleid;
    }

    public String getHrmcol() {
        return hrmcol;
    }

    public void setHrmcol(String hrmcol) {
        this.hrmcol = hrmcol;
    }

    public String getNexuscol() {
        return nexuscol;
    }

    public void setNexuscol(String nexuscol) {
        this.nexuscol = nexuscol;
    }

    public String getSumcol() {
        return sumcol;
    }

    public void setSumcol(String sumcol) {
        this.sumcol = sumcol;
    }

    public String getMathString() {
        return mathString;
    }

    public void setMathString(String mathString) {
        this.mathString = mathString;
    }

    public String getAddcol() {
        return addcol;
    }

    public void setAddcol(String addcol) {
        this.addcol = addcol;
    }

}
