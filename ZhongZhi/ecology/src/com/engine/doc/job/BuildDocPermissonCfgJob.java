package com.engine.doc.job;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;

/**
 * FileName: BuildDocPermissonCfgJob.java
 * 自动生成人力资源文档权限配置信息
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/14
 */
public class BuildDocPermissonCfgJob extends BaseCronJob {

    /**
     * 人员类型配置建模表
     */
    private String hrmCfgModeTableName;
    /**
     * 文档权限数量建模表
     */
    private String docCfgModeTableName;
    /**
     * 文档权限数量建模表id
     */
    private String docCfgModeId;
    /**
     * 人力资源自定义字段，人员类型
     */
    private String hrmRyflField;
    /**
     * 建模，申请数量字段
     */
    private String modeSqslField;
    /**
     * 建模，人员分类字段
     */
    private String modeRyflField;
    /**
     * 建模，员工状态字段
     */
    private String modeYgztField;
    /**
     * 建模，下载数量字段
     */
    private String modeXzslField;
    private BaseBean bb;

    private void _init() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _init();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            RecordSet rs = new RecordSet();
            String sql = "select a.id,c." + modeSqslField + " as sqsl," +
                    " c." + modeXzslField + " as xzsl," +
                    " c." + modeRyflField + " as ryfl from HrmResource a " +
                    " left join cus_fielddata b " +
                    " on (a.id = b.id and  b.scope='HrmCustomFieldByInfoType' and b.scopeid=-1) " +
                    " left join " + hrmCfgModeTableName + " c " +
                    " on (b." + hrmRyflField + " = c." + modeRyflField + " and a.status = c." + modeYgztField + ") " +
                    " where a.status in (0,1,2,3) " +
                    " and c." + modeSqslField + " is not null ";
            bb.writeLog("query sql:" + sql);
            boolean flag = rs.executeQuery(sql);
            if (flag) {
                //先清除原有建模数据
                delModeData();
                List<String> insertFields = new ArrayList<>();
                List<Object> values;
                //姓名
                insertFields.add("xm");
                //资料申请权限
                insertFields.add("zlsqqx");
                //剩余查看数
                insertFields.add("syzlsqs");
                //已使用查看数
                insertFields.add("ysysqs");
                //人员分类
                insertFields.add("ryfl");
                //剩余下载数
                insertFields.add("syxzs");
                //已使用下载数
                insertFields.add("ysyxzs");

                int creatId = SDUtil.getSystemMangerByLoginId();
                while (rs.next()) {
                    values = new ArrayList<>();
                    values.add(rs.getInt("id"));
                    values.add(0);
                    values.add(rs.getInt("sqsl"));
                    values.add(0);
                    values.add(rs.getInt("ryfl"));
                    values.add(rs.getInt("xzsl"));
                    values.add(0);
                    InsertModuleUtil.ModuleInsert(docCfgModeTableName, insertFields, values, creatId, Integer.parseInt(docCfgModeId), null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expt:" + e.getMessage());
            bb.writeLog(this.getClass().getName() + "---FAIL END");
        }
        bb.writeLog(this.getClass().getName() + "---SUCCESS END");
    }

    private void delModeData() {
        RecordSet rs = new RecordSet();
        rs.executeUpdate("delete from " + docCfgModeTableName);
    }

    public String getHrmCfgModeTableName() {
        return hrmCfgModeTableName;
    }

    public void setHrmCfgModeTableName(String hrmCfgModeTableName) {
        this.hrmCfgModeTableName = hrmCfgModeTableName;
    }

    public String getDocCfgModeTableName() {
        return docCfgModeTableName;
    }

    public void setDocCfgModeTableName(String docCfgModeTableName) {
        this.docCfgModeTableName = docCfgModeTableName;
    }

    public String getDocCfgModeId() {
        return docCfgModeId;
    }

    public void setDocCfgModeId(String docCfgModeId) {
        this.docCfgModeId = docCfgModeId;
    }

    public String getHrmRyflField() {
        return hrmRyflField;
    }

    public void setHrmRyflField(String hrmRyflField) {
        this.hrmRyflField = hrmRyflField;
    }

    public String getModeSqslField() {
        return modeSqslField;
    }

    public void setModeSqslField(String modeSqslField) {
        this.modeSqslField = modeSqslField;
    }

    public String getModeRyflField() {
        return modeRyflField;
    }

    public void setModeRyflField(String modeRyflField) {
        this.modeRyflField = modeRyflField;
    }

    public String getModeYgztField() {
        return modeYgztField;
    }

    public void setModeYgztField(String modeYgztField) {
        this.modeYgztField = modeYgztField;
    }

    public String getModeXzslField() {
        return modeXzslField;
    }

    public void setModeXzslField(String modeXzslField) {
        this.modeXzslField = modeXzslField;
    }
}
