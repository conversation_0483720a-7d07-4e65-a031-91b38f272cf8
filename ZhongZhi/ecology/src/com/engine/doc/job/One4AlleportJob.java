package com.engine.doc.job;

import com.alibaba.fastjson.JSONArray;
import com.engine.contract.utils.reporttable.Sql2Module;
import com.weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;

/**
 * 开票计划
 */
public class One4AlleportJob extends BaseCronJob {

    private String view;

    private String table;

    private String moduleid;

    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("start--");
          HashMap<String,Object> result = Sql2Module.viewinsetmodule(moduleid,table,view);
          if("true".equals(result.get("flag"))){
              baseBean.writeLog("同步成功");
          }else {
              baseBean.writeLog("同步失败"+ result.get("dec"));
          }
    }

    public String getView() {
        return view;
    }

    public void setView(String view) {
        this.view = view;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getModuleid() {
        return moduleid;
    }

    public void setModuleid(String moduleid) {
        this.moduleid = moduleid;
    }
}
