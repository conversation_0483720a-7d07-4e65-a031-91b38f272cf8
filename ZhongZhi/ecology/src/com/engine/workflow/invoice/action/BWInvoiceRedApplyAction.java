package com.engine.workflow.invoice.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.workflow.invoice.dto.InvoiceRedApplyDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName BWInvoiceRedApplyAction.java
 * @Description 调用红字确认单申请接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/19
 */
@Getter
@Setter
public class BWInvoiceRedApplyAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 功能列表的功能id
     */
    private String functionId;
    //---Action参数---

    /**
     * 线程池执行超时时间 分钟
     */
    private static final int POOL_TIMEOUT = 10;

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg;
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            Map<String, String> mainData = actionInfo.getMainData();
            //step 1 : 根据workflowid，functionid，读取配置
            InvoiceRedApplyDto config = WfConfigUtil.getObjConfigWithoutEnable(functionId, actionInfo.getWorkflowId(), InvoiceRedApplyDto.class);
            writeLog("config:" + config);
            //step 2:校验配置
            errorMsg = checkParam(config);
            if (errorMsg.isEmpty()) {
                //是否是全电红冲字段名，是的情况才走接口
                String fieldFlag = config.getFieldFlag();
                String fieldFlagValue = mainData.get(fieldFlag);
                //明细index
                int detailIndex = Integer.parseInt(config.getDtIndex());
                //明细Map
                Map<Integer, List<Map<String, String>>> detailMap = actionInfo.getDetailData();
                //明细数据
                List<Map<String, String>> detailData = detailMap.get(detailIndex);

                //0为全电票，非全电票不走逻辑
                if ("0".equals(fieldFlagValue)) {
                    // 使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
                    ExecutorService executorService = Executors.newFixedThreadPool(5);
                    CountDownLatch latch = new CountDownLatch(detailData.size());
                    for (Map<String, String> eachDetail : detailData) {
                        //判断明细apiflag字段是否已经成功
                        executorService.submit(() -> {
                            try {
                                //处理明细的每一行数据
                                processDetailData(eachDetail, actionInfo, config);
                            } finally {
                                latch.countDown(); // 线程完成任务后减少计数器
                            }
                        });
                    }
                    try {
                        // 等待所有线程完成任务，超时POOL_TIMEOUT分钟
                        if (!latch.await(POOL_TIMEOUT, TimeUnit.MINUTES)) {
                            writeLog("超时未执行完所有线程任务，请检查");
                        }
                    } catch (InterruptedException e) {
                        writeLog("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
                    } finally {
                        executorService.shutdown();
                    }
                    //执行所有任务后，往下走其他业务逻辑...
                    //查询明细行，是否都成功
                    String detailTableName = actionInfo.getFormtableName() + "_dt" + detailIndex;
                    String fieldApiFlag = config.getFieldApiFlag();
                    String mainId = mainData.get("id");
                    if (!checkAllRunedSuccess(mainId, detailTableName, fieldApiFlag)) {
                        errorMsg = "明细行存在接口出错的数据，请检查！";
                    }
                } else {
                    writeLog("非全电票");
                }
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        writeLog("errorMsg:" + errorMsg);
        writeLog(this.getClass().getName() + "---END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    private boolean checkAllRunedSuccess(String mainId, String detailTableName, String fieldApiFlag) {
        RecordSet rs = new RecordSet();
        boolean flag = true;
        String finalFlagFieldValue;
        if (rs.executeQuery("select * from " + detailTableName + "  where mainid = " + mainId)) {
            while (rs.next()) {
                finalFlagFieldValue = Util.null2String(rs.getString(fieldApiFlag));
                if (!"0".equals(finalFlagFieldValue)) {
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }

    private void processDetailData(Map<String, String> eachDetail, ActionInfo actionInfo, InvoiceRedApplyDto config) {
        String esbEventName, errorMsg = "";
        String fromField, fieldType, fromValue;
        EsbEventResult er = null;
        String detailId = "";
        try {
            writeLog("processDetailData--START");
            writeLog("processDetailData eachDetail:" + eachDetail);
            detailId = Util.null2String(eachDetail.get("id"));
            List<InvoiceRedApplyDto.InField> inFields = config.getInFields();
            //ESB 事件名
            esbEventName = config.getEsbEventName();
            //组装ESB参数
            JSONObject esbParam = new JSONObject();
            //入参
            for (InvoiceRedApplyDto.InField eachFieldConfig : inFields) {
                fromField = eachFieldConfig.getFromField();
                fieldType = eachFieldConfig.getFromType();
                fromValue = getFromFieldValue(fromField, fieldType, actionInfo, eachDetail);
                writeLog("fromField :" + fromField + ",fromValue:" + fromValue);
                if (StringUtils.isNotBlank(fromValue)) {
                    esbParam.put(eachFieldConfig.getEsbField(), fromValue);
                }
            }
            writeLog("esbParam:" + esbParam);
            if (!esbParam.isEmpty()) {
                //调用ESB
                er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                if (!er.isSuccess()) {
                    errorMsg = er.getErroMsg();
                }
            } else {
                errorMsg = "获取到的ESB传入参数为空，请检查配置！";
            }
        } catch (Exception e) {
            errorMsg = "processDetailData 异常:" + SDUtil.getExceptionDetail(e);
        }

        if (!errorMsg.isEmpty()) {
            writeLog("processDetailData出错：" + errorMsg);
        }
        if (!detailId.isEmpty()) {
            //根据配置回写
            writeBack(config, actionInfo, er, errorMsg, detailId);
        } else {
            writeLog("detailId 为空");
        }
    }

    private String getFromFieldValue(String fromField, String fieldType, ActionInfo actionInfo, Map<String, String> detailData) {
        String result = "";
        Map<String, String> mainData = actionInfo.getMainData();
        //0 流程主表字段
        if ("0".equals(fieldType)) {
            result = mainData.get(fromField);
        } else if ("1".equals(fieldType)) {
            //1 当前明细行字段
            result = detailData.get(fromField);
        } else if ("2".equals(fieldType)) {
            //2 固定值
            result = fromField;
        } else if ("3".equals(fieldType)) {
            //表达式
            if ("$requestid$".equals(fromField)) {
                result = actionInfo.getRequestId();
            } else if ("$workflowid$".equals(fromField)) {
                result = actionInfo.getWorkflowId();
            }
        }
        return result;
    }

    /**
     * 回写
     *
     * @param config
     * @param actionInfo
     * @param er
     * @return
     */
    private void writeBack(InvoiceRedApplyDto config, ActionInfo actionInfo, EsbEventResult er, String errorMsg, String detailId) {

        String fromField, fromType, wfField, wfFieldValue;
        List<String> updateFields = new ArrayList<>();
        List<Object> updateValues = new ArrayList<>();
        String sql;
        StringBuilder sbUpdate = new StringBuilder();
        try {
            writeLog("writeBack--START");
            String detailIndex = config.getDtIndex();
            String detailTableName = actionInfo.getFormtableName() + "_dt" + detailIndex;
            String fieldApiFlag = config.getFieldApiFlag();
            RecordSet rs = new RecordSet();
            //成功才回写数据
            if (errorMsg.isEmpty()) {
                JSONObject esbReturnData = er.getData();
                JSONArray model = esbReturnData.getJSONArray("model");
                if (model != null && !model.isEmpty()) {
                    JSONObject firstModel = model.getJSONObject(0);
                    //回写字段配置
                    List<InvoiceRedApplyDto.WriteBackField> writeBackFields = config.getWriteBackFields();
                    for (InvoiceRedApplyDto.WriteBackField eachConfig : writeBackFields) {
                        fromField = eachConfig.getFromField();
                        fromType = eachConfig.getFromType();
                        wfField = eachConfig.getWfField();
                        //获取反写的字段值
                        wfFieldValue = getWriteBackFieldValue(fromField, fromType, actionInfo, firstModel);
                        if (!wfFieldValue.isEmpty()) {
                            updateFields.add(wfField);
                            updateValues.add(wfFieldValue);
                        }
                    }
                    //默认更新接口标记为成功
                    updateFields.add(fieldApiFlag);
                    updateValues.add("0");

                    //拼接update语句，value值用?占位
                    for (int i = 0; i < updateFields.size(); i++) {
                        sbUpdate.append(updateFields.get(i)).append("=").append("?");
                        if (i < updateFields.size() - 1) {
                            sbUpdate.append(", ");
                        }
                    }
                    // step 2 : 定义更新数据的sql
                    sql = "update " + detailTableName + " set " + sbUpdate + " where id= " + detailId;
                    writeLog("update formtable sql:" + sql);
                    writeLog("update formtable updateValues:" + updateValues);
                    rs.executeUpdate(sql, updateValues);
                }
            }
        } catch (Exception e) {
            writeLog("回写异常：" + SDUtil.getExceptionDetail(e));
        }
    }

    private String getWriteBackFieldValue(String fromField, String fieldType, ActionInfo actionInfo, JSONObject esbReturnData) {
        String result = "";
        Map<String, String> mainData = actionInfo.getMainData();
        //流程主表
        if ("0".equals(fieldType)) {
            result = mainData.get(fromField);
        } else if ("1".equals(fieldType)) {
            //固定值
            result = fromField;
        } else if ("2".equals(fieldType)) {
            //ESB响应的字段值
            result = Util.null2String(esbReturnData.get(fromField));
        }
        return result;
    }

    private String checkParam(InvoiceRedApplyDto config) {
        if (config == null) {
            return "未获取到对应的开发平台配置信息，请检查！";
        }
        if (StringUtils.isBlank(config.getEsbEventName()) ||
                StringUtils.isBlank(config.getDtIndex()) ||
                StringUtils.isBlank(config.getFieldFlag()) ||
                config.getInFields().isEmpty() ||
                config.getWriteBackFields().isEmpty()
        ) {
            return "配置缺失必填项";
        }
        return "";
    }

}
