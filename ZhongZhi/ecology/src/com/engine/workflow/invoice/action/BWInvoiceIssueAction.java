package com.engine.workflow.invoice.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.workflow.invoice.dto.InvoiceIssueDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName BWInvoiceIssueAction.java
 * @Description 百望开票接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/19
 */
@Getter
@Setter
public class BWInvoiceIssueAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 功能列表的功能id
     */
    private String functionId;
    //---Action参数---

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //出错信息
        String errorMsg = "";
        String esbEventName;
        JSONObject jo;
        String fromField, fieldType, fromValue;
        try {
            //获取action相关信息
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //step 1 : 根据workflowid，functionid，读取配置
            InvoiceIssueDto config = WfConfigUtil.getObjConfigWithoutEnable(functionId, actionInfo.getWorkflowId(), InvoiceIssueDto.class);
            writeLog("config:" + config);
            //step 2:校验配置
            errorMsg = checkParam(config);
            if (errorMsg.isEmpty()) {
                String fieldFlagValue = actionInfo.getMainData().get(config.getFieldFlag());
                writeLog("fieldFlagValue:" + fieldFlagValue);
                //0为全电票，非全电票不走逻辑
                if ("0".equals(fieldFlagValue)) {
                    List<InvoiceIssueDto.InField> inFields = config.getInFields();
                    //ESB 事件名
                    esbEventName = config.getEsbEventName();
                    //组装ESB参数
                    JSONObject esbParam = new JSONObject();
                    for (InvoiceIssueDto.InField eachFieldConfig : inFields) {
                        fromField = eachFieldConfig.getFromField();
                        fieldType = eachFieldConfig.getFromType();
                        fromValue = getFromFieldValue(fromField, fieldType, actionInfo);
                        writeLog("fromValue:" + fromValue);
                        if (StringUtils.isNotBlank(fromValue)) {
                            esbParam.put(eachFieldConfig.getEsbField(), fromValue);
                        }
                    }
                    writeLog("esbParam:" + esbParam);
                    if (!esbParam.isEmpty()) {
                        esbParam.put("requestid", actionInfo.getRequestId());
                        //调用ESB
                        EsbEventResult er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                        if (er.isSuccess()) {
                            //成功后，根据配置回写
                            errorMsg = writeBack(config, actionInfo, er);
                        } else {
                            errorMsg = er.getErroMsg();
                        }
                    } else {
                        errorMsg = "获取到的ESB传入参数为空，请检查配置！";
                    }
                } else {
                    writeLog("非全电票");
                }

            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        writeLog("errorMsg:" + errorMsg);
        writeLog(this.getClass().getName() + "---END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    private String getFromFieldValue(String fromField, String fieldType, ActionInfo actionInfo) {
        String result = "";
        Map<String, String> mainData = actionInfo.getMainData();
        //流程主表
        if ("0".equals(fieldType)) {
            result = mainData.get(fromField);
        } else if ("1".equals(fieldType)) {
            //固定值
            result = fromField;
        } else if ("2".equals(fieldType)) {
            //表达式
            if ("$requestid$".equals(fromField)) {
                result = actionInfo.getRequestId();
            } else if ("$workflowid$".equals(fromField)) {
                result = actionInfo.getWorkflowId();
            }
        }
        return result;
    }

    /**
     * 回写
     *
     * @param config
     * @param actionInfo
     * @param er
     * @return
     */
    private String writeBack(InvoiceIssueDto config, ActionInfo actionInfo, EsbEventResult er) {
        String errorMsg = "";
        String fromField, fromType, wfField, wfFieldValue;
        StringBuilder sbFields = new StringBuilder();
        StringBuilder sbValues = new StringBuilder();
        List<String> insertFields = new ArrayList<>();
        List<Object> insertValues = new ArrayList<>();
        try {
            String mainId = actionInfo.getMainData().get("id");
            String detailIndex = config.getDtIndex();
            String detailTableName = actionInfo.getFormtableName() + "_dt" + detailIndex;
            JSONObject esbReturnData = er.getData().getJSONObject("model").getJSONArray("success").getJSONObject(0);
            if (esbReturnData.isEmpty()) {
                errorMsg = "未获取到接口返回success节点的数据";
            } else {
                List<InvoiceIssueDto.WriteBackField> writeBackFields = config.getWriteBackFields();
                for (InvoiceIssueDto.WriteBackField eachConfig : writeBackFields) {
                    fromField = eachConfig.getFromField();
                    fromType = eachConfig.getFromType();
                    wfField = eachConfig.getWfField();
                    wfFieldValue = getWriteBackFieldValue(fromField, fromType, actionInfo, esbReturnData);
                    if (!wfFieldValue.isEmpty()) {
                        insertFields.add(wfField);
                        insertValues.add(wfFieldValue);
                    }
                }
                insertFields.add("mainid");
                insertValues.add(mainId);
                //拼接insert语句，value值用?占位
                for (int i = 0; i < insertFields.size(); i++) {
                    sbFields.append(insertFields.get(i));
                    sbValues.append("?");
                    if (i < insertFields.size() - 1) {
                        sbFields.append(",");
                        sbValues.append(",");
                    }
                }
                // step 2 : 定义插入数据的sql
                String sql = "insert into " + detailTableName + "(" + sbFields + ") values (" + sbValues + ") ";
                writeLog("insert formtable sql:" + sql);
                writeLog("insert formtable insertValues:" + insertValues);
                RecordSet rs = new RecordSet();
                if (!rs.executeUpdate(sql, insertValues)) {
                    errorMsg = "回写明细表单出错：" + rs.getExceptionMsg();
                }
            }

        } catch (Exception e) {
            errorMsg = "回写异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    private String getWriteBackFieldValue(String fromField, String fieldType, ActionInfo actionInfo, JSONObject esbReturnData) {
        String result = "";
        Map<String, String> mainData = actionInfo.getMainData();
        //流程主表
        if ("0".equals(fieldType)) {
            result = mainData.get(fromField);
        } else if ("1".equals(fieldType)) {
            //固定值
            result = fromField;
        } else if ("2".equals(fieldType)) {
            //ESB响应的字段值
            result = Util.null2String(esbReturnData.get(fromField));
        }
        return result;
    }

    private String checkParam(InvoiceIssueDto config) {
        if (config == null) {
            return "未获取到对应的开发平台配置信息，请检查！";
        }
        if (StringUtils.isBlank(config.getEsbEventName()) ||
                StringUtils.isBlank(config.getDtIndex()) ||
                StringUtils.isBlank(config.getFieldFlag()) ||
                config.getInFields().isEmpty() ||
                config.getWriteBackFields().isEmpty()
        ) {
            return "配置缺失必填项";
        }
        return "";
    }

}
