package com.engine.workflow.invoice.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.workflow.invoice.dto.InvoiceRedQueryFileDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.ThreadPoolUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName BWInvoiceRedQueryFileAction.java
 * @Description 百望开票红冲, 查询，下载action
 * 通过一系列接口，最终要下载文件到流程上
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/21
 */
@Getter
@Setter
public class BWInvoiceRedQueryFileAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 功能列表的功能id
     */
    private String functionId;
    //---Action参数---

    /**
     * 当前action使用的线程池名称
     */
    private final static String THREAD_POOL_NAME = "BWInvoiceRedQueryFileAction_Pool";
    /**
     * 线程池执行超时时间 分钟
     */
    private static final int POOL_TIMEOUT = 60;
    /**
     * 流程主表名称
     */
    private String mainTableName;
    /**
     * 流程明细表名
     */
    private String detailTableName;
    /**
     * 流程requestid
     */
    private String requestid;

    // 创建一个对象作为锁
    private static final Object lock = new Object();

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //获取action相关信息
        ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
        //这里做异步操作，不影响流程流转
        ExecutorService executorService = ThreadPoolUtil.getThreadPool(THREAD_POOL_NAME, "5");
        int userId = actionInfo.getUser().getUID();
        writeLog("Action触发红冲全电票--START");
        writeLog("Action触发红冲全电票--requestid:" + actionInfo.getRequestId());
        executorService.execute(() -> executeData(actionInfo.getRequestId(), functionId, userId, true));
        writeLog(this.getClass().getName() + "---END");
        return ActionUtil.handleResult("", requestInfo);
    }

    private void _init() {
        mainTableName = "";
        requestid = "";
        detailTableName = "";
    }

    /**
     * 实际执行
     *
     * @param functionId
     * @param userId
     * @param needRetry  是否需要重试
     * @return
     */
    public synchronized String executeData(String requestidParam, String functionId, int userId, boolean needRetry) {
        synchronized (lock) {
            writeLog("BWInvoiceRedQueryFileAction executeData--START");
            writeLog("requestid:" + requestidParam);
            writeLog("functionId:" + functionId);
            writeLog("userId:" + userId);
            // step 1: 校验参数
            // step 2: 调用全电票查询接口
            // step 3: step2 成功调用板式文件生成接口-下载文件到OA文档-回写表单
            // step 4: step2 失败要重复走5次
            String errorMsg;
            Map<String, Object> mainData;
            List<Map<String, Object>> detailData;
            String mainid;
            try {
                //step 0：初始化
                _init();
                requestid = requestidParam;
                //step 1 : 根据requestid获取流程相关信息
                WfInfo wfInfo = WfUtil.getWfInfoByReqId(requestid);
                if (wfInfo.getFormtableName().isEmpty()) {
                    errorMsg = "未获取到requestid:" + requestid + "的相关流程信息";
                } else {

                    //step 1 : 根据workflowid，functionid，读取配置
                    InvoiceRedQueryFileDto config = WfConfigUtil.getObjConfigWithoutEnable(functionId, wfInfo.getWorkflowId(), InvoiceRedQueryFileDto.class);
                    writeLog("config:" + config);
                    //step 2: 校验配置
                    errorMsg = checkParam(config);
                    if (errorMsg.isEmpty()) {
                        mainTableName = wfInfo.getFormtableName();
                        //step 2: 获取主表、明细表数据数据
                        mainData = getMainData();
                        if (mainData.isEmpty()) {
                            errorMsg = "未获取到主表数据";
                        } else {
                            writeLog("mainData:" + mainData);
                            mainid = Util.null2String(mainData.get("id"));
                            //明细index
                            int detailIndex = Integer.parseInt(config.getDtIndex());
                            detailTableName = wfInfo.getFormtableName() + "_dt" + detailIndex;
                            detailData = getDetailData(mainid);
                            if (detailData.isEmpty()) {
                                errorMsg = "未获取到明细表数据";
                            } else {
                                //全店票flag字段值
                                String fieldFlagValue = Util.null2String(mainData.get(config.getFieldFlag()));
                                //0为全电票，非全电票不走逻辑
                                if ("0".equals(fieldFlagValue)) {
                                    List<String> allErrorMsg = new ArrayList<>();
                                    //每行明细数据执行
                                    // 使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
                                    ExecutorService executorService = Executors.newFixedThreadPool(5);
                                    CountDownLatch latch = new CountDownLatch(detailData.size());
                                    for (Map<String, Object> eachDetail : detailData) {
                                        //判断明细apiflag字段是否已经成功
                                        executorService.submit(() -> {
                                            try {
                                                //处理明细的每一行数据
                                                //递归执行 这里是第一次执行
                                                String eachErrroMsg = process("", config, mainData, eachDetail, userId, 1, needRetry);
                                                if (!eachErrroMsg.isEmpty()) {
                                                    allErrorMsg.add(eachErrroMsg);
                                                }
                                            } finally {
                                                latch.countDown(); // 线程完成任务后减少计数器
                                            }
                                        });
                                    }
                                    try {
                                        // 等待所有线程完成任务，超时POOL_TIMEOUT分钟
                                        if (!latch.await(POOL_TIMEOUT, TimeUnit.MINUTES)) {
                                            errorMsg = "超时未执行完所有线程任务，请检查";
                                        }
                                    } catch (InterruptedException e) {
                                        errorMsg = "latch.await() 出错：" + SDUtil.getExceptionDetail(e);
                                    } finally {
                                        executorService.shutdown();
                                    }
                                    if (errorMsg.isEmpty() && !allErrorMsg.isEmpty()) {
                                        errorMsg = allErrorMsg.toString();
                                    }
                                } else {
                                    writeLog("非全电票");
                                }
                            }
                        }
                    }
                }

            } catch (Exception e) {
                errorMsg = SDUtil.getExceptionDetail(e);
            }
            return errorMsg;
        }

    }

    private Map<String, Object> getMainData() {
        Map<String, Object> mainData = new HashMap<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + mainTableName + " where requestid = ?", requestid)) {
            mainData = QueryUtil.getMap(rs);
        }
        return mainData;
    }

    private Map<String, Object> getEachDetailData(String detailId) {
        Map<String, Object> map = new HashMap<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + detailTableName + " where id = ?", detailId)) {
            map = QueryUtil.getMap(rs);
        }
        return map;
    }


    private List<Map<String, Object>> getDetailData(String mainid) {
        List<Map<String, Object>> detailData = new ArrayList<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + detailTableName + " where mainid = ?", mainid)) {
            detailData = QueryUtil.getMapList(rs);
        }
        return detailData;
    }

    /**
     * 多次执行查询和文件回写动作
     *
     * @param errorMsg
     * @param config
     * @param mainData
     * @param eachDetailData
     * @param userId
     * @param attempt
     * @return
     */
    private String process(String errorMsg,
                           InvoiceRedQueryFileDto config,
                           Map<String, Object> mainData,
                           Map<String, Object> eachDetailData,
                           int userId,
                           int attempt,
                           boolean needRetry) {
        if (attempt > 5) {
            return "5次全部失败," + errorMsg; // 5次全部失败，返回失败
        }
        writeLog("第 " + attempt + " 次执行processJob");
        writeLog("errorMsg: " + errorMsg);
        writeLog("attempt: " + attempt);
        try {
            //String detailId = Util.null2String(eachDetailData.get("id"));
            // step1 : 调用全电查询接口
            errorMsg = callQueryEsb(config, mainData, eachDetailData);
            if (!errorMsg.isEmpty()) {
                //step 2 :调用板式文件 2023-11-16 这个逻辑先不用了，注释掉
                // errorMsg = callFileEsb(config, mainData, eachDetailData, userId);
                //step 3:获取当前最新主表数据、明细表数据
//                mainData = getMainData();
//                eachDetailData = getEachDetailData(detailId);
                //step 4: 检查是否都已经回写文件
//                if (errorMsg.isEmpty() && !checkAllFileSuccess(config, mainData)) {
//                    errorMsg = "未完全回写版式文件";
//                }
                //step 5：判断是否要重试,递归调用
                if (needRetry) {
                    // 继续递归调用，相隔2分钟
                    Thread.sleep(120000);
                    return process(errorMsg, config, mainData, eachDetailData, userId, attempt + 1, true);
                } else {
                    return errorMsg;
                }
            }
        } catch (Exception e) {
            errorMsg = "processJob 异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    /**
     * 检查所有版式文件链接都下载成功
     *
     * @param config
     * @param mainData
     * @return
     */
    private boolean checkAllFileSuccess(InvoiceRedQueryFileDto config, Map<String, Object> mainData) {
        String fieldFileLink, fieldFileLinkValue;
        //获取文件需要调用多次
        List<InvoiceRedQueryFileDto.QueryMoreFile> queryMoreFiles = config.getQueryMoreFile();
        for (InvoiceRedQueryFileDto.QueryMoreFile eachConfig : queryMoreFiles) {
            fieldFileLink = eachConfig.getFieldFileLink();
            fieldFileLinkValue = Util.null2String(mainData.get(fieldFileLink));
            if (fieldFileLinkValue.isEmpty()) {
                return false;
            }
        }
        return true;
    }


    /**
     * 调用查询接口ESB
     *
     * @param config
     * @param mainData
     * @param eachDetailData
     * @return
     */
    private String callQueryEsb(InvoiceRedQueryFileDto config,
                                Map<String, Object> mainData,
                                Map<String, Object> eachDetailData) {
        String errorMsg;
        String fromField, fromType, fromValue;
        try {
            //ESB时间名称
            String esbEventName = config.getQueryEsbEventName();
            //组装ESB参数
            JSONObject esbParam = new JSONObject();
            List<InvoiceRedQueryFileDto.QueryInField> inFields = config.getQueryInFields();
            for (InvoiceRedQueryFileDto.QueryInField eachField : inFields) {
                fromField = eachField.getFromField();
                fromType = eachField.getFromType();
                fromValue = geInFieldValue(fromField, fromType, mainData, eachDetailData);
                if (!fromValue.isEmpty()) {
                    esbParam.put(eachField.getEsbField(), fromValue);
                }
            }
            if (!esbParam.isEmpty()) {
                //调用ESB
                EsbEventResult er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                if (er.isSuccess()) {
                    //成功后，根据配置回写
                    errorMsg = writeBackQuery(config, er);
                } else {
                    errorMsg = er.getErroMsg();
                }
            } else {
                errorMsg = "获取到的ESB传入参数为空，请检查配置！";
            }
        } catch (Exception e) {
            errorMsg = "callQueryEsb 异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    private String callFileEsb(InvoiceRedQueryFileDto config,
                               Map<String, Object> mainData,
                               Map<String, Object> eachDetailData,
                               int userId) {
        String errorMsg = "";
        String fromField, fromType, fromValue, fieldFileLink, fieldFileLinkValue;
        writeLog("callFileEsb--START");
        try {
            String esbEventName = config.getFileEsbEventName();
            String detailId = Util.null2String(eachDetailData.get("id"));
            //获取文件需要调用多次
            List<InvoiceRedQueryFileDto.QueryMoreFile> queryMoreFiles = config.getQueryMoreFile();
            for (InvoiceRedQueryFileDto.QueryMoreFile eachConfig : queryMoreFiles) {
                writeLog("queryMoreFiles eachConfig:" + eachConfig);
                fieldFileLink = eachConfig.getFieldFileLink();
                fieldFileLinkValue = Util.null2String(mainData.get(fieldFileLink));
                writeLog("fieldFileLinkValue:" + fieldFileLinkValue);
                //只有在连接字段没值时触发回写文件动作
                if (fieldFileLinkValue.isEmpty()) {
                    //组装ESB参数
                    JSONObject esbParam = new JSONObject();
                    List<InvoiceRedQueryFileDto.FileInField> inFields = eachConfig.getFileInFields();
                    for (InvoiceRedQueryFileDto.FileInField eachField : inFields) {
                        fromField = eachField.getFromField();
                        fromType = eachField.getFromType();
                        fromValue = geInFieldValue(fromField, fromType, mainData, eachDetailData);
                        if (!fromValue.isEmpty()) {
                            esbParam.put(eachField.getEsbField(), fromValue);
                        }
                    }
                    if (!esbParam.isEmpty()) {
                        //调用ESB
                        EsbEventResult er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                        if (er.isSuccess()) {
                            //成功后，根据配置回写
                            errorMsg = writeBackFile(eachConfig, detailTableName, detailId, er, config.getFieldAttach(), config.getCatId(), userId);
                        } else {
                            errorMsg = er.getErroMsg();
                        }
                    } else {
                        errorMsg = "获取到的ESB传入参数为空，请检查配置！";
                    }
                }

            }

        } catch (Exception e) {
            errorMsg = "callQueryEsb 异常：" + SDUtil.getExceptionDetail(e);
        }
        writeLog("callFileEsb--END");
        return errorMsg;
    }


    /**
     * 获取当前数据库已经有的附件值
     *
     * @param fieldAttach
     * @param tableName
     * @param dataid
     * @return
     */
    private String getAlreadyAttachValue(String fieldAttach, String tableName, String dataid) {
        RecordSet rs = new RecordSet();
        String docids = "";
        String sql = "select " + fieldAttach + " as docid from " + tableName + " where id = " + dataid;
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                docids = Util.null2String(rs.getString("docid"));
            }
        }
        return docids;
    }


    /**
     * 获取ESB入参值
     *
     * @param fromField
     * @param fromType
     * @param mainData
     * @param eachDetailData
     * @return
     */
    private String geInFieldValue(String fromField,
                                  String fromType,
                                  Map<String, Object> mainData,
                                  Map<String, Object> eachDetailData) {
        String result = "";
        //0流程主表
        if ("0".equals(fromType)) {
            result = Util.null2String(mainData.get(fromField));
        } else if ("1".equals(fromType)) {
            //1明细表
            result = Util.null2String(eachDetailData.get(fromField));
        } else if ("2".equals(fromType)) {
            //2固定值
            result = fromField;
        }
        return result;
    }

    /**
     * 回写查询接口数据
     *
     * @param config
     * @param er
     * @return
     */
    private String writeBackQuery(InvoiceRedQueryFileDto config, EsbEventResult er) {
        String errorMsg = "";
        String fromField, fromType, wfField, wfFieldValue;
        String eachField;
        StringBuilder sbUpdate = new StringBuilder();
        List<String> updateFields = new ArrayList<>();
        List<Object> updateValues = new ArrayList<>();
        try {
            //获取回写的字段配置
            List<InvoiceRedQueryFileDto.QueryWriteBackField> writeBackFields = config.getQueryWriteBackFields();
            //获取ESB响应数据
            JSONObject esbReturnData = er.getData();
            JSONArray model = esbReturnData.getJSONArray("model");
            if (model != null && !model.isEmpty()) {
                JSONObject modelFirst = model.getJSONObject(0);
                writeLog("writeBackFields:" + writeBackFields);
                writeLog("esbReturnData:" + esbReturnData);
                for (InvoiceRedQueryFileDto.QueryWriteBackField eachConfig : writeBackFields) {
                    fromField = eachConfig.getFromField();
                    fromType = eachConfig.getFromType();
                    wfField = eachConfig.getWfField();
                    if (!fromField.isEmpty() && !fromType.isEmpty() && !wfField.isEmpty()) {
                        //获取设置的流程字段值
                        wfFieldValue = getWriteBackFieldValue(fromField, fromType, modelFirst);
                        if (!wfFieldValue.isEmpty()) {
                            updateFields.add(wfField);
                            updateValues.add(wfFieldValue);
                        }
                    }
                }
            } else {
                errorMsg = "未获取到响应model节点数据";
            }
        } catch (Exception e) {
            errorMsg = "writeBackQuery异常：" + SDUtil.getExceptionDetail(e);
        }
        if (updateFields.isEmpty()) {
            writeLog("updateFields 为空，跳过回写");
        } else {
            //拼接update语句，value值用?占位
            for (int i = 0; i < updateFields.size(); i++) {
                eachField = updateFields.get(i);
                sbUpdate.append(eachField).append("=").append("?");
                if (i < updateFields.size() - 1) {
                    sbUpdate.append(", ");
                }
            }
            // step 2 : 定义更新数据的sql
            String sql = "update " + mainTableName + " set " + sbUpdate + " where requestid= " + requestid;
            writeLog("update mainTableName sql:" + sql);
            writeLog("update mainTableName updateValues: " + updateValues);
            RecordSet rs = new RecordSet();
            if (!rs.executeUpdate(sql, updateValues)) {
                errorMsg = "回写表单出错：" + rs.getExceptionMsg();
                writeLog("回写表单出错：" + rs.getExceptionMsg());
            }
        }
        return errorMsg;
    }

    /**
     * 版式文件回写
     *
     * @param config
     * @param detailTableName
     * @param detailId
     * @param er
     * @return
     */
    private String writeBackFile(InvoiceRedQueryFileDto.QueryMoreFile config,
                                 String detailTableName,
                                 String detailId,
                                 EsbEventResult er,
                                 String fieldAttach,
                                 int catId,
                                 int userId) {
        String errorMsg = "";
        String fromField, fromType, wfField, wfFieldValue;
        String eachField;
        StringBuilder sbUpdate = new StringBuilder();
        List<String> updateFields = new ArrayList<>();
        List<Object> updateValues = new ArrayList<>();
        try {
            //查询接口回写
            //板式文件接口回写
            List<InvoiceRedQueryFileDto.FileWriteBackField> writeBackFields = config.getFileWriteBackFields();
            JSONObject esbReturnData = er.getData().getJSONObject("model");
            JSONObject model = esbReturnData.getJSONObject("model");
            if (model != null && !model.isEmpty()) {
                writeLog("writeBackFields:" + writeBackFields);
                writeLog("model:" + model);
                for (InvoiceRedQueryFileDto.FileWriteBackField eachConfig : writeBackFields) {
                    fromField = eachConfig.getFromField();
                    fromType = eachConfig.getFromType();
                    wfField = eachConfig.getWfField();
                    if (!fromField.isEmpty() && !fromType.isEmpty() && !wfField.isEmpty()) {
                        //获取设置的流程字段值
                        wfFieldValue = getWriteBackFieldValue(fromField, fromType, model);
                        if (!wfFieldValue.isEmpty()) {
                            updateFields.add(wfField);
                            updateValues.add(wfFieldValue);
                        }
                    }
                }

                if (updateFields.isEmpty()) {
                    writeLog("updateFields 为空，跳过回写");
                } else {
                    //拼接update语句，value值用?占位
                    for (int i = 0; i < updateFields.size(); i++) {
                        eachField = updateFields.get(i);
                        sbUpdate.append(eachField).append("=").append("?");
                        if (i < updateFields.size() - 1) {
                            sbUpdate.append(", ");
                        }
                    }

                    // step 2 : 定义更新数据的sql
                    String sql = "update " + detailTableName + " set " + sbUpdate + " where id= " + detailId;
                    writeLog("update detailtable sql:" + sql);
                    writeLog("update detailtable updateValues: " + updateValues);
                    RecordSet rs = new RecordSet();
                    if (!rs.executeUpdate(sql, updateValues)) {
                        errorMsg = "版式文件接口回写明细表单出错：" + rs.getExceptionMsg();
                    } else {
                        //回写成功后，将文件下载下来回写到文档并回写到表单
                        String fileUrl = Util.null2String(er.getData().getJSONObject("model").get("query_data"));
                        if (fileUrl.isEmpty()) {
                            errorMsg = "未获取到文件url地址";
                        } else {
                            //将文件url转换为OA的文档
                            int docId = DocUtil.addUrlFile2Doc(fileUrl, catId, userId);
                            if (docId == -1) {
                                errorMsg = "转换OA文档出错";
                            } else {
                                //文档生成成功后，回写表单字段
                                String finalDocIds;
                                //查询当前表单有无附件数据，有的话叠加
                                String alreadyDocIds = getAlreadyAttachValue(fieldAttach, detailTableName, detailId);
                                if (alreadyDocIds.isEmpty()) {
                                    finalDocIds = String.valueOf(docId);
                                } else {
                                    finalDocIds = alreadyDocIds + "," + docId;
                                }
                                sql = "update " + detailTableName + " set " + fieldAttach + " = '" + finalDocIds + "' where id = " + detailId;
                                writeLog("回写文档id sql：" + sql);
                                if (!rs.executeUpdate(sql)) {
                                    errorMsg = "回写文档id出错：" + rs.getExceptionMsg();
                                }
                            }
                        }
                    }
                }
            } else {
                errorMsg = "未获取到接口model节点响应数据";
            }
        } catch (Exception e) {
            errorMsg = "回写异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    private String getWriteBackFieldValue(String fromField, String fieldType, JSONObject esbReturnData) {
        String result = "";
        //ESB响应的字段值
        if ("0".equals(fieldType)) {
            result = Util.null2String(esbReturnData.get(fromField));
        } else if ("1".equals(fieldType)) {
            //固定值
            result = fromField;
        }
        return result;
    }

    /**
     * 校验参数
     *
     * @param config
     * @return
     */
    private String checkParam(InvoiceRedQueryFileDto config) {
        if (config == null) {
            return "未获取到对应的开发平台配置信息，请检查！";
        }
        if (StringUtils.isBlank(config.getDtIndex()) ||
                StringUtils.isBlank(config.getFieldAttach()) ||
                StringUtils.isBlank(config.getFieldFlag()) ||
                StringUtils.isBlank(config.getQueryEsbEventName()) ||
                config.getQueryInFields() == null ||
                config.getQueryWriteBackFields() == null ||
                config.getQueryMoreFile() == null ||
                config.getQueryInFields().isEmpty() ||
                config.getQueryWriteBackFields().isEmpty() ||
                config.getQueryMoreFile().isEmpty() ||
                StringUtils.isBlank(config.getFileEsbEventName()) ||
                StringUtils.isBlank(config.getFieldQueryStatus()) ||
                StringUtils.isBlank(config.getFieldQueryDraw())
        ) {
            return "配置缺失必填项";
        }
        return "";
    }

}
