package com.engine.workflow.invoice.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.workflow.invoice.dto.InvoiceFileDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.ThreadPoolUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * @FileName BWInvoiceFileAction.java
 * @Description 百望开票, 查询，下载action
 * 通过一系列接口，最终要下载文件到流程上
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/21
 */
@Getter
@Setter
public class BWInvoiceFileAction extends BaseBean implements Action {
    //---Action参数---
    /**
     * 功能列表的功能id
     */
    private String functionId;
    //---Action参数---

    /**
     * 当前action使用的线程池名称
     */
    private final static String THREAD_POOL_NAME = "BWInvoiceFileAction_Pool";

    /**
     * 流程主表名称
     */
    private String mainTableName;
    /**
     * 流程requestid
     */
    private String requestid;
    /**
     * 主表id
     */
    private String mainId;

    // 创建一个对象作为锁
    private static final Object lock = new Object();

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //获取action相关信息
        ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
        //这里做异步操作，不影响流程流转
        ExecutorService executorService = ThreadPoolUtil.getThreadPool(THREAD_POOL_NAME, "5");
        int userId = actionInfo.getUser().getUID();
        writeLog("Action触发全电票--START");
        writeLog("Action触发全电票--requestid:" + actionInfo.getRequestId());
        executorService.execute(() -> executeData(actionInfo.getRequestId(), functionId, userId, true));
        writeLog(this.getClass().getName() + "---END");
        return ActionUtil.handleResult("", requestInfo);
    }

    private void _init() {
        mainTableName = "";
        requestid = "";
        mainId = "";
    }

    /**
     * 实际执行
     *
     * @param requestidParam
     * @param functionId
     * @param userId
     * @param needRetry      是否需要重试 调用接口
     * @return
     */
    public synchronized String executeData(String requestidParam, String functionId, int userId, boolean needRetry) {
        synchronized (lock) {
            // step 1: 校验参数
            // step 2: 调用全电票查询接口
            // step 3: step2 成功调用板式文件生成接口-下载文件到OA文档-回写表单
            // step 4: step2 失败要重复走5次
            String errorMsg;
            writeLog("BWInvoiceFileAction executeData--START");
            writeLog("requestid:" + requestidParam);
            writeLog("functionId:" + functionId);
            writeLog("userId:" + userId);
            try {
                //step 0：初始化
                _init();
                requestid = requestidParam;
                //step 1 : 根据requestid获取流程相关信息
                WfInfo wfInfo = WfUtil.getWfInfoByReqId(requestid);
                if (wfInfo.getFormtableName().isEmpty()) {
                    errorMsg = "未获取到requestid:" + requestid + "的相关流程信息";
                } else {
                    mainTableName = wfInfo.getFormtableName();
                    Map<String, Object> mainData = getMainData();
                    if (mainData == null) {
                        errorMsg = "未查询到流程主数据";
                    } else {
                        writeLog("mainData:" + mainData);
                        mainId = Util.null2String(mainData.get("id"));
                        //step 1 : 根据workflowid，functionid，读取配置
                        InvoiceFileDto config = WfConfigUtil.getObjConfigWithoutEnable(functionId, wfInfo.getWorkflowId(), InvoiceFileDto.class);
                        writeLog("config:" + config);
                        //step 2:校验配置
                        errorMsg = checkParam(config);
                        if (errorMsg.isEmpty()) {
                            String fieldFlagValue = Util.null2String(mainData.get(config.getFieldFlag()));
                            //0为全电票，非全电票不走逻辑
                            if ("0".equals(fieldFlagValue)) {
                                //step 3: 递归执行
                                errorMsg = process("", config, userId, 1, needRetry);
                            } else {
                                writeLog("非全电票");
                            }
                        }
                    }


                }

            } catch (Exception e) {
                errorMsg = SDUtil.getExceptionDetail(e);
            }
            return errorMsg;
        }
    }


    /**
     * 递归执行
     *
     * @param errorMsg
     * @param config
     * @param userId
     * @param attempt
     * @param needRetry
     * @return
     */
    private String process(String errorMsg, InvoiceFileDto config, int userId, int attempt, boolean needRetry) {
        if (attempt > 5) {
            return "5次全部失败," + errorMsg; // 5次全部失败，返回失败
        }
        writeLog("第 " + attempt + " 次执行processJob");
        writeLog("errorMsg: " + errorMsg);
        writeLog("attempt: " + attempt);

        try {
            //判断是否需要调用下载文件，根据多个下载链接字段来判断
            // step1 : 调用全电查询接口
            errorMsg = callQueryEsb(config, mainTableName);
            if (errorMsg.isEmpty()) {
                //step 2 :调用板式文件
                errorMsg = callFileEsb(config, mainTableName, userId);
                //step 4: 检查是否都已经回写文件
                if (errorMsg.isEmpty() && !checkAllFileSuccess(config)) {
                    errorMsg = "未完全回写版式文件";
                }
                //step 5：有错误信息时，判断是否要重试,递归调用
                if (!errorMsg.isEmpty() && needRetry) {
                    // 继续递归调用，相隔2分钟
                    Thread.sleep(120000);
                    return process(errorMsg, config, userId, attempt + 1, true);
                } else {
                    return errorMsg;
                }
            }
        } catch (Exception e) {
            errorMsg = "processJob 异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    /**
     * 获取主表数据
     *
     * @return
     */
    private Map<String, Object> getMainData() {
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + mainTableName + " where requestid = ?", requestid)) {
            return QueryUtil.getMap(rs);
        }
        return new HashMap<>();
    }

    /**
     * 检查所有版式文件链接都下载成功
     *
     * @param config
     * @return
     */
    private boolean checkAllFileSuccess(InvoiceFileDto config) {
        Map<String, Object> mainData = getMainData();
        String fieldFileLink, fieldFileLinkValue;
        List<InvoiceFileDto.QueryMoreFile> queryMoreFiles = config.getQueryMoreFiles();
        for (InvoiceFileDto.QueryMoreFile eachConfig : queryMoreFiles) {
            fieldFileLink = eachConfig.getFieldFileLink();
            fieldFileLinkValue = Util.null2String(mainData.get(fieldFileLink));
            if (fieldFileLinkValue.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 调用版式文件查询
     *
     * @param config
     * @param mainTableName
     * @return
     */
    private String callQueryEsb(InvoiceFileDto config, String mainTableName) {
        String errorMsg;
        String fromField, fromType, fromValue;
        try {
            Map<String, Object> mainData = getMainData();
            String esbEventName = config.getQueryEsbEventName();
            String detailIndx = config.getDtIndex();
            String detailTableName = mainTableName + "_dt" + detailIndx;
            Map<String, Object> firstDetailData = getFirstDetailData(detailTableName);
            //组装ESB参数
            JSONObject esbParam = new JSONObject();
            List<InvoiceFileDto.QueryInField> inFields = config.getQueryInFields();
            for (InvoiceFileDto.QueryInField eachField : inFields) {
                fromField = eachField.getFromField();
                fromType = eachField.getFromType();
                fromValue = geInFieldValue(fromField, fromType, mainData, firstDetailData);
                if (!fromValue.isEmpty()) {
                    esbParam.put(eachField.getEsbField(), fromValue);
                }
            }
            if (!esbParam.isEmpty()) {
                //调用ESB
                esbParam.put("requestid", requestid);
                EsbEventResult er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                if (er.isSuccess()) {
                    //成功后，根据配置回写
                    errorMsg = writeBackQuery(config, mainTableName, er);
                } else {
                    errorMsg = er.getErroMsg();
                }
            } else {
                errorMsg = "获取到的ESB传入参数为空，请检查配置！";
            }
        } catch (Exception e) {
            errorMsg = "callQueryEsb 异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    /**
     * 调用版式文件查询并回写
     *
     * @param config
     * @param mainTableName
     * @param userId
     * @return
     */
    private String callFileEsb(InvoiceFileDto config, String mainTableName, int userId) {
        String errorMsg = "";
        String fromField, fromType, fromValue, fieldFileLink, fieldFileLinkValue, isXml;
        Map<String, Object> mainData;
        EsbEventResult er;
        try {
            String esbEventName = config.getFileEsbEventName();
            String xmlFileEsbName = config.getXmlFileEsbName();
            String detailIndx = config.getDtIndex();
            String detailTableName = mainTableName + "_dt" + detailIndx;
            //获取明细第一行数据
            Map<String, Object> firstDetailData = getFirstDetailData(detailTableName);
            writeLog("firstDetailData:" + firstDetailData);
            //多个获取文件的配置
            //获取文件需要调用多次
            List<InvoiceFileDto.QueryMoreFile> queryMoreFiles = config.getQueryMoreFiles();
            for (InvoiceFileDto.QueryMoreFile eachConfig : queryMoreFiles) {
                //每一次配置获取调用ESB文件查询接口，获取文件并反写
                //step 3:获取当前最新主表数据
                mainData = getMainData();
                writeLog("queryMoreFiles eachConfig:" + eachConfig);

                fieldFileLink = eachConfig.getFieldFileLink();
                fieldFileLinkValue = Util.null2String(mainData.get(fieldFileLink));
                writeLog("fieldFileLinkValue:" + fieldFileLinkValue);
                //是否是XML字段，如果是则需要调用XML接口
                isXml = eachConfig.getIsXml();
                //只有在连接字段没值时触发回写文件动作
                if (fieldFileLinkValue.isEmpty()) {
                    //组装ESB参数
                    JSONObject esbParam = new JSONObject();
                    List<InvoiceFileDto.FileInField> inFields = eachConfig.getFileInFields();
                    for (InvoiceFileDto.FileInField eachField : inFields) {
                        fromField = eachField.getFromField();
                        fromType = eachField.getFromType();
                        writeLog("callFileEsb fromField:" + fromField);
                        writeLog("callFileEsb fromType:" + fromType);
                        fromValue = geInFieldValue(fromField, fromType, mainData, firstDetailData);
                        writeLog("callFileEsb fromValue:" + fromValue);
                        if (!fromValue.isEmpty()) {
                            esbParam.put(eachField.getEsbField(), fromValue);
                        }
                    }
                    if (!esbParam.isEmpty()) {
                        esbParam.put("requestid", requestid);
                        if ("1".equals(isXml)) {
                            //调用XML文件查询ESB
                            er = EsbUtil.callEsbEvent(xmlFileEsbName, esbParam.toJSONString());
                        } else {
                            //调用版式文件查询ESB
                            er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                        }
                        if (er.isSuccess()) {
                            //成功后，根据配置回写
                            errorMsg = writeBackFile(eachConfig, mainTableName, er, config.getFieldAttach(), config.getCatId(), userId);
                        } else {
                            errorMsg = er.getErroMsg();
                        }
                    } else {
                        errorMsg = "获取到的ESB传入参数为空，请检查配置！";
                    }
                }
            }

        } catch (Exception e) {
            errorMsg = "callQueryEsb 异常：" + SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }


    private Map<String, Object> getFirstDetailData(String detailTableName) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + detailTableName + " where mainid = ?", mainId)) {
            List<Map<String, Object>> list = QueryUtil.getMapList(rs);
            if (!list.isEmpty()) {
                result = list.get(0);
            }
        }
        return result;
    }

    private String geInFieldValue(String fromField,
                                  String fromType,
                                  Map<String, Object> mainData,
                                  Map<String, Object> firstDetailData) {
        String result = "";
        //流程主表
        if ("0".equals(fromType)) {
            result = Util.null2String(mainData.get(fromField));
        } else if ("1".equals(fromType)) {
            //明细表
            result = Util.null2String(firstDetailData.get(fromField));
        } else if ("2".equals(fromType)) {
            //2固定值
            result = fromField;
        }
        return result;
    }

    /**
     * 查询接口回写
     *
     * @param config
     * @param mainTableName
     * @param er
     * @return
     */
    private String writeBackQuery(InvoiceFileDto config, String mainTableName, EsbEventResult er) {
        String errorMsg = "";
        String fromField, fromType, wfField, wfFieldValue;
        String eachField;
        StringBuilder sbUpdate = new StringBuilder();
        List<String> updateFields = new ArrayList<>();
        List<Object> updateValues = new ArrayList<>();
        writeLog("writeBackQuery ---START");
        try {
            List<InvoiceFileDto.QueryWriteBackField> writeBackFields = config.getQueryWriteBackFields();
            JSONObject esbReturnData = er.getData().getJSONArray("model").getJSONObject(0);
            writeLog("writeBackFields:" + writeBackFields);
            writeLog("esbReturnData:" + esbReturnData);
            for (InvoiceFileDto.QueryWriteBackField eachConfig : writeBackFields) {
                fromField = eachConfig.getFromField();
                fromType = eachConfig.getFromType();
                wfField = eachConfig.getWfField();
                if (!fromField.isEmpty() && !fromType.isEmpty() && !wfField.isEmpty()) {
                    //获取设置的流程字段值
                    wfFieldValue = getWriteBackFieldValue(fromField, fromType, esbReturnData);
                    if (!wfFieldValue.isEmpty()) {
                        updateFields.add(wfField);
                        updateValues.add(wfFieldValue);
                    }
                }
            }
            if (updateFields.isEmpty()) {
                writeLog("updateFields 为空，跳过回写");
            } else {
                //拼接update语句，value值用?占位
                for (int i = 0; i < updateFields.size(); i++) {
                    eachField = updateFields.get(i);
                    sbUpdate.append(eachField).append("=").append("?");
                    if (i < updateFields.size() - 1) {
                        sbUpdate.append(", ");
                    }
                }

                // step 2 : 定义插入数据的sql
                String sql = "update " + mainTableName + " set " + sbUpdate + " where id= " + mainId;

                writeLog("update formtable sql:" + sql);
                writeLog("update formtable updateValues: " + updateValues);
                RecordSet rs = new RecordSet();
                if (!rs.executeUpdate(sql, updateValues)) {
                    errorMsg = "回写明细表单出错：" + rs.getExceptionMsg();
                }
            }
        } catch (Exception e) {
            errorMsg = "回写异常：" + SDUtil.getExceptionDetail(e);
        }
        writeLog("writeBackQuery ---END");
        return errorMsg;
    }

    /**
     * 回写附件接口
     *
     * @param config
     * @param mainTableName
     * @param er
     * @return
     */
    private String writeBackFile(InvoiceFileDto.QueryMoreFile config,
                                 String mainTableName,
                                 EsbEventResult er,
                                 String fieldAttach,
                                 int catId,
                                 int userId) {
        String errorMsg = "";
        String fromField, fromType, wfField, wfFieldValue;
        String eachField;
        StringBuilder sbUpdate = new StringBuilder();
        List<String> updateFields = new ArrayList<>();
        List<Object> updateValues = new ArrayList<>();
        writeLog("writeBackFile ---START");
        try {
            //是否是XML字段，如果是则需要用xml接口返回的文件地址
            String isXml = config.getIsXml();
            //板式文件接口回写
            List<InvoiceFileDto.FileWriteBackField> writeBackFields = config.getFileWriteBackFields();
            JSONObject esbReturnData = er.getData().getJSONObject("model");
            writeLog("writeBackFields:" + writeBackFields);
            writeLog("esbReturnData:" + esbReturnData);
            RecordSet rs = new RecordSet();
            //组装要回写更新的字段和值
            for (InvoiceFileDto.FileWriteBackField eachConfig : writeBackFields) {
                fromField = eachConfig.getFromField();
                fromType = eachConfig.getFromType();
                wfField = eachConfig.getWfField();
                if (!fromField.isEmpty() && !fromType.isEmpty() && !wfField.isEmpty()) {
                    //获取设置的流程字段值
                    wfFieldValue = getWriteBackFieldValue(fromField, fromType, esbReturnData);
                    if (!wfFieldValue.isEmpty()) {
                        updateFields.add(wfField);
                        updateValues.add(wfFieldValue);
                    }
                }
            }

            if (!updateFields.isEmpty()) {
                //拼接update语句，value值用?占位
                for (int i = 0; i < updateFields.size(); i++) {
                    eachField = updateFields.get(i);
                    sbUpdate.append(eachField).append("=").append("?");
                    if (i < updateFields.size() - 1) {
                        sbUpdate.append(", ");
                    }
                }
                // step 2 : 定义插入数据的sql
                String sql = "update " + mainTableName + " set " + sbUpdate + " where id= " + mainId;
                writeLog("update formtable sql:" + sql);
                writeLog("update formtable updateValues: " + updateValues);

                if (!rs.executeUpdate(sql, updateValues)) {
                    errorMsg = "回写明细表单出错：" + rs.getExceptionMsg();
                }
            } else {
                writeLog("updateFields 为空，跳过回写");
            }
            //回写表单字段后，回写文件到主表的字段上
            if (errorMsg.isEmpty()) {
                String fileUrl;
                if ("1".equals(isXml)) {
                    fileUrl = Util.null2String(er.getData().getJSONObject("model").get("xml_url"));
                } else {
                    fileUrl = Util.null2String(er.getData().getJSONObject("model").get("query_data"));
                }

                if (fileUrl.isEmpty()) {
                    errorMsg = "未获取到文件url地址";
                } else {
                    writeLog("fileUrl:" + fileUrl);
                    //将文件url转换为OA的文档
                    int docId = DocUtil.addUrlFile2Doc(fileUrl, catId, userId);
                    if (docId == -1) {
                        errorMsg = "转换OA文档出错";
                    } else {
                        //文档生成成功后，回写表单字段
                        String finalDocIds;
                        //查询当前表单有无附件数据，有的话叠加
                        String alreadyDocIds = getAlreadyAttachValue(fieldAttach, mainTableName, mainId);
                        if (alreadyDocIds.isEmpty()) {
                            finalDocIds = String.valueOf(docId);
                        } else {
                            finalDocIds = alreadyDocIds + "," + docId;
                        }
                        String sql = "update " + mainTableName + " set " + fieldAttach + " = '" + finalDocIds + "' where id = " + mainId;
                        writeLog("回写文档id sql：" + sql);
                        if (!rs.executeUpdate(sql)) {
                            errorMsg = "回写文档id出错：" + rs.getExceptionMsg();
                        }
                    }
                }
            }

        } catch (Exception e) {
            errorMsg = "回写异常：" + SDUtil.getExceptionDetail(e);
        }
        writeLog("writeBackFile ---END");
        return errorMsg;
    }


    /**
     * 获取当前数据库已经有的附件值
     *
     * @param fieldAttach
     * @param tableName
     * @param dataid
     * @return
     */
    private String getAlreadyAttachValue(String fieldAttach, String tableName, String dataid) {
        RecordSet rs = new RecordSet();
        String docids = "";
        String sql = "select " + fieldAttach + " as docid from " + tableName + " where id = " + dataid;
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                docids = Util.null2String(rs.getString("docid"));
            }
        }
        return docids;
    }

    private String getWriteBackFieldValue(String fromField, String fieldType, JSONObject esbReturnData) {
        String result = "";
        //ESB响应的字段值
        if ("0".equals(fieldType)) {
            result = Util.null2String(esbReturnData.get(fromField));
        } else if ("1".equals(fieldType)) {
            //固定值
            result = fromField;
        }
        return result;
    }

    private String checkParam(InvoiceFileDto config) {
        if (config == null) {
            return "未获取到对应的开发平台配置信息，请检查！";
        }
        if (StringUtils.isBlank(config.getDtIndex()) ||
                StringUtils.isBlank(config.getQueryEsbEventName()) ||
                StringUtils.isBlank(config.getFileEsbEventName()) ||
                StringUtils.isBlank(config.getFieldFlag()) ||
                config.getQueryMoreFiles() == null ||
                config.getQueryInFields() == null ||
                config.getQueryInFields().isEmpty() ||
                config.getQueryMoreFiles().isEmpty()
        ) {
            return "配置缺失必填项";
        }
        return "";
    }

}
