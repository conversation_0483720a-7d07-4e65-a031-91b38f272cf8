package com.engine.workflow.invoice.executor;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.control.util.WfConfigUtil;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.workflow.invoice.dto.InvoiceRedApplyDto;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;


/**
 * @FileName BWInvoiceRedRefreshStatusExe.java
 * @Description 调用红字确认单申请刷新状态
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/3/3
 */
public class BWInvoiceRedRefreshStatusExe {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 流程主表名称
     */
    private String mainTableName;
    /**
     * 流程明细表名
     */
    private String detailTableName;
    /**
     * 流程requestid
     */
    private String requestid;
    /**
     * 流程workflowid
     */
    private String workflowid;
    /**
     * 流程主表数据map
     */
    private Map<String, Object> mainData;
    /**
     * 出错信息
     */
    private String error;
    /**
     * 线程池执行超时时间 分钟
     */
    private static final int POOL_TIMEOUT = 10;


    /**
     * 初始化
     */
    private void _init() {
        mainTableName = "";
        detailTableName = "";
        requestid = "";
        workflowid = "";
        mainData = null;
        error = "";
    }

    /**
     * 执行
     *
     * @param requestidParam
     * @param functionId
     * @param userId
     * @return
     */
    public synchronized String execute(String requestidParam, String functionId, int userId) {
        log.info(this.getClass().getName() + "---START");
        log.info("BWInvoiceRedRefreshStatusExe execute--START");
        log.info("requestid:" + requestidParam);
        log.info("functionId:" + functionId);
        log.info("userId:" + userId);
        //明细数据
        List<Map<String, Object>> detailData;
        try {
            //初始化
            _init();
            //设置requestid
            requestid = requestidParam;
            //step 1 : 根据requestid获取流程相关信息
            WfInfo wfInfo = WfUtil.getWfInfoByReqId(requestidParam);
            if (wfInfo.getFormtableName().isEmpty()) {
                error = "未获取到requestid:" + requestidParam + "的相关流程信息";
            }
            if (error.isEmpty()) {
                //step 1 : 根据workflowid，functionid，读取配置
                workflowid = wfInfo.getWorkflowId();
                log.info("wfInfo:" + wfInfo);
                InvoiceRedApplyDto config = WfConfigUtil.getObjConfigWithoutEnable(functionId, workflowid, InvoiceRedApplyDto.class);
                log.info("config:" + config);
                //step 2:校验配置
                error = checkParam(config);
                if (error.isEmpty()) {
                    //step 2: 获取主表、明细表数据数据
                    mainTableName = wfInfo.getFormtableName();
                    log.info("mainTableName:" + mainTableName);
                    mainData = getMainData();
                    if (mainData == null || mainData.isEmpty()) {
                        error = "未获取到主表数据";
                    }
                    if (error.isEmpty()) {
                        log.info("mainData:" + mainData);
                        String mainid = Util.null2String(mainData.get("id"));
                        //明细index
                        int detailIndex = Util.getIntValue(config.getDtIndex());
                        detailTableName = mainTableName + "_dt" + detailIndex;
                        detailData = getDetailData(mainid);
                        log.info("detailData:" + detailData);
                        if (detailData.isEmpty()) {
                            error = "未获取到明细表数据";
                        }
                        if (error.isEmpty()) {
                            //是否是全电红冲字段名，是的情况才走接口
                            String fieldFlag = config.getFieldFlag();
                            String fieldFlagValue = Util.null2String(mainData.get(fieldFlag));
                            log.info("fieldFlagValue:" + fieldFlagValue);
                            //0为全电票，非全电票不走逻辑
                            if ("0".equals(fieldFlagValue)) {
                                List<String> allErrorMsg = new ArrayList<>();
                                // 使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
                                ExecutorService executorService = Executors.newFixedThreadPool(5);
                                CountDownLatch latch = new CountDownLatch(detailData.size());
                                for (Map<String, Object> eachDetail : detailData) {
                                    //判断明细apiflag字段是否已经成功
                                    executorService.submit(() -> {
                                        try {
                                            //处理明细的每一行数据
                                            String eachErrroMsg = processDetailData(eachDetail, config);
                                            if (!eachErrroMsg.isEmpty()) {
                                                allErrorMsg.add(eachErrroMsg);
                                            }
                                        } finally {
                                            latch.countDown(); // 线程完成任务后减少计数器
                                        }
                                    });
                                }
                                try {
                                    // 等待所有线程完成任务，超时POOL_TIMEOUT分钟
                                    if (!latch.await(POOL_TIMEOUT, TimeUnit.MINUTES)) {
                                        log.info("超时未执行完所有线程任务，请检查");
                                    }
                                } catch (InterruptedException e) {
                                    log.error("latch.await() 出错：" + SDUtil.getExceptionDetail(e));
                                } finally {
                                    executorService.shutdown();
                                }
                                if (error.isEmpty() && !allErrorMsg.isEmpty()) {
                                    error = allErrorMsg.toString();
                                }
                                log.info("执行完毕");
                            } else {
                                log.info("非全电票");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            error = SDUtil.getExceptionDetail(e);
        }
        log.info("error:" + error);
        log.info(this.getClass().getName() + "---END");
        return error;

    }

    /**
     * 执行明细数据
     *
     * @param eachDetail
     * @param config
     */
    private String processDetailData(Map<String, Object> eachDetail, InvoiceRedApplyDto config) {
        String esbEventName, error = "";
        String fromField, fieldType, fromValue;
        EsbEventResult er = null;
        String detailId = "";
        try {
            log.info("processDetailData--START");
            log.info("processDetailData eachDetail:" + eachDetail);
            detailId = Util.null2String(eachDetail.get("id"));
            List<InvoiceRedApplyDto.InField> inFields = config.getInFields();
            //ESB 事件名
            esbEventName = config.getEsbEventName();
            //组装ESB参数
            JSONObject esbParam = new JSONObject();
            //入参
            for (InvoiceRedApplyDto.InField eachFieldConfig : inFields) {
                fromField = eachFieldConfig.getFromField();
                fieldType = eachFieldConfig.getFromType();
                fromValue = getFromFieldValue(fromField, fieldType, eachDetail);
                log.info("fromField :" + fromField + ",fromValue:" + fromValue);
                if (StringUtils.isNotBlank(fromValue)) {
                    esbParam.put(eachFieldConfig.getEsbField(), fromValue);
                }
            }
            log.info("esbParam:" + esbParam);
            if (!esbParam.isEmpty()) {
                //调用ESB
                er = EsbUtil.callEsbEvent(esbEventName, esbParam.toJSONString());
                if (!er.isSuccess()) {
                    error = er.getErroMsg();
                }
            } else {
                error = "获取到的ESB传入参数为空，请检查配置！";
            }
        } catch (Exception e) {
            error = "processDetailData 异常:" + SDUtil.getExceptionDetail(e);
        }

        if (!error.isEmpty()) {
            log.info("processDetailData出错：" + error);
        }
        if (!detailId.isEmpty() && error.isEmpty()) {
            //根据配置回写
            error = writeBack(config, er, detailId);
        } else {
            log.info("detailId 为空");
        }
        return error;
    }

    private String getFromFieldValue(String fromField, String fieldType, Map<String, Object> detailData) {
        String result = "";
        //0 流程主表字段
        if ("0".equals(fieldType)) {
            result = Util.null2String(mainData.get(fromField));
        } else if ("1".equals(fieldType)) {
            //1 当前明细行字段
            result = Util.null2String(detailData.get(fromField));
        } else if ("2".equals(fieldType)) {
            //2 固定值
            result = fromField;
        } else if ("3".equals(fieldType)) {
            //表达式
            if ("$requestid$".equals(fromField)) {
                result = requestid;
            } else if ("$workflowid$".equals(fromField)) {
                result = workflowid;
            }
        }
        return result;
    }

    /**
     * 回写
     *
     * @param config
     * @param er
     * @param detailId
     */
    private String writeBack(InvoiceRedApplyDto config, EsbEventResult er, String detailId) {
        String error = "";
        String fromField, fromType, wfField, wfFieldValue;
        List<String> updateFields = new ArrayList<>();
        List<Object> updateValues = new ArrayList<>();
        String sql;
        StringBuilder sbUpdate = new StringBuilder();
        try {
            log.info("writeBack--START");
            String detailIndex = config.getDtIndex();
            String detailTableName = mainTableName + "_dt" + detailIndex;
            String fieldApiFlag = config.getFieldApiFlag();
            RecordSet rs = new RecordSet();
            //成功才回写数据
            JSONObject esbReturnData = er.getData();
            JSONArray model = esbReturnData.getJSONArray("model");
            if (model != null && !model.isEmpty()) {
                JSONObject firstModel = model.getJSONObject(0);
                //回写字段配置
                List<InvoiceRedApplyDto.WriteBackField> writeBackFields = config.getWriteBackFields();
                for (InvoiceRedApplyDto.WriteBackField eachConfig : writeBackFields) {
                    fromField = eachConfig.getFromField();
                    fromType = eachConfig.getFromType();
                    wfField = eachConfig.getWfField();
                    //获取反写的字段值
                    wfFieldValue = getWriteBackFieldValue(fromField, fromType, firstModel);
                    if (!wfFieldValue.isEmpty()) {
                        updateFields.add(wfField);
                        updateValues.add(wfFieldValue);
                    }
                }
                //默认更新接口标记为成功
                updateFields.add(fieldApiFlag);
                updateValues.add("0");

                //拼接update语句，value值用?占位
                for (int i = 0; i < updateFields.size(); i++) {
                    sbUpdate.append(updateFields.get(i)).append("=").append("?");
                    if (i < updateFields.size() - 1) {
                        sbUpdate.append(", ");
                    }
                }
                // step 2 : 定义更新数据的sql
                sql = "update " + detailTableName + " set " + sbUpdate + " where id= " + detailId;
                log.info("update formtable sql:" + sql);
                log.info("update formtable updateValues:" + updateValues);
                if (!rs.executeUpdate(sql, updateValues)) {
                    error = "回写明细表出错：" + rs.getExceptionMsg();
                    log.error(error);
                }
            } else {
                error = "ESB响应没有model节点";
                log.error(error);
            }

        } catch (Exception e) {
            error = SDUtil.getExceptionDetail(e);
            log.info("回写异常：" + SDUtil.getExceptionDetail(e));
        }
        return error;
    }

    private String getWriteBackFieldValue(String fromField, String fieldType, JSONObject esbReturnData) {
        String result = "";
        //流程主表
        if ("0".equals(fieldType)) {
            result = Util.null2String(mainData.get(fromField));
        } else if ("1".equals(fieldType)) {
            //固定值
            result = fromField;
        } else if ("2".equals(fieldType)) {
            //ESB响应的字段值
            result = Util.null2String(esbReturnData.get(fromField));
        }
        return result;
    }


    private Map<String, Object> getMainData() {
        Map<String, Object> mainData = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql = "select * from " + mainTableName + " where requestid = " + requestid;
        log.info("getMainData sql:" + sql);
        if (rs.executeQuery(sql)) {
            mainData = QueryUtil.getMap(rs);
        }
        return mainData;
    }

    private List<Map<String, Object>> getDetailData(String mainid) {
        List<Map<String, Object>> detailData = new ArrayList<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select * from " + detailTableName + " where mainid = ?", mainid)) {
            detailData = QueryUtil.getMapList(rs);
        }
        return detailData;
    }


    private String checkParam(InvoiceRedApplyDto config) {
        if (config == null) {
            return "未获取到对应的开发平台配置信息，请检查！";
        }
        if (StringUtils.isBlank(config.getEsbEventName()) ||
                StringUtils.isBlank(config.getDtIndex()) ||
                StringUtils.isBlank(config.getFieldFlag()) ||
                config.getInFields().isEmpty() ||
                config.getWriteBackFields().isEmpty()
        ) {
            return "配置缺失必填项";
        }
        return "";
    }

}
