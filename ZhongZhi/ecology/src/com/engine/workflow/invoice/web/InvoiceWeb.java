package com.engine.workflow.invoice.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.workflow.invoice.action.BWInvoiceFileAction;
import com.engine.workflow.invoice.action.BWInvoiceRedQueryFileAction;
import com.engine.workflow.invoice.executor.BWInvoiceRedRefreshStatusExe;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName InvoiceWeb.java
 * @Description 发票web
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/10/8
 */
public class InvoiceWeb {

    /**
     * 刷新全电票
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refresh")
    @Produces({MediaType.TEXT_PLAIN})
    public String refresh(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        BaseBean bb = new BaseBean();
        bb.writeLog("刷新全电票--START");
        String errorMsg;
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            String requestid = Util.null2String(params.get("requestid"));
            String functionId = Util.null2String(params.get("functionId"));
            bb.writeLog("刷新全电票--requestid:" + requestid);
            if (!requestid.isEmpty() && !functionId.isEmpty()) {
                BWInvoiceFileAction action = new BWInvoiceFileAction();
                action.setFunctionId(functionId);
                errorMsg = action.executeData(requestid, functionId, user.getUID(), false);
            } else {
                errorMsg = "param missing";
            }
        } else {
            errorMsg = "user info error";
        }
        result.put("status", errorMsg.isEmpty());
        result.put("errorMsg", errorMsg);
        return JSONObject.toJSONString(result);
    }

    /**
     * 刷新红冲票
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshRed")
    @Produces({MediaType.TEXT_PLAIN})
    public String refreshRed(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        BaseBean bb = new BaseBean();
        bb.writeLog("刷新红冲全电票--START");
        String errorMsg;
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            String requestid = Util.null2String(params.get("requestid"));
            String functionId = Util.null2String(params.get("functionId"));
            bb.writeLog("刷新红冲全电票--requestid:" + requestid);
            if (!requestid.isEmpty() && !functionId.isEmpty()) {
                BWInvoiceRedQueryFileAction action = new BWInvoiceRedQueryFileAction();
                action.setFunctionId(functionId);
                errorMsg = action.executeData(requestid, functionId, user.getUID(), false);
            } else {
                errorMsg = "param missing";
            }
        } else {
            errorMsg = "user info error";
        }
        result.put("status", errorMsg.isEmpty());
        result.put("errorMsg", errorMsg);
        return JSONObject.toJSONString(result);
    }


    /**
     * 刷新红冲票的状态
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/refreshRedStatus")
    @Produces({MediaType.TEXT_PLAIN})
    public String refreshRedStatus(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        BaseBean bb = new BaseBean();
        bb.writeLog("refreshRedStatus--START");
        String errorMsg;
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            String requestid = Util.null2String(params.get("requestid"));
            String functionId = Util.null2String(params.get("functionId"));
            bb.writeLog("refreshRedStatus--requestid:" + requestid);
            if (!requestid.isEmpty() && !functionId.isEmpty()) {
                BWInvoiceRedRefreshStatusExe executor = new BWInvoiceRedRefreshStatusExe();
                errorMsg = executor.execute(requestid, functionId, user.getUID());
            } else {
                errorMsg = "param missing";
            }
        } else {
            //BWInvoiceRedApplyAction
            errorMsg = "user info error";
        }
        result.put("status", errorMsg.isEmpty());
        result.put("errorMsg", errorMsg);
        return JSONObject.toJSONString(result);
    }

}
