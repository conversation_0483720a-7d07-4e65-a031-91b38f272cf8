package com.engine.workflow.invoice.dto;

import lombok.Data;

import java.util.List;

/**
 * @FileName InvoiceRedQueryFileDto.java
 * @Description 红冲发票查询下载配置
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/19
 */
@Data
public class InvoiceRedQueryFileDto {

    /**
     * 明细数据的index 1，2，3
     */
    private String dtIndex;
    /**
     * 文档目录id，版式文件存放的目录id
     */
    private int catId;
    /**
     * 明细字段，版式文件附件
     */
    private String fieldAttach;
    /**
     * 主表字段名-是否推全电票，0推送1不推送
     */
    private String fieldFlag;


    //---- 全电发票查询接口---//
    /**
     * ESB 事件名称 全电发票查询接口
     */
    private String queryEsbEventName;
    /**
     * 全电发票查询接口 入参字段
     */
    private List<QueryInField> queryInFields;
    /**
     * 全电发票查询接口 成功回写流程数据配置。默认回写主表
     */
    private List<QueryWriteBackField> queryWriteBackFields;


    //---- 板式文件生成接口---//
    /**
     * 多个文件查询回写配置
     */
    private List<QueryMoreFile> queryMoreFile;
    /**
     * ESB 事件名称 板式文件生成接口
     */
    private String fileEsbEventName;
    /**
     * 明细字段-红字确认单状态，查询接口回写需要配置该字段回写对应关系
     */
    private String fieldQueryStatus;
    /**
     * 明细字段-已开具红字发票标记，查询接口回写需要配置该字段回写对应关系
     */
    private String fieldQueryDraw;

    @Data
    public static class QueryInField {
        /**
         * 来源字段名
         */
        private String fromField;
        /**
         * 来源字段类型，0主表 1明细表（具体哪个明细由dtIndex决定）2固定值
         */
        private String fromType;
        /**
         * ESB事件参数名
         */
        private String esbField;
    }

    @Data
    public static class QueryWriteBackField {
        /**
         * 来源字段名
         */
        private String fromField;
        /**
         * 来源字段类型，0ESB事件响应字段名；1固定值
         */
        private String fromType;
        /**
         * 流程主表字段名
         */
        private String wfField;
    }

    @Data
    public static class QueryMoreFile {
        /**
         * 板式文件 入参字段
         */
        private List<FileInField> fileInFields;
        /**
         * 板式文件生成 接口成功回写流程数据配置。默认回写主表
         */
        private List<FileWriteBackField> fileWriteBackFields;
        /**
         * 明细表字段名-文件链接
         */
        private String fieldFileLink;
    }


    @Data
    public static class FileInField {
        /**
         * 来源字段名
         */
        private String fromField;
        /**
         * 来源字段类型，0主表 1明细表（具体哪个明细由dtIndex决定）2固定值
         */
        private String fromType;
        /**
         * ESB事件参数名
         */
        private String esbField;
    }

    @Data
    public static class FileWriteBackField {
        /**
         * 来源字段名
         */
        private String fromField;
        /**
         * 来源字段类型，0ESB事件响应字段名；1固定值
         */
        private String fromType;
        /**
         * 流程主表字段名
         */
        private String wfField;
    }

}
