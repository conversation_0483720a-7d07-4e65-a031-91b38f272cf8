package com.engine.workflow.invoice.dto;

import lombok.Data;

import java.util.List;

/**
 * @FileName InvoiceIssueDto.java
 * @Description 发票开具接口配置
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/9/19
 */
@Data
public class InvoiceIssueDto {
    /**
     * ESB 事件名称
     */
    private String esbEventName;
    /**
     * 主表字段名-是否推全电票，0推送1不推送
     */
    private String fieldFlag;
    /**
     * 入参字段
     */
    private List<InField> inFields;
    /**
     * 接口成功回写流程数据配置。默认新增一行明细并赋值
     */
    private List<WriteBackField> writeBackFields;
    /**
     * 新增一行明细的位置，1,2,3...
     */
    private String dtIndex;

    @Data
    public static class InField {
        /**
         * 来源字段名
         */
        private String fromField;
        /**
         * 来源字段类型，0流程主表字段；1固定值；2表达式
         * 表达式支持：
         * $requestid$ 流程请求id
         * $workflowid$ 流程id
         */
        private String fromType;
        /**
         * ESB事件参数名
         */
        private String esbField;
    }

    @Data
    public static class WriteBackField {
        /**
         * 来源字段名
         */
        private String fromField;
        /**
         * 来源字段类型，0流程主表；1固定值；2ESB事件响应字段名
         */
        private String fromType;
        /**
         * ESB事件参数名
         */
        private String wfField;
    }

}
