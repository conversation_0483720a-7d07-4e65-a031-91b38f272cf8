package com.engine.working.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.working.service.WorkingService;
import com.engine.working.service.impl.WorkingServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * FileName: WorkingWeb.java
 * 工时管理web
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/12/26
 */
public class WorkingWeb {

    private WorkingService getService(User user) {
        return ServiceUtil.getService(WorkingServiceImpl.class, user);
    }

    /**
     * 获取开票数据
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getPrjInfo")
    @Produces({MediaType.TEXT_PLAIN})
    public String getPrjInfo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            apidatas.putAll(getService(user).getPrjInfo(ParamUtil.request2Map(request), user));
            apidatas.put("api_status", true);
        } catch (Exception e) {
            e.printStackTrace();
            apidatas.put("api_status", false);
            apidatas.put("erroMsg", e.getMessage());
        }
        return JSONObject.toJSONString(apidatas);
    }

    /**
     * 获取开票数据
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getKaipiao")
    @Produces({MediaType.TEXT_PLAIN})
    public String getKaipiao(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            apidatas.putAll(getService(user).getKaipiao(ParamUtil.request2Map(request), user));
            apidatas.put("api_status", true);
        } catch (Exception e) {
            e.printStackTrace();
            apidatas.put("api_status", false);
            apidatas.put("erroMsg", e.getMessage());
        }
        return JSONObject.toJSONString(apidatas);
    }

    /**
     * 获取计划数据
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getPlan")
    @Produces({MediaType.TEXT_PLAIN})
    public String getPlan(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            apidatas.putAll(getService(user).getPlan(ParamUtil.request2Map(request), user));
            apidatas.put("api_status", true);
        } catch (Exception e) {
            e.printStackTrace();
            apidatas.put("api_status", false);
            apidatas.put("erroMsg", e.getMessage());
        }
        return JSONObject.toJSONString(apidatas);
    }

    /**
     * 获取实际数据
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getActual")
    @Produces({MediaType.TEXT_PLAIN})
    public String getActual(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            apidatas.putAll(getService(user).getActual(ParamUtil.request2Map(request), user));
            apidatas.put("api_status", true);
        } catch (Exception e) {
            e.printStackTrace();
            apidatas.put("api_status", false);
            apidatas.put("erroMsg", e.getMessage());
        }
        return JSONObject.toJSONString(apidatas);
    }

    /**
     * 一键填报
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/oneClickFill")
    @Produces({MediaType.TEXT_PLAIN})
    public String oneClickFill(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            apidatas.putAll(getService(user).oneClickFill(ParamUtil.request2Map(request), user));
        } catch (Exception e) {
            e.printStackTrace();
            apidatas.put("api_status", false);
            apidatas.put("erroMsg", e.getMessage());
        }
        return JSONObject.toJSONString(apidatas);
    }

    /**
     * 保存实际工时
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveActualData")
    @Produces({MediaType.TEXT_PLAIN})
    public String saveActualData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            result.putAll(getService(user).saveActualData(ParamUtil.request2Map(request), user));
        } catch (Exception e) {
            result.put("status", false);
            result.put("erroMsg", SDUtil.getExceptionDetail(e));
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 保存实际工时
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/savePlanData")
    @Produces({MediaType.TEXT_PLAIN})
    public String savePlanData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            result.putAll(getService(user).savePlanData(ParamUtil.request2Map(request), user));
        } catch (Exception e) {
            result.put("status", false);
            result.put("erroMsg", SDUtil.getExceptionDetail(e));
        }
        return JSONObject.toJSONString(result);
    }

}
