package com.engine.working.service;

import weaver.hrm.User;

import java.util.Map;

public interface WorkingService {
    /**
     * 项目信息
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getPrjInfo(Map<String, Object> params, User user);

    /**
     * 开票数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getKaipiao(Map<String, Object> params, User user);

    /**
     * 计划数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getPlan(Map<String, Object> params, User user);

    /**
     * 实际数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getActual(Map<String, Object> params, User user);

    /**
     * 一键填报
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> oneClickFill(Map<String, Object> params, User user);

    /**
     * 保存实际工时
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> saveActualData(Map<String, Object> params, User user);

    /**
     * 保存计划工时
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> savePlanData(Map<String, Object> params, User user);
}
