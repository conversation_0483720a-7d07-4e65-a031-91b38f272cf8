package com.engine.working.service.impl;

import com.engine.core.impl.Service;
import com.engine.working.cmd.*;
import com.engine.working.service.WorkingService;
import weaver.hrm.User;

import java.util.Map;

public class WorkingServiceImpl extends Service implements WorkingService {
    @Override
    public Map<String, Object> getPrjInfo(Map<String, Object> params, User user) {
        return commandExecutor.execute(new PrjInfoCmd(params, user));
    }

    @Override
    public Map<String, Object> getKaipiao(Map<String, Object> params, User user) {
        return commandExecutor.execute(new KaiPiaoDataCmd(params, user));
    }

    @Override
    public Map<String, Object> getPlan(Map<String, Object> params, User user) {
        return commandExecutor.execute(new PlanDataCmd(params, user));
    }

    @Override
    public Map<String, Object> getActual(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ActualDataCmd(params, user));
    }

    @Override
    public Map<String, Object> oneClickFill(Map<String, Object> params, User user) {
        return commandExecutor.execute(new OneClickFillCmd(params, user));
    }

    /**
     * 保存实际工时
     *
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveActualData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveActualDataCmd(params, user));
    }

    /**
     * 保存计划工时
     *
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> savePlanData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SavePlanDataCmd(params, user));
    }
}
