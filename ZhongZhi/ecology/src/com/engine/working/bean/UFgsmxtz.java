package com.engine.working.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @FileName UFgsmxtz
 * @Description 工时明细台账-按日
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/27
 */
@Data
@Accessors(chain = true)//可以使用链式set
public class UFgsmxtz {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 填报人（人员id）
     */
    private Integer tbr;
    /**
     * 相关项目
     */
    private String xgxm;
    /**
     * 填报日期
     */
    private String tbrq;
    /**
     * 填报部门（部门id）
     */
    private Integer tbbm;
    /**
     * 员工姓名（人员id）
     */
    private Integer xm;
    /**
     * Rate
     */
    private String rate;
    /**
     * 日期
     */
    private String rq;
    /**
     * 工时
     */
    private BigDecimal gs;
    /**
     * 职级
     */
    private String zj;
    /**
     * 金额
     */
    private BigDecimal je;
    /**
     * 项目编号
     */
    private String xmbh;
    /**
     * 年月
     */
    private String yf;

    /**
     * 获取建模表名
     *
     * @return
     */
    public static String getTableName() {
        return "uf_gsmxtz";
    }
}
