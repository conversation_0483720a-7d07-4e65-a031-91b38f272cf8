package com.engine.working.util;

import weaver.general.Util;

import java.text.DecimalFormat;

public class TransUtil {
    DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###,###,###,###,###,###,###,###,###,##0.00");

    public String getNumberValue(String value) {
        return df.format(Util.getDoubleValue(value, 0.00));
    }

    public String transNumUrl(String value) {
        return "<a onclick=\"window.openCustom(" + value + ")\" href=\"javascript:void(0);\""
                + ">" + value
                + "</a>";
    }
}
