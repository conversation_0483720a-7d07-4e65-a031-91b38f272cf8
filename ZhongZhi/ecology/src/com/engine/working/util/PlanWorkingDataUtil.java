package com.engine.working.util;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.dto.ModuleUpdateBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.working.bean.UFjhgsmxtz;
import weaver.general.BaseBean;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @FileName PlanWorkingDataUtil
 * @Description 计划工时数据工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/7/27
 */
public class PlanWorkingDataUtil {

    /**
     * 插入计划工时明细
     *
     * @param list
     * @param moduleId
     * @param userId
     * @return
     */
    public static String insertWorkingDetail(List<UFjhgsmxtz> list, int moduleId, int userId) {
        BaseBean bb = new BaseBean();
        String errorMsg;
        String fieldName;
        Object fieldValue;
        bb.writeLog("PlanWorkingDataUtil insertWorkingDetail ---START");
        List<String> insertFields = new ArrayList<>();
        List<List<Object>> values = new ArrayList<>();
        List<Object> value;
        try {
            //获取字段和字段值
            Class<?> clazz = UFjhgsmxtz.class;
            Field[] fields = clazz.getDeclaredFields();
            //组装插入字段列表，排除id字段
            for (Field field : fields) {
                //设置可访问私有字段
                field.setAccessible(true);
                //字段名
                fieldName = field.getName();
                if (!"id".equals(fieldName)) {
                    insertFields.add(fieldName);
                }
            }
            //组装所有插入数据，排除id字段
            for (UFjhgsmxtz uf : list) {
                value = new ArrayList<>();
                for (Field field : fields) {
                    //设置可访问私有字段
                    field.setAccessible(true);
                    //字段名
                    fieldName = field.getName();
                    //获取字段值
                    fieldValue = field.get(uf);
                    if (!"id".equals(fieldName)) {
                        value.add(fieldValue);
                    }
                }
                values.add(value);
            }

            //构造插入bean
            ModuleInsertBean mb = new ModuleInsertBean();
            mb.setTableName(UFjhgsmxtz.getTableName())
                    .setFields(insertFields)
                    .setValues(values)
                    .setCreatorId(userId)
                    .setModuleId(moduleId);
            bb.writeLog("mb :" + mb);
            //同步插入批量数据
            ModuleResult mr = ModuleDataUtil.insert(mb);
            errorMsg = mr.getErroMsg();
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }

    /**
     * 更新工时明细
     *
     * @param list
     * @param userId
     * @return
     */
    public static String updateWorkingDetail(List<UFjhgsmxtz> list, int userId) {
        BaseBean bb = new BaseBean();
        String errorMsg;
        String fieldName;
        Object fieldValue;
        bb.writeLog("WorkingDataUtil updateWorkingDetail ---START");
        List<String> updateFields = new ArrayList<>();
        List<List<Object>> values = new ArrayList<>();
        List<Object> value;
        try {
            //获取字段和字段值
            Class<?> clazz = UFjhgsmxtz.class;
            Field[] fields = clazz.getDeclaredFields();
            //组装插入字段列表，排除id字段
            for (Field field : fields) {
                //设置可访问私有字段
                field.setAccessible(true);
                //字段名
                fieldName = field.getName();
                if (!"id".equals(fieldName)) {
                    updateFields.add(fieldName);
                }
            }
            //组装所有更新数据，最后id的值
            for (UFjhgsmxtz uf : list) {
                value = new ArrayList<>();
                for (Field field : fields) {
                    //设置可访问私有字段
                    field.setAccessible(true);
                    //字段名
                    fieldName = field.getName();
                    //获取字段值
                    fieldValue = field.get(uf);
                    if (!"id".equals(fieldName)) {
                        value.add(fieldValue);
                    }
                }
                //id值保证在最后
                value.add(uf.getId());
                values.add(value);
            }

            //构造更新bean
            ModuleUpdateBean mb = new ModuleUpdateBean();
            mb.setFields(updateFields);
            mb.setTableName(UFjhgsmxtz.getTableName());
            mb.setValues(values);
            mb.setUpdater(userId);
            bb.writeLog("mb :" + mb);
            //同步更新批量数据
            ModuleResult mr = ModuleDataUtil.update(mb);
            errorMsg = mr.getErroMsg();
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        return errorMsg;
    }
}
