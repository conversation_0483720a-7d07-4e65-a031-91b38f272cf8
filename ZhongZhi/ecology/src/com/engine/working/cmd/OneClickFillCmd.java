package com.engine.working.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.module.util.InsertModuleUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

/**
 * 工时管理-项目数据
 */
public class OneClickFillCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    private String erroMsg;

    public OneClickFillCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
        erroMsg = "";
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>();

        if (null == user) {
            result.put("hasRight", false);
            return result;
        }
        bb.writeLog("params:" + params);
        try {
            //项目id
            String prjid = Util.null2String(params.get("prjid"));
            //实际工时建模id
            String actualModuleId = Util.null2String(params.get("actualModuleId"));
            if (!prjid.isEmpty() && !actualModuleId.isEmpty()) {
                updateActualData(prjid, Integer.parseInt(actualModuleId));
            } else {
                erroMsg = "项目id或实际工时建模id参数为空";
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expection:" + e.getMessage());
            erroMsg = "执行异常：" + e.getMessage();
            bb.writeLog("catch expection:" + Arrays.toString(e.getStackTrace()));
        }
        if (erroMsg.isEmpty()) {
            result.put("api_status", true);
        } else {
            result.put("api_status", false);
            result.put("erroMsg", erroMsg);
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 一键更新实际填报数据
     * 只操作当前年月的数据
     *
     * @return
     */
    public void updateActualData(String prjid, int actualModuleId) throws Exception {
        RecordSet rs;
        String sql;
        int creator;
        int newBillId;
        //当前年月 yyyy-MM
        String currentYearMonth = TimeUtil.getCurrentDateString().substring(0, 7);
        //step1: 将计划的项目+人+rate+月份工时，对应更新到实际中
        sql = " update a set a.gs = b.gs " +
                " from uf_sjgsmx a " +
                " inner join uf_jhgsmx b on (a.xgxm = b.xgxm and a.xm = b.xm and a.rate=b.rate and a.ny = b.ny) " +
                " where a.xgxm = '" + prjid + "'" +
                " and a.ny = '" + currentYearMonth + "' ";

        bb.writeLog("update actual sql:" + sql);
        rs = new RecordSet();
        if (rs.executeUpdate(sql)) {

            //step2 : 将实际中没有的人，补充上计划的工时信息
            sql = " select a.* from uf_jhgsmx a where not exists ( " +
                    " select 1 from uf_sjgsmx b where a.xgxm = b.xgxm and " +
                    " a.xm = b.xm and a.rate = b.rate and b.ny = '" + currentYearMonth + "' ) " +
                    " and a.xgxm = '" + prjid + "' " +
                    " and a.ny = '" + currentYearMonth + "' ";
            bb.writeLog("query not exists actual sql:" + sql);
            rs = new RecordSet();
            if (rs.executeQuery(sql)) {
                //将实际中缺少的数据插入
                String insertFields = "tbr,xgxm,tbrq,tbbm,xm,rate,ny,gs,zj";
                List<Object> values;
                while (rs.next()) {
                    int tbr = rs.getInt("tbr");
                    if (tbr == -1) {
                        creator = 1;
                    } else {
                        creator = tbr;
                    }
                    values = new ArrayList<>();
                    values.add(tbr);
                    values.add(rs.getString("xgxm"));
                    values.add(rs.getString("tbrq"));
                    values.add(rs.getString("tbbm"));
                    values.add(rs.getString("xm"));
                    values.add(rs.getString("rate"));
                    values.add(rs.getString("ny"));
                    values.add(rs.getString("gs"));
                    values.add(rs.getString("zj"));
                    newBillId = InsertModuleUtil.ModuleInsert("uf_sjgsmx",
                            insertFields.split(CommonCst.COMMA_EN),
                            values, creator, actualModuleId, null);
                    if (newBillId == -1) {
                        erroMsg = "插入实际工时数据时出错";
                    }
                }
            } else {
                erroMsg = "查询实际工时缺失人员信息数据出错：" + rs.getExceptionMsg();
            }
        } else {
            erroMsg = "更新实际工时数据出错：" + rs.getExceptionMsg();
        }
    }
}
