package com.engine.working.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.query.util.QueryUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * 工时管理-项目数据
 */
public class PrjInfoCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public PrjInfoCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>();
        bb.writeLog("params:" + params);
        result.put("hasRight", true);
        if (null == user) {
            result.put("hasRight", false);
            return result;
        }

        //基础信息
        JSONObject baseInfo = new JSONObject();
        //项目id
        String prjid = Util.null2String(params.get("prjid"));

        if (!prjid.isEmpty()) {
            //判断当前人是否有该项目的权限
            if (!checkRight(prjid)) {
                result.put("hasRight", false);
                return result;
            }
            baseInfo = getBaseInfo(prjid);
        }
        result.put("data", baseInfo);

        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 获取项目信息
     *
     * @return
     */
    public JSONObject getBaseInfo(String prjid) {
        JSONArray ja;
        JSONObject jo = new JSONObject();
        String sql = "SELECT * from V_workding_info where 1=1 and prjid = ? ";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            ja = QueryUtil.getJSONList(rs);
            if (!ja.isEmpty()) {
                jo = ja.getJSONObject(0);
            }
        } else {
            bb.writeLog("getBaseInfo sql erro:" + rs.getExceptionMsg());
        }
        return jo;
    }

    /**
     * 校验权限
     *
     * @param prjid
     * @return
     */
    private boolean checkRight(String prjid) {
        RecordSet rs;
        String users, sql, manager, director, dept = "-1", bmfzr, qyfzr;
        //工时管理员角色id
        String roleId = Util.null2String(params.get("role_id"));
        //项目经理字段名
        String managerField = Util.null2String(params.get("manager_field"));
        //项目总监字段名
        String directorField = Util.null2String(params.get("director_field"));
        //立项部门字段名
        String deptField = Util.null2String(params.get("dept_field"));
        //项目看板权限的看板权限人员字段名
        String userQxField = Util.null2String(params.get("user_qx_field"));

        //STEP 1:校验当前人是否在角色范围内，如果对应包含当前人，则有权限
        if (!roleId.isEmpty()) {
            HrmUserVarify hrmUserVarify = new HrmUserVarify();
            String roleLevel = hrmUserVarify.getRightLevel(String.valueOf(user.getUID()), roleId);
            bb.writeLog("roleLevel:" + roleLevel);
            boolean hasRoleRight = hrmUserVarify.checkUserRight(String.valueOf(user.getUID()), roleId, roleLevel);
            bb.writeLog("hasRoleRight:" + hasRoleRight);
            //如果有角色权限，直接返回true
            if (hasRoleRight) {
                return true;
            }
        }
        if (!managerField.isEmpty() && !directorField.isEmpty()) {
            //STEP 2: 再判断是否是项目经理、项目总监，如果对应包含当前人，则有权限
            rs = new RecordSet();
            sql = "select " + managerField + " as manager, " +
                    directorField + " as director, " +
                    deptField + " as dept from prj_projectinfo where id = ?";
            rs.executeQuery(sql, prjid);
            if (rs.next()) {
                manager = Util.null2String(rs.getString("manager"));
                director = Util.null2String(rs.getString("director"));
                dept = Util.null2String(rs.getString("dept"));
                bb.writeLog("manager:" + manager);
                bb.writeLog("director:" + director);
                bb.writeLog("dept:" + dept);
                if (checkHasManager(manager)) {
                    return true;
                }
                if (checkHasManager(director)) {
                    return true;
                }
            }
        }

        if (!deptField.isEmpty()) {
            //STEP 3: 校验部门矩阵，立项部门为矩阵中对应部门的，分管领导和部门负责人，如果包含当前人，则有权限
            rs = new RecordSet();
            sql = "select bmfzr,qyfzr from matrixtable_2 where id = ?";
            rs.executeQuery(sql, dept);
            if (rs.next()) {
                bmfzr = Util.null2String(rs.getString("bmfzr"));
                qyfzr = Util.null2String(rs.getString("qyfzr"));
                bb.writeLog("bmfzr:" + bmfzr);
                bb.writeLog("qyfzr:" + qyfzr);
                if (checkHasManager(bmfzr)) {
                    return true;
                }
                if (checkHasManager(qyfzr)) {
                    return true;
                }
            }
        }

        //STEP 4: 校验建模对照表，如果对应项目包含当前人，则有权限
        rs = new RecordSet();
        sql = "select " + userQxField + " as qxry from uf_xmkbqx where xmmc = ? ";
        if (rs.executeQuery(sql, prjid)) {
            while (rs.next()) {
                users = Util.null2String(rs.getString("qxry"));
                bb.writeLog("qxry:" + users);
                if (checkHasManager(users)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 查看人力资源字段值是否包含当前登录人
     *
     * @param userIds
     * @return
     */
    private boolean checkHasManager(String userIds) {
        if (!userIds.isEmpty()) {
            for (String userId : userIds.split(CommonCst.COMMA_EN)) {
                if (userId.equals(String.valueOf(user.getUID()))) {
                    return true;
                }
            }
        }
        return false;
    }
}
