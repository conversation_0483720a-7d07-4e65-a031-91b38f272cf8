package com.engine.working.cmd;

import cn.hutool.core.date.DateUtil;
import com.cloudstore.eccom.constant.WeaBoolAttr;
import com.cloudstore.eccom.pc.table.WeaTable;
import com.cloudstore.eccom.pc.table.WeaTableColumn;
import com.cloudstore.eccom.pc.table.WeaTableType;
import com.cloudstore.eccom.result.WeaResultMsg;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 工时管理-实际数据
 */
public class ActualDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public ActualDataCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> apidatas = new HashMap<>();

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }
        bb.writeLog("params:" + params);
        try {
            String prjid = Util.null2String(params.get("prjid"));
            bb.writeLog("prjid:" + prjid);
            //返回消息结构体
            WeaResultMsg result = new WeaResultMsg(false);

            String pageID = "127f4326-c6be-48b2-a665-9a33fdda4720";
            String pageUid = pageID + "_" + user.getUID();
            String pageSize = PageIdConst.getPageSize(pageID, user.getUID());
            String sqlwhere = "";
            Map<String, Object> mapSql = getTotalSql(prjid);
            String totalSql = Util.null2String(mapSql.get("sql"));
            Map<String, String> columns = (Map<String, String>) mapSql.get("columns");
            //新建一个weatable
            WeaTable table = new WeaTable();
            table.setPageUID(pageUid);
            table.setPageID(pageID);
            table.setPagesize(pageSize);
            //这里设置星号，这里用到PIVOT函数，无法确定固定字段名，得用*取所有
            table.setBackfields("*");
            table.setSqlform(" (" + totalSql + ") TBTOP ");
            table.setSqlwhere(sqlwhere);
            table.setSqlorderby("rate desc,workcode");
            table.setSqlprimarykey("id");
            table.setSqlisdistinct("false");
            //设置标题名称
            setColumnName(table, columns);

            //设置左侧check默认不存在
            table.setCheckboxList(null);
            table.setCheckboxpopedom(null);
            table.setTableType(WeaTableType.NONE);
            result.putAll(table.makeDataResult());
            result.put("hasRight", true);
            result.success();
            apidatas = result.getResultMap();

        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch excption:" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return apidatas;
    }

    /**
     * 获取查询总sql
     *
     * @return
     */
    public Map<String, Object> getTotalSql(String prjid) {
        Map<String, Object> result = new HashMap<>(2);
        String resultSql = "";
        Map<String, String> mapColumn = new LinkedHashMap<>(6);
        RecordSet rs = new RecordSet();
        StringBuilder sbColumn = new StringBuilder();
        StringBuilder sbCase = new StringBuilder();
        String nextYearMonth, nextYearMonthNormal;
        //总工时
        StringBuilder sbSumGongshi = new StringBuilder();
        //总金额
        StringBuilder sbSumJine = new StringBuilder();
        //配置的项目开始日期阈值，如果项目开始日期大于等于该日期，则按项目的开始日期计算，如果小于，则按照阈值的开始日期算
        String limitDate = bb.getPropValue("ciic_working", "project_limit_date");

        //获取项目的最新预估开始日期，和最新预估结束日期，相差月份
        String sql = " select xmsjkssj as startdate,xmssjssj as enddate," +
                "DATEDIFF(mm,xmsjkssj,xmssjssj) as diff from prj_projectinfo where id = ? ";
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                String startDate = Util.null2String(rs.getString("startdate"));
                String endDate = Util.null2String(rs.getString("enddate"));
                bb.writeLog("startDate:" + startDate);
                bb.writeLog("endDate:" + endDate);
                if (TimeUtil.dateInterval(limitDate, startDate) < 0) {
                    startDate = limitDate;
                }
                bb.writeLog("startDate2:" + startDate);
                bb.writeLog("endDate2:" + endDate);
                if (TimeUtil.dateInterval(startDate, endDate) >= 0) {
                    int diff = getBetweenMonth(startDate, endDate);
                    bb.writeLog("diff:" + diff);
                    if (diff >= 0 && StringUtils.isNotBlank(startDate)) {
                        String startYearMonth = startDate.substring(0, 7).replaceAll("-", "");
                        String startYearMonthNormal = startDate.substring(0, 7);
                        sbColumn.append(" tbb.gs").append(startYearMonth).append(", ");
                        sbColumn.append(" tbb.je").append(startYearMonth).append(", ");

                        sbCase.append(" sum(case when a.ny = '").append(startYearMonthNormal).append("' then a.gs else 0 end) as gs").append(startYearMonth).append(", ");
                        sbCase.append(" sum(case when a.ny = '").append(startYearMonthNormal).append("' then a.rate*a.gs else 0 end) as je").append(startYearMonth).append(", ");

                        sbSumGongshi.append(" (tbb.gs").append(startYearMonth);
                        sbSumJine.append(" (tbb.je").append(startYearMonth);

                        mapColumn.put("gs" + startYearMonth, startYearMonthNormal + "工时");
                        mapColumn.put("je" + startYearMonth, startYearMonthNormal + "金额");
                        for (int i = 1; i <= diff; i++) {
                            startDate = getMonthAddDate(startDate, 1);
                            if (startDate != null) {
                                nextYearMonth = startDate.substring(0, 7).replaceAll("-", "");
                                nextYearMonthNormal = startDate.substring(0, 7);
                                sbColumn.append(" tbb.gs").append(nextYearMonth).append(", ");
                                sbColumn.append(" tbb.je").append(nextYearMonth).append(", ");

                                sbCase.append(" sum(case when a.ny = '").append(nextYearMonthNormal).append("' then a.gs else 0 end) as gs").append(nextYearMonth).append(", ");
                                sbCase.append(" sum(case when a.ny = '").append(nextYearMonthNormal).append("' then a.rate*a.gs else 0 end) as je").append(nextYearMonth).append(", ");

                                sbSumGongshi.append("+").append(" tbb.gs").append(nextYearMonth);
                                sbSumJine.append("+").append(" tbb.je").append(nextYearMonth);
//                                if (i < diff) {
//                                    sbSumGongshi.append("+");
//                                    sbSumJine.append("+");
//                                }
                                mapColumn.put("gs" + nextYearMonth, nextYearMonthNormal + "工时");
                                mapColumn.put("je" + nextYearMonth, nextYearMonthNormal + "金额");

                            }
                        }
                        sbSumGongshi.append(") as totalgs,");
                        sbSumJine.append(") as totalje,");
                        resultSql = "select " +
                                " NEWID() as id," +
                                sbSumGongshi +
                                sbSumJine +
                                sbColumn +
                                " tba.* " +
                                " from (select  " +
                                " a.xm, " +
                                " b.lastname, " +
                                " b.workcode, " +
                                " a.zj," +
                                " c.jb as joblevel, " +
                                " a.rate " +
                                " from uf_sjgsmx a " +
                                " left join hrmresource b on (a.xm = b.id) " +
                                " left join uf_ygzj c on (a.zj = c.id) " +
                                " where a.xgxm = '" + prjid + "' " +
                                " group by  " +
                                " a.xm, " +
                                " b.lastname, " +
                                " b.workcode, " +
                                " a.zj, " +
                                " c.jb, " +
                                " a.rate " +
                                " ) tba " +
                                "left join ( " +
                                " select  " +
                                sbCase +
                                " a.xm, " +
                                " a.rate " +
                                " from uf_sjgsmx a " +
                                " where a.xgxm = '" + prjid + "' " +
                                " group by a.xm,a.rate " +
                                ") tbb on (tba.xm = tbb.xm and tba.rate = tbb.rate)";
                    }
                }
            } else {
                bb.writeLog("query diff yearmonth empty");
            }
        } else {
            bb.writeLog("query diff yearmonth erro:" + rs.getExceptionMsg());
        }
        bb.writeLog("totalsql: " + resultSql);
        bb.writeLog("mapColumn: " + mapColumn);
        result.put("sql", resultSql);
        result.put("columns", mapColumn);
        return result;
    }


    /**
     * 设置标题栏名称
     *
     * @param table
     */
    private void setColumnName(WeaTable table, Map<String, String> columns) {
        String hoursShow = bb.getPropValue("ciic_working", "actual_hours_show");
        String amountShow = bb.getPropValue("ciic_working", "actual_amount_show");

        table.getColumns().add(new WeaTableColumn("id").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        table.getColumns().add(new WeaTableColumn("20%", "员工编号", "workcode", "workcode"));
        table.getColumns().add(new WeaTableColumn("20%", "员工姓名", "lastname", "lastname"));
        //姓名id
        table.getColumns().add(new WeaTableColumn("xm").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        //职级id
        table.getColumns().add(new WeaTableColumn("zj").setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
        table.getColumns().add(new WeaTableColumn("20%", "员工职级", "joblevel", "joblevel"));
        table.getColumns().add(new WeaTableColumn("20%", "Rate", "rate", "rate"));
        table.getColumns().add(new WeaTableColumn("20%", "工时合计", "totalgs", "totalgs"));
        table.getColumns().add(new WeaTableColumn("20%", "金额合计", "totalje", "totalje"));
        //通过Map.entrySet遍历key和value
        for (Map.Entry<String, String> entry : columns.entrySet()) {
            //工时
            if (entry.getKey().contains("gs")) {
                if ("true".equals(hoursShow)) {
                    table.getColumns().add(new WeaTableColumn("20%", entry.getValue(), entry.getKey(), entry.getKey()));
                } else {
                    table.getColumns().add(new WeaTableColumn(entry.getKey()).setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
                }
            } else if (entry.getKey().contains("je")) {
                //金额
                if ("true".equals(amountShow)) {
                    table.getColumns().add(new WeaTableColumn("20%", entry.getValue(), entry.getKey(), entry.getKey()));
                } else {
                    table.getColumns().add(new WeaTableColumn(entry.getKey()).setDisplay(WeaBoolAttr.FALSE));   //设置为不显示
                }
            }
        }
    }

    /**
     * @param date  – 日期格式的字符串”YYYY-MM-DD”
     * @param month – 加上的月份
     * @return 返回增加day后的日期格式的字符串
     * <p>
     * 给日期date加上月数month.
     * date 必须为 ”YYYY-MM-DD” ，如”2004-07-04”，month为需要增加的月数，如果为负数，则为减去。
     * 如果date不满足日期格式，返回null。如：
     * <p>
     * monthAdd (”2004-02-29”,1) 返回”2004-03-29”
     * monthAdd (”2004-7-4”,1)  返回null
     */
    public static String getMonthAddDate(String date, int month) {
        Calendar calendar = TimeUtil.getCalendar(date);
        if (calendar == null) return null;
        calendar.add(Calendar.MONTH, month);
        return TimeUtil.getDateString(calendar);
    }


    /**
     * 获取两个日期相差的月份数
     * yyyy-MM-dd
     *
     * @param startdate
     * @param enddate
     * @return
     */
    public static int getBetweenMonth(String startdate, String enddate) {
        int result = 0;
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("getBetweenMonth startdate:" + startdate);
        baseBean.writeLog("getBetweenMonth enddate:" + enddate);
        if (!startdate.isEmpty() && !enddate.isEmpty()) {
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date date1 = format.parse(startdate);
                Date date2 = format.parse(enddate);

                long betweenMonth = DateUtil.betweenMonth(date1, date2, true);
                result = Integer.parseInt(Util.null2String(betweenMonth));
            } catch (Exception e) {
                e.printStackTrace();
                baseBean.writeLog("getBetweenMonth expt:" + e.getMessage());
            }
        }
        baseBean.writeLog("getBetweenMonth result:" + result);
        return result;
    }

}
