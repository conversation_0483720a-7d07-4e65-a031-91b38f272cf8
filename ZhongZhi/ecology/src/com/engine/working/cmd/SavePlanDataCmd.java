package com.engine.working.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.working.bean.UFjhgsmxtz;
import com.engine.working.util.PlanWorkingDataUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SavePlanDataCmd
 * @Description 保存计划工时
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/27
 */
public class SavePlanDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;


    public SavePlanDataCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        String errorMsg;
        Map<String, Object> result = new HashMap<>();
        JSONArray datas;
        //工时明细的台账的建模id
        int moduleId;
        try {
            if (null == user) {
                result.put("status", false);
                result.put("hasRight", false);
                return result;
            }
            bb.writeLog("params:" + params);
            //项目id
            String prjid = Util.null2String(params.get("prjid"));
            //明细数据
            String datasStr = Util.null2String(params.get("datas"));
            //工时明细的台账的建模id
            String moduleIdStr = Util.null2String(params.get("moduleId"));
            if (!prjid.isEmpty() && !datasStr.isEmpty() && !moduleIdStr.isEmpty()) {
                datas = JSONArray.parseArray(datasStr);
                moduleId = Integer.parseInt(moduleIdStr);
                if (!datas.isEmpty()) {
                    errorMsg = doExecute(datas, prjid, moduleId);
                } else {
                    errorMsg = "明细数据为空！";
                }
            } else {
                errorMsg = "prjid/datas/moduleId 参数缺失！";
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        result.put("erroMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    private String doExecute(JSONArray data, String prjid, int moduleId) {
        JSONObject jo;
        String errorMsg = "";
        int db_id;
        String cy, zj, rate;
        BigDecimal jhgs, je, rateNum;
        List<UFjhgsmxtz> newData = new ArrayList<>();
        List<UFjhgsmxtz> updateData = new ArrayList<>();
        UFjhgsmxtz uf;
        //获取所有工时
        JSONArray allWorking = getAllGs(prjid);
        String proCode = getPrjXmbh(prjid);

        for (int i = 0; i < data.size(); i++) {
            jo = data.getJSONObject(i);
            cy = Util.null2String(jo.get("cy"));
            zj = Util.null2String(jo.get("zj"));
            rate = Util.null2String(jo.get("rate"));
            rateNum = SDUtil.getBigDecimalValue(jo.get("rate"));
            jhgs = SDUtil.getBigDecimalValue(jo.get("jhgs"));
            je = jhgs.multiply(rateNum).setScale(2, RoundingMode.HALF_UP);
            //根据 人员+职级+rate，判断当前时是否有数据，存在就更新，不存在就插入
            db_id = checkHasData(allWorking, cy, zj, rate);
            uf = new UFjhgsmxtz();
            uf.setTbr(user.getUID())
                    .setTbbm(user.getUserDepartment())
                    .setTbrq(TimeUtil.getCurrentDateString())
                    .setXgxm(prjid)
                    .setXm(Integer.parseInt(cy))
                    .setZj(zj)
                    .setRate(rate)
                    .setGs(jhgs)
                    .setJe(je)
                    .setXmbh(proCode);
            //新增
            if (db_id == -1) {
                newData.add(uf);
            } else {
                //更新
                uf.setId(db_id);
                updateData.add(uf);
            }

        }
        bb.writeLog("newData:" + newData);
        bb.writeLog("updateData:" + updateData);
        if (!newData.isEmpty()) {
            errorMsg = PlanWorkingDataUtil.insertWorkingDetail(newData, moduleId, user.getUID());
        }
        if (!updateData.isEmpty() && errorMsg.isEmpty()) {
            errorMsg = PlanWorkingDataUtil.updateWorkingDetail(updateData, user.getUID());
        }
        return errorMsg;
    }

    /**
     * 获取当前项目所有的明细工时数据
     *
     * @param prjid
     * @return
     */
    private JSONArray getAllGs(String prjid) {
        JSONArray result = new JSONArray();
        String sql = "select * from uf_jhgsmxtz where xgxm = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            result = QueryUtil.getJSONList(rs);
        }
        return result;
    }

    /**
     * 检查当前日期是否已经有数据，有的话则返回数据id，无则返回-1
     *
     * @param allWork
     * @param cy
     * @param zj
     * @param rate
     * @return
     */
    private int checkHasData(JSONArray allWork, String cy, String zj, String rate) {
        JSONObject jo;
        String db_cy, db_zj, db_rate;
        int db_id = -1;
        for (int i = 0; i < allWork.size(); i++) {
            jo = allWork.getJSONObject(i);
            db_cy = Util.null2String(jo.get("xm"));
            db_zj = Util.null2String(jo.get("zj"));
            db_rate = Util.null2String(jo.get("rate"));
            if (db_cy.equals(cy) &&
                    db_zj.equals(zj) &&
                    db_rate.equals(rate)) {
                db_id = Integer.parseInt(Util.null2String(jo.get("id")));
                break;
            }
        }
        return db_id;
    }

    private String getPrjXmbh(String prjid) {
        String sql = "select procode from prj_projectinfo where id = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                return Util.null2String(rs.getString("procode"));
            }
        }
        return "";

    }

}
