package com.engine.working.cmd;

import com.cloudstore.eccom.pc.table.WeaTable;
import com.cloudstore.eccom.pc.table.WeaTableColumn;
import com.cloudstore.eccom.pc.table.WeaTableType;
import com.cloudstore.eccom.result.WeaResultMsg;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.general.BaseBean;
import weaver.general.PageIdConst;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * 工时管理-开票数据
 */
public class KaiPiaoDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public KaiPiaoDataCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> apidatas = new HashMap<>();

        if (null == user) {
            apidatas.put("hasRight", false);
            return apidatas;
        }
        bb.writeLog("params:" + params);
        try {
            String prjid = Util.null2String(params.get("prjid"));
            bb.writeLog("prjid:" + prjid);
            //返回消息结构体
            WeaResultMsg result = new WeaResultMsg(false);

            String pageID = "d27b06d6-bd08-43cd-9048-e005e9fabe3c";
            String pageUid = pageID + "_" + user.getUID();
            String pageSize = PageIdConst.getPageSize(pageID, user.getUID());
            String sqlwhere = "";
            //新建一个weatable
            WeaTable table = new WeaTable();
            table.setPageUID(pageUid);
            table.setPageID(pageID);
            table.setPagesize(pageSize);
            //这里设置星号，这里用到PIVOT函数，无法确定固定字段名，得用*取所有
            table.setBackfields("*");
            table.setSqlform(" (" + getTotalSql(prjid) + ") TBTOP ");
            table.setSqlwhere(sqlwhere);
            table.setSqlorderby("ordercol");

            table.setSqlprimarykey("id");
            table.setSqlisdistinct("false");
            //设置标题名称
            setColumnName(table);

            //设置左侧check默认不存在
            table.setCheckboxList(null);
            table.setCheckboxpopedom(null);
            table.setTableType(WeaTableType.NONE);
            result.putAll(table.makeDataResult());
            result.put("hasRight", true);
            result.success();
            apidatas = result.getResultMap();

        } catch (Exception e) {
            bb.writeLog("catch excption:" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return apidatas;
    }

    /**
     * 获取查询总sql
     *
     * @return
     */
    public String getTotalSql(String prjid) {
        String sql = "select " +
                " NEWID() as id," +
                " tbb.*, " +
                " cast(replace(tbb.ny,'-','') as decimal) as ordercol," +
                " tbc.jhje, " +
                " tbd.sjje from  " +
                " (select distinct tba.ny from  " +
                " (select  " +
                " left(b.yjkprq,7) as ny " +
                " from uf_ywhtxx a  " +
                " left join uf_xkpjh b on (b.htmc = a.id) " +
                " left join prj_projectinfo c on (a.xmbh = c.procode) " +
                " where c.id = '" + prjid + "' and b.yjkprq is not null " +
                " union all  " +
                " select  " +
                " left(b.sjkprq,7) as ny " +
                " from uf_ywhtxx a  " +
                " left join uf_xkpjh b on (b.htmc = a.id) " +
                " left join prj_projectinfo c on (a.xmbh = c.procode) " +
                " where c.id = '" + prjid + "' and b.sjkprq is not null " +
                " ) tba " +
                " )tbb " +
                " left join ( " +
                "   select  " +
                "   sum(b.fpje) as jhje, " +
                "   left(b.yjkprq,7) as ny " +
                "   from uf_ywhtxx a  " +
                "   left join uf_xkpjh b on (b.htmc = a.id) " +
                "   left join prj_projectinfo c on (a.xmbh = c.procode) " +
                "   where c.id = '" + prjid + "' " +
                "   group by left(b.yjkprq,7) " +
                " )tbc on (tbb.ny = tbc.ny) " +
                "  left join ( " +
                "   select  " +
                "   sum(b.fpje) as sjje, " +
                "   left(b.sjkprq,7) as ny " +
                "   from uf_ywhtxx a  " +
                "   left join uf_xkpjh b on (b.htmc = a.id) " +
                "   left join prj_projectinfo c on (a.xmbh = c.procode) " +
                "   where c.id = '" + prjid + "' " +
                "   and b.kpzt = 1 " +
                "   group by left(b.sjkprq,7) " +
                " )tbd on (tbb.ny = tbd.ny) ";

        bb.writeLog("totalsql: " + sql);
        return sql;
    }


    /**
     * 设置标题栏名称
     *
     * @param table
     */
    private void setColumnName(WeaTable table) {
        table.getColumns().add(new WeaTableColumn("20%", "年月", "ny", "ny"));
        table.getColumns().add(new WeaTableColumn("20%", "计划开票", "jhje", "jhje"));
        table.getColumns().add(new WeaTableColumn("20%", "实际开票", "sjje", "sjje"));

    }

}
