package com.engine.working.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.working.bean.UFgsmxtz;
import com.engine.working.util.WorkingDataUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @FileName SaveActualDataCmd
 * @Description 保存实际工时
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/27
 */
public class SaveActualDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;


    public SaveActualDataCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        String errorMsg;
        Map<String, Object> result = new HashMap<>();
        JSONArray datas;
        //工时明细的台账的建模id
        int moduleId;
        try {
            if (null == user) {
                result.put("status", false);
                result.put("hasRight", false);
                return result;
            }
            bb.writeLog("params:" + params);
            //项目id
            String prjid = Util.null2String(params.get("prjid"));
            //明细数据
            String datasStr = Util.null2String(params.get("datas"));
            //工时明细的台账的建模id
            String moduleIdStr = Util.null2String(params.get("moduleId"));
            if (!prjid.isEmpty() && !datasStr.isEmpty() && !moduleIdStr.isEmpty()) {
                datas = JSONArray.parseArray(datasStr);
                moduleId = Integer.parseInt(moduleIdStr);
                if (!datas.isEmpty()) {
                    errorMsg = doExecute(datas, prjid, moduleId);
                } else {
                    errorMsg = "明细数据为空！";
                }
            } else {
                errorMsg = "prjid/datas/moduleId 参数缺失！";
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
        }
        result.put("erroMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    @SuppressWarnings("unchecked")
    private String doExecute(JSONArray data, String prjid, int moduleId) {
        JSONObject jo;
        String date;
        //日期值（工时），金额
        BigDecimal dateValue, amount, rateNum;
        String errorMsg = "";
        int db_id;
        String cy, zj, rate;
        List<UFgsmxtz> newData = new ArrayList<>();
        List<UFgsmxtz> updateData = new ArrayList<>();
        UFgsmxtz uf;
        //获取所有工时
        JSONArray allWorking = getAllGs(prjid);
        String proCode = getPrjXmbh(prjid);
        //获取项目的开始，结束日期
        Map<String, String> mapPrjDate = getPrjDate(prjid);
        String prjStartDate = mapPrjDate.get("date1");
        String prjEndDate = mapPrjDate.get("date2");
        BigDecimal prjStartDateNum = SDUtil.getBigDecimalValue(prjStartDate.replace("-", ""));
        BigDecimal prjEndDateNum = SDUtil.getBigDecimalValue(prjEndDate.replace("-", ""));
        bb.writeLog("prjStartDateNum", prjStartDateNum);
        bb.writeLog("prjEndDateNum", prjEndDateNum);
        //当前年月
        String currentYearMonth = TimeUtil.getCurrentDateString().substring(0, 7);
        for (int i = 0; i < data.size(); i++) {
            jo = data.getJSONObject(i);
            cy = Util.null2String(jo.get("cy"));
            zj = Util.null2String(jo.get("zj"));
            rate = Util.null2String(jo.get("rate"));
            rateNum = SDUtil.getBigDecimalValue(rate);

            Set<Map.Entry<String, Object>> entries = jo.entrySet();
            //遍历字段，获取日期的数据
            for (Map.Entry<String, Object> entry : entries) {
                //获取日期的字段值,一个日期一条数据
                if (entry.getKey().startsWith("$date$")) {
                    date = entry.getKey().substring(6);
                    //日期变为num
                    BigDecimal dateNum = SDUtil.getBigDecimalValue(date.replace("-", ""));
                    dateValue = SDUtil.getBigDecimalValue(entry.getValue());
                    //rate*工时=金额
                    amount = dateValue.multiply(rateNum).setScale(2, RoundingMode.HALF_UP);
                    //如果当前日期不可编辑，则不做处理
                    //判断当前日期，是否是当前月的，只能编辑当前月份的日期,非当前月份的，并且要在项目范围日期内的
                    if (date.substring(0, 7).equals(currentYearMonth) && dateNum.compareTo(prjStartDateNum) >= 0 && dateNum.compareTo(prjEndDateNum) <= 0) {
                        //根据 人员+职级+rate+日期，判断当前日期的工时是否有数据，存在就更新，不存在就插入
                        db_id = checkHasData(allWorking, cy, zj, rate, date);
                        uf = new UFgsmxtz();
                        uf.setTbr(user.getUID())
                                .setTbbm(user.getUserDepartment())
                                .setTbrq(TimeUtil.getCurrentDateString())
                                .setXgxm(prjid)
                                .setXm(Integer.parseInt(cy))
                                .setZj(zj)
                                .setRate(rate)
                                .setRq(date)
                                .setGs(dateValue)
                                .setJe(amount)
                                .setXmbh(proCode)
                                .setYf(date.substring(0, 7));
                        //新增
                        if (db_id == -1) {
                            newData.add(uf);
                        } else {
                            //更新
                            uf.setId(db_id);
                            updateData.add(uf);
                        }
                    } else {
                        bb.writeLog("date:" + date + "不符合编辑条件，跳过处理");
                    }
                }
            }
        }
        bb.writeLog("newData:" + newData);
        bb.writeLog("updateData:" + updateData);
        if (!newData.isEmpty()) {
            errorMsg = WorkingDataUtil.insertWorkingDetail(newData, moduleId, user.getUID());
        }
        if (!updateData.isEmpty() && errorMsg.isEmpty()) {
            errorMsg = WorkingDataUtil.updateWorkingDetail(updateData, user.getUID());
        }
        return errorMsg;
    }

    /**
     * 获取当前项目所有的明细工时数据
     *
     * @param prjid
     * @return
     */
    private JSONArray getAllGs(String prjid) {
        JSONArray result = new JSONArray();
        String sql = "select * from uf_gsmxtz where xgxm = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            result = QueryUtil.getJSONList(rs);
        }
        return result;
    }

    /**
     * 检查当前日期是否已经有数据，有的话则返回数据id，无则返回-1
     *
     * @param allWork
     * @param cy
     * @param zj
     * @param rate
     * @param rq
     * @return
     */
    private int checkHasData(JSONArray allWork, String cy, String zj, String rate, String rq) {
        JSONObject jo;
        String db_cy, db_zj, db_rate, db_rq;
        int db_id = -1;
        for (int i = 0; i < allWork.size(); i++) {
            jo = allWork.getJSONObject(i);
            db_cy = Util.null2String(jo.get("xm"));
            db_zj = Util.null2String(jo.get("zj"));
            db_rate = Util.null2String(jo.get("rate"));
            db_rq = Util.null2String(jo.get("rq"));
            if (db_cy.equals(cy) &&
                    db_zj.equals(zj) &&
                    db_rate.equals(rate) &&
                    db_rq.equals(rq)) {
                db_id = Integer.parseInt(Util.null2String(jo.get("id")));
                break;
            }
        }
        return db_id;
    }

    private Map<String, String> getPrjDate(String prjid) {
        Map<String, String> map = new HashMap<>();
        String sql = "select xmsjkssj as date1,xmssjssj as date2 from prj_projectinfo where id = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                map.put("date1", Util.null2String(rs.getString("date1")));
                map.put("date2", Util.null2String(rs.getString("date2")));
            }
        }
        return map;
    }

    private String getPrjXmbh(String prjid) {
        String sql = "select procode from prj_projectinfo where id = ?";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                return Util.null2String(rs.getString("procode"));
            }
        }
        return "";

    }
}
