package com.engine.working.job;

import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.*;

/**
 * @FileName PrjMemberJob
 * @Description 项目成员
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/7/14
 */
@Getter
@Setter
public class PrjMemberJob extends BaseCronJob {
    private BaseBean bb;
    //项目成员清单的建模id uf_xmcyqd
    private String moduleid_xmcy;

    @Override
    public void execute() {
        bb = new BaseBean();
        bb.writeLog("PrjMemberJob ---START");
        try {
            bb.writeLog("moduleid_xmcy:" + moduleid_xmcy);
            Map<String, String> allPrjMember = getAllPrjMember();

            if (!allPrjMember.isEmpty()) {
                //uf_zjrwhb 所有人员rate配置信息
                List<Map<String, Object>> allMemberRate = getAllMemberRate();
                //uf_xmcyqd 所有项目成员清单
                List<Map<String, Object>> allMemberList = getMemberList();
                //构造插入建模的bean
                ModuleInsertBean mb = new ModuleInsertBean();
                String[] insertFields = new String[]{"xgxm", "cy", "zj", "rate"};
                String moduleTableName = ModuleDataUtil.getModuleNameById(moduleid_xmcy);
                mb.setFields(Arrays.asList(insertFields));
                mb.setCreatorId(1);
                mb.setTableName(moduleTableName);
                mb.setModuleId(Integer.parseInt(moduleid_xmcy));
                List<List<Object>> values = new ArrayList<>();
                List<List<Object>> eachValues;

                //通过Map.entrySet遍历key和value
                //注意HashMap无序
                for (Map.Entry<String, String> entry : allPrjMember.entrySet()) {
                    eachValues = executeEachPrj(entry.getKey(), entry.getValue(), allMemberRate, allMemberList);
                    if (!eachValues.isEmpty()) {
                        values.addAll(eachValues);
                    }
                }
                mb.setValues(values);
                bb.writeLog("new values:" + values);
                ModuleDataUtil.insert(mb);

            }
        } catch (Exception e) {
            bb.writeLog(SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("PrjMemberJob ---END");
    }

    /**
     * 获取所有项目对应的所有成员
     *
     * @return
     */
    private Map<String, String> getAllPrjMember() {
        Map<String, String> result = new HashMap<>();
        //项目id，经理，项目成员，项目总监
        String id, manager, members, xmzj;
        String allMember;
        String sql = "select id,manager,members,xmzj from prj_projectinfo";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                allMember = "";
                id = Util.null2String(rs.getString("id"));
                manager = Util.null2String(rs.getString("manager"));
                allMember = append2Member(allMember, manager);
                members = Util.null2String(rs.getString("members"));
                allMember = append2Member(allMember, members);
                xmzj = Util.null2String(rs.getString("xmzj"));
                allMember = append2Member(allMember, xmzj);
                if (!allMember.isEmpty()) {
                    allMember = allMember.substring(0, allMember.length() - 1);
                    //给allMember去重
                    allMember = uniqueMember(allMember);

                    result.put(id, allMember);
                }

            }
        }
        return result;
    }

    /**
     * 给人员id去重
     *
     * @param allMember
     * @return
     */
    private String uniqueMember(String allMember) {
        // 将字符串拆分成人员ID数组，使用英文逗号作为分隔符
        String[] idsArray = allMember.split(CommonCst.COMMA_EN);
        // 使用 HashSet 来存储去重后的人员ID
        HashSet<String> uniqueIdsSet = new HashSet<>(Arrays.asList(idsArray));
        // 使用 StringBuilder 构建去重后的人员ID字符串
        StringBuilder result = new StringBuilder();
        for (String id : uniqueIdsSet) {
            result.append(id).append(CommonCst.COMMA_EN);
        }
        // 去除最后一个逗号并返回结果
        return result.substring(0, result.length() - 1);
    }

    /**
     * 添加各个成员
     *
     * @param member
     * @param users
     * @return
     */
    private String append2Member(String member, String users) {
        StringBuilder memberBuilder = new StringBuilder(member);
        if (!users.isEmpty()) {
            for (String str : users.split(CommonCst.COMMA_EN)) {
                memberBuilder.append(str).append(CommonCst.COMMA_EN);
            }
        }
        return memberBuilder.toString();
    }

    /**
     * 每个项目执行
     *
     * @param prjid
     * @param members
     * @param allMemberRate
     * @param allMemberList
     * @return
     */
    private List<List<Object>> executeEachPrj(String prjid,
                                              String members,
                                              List<Map<String, Object>> allMemberRate,
                                              List<Map<String, Object>> allMemberList) {
        List<List<Object>> result = new ArrayList<>();
        Map<String, Object> rateMap;
        for (String eachMember : members.split(CommonCst.COMMA_EN)) {
            rateMap = getMemberRate(eachMember, allMemberRate);
            if (rateMap != null) {
                //校验该rate在项目成员清单是否存在uf_xmcyqd，不存在则要新增
                List<Object> eachValue = checkMemberAndGenerate(prjid, rateMap, allMemberList);
                if (eachValue != null) {
                    result.add(eachValue);
                }
            }
        }
        return result;
    }

    /**
     * 获取所有项目人员rate配置清单
     *
     * @return
     */
    private List<Map<String, Object>> getAllMemberRate() {
        List<Map<String, Object>> list = new ArrayList<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select distinct xm,rate,zj1 from uf_zjrwhb")) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

    /**
     * 获取所有项目人员清单
     *
     * @return
     */
    private List<Map<String, Object>> getMemberList() {
        List<Map<String, Object>> list = new ArrayList<>();
        RecordSet rs = new RecordSet();
        if (rs.executeQuery("select distinct xgxm,cy,rate,zj from uf_xmcyqd")) {
            list = QueryUtil.getMapList(rs);
        }
        return list;
    }

    /**
     * 获取人员对应的rate信息
     *
     * @param member
     * @param list
     * @return
     */
    private Map<String, Object> getMemberRate(String member, List<Map<String, Object>> list) {
        String eachMember;
        for (Map<String, Object> map : list) {
            eachMember = Util.null2String(map.get("xm"));
            if (member.equals(eachMember)) {
                return map;
            }
        }
        return null;
    }

    /**
     * 校验当前人员是否在项目人员清单中
     *
     * @param prjid
     * @param member
     * @param allMemberList
     * @return
     */
    private List<Object> checkMemberAndGenerate(String prjid, Map<String, Object> member, List<Map<String, Object>> allMemberList) {
        List<Object> result = null;
        //rate中的存在的人员信息
        String xm = Util.null2String(member.get("xm"));
        String rate = Util.null2String(member.get("rate"));
        String zj = Util.null2String(member.get("zj1"));
        //项目成员清单值
        boolean hasMember = false;
        String qd_cy, qd_zj, qd_rate, qd_prj;
        for (Map<String, Object> map : allMemberList) {
            qd_prj = Util.null2String(map.get("xgxm"));
            qd_cy = Util.null2String(map.get("cy"));
            qd_rate = Util.null2String(map.get("rate"));
            qd_zj = Util.null2String(map.get("zj"));
            if (prjid.equals(qd_prj) && xm.equals(qd_cy) && zj.equals(qd_zj) && rate.equals(qd_rate)) {

                hasMember = true;
                break;
            }
        }
        //成员不存在，则添加到建模中
        if (!hasMember) {
            result = new ArrayList<>();
            result.add(prjid);
            result.add(xm);
            result.add(zj);
            result.add(rate);
        }
        return result;
    }

}
