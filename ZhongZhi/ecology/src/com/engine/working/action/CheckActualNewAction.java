package com.engine.working.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.working.cmd.PlanDataCmd;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 实际工时填报，提交校验
 */
public class CheckActualNewAction extends BaseBean implements Action {

    //Action 参数----START----
    //相关项目字段名(主表)
    private String projectField;
    //姓名字段名（明细1）
    private String xmField;
    //工时字段名（明细1）
    private String gsField;
    //Rate字段名（明细1）
    private String rateField;
    //年月字段名（明细1）
    private String yearMonthField;
    //校验.工时合计金额失败提示信息
    private String amountErroMsg;
    //校验.明细行重复提示信息
    private String repeatErroMsg;
    //校验.实际工时超过计划提示信息
    private String gsErroMsg;
    //明细行不存在的年月提示信息
    private String yearMonthExistErroMsg;
    //校验.明细行年月时间不合法提示信息
    private String yearMonthValidErroMsg;


    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //流程id
        String workflowId = requestInfo.getWorkflowid();
        //请求id
        String requestId = requestInfo.getRequestid();
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();

        //是否为单据(1为是)
        int isBill = rm.getIsbill();
        //获取数据库主表名(如果 不为1，流程数据是存在"workflow_form"表中)
        String tableName = isBill == 1 ? rm.getBillTableName() : "workflow_form";
        //获取主表信息
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, Object> mapMain = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            mapMain.put(str, value);
        }
        writeLog("CheckActualNewAction,workflowId:" + workflowId);
        writeLog("CheckActualNewAction,requestId:" + requestId);
        writeLog("CheckActualNewAction,tableName:" + tableName);
        String erroMsg;
        //校验
        //项目id
        String projectId = Util.null2String(mapMain.get(projectField));
        //明细行数据
        JSONArray jaWfDetail = getWfDetail(requestId, tableName);
        //校验1，action参数
        erroMsg = checkActionParam();
        if (erroMsg.isEmpty()) {
            //校验2：明细行重复数据，姓名+年月+rate
            erroMsg = checkDetailRepeat(jaWfDetail);
            if (erroMsg.isEmpty()) {
                //校验3：工时金额合计
                if (!projectId.isEmpty()) {
                    //获取项目信息
                    JSONObject prjInfo = getPriInfo(projectId);
                    //剩余可分配实际工时（金额口径）
                    BigDecimal leftAmount = SDUtil.getBigDecimalValue(prjInfo.get("sysjgs"));
                    writeLog("leftAmount:" + leftAmount);
                    //当前明细合计工时金额
                    BigDecimal wfTotalAmount = getWfTotalAmount(jaWfDetail);
                    writeLog("wfTotalAmount:" + wfTotalAmount);
                    //当前项目已有的台账里的工时金额
                    BigDecimal moduleTotalAmount = getModuleTotalAmount(projectId);
                    writeLog("moduleTotalAmount:" + moduleTotalAmount);
                    if (wfTotalAmount.compareTo(leftAmount.add(moduleTotalAmount)) > 0) {
                        erroMsg = amountErroMsg;
                    }
                } else {
                    erroMsg = "相关项目值为空，请检查！";
                }
                if (erroMsg.isEmpty()) {
                    //校验4：若选择的月份不在生成好的月份内，报错
                    erroMsg = checkDetailYearMonthExist(jaWfDetail, projectId);
                    if (erroMsg.isEmpty()) {
                        //校验5：若当前填报的实际工时若超过该员工的计划工时，报错
                        erroMsg = checkDetailGs2Plan(jaWfDetail, projectId);
                    }
                }
            }
        }
        writeLog("erroMsg:" + erroMsg);
        writeLog(this.getClass().getName() + "---END");
        if (erroMsg.isEmpty()) {
            return Action.SUCCESS;
        } else {
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent(erroMsg);
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    /**
     * 获取项目工时信息
     *
     * @param prjid
     * @return
     */
    public JSONObject getPriInfo(String prjid) {
        RecordSet rs;
        String sql;
        JSONArray ja;
        JSONObject jo = new JSONObject();
        sql = "SELECT * from V_workding_info where 1=1 and prjid = ? ";
        rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            ja = QueryUtil.getJSONList(rs);
            if (!ja.isEmpty()) {
                jo = ja.getJSONObject(0);
            }
        } else {
            writeLog("getBaseInfo sql erro:" + rs.getExceptionMsg());
        }
        return jo;
    }

    /**
     * 获取明细行数据
     *
     * @param requestId
     * @param tableName
     * @return
     */
    public JSONArray getWfDetail(String requestId, String tableName) {
        RecordSet rs;
        String sql;
        JSONArray ja = new JSONArray();
        rs = new RecordSet();
        sql = "select d.* from " + tableName + "_dt1 d left join " + tableName + " m on (d.mainid = m.id) where m.requestid = ? ";
        if (rs.executeQuery(sql, requestId)) {
            ja = QueryUtil.getJSONList(rs);
        } else {
            writeLog("getBaseInfo sql erro:" + rs.getExceptionMsg());
        }
        return ja;
    }

    /**
     * 获取明细工时合计金额
     *
     * @return
     */
    private BigDecimal getWfTotalAmount(JSONArray ja) {
        BigDecimal result = BigDecimal.valueOf(0);
        JSONObject jo;
        for (int i = 0; i < ja.size(); i++) {
            jo = ja.getJSONObject(i);
            result = result.add(SDUtil.getBigDecimalValue(jo.get(gsField)).multiply(SDUtil.getBigDecimalValue(jo.get(rateField))));
        }
        return result;
    }

    /**
     * 获取建模总金额
     *
     * @param projectId
     * @return
     */
    private BigDecimal getModuleTotalAmount(String projectId) {
        RecordSet rs;
        String sql;
        BigDecimal result = BigDecimal.valueOf(0);
        rs = new RecordSet();
        sql = " select sum(gs*rate) as je from uf_sjgsmx where xgxm = ? ";
        if (rs.executeQuery(sql, projectId)) {
            if (rs.next()) {
                result = SDUtil.getBigDecimalValue(rs.getString("je"));
            }
        } else {
            writeLog("getModuleTotalAmount sql erro:" + rs.getExceptionMsg());
        }
        return result;
    }

    /**
     * 校验action参数必填
     */
    private String checkActionParam() {
        String erroMsg = "";
        if (StringUtils.isBlank(projectField)) {
            erroMsg = "projectField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(xmField)) {
            erroMsg = "xmField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(gsField)) {
            erroMsg = "gsField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(rateField)) {
            erroMsg = "rateField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(yearMonthField)) {
            erroMsg = "yearMonthField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(amountErroMsg)) {
            erroMsg = "amountErroMsg参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(repeatErroMsg)) {
            erroMsg = "repeatErroMsg参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(gsErroMsg)) {
            erroMsg = "gsErroMsg参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(yearMonthExistErroMsg)) {
            erroMsg = "yearMonthExistErroMsg参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(yearMonthValidErroMsg)) {
            erroMsg = "yearMonthValidErroMsg参数为空，请检查action配置！";
        }
        return erroMsg;

    }

    /**
     * 校验流程明细重复行
     */
    private String checkDetailRepeat(JSONArray ja) {
        String erroMsg = "";
        JSONObject jo;
        Map<String, Object> mapCheck = new HashMap<>();
        String key;
        int index;
        for (int i = 0; i < ja.size(); i++) {
            index = i + 1;
            jo = ja.getJSONObject(i);
            key = Util.null2String(jo.get(xmField)) + "&" + Util.null2String(jo.get(gsField)) + "&" + Util.null2String(jo.get(rateField));
            if (mapCheck.containsKey(key)) {
                erroMsg = "第" + index + "行，" + repeatErroMsg;
                break;
            } else {
                mapCheck.put(key, "");
            }
        }
        return erroMsg;
    }

    /**
     * 校验明细行，对应的年月必要在台账中存在
     *
     * @param ja
     * @param projectId
     */
    private String checkDetailYearMonthExist(JSONArray ja, String projectId) {
        RecordSet rs;
        String sql;
        String erroMsg = "";
        JSONObject jo;
        int index;
        String xm, rate, yearMonth;
        Map<String, Object> mapValidYearMonth = new HashMap<>(6);
        String startYearMonthNormal, nextYearMonthNormal;
        //配置的项目开始日期阈值，如果项目开始日期大于等于该日期，则按项目的开始日期计算，如果小于，则按照阈值的开始日期算
        String limitDate = getPropValue("ciic_working", "project_limit_date");
        rs = new RecordSet();
        //获取项目的最新预估开始日期，和最新预估结束日期，相差月份
        sql = " select xmsjkssj as startdate,xmssjssj as enddate " +
                " from prj_projectinfo where id = ? ";
        if (rs.executeQuery(sql, projectId)) {
            if (rs.next()) {
                String startDate = Util.null2String(rs.getString("startdate"));
                String endDate = Util.null2String(rs.getString("enddate"));
                writeLog("startDate:" + startDate);
                writeLog("endDate:" + endDate);
                if (TimeUtil.dateInterval(limitDate, startDate) < 0) {
                    startDate = limitDate;
                }
                writeLog("startDate2:" + startDate);
                writeLog("endDate2:" + endDate);
                if (TimeUtil.dateInterval(startDate, endDate) >= 0) {
                    int diff = PlanDataCmd.getBetweenMonth(startDate, endDate);
                    writeLog("diff:" + diff);
                    if (diff >= 0 && StringUtils.isNotBlank(startDate)) {
                        startYearMonthNormal = startDate.substring(0, 7);
                        mapValidYearMonth.put(startYearMonthNormal, "");
                        for (int i = 1; i <= diff; i++) {
                            startDate = PlanDataCmd.getMonthAddDate(startDate, 1);
                            writeLog("startDate:" + startDate);
                            if (startDate != null) {
                                nextYearMonthNormal = startDate.substring(0, 7);
                                mapValidYearMonth.put(nextYearMonthNormal, "");
                            }
                        }
                    }
                }
            }
        }
        writeLog("mapValidYearMonth:" + mapValidYearMonth);
        for (int i = 0; i < ja.size(); i++) {
            index = i + 1;
            jo = ja.getJSONObject(i);
            yearMonth = Util.null2String(jo.get(yearMonthField));
            if (!mapValidYearMonth.containsKey(yearMonth)) {
                erroMsg = "第" + index + "行，" + yearMonthExistErroMsg;
                break;
            }
        }
        return erroMsg;
    }

    /**
     * 校验明细行，比较当前填报的实际工时若超过该员工的计划工时
     *
     * @param ja
     * @param projectId
     */
    private String checkDetailGs2Plan(JSONArray ja, String projectId) {
        RecordSet rs;
        String erroMsg = "";
        JSONObject jo;
        int index;
        String xm, rate, yearMonth;
        //计划工时
        BigDecimal planGs = BigDecimal.valueOf(0);
        //当前填报的实际工时
        BigDecimal currentGs;
        for (int i = 0; i < ja.size(); i++) {
            index = i + 1;
            jo = ja.getJSONObject(i);
            xm = Util.null2String(jo.get(xmField));
            rate = Util.null2String(jo.get(rateField));
            yearMonth = Util.null2String(jo.get(yearMonthField));
            currentGs = SDUtil.getBigDecimalValue(jo.get(gsField));
            rs = new RecordSet();
            if (rs.executeQuery("select gs from uf_jhgsmx " +
                    " where xgxm = ? and xm = ? and rate = ? and ny = ? ", projectId, xm, rate, yearMonth)) {
                if (rs.next()) {
                    planGs = SDUtil.getBigDecimalValue(rs.getString("gs"));

                }
            } else {
                writeLog("checkDetailGs2Plan sql erro:" + rs.getExceptionMsg());
            }
            if (currentGs.compareTo(planGs) > 0) {
                erroMsg = "第" + index + "行，" + gsErroMsg;
                break;
            }
        }
        return erroMsg;
    }


    public String getProjectField() {
        return projectField;
    }

    public void setProjectField(String projectField) {
        this.projectField = projectField;
    }

    public String getXmField() {
        return xmField;
    }

    public void setXmField(String xmField) {
        this.xmField = xmField;
    }

    public String getGsField() {
        return gsField;
    }

    public void setGsField(String gsField) {
        this.gsField = gsField;
    }

    public String getRateField() {
        return rateField;
    }

    public void setRateField(String rateField) {
        this.rateField = rateField;
    }

    public String getYearMonthField() {
        return yearMonthField;
    }

    public void setYearMonthField(String yearMonthField) {
        this.yearMonthField = yearMonthField;
    }

    public String getAmountErroMsg() {
        return amountErroMsg;
    }

    public void setAmountErroMsg(String amountErroMsg) {
        this.amountErroMsg = amountErroMsg;
    }

    public String getRepeatErroMsg() {
        return repeatErroMsg;
    }

    public void setRepeatErroMsg(String repeatErroMsg) {
        this.repeatErroMsg = repeatErroMsg;
    }

    public String getGsErroMsg() {
        return gsErroMsg;
    }

    public void setGsErroMsg(String gsErroMsg) {
        this.gsErroMsg = gsErroMsg;
    }

    public String getYearMonthExistErroMsg() {
        return yearMonthExistErroMsg;
    }

    public void setYearMonthExistErroMsg(String yearMonthExistErroMsg) {
        this.yearMonthExistErroMsg = yearMonthExistErroMsg;
    }

    public String getYearMonthValidErroMsg() {
        return yearMonthValidErroMsg;
    }

    public void setYearMonthValidErroMsg(String yearMonthValidErroMsg) {
        this.yearMonthValidErroMsg = yearMonthValidErroMsg;
    }
}
