package com.engine.working.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 实际工时调整，提交校验
 */
public class CheckActualAdjustAction extends BaseBean implements Action {

    //Action 参数----START----
    //相关项目字段名(主表)
    private String projectField;
    //姓名字段名（明细1）
    private String xmField;
    //调整后工时字段名（主表）
    private String gsField;
    //Rate字段名（主表）
    private String rateField;
    //年月字段名（主表）
    private String yearMonthField;
    //校验.工时合计金额失败提示信息
    private String amountErroMsg;

    //校验.当前调整的实际工时时长错误信息
    private String gsErroMsg;
    //校验.当前调整的年月是否为当前月
    private String nyErroMsg;


    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "---START");
        //流程id
        String workflowId = requestInfo.getWorkflowid();
        //请求id
        String requestId = requestInfo.getRequestid();
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();

        //是否为单据(1为是)
        int isBill = rm.getIsbill();
        //获取数据库主表名(如果 不为1，流程数据是存在"workflow_form"表中)
        String tableName = isBill == 1 ? rm.getBillTableName() : "workflow_form";
        //获取主表信息
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, Object> mapMain = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            mapMain.put(str, value);
        }
        writeLog("CheckPlanAdjustAction,workflowId:" + workflowId);
        writeLog("CheckPlanAdjustAction,requestId:" + requestId);
        writeLog("CheckPlanAdjustAction,tableName:" + tableName);
        String erroMsg;
        //校验
        //项目id
        String projectId = Util.null2String(mapMain.get(projectField));
        //校验1，action参数
        erroMsg = checkActionParam();
        if (erroMsg.isEmpty()) {
            //校验2：工时金额合计
            if (!projectId.isEmpty()) {
                //获取项目信息
                JSONObject prjInfo = getPriInfo(projectId);
                //剩余可分配实际工时（金额口径）
                BigDecimal leftAmount = SDUtil.getBigDecimalValue(prjInfo.get("sysjgs"));
                writeLog("leftAmount:" + leftAmount);
                //当前流程合计工时金额
                BigDecimal wfTotalAmount = SDUtil.getBigDecimalValue(mapMain.get(rateField)).multiply(SDUtil.getBigDecimalValue(mapMain.get(gsField)));
                writeLog("wfTotalAmount:" + wfTotalAmount);
                //当前项目已有的台账里的工时金额
                BigDecimal moduleTotalAmount = getModuleTotalAmount(projectId, mapMain);
                writeLog("moduleTotalAmount:" + moduleTotalAmount);
                if (wfTotalAmount.compareTo(leftAmount.add(moduleTotalAmount)) > 0) {
                    erroMsg = amountErroMsg;
                }
            } else {
                erroMsg = "相关项目值为空，请检查！";
            }
            if (erroMsg.isEmpty()) {
                //校验3：若当前调整的实际工时若超过该员工的计划工时,报错
                erroMsg = checkHours(mapMain, projectId);
                if (erroMsg.isEmpty()) {
                    //校验4：校验调整年月，只能是当前年月
                    erroMsg = checkYearMonth(mapMain);
                }

            }
        }
        writeLog("erroMsg:" + erroMsg);
        writeLog(this.getClass().getName() + "---END");
        if (erroMsg.isEmpty()) {
            return Action.SUCCESS;
        } else {
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent(erroMsg);
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    /**
     * 获取项目工时信息
     *
     * @param prjid
     * @return
     */
    public JSONObject getPriInfo(String prjid) {
        String sql;
        RecordSet rs;
        JSONArray ja;
        JSONObject jo = new JSONObject();
        sql = "SELECT * from V_workding_info where 1=1 and prjid = ? ";
        rs = new RecordSet();
        if (rs.executeQuery(sql, prjid)) {
            ja = QueryUtil.getJSONList(rs);
            if (!ja.isEmpty()) {
                jo = ja.getJSONObject(0);
            }
        } else {
            writeLog("getBaseInfo sql erro:" + rs.getExceptionMsg());
        }
        return jo;
    }


    /**
     * 获取建模总金额
     *
     * @param projectId
     * @return
     */
    private BigDecimal getModuleTotalAmount(String projectId, Map<String, Object> mapMain) {
        String sql;
        RecordSet rs;
        BigDecimal result = BigDecimal.valueOf(0);
        //姓名
        String xm = Util.null2String(mapMain.get(xmField));
        //年月
        String yearMonth = Util.null2String(mapMain.get(yearMonthField));
        rs = new RecordSet();
        sql = " select gs*rate as je from uf_sjgsmx where xgxm = ? and xm = ? and ny = ?  ";
        if (rs.executeQuery(sql, projectId, xm, yearMonth)) {
            if (rs.next()) {
                result = SDUtil.getBigDecimalValue(rs.getString("je"));
            }
        } else {
            writeLog("getModuleTotalAmount sql erro:" + rs.getExceptionMsg());
        }
        return result;
    }

    /**
     * 校验action参数必填
     */
    private String checkActionParam() {
        String erroMsg = "";
        if (StringUtils.isBlank(projectField)) {
            erroMsg = "projectField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(xmField)) {
            erroMsg = "xmField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(gsField)) {
            erroMsg = "gsField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(rateField)) {
            erroMsg = "rateField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(yearMonthField)) {
            erroMsg = "yearMonthField参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(amountErroMsg)) {
            erroMsg = "amountErroMsg参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(gsErroMsg)) {
            erroMsg = "gsErroMsg参数为空，请检查action配置！";
        }
        if (StringUtils.isBlank(nyErroMsg)) {
            erroMsg = "nyErroMsg参数为空，请检查action配置！";
        }
        return erroMsg;
    }


    /**
     * 校验工时
     *
     * @param mainMap
     * @param projectId
     */
    private String checkHours(Map<String, Object> mainMap, String projectId) {
        String sql;
        RecordSet rs;
        String erroMsg = "";
        BigDecimal planNum = BigDecimal.valueOf(0);
        //姓名
        String xm = Util.null2String(mainMap.get(xmField));
        //rate
        String rate = Util.null2String(mainMap.get(rateField));
        //年月
        String yearMonth = Util.null2String(mainMap.get(yearMonthField));
        rs = new RecordSet();
        sql = "select gs from uf_jhgsmx where xgxm = ? and xm = ? and ny = ? and rate = ?";
        if (rs.executeQuery(sql, projectId, xm, yearMonth, rate)) {
            if (rs.next()) {
                planNum = SDUtil.getBigDecimalValue(rs.getString("gs"));
            }
        } else {
            writeLog("checkHours sql erro:" + rs.getExceptionMsg());
        }
        writeLog("planNum:" + planNum);
        BigDecimal currentNum = SDUtil.getBigDecimalValue(mainMap.get(gsField));
        writeLog("currentNum:" + currentNum);
        if (currentNum.compareTo(planNum) > 0) {
            erroMsg = gsErroMsg;
        }
        return erroMsg;
    }

    private String checkYearMonth(Map<String, Object> mainMap) {
        String erroMsg = "";
        //年月
        String yearMonth = Util.null2String(mainMap.get(yearMonthField));
        //当前时间的年月
        String currentYearMonth = TimeUtil.getCurrentDateString().substring(0, 7);
        if (!yearMonth.equals(currentYearMonth)) {
            erroMsg = nyErroMsg;
        }
        return erroMsg;
    }

    public String getProjectField() {
        return projectField;
    }

    public void setProjectField(String projectField) {
        this.projectField = projectField;
    }

    public String getXmField() {
        return xmField;
    }

    public void setXmField(String xmField) {
        this.xmField = xmField;
    }

    public String getGsField() {
        return gsField;
    }

    public void setGsField(String gsField) {
        this.gsField = gsField;
    }

    public String getRateField() {
        return rateField;
    }

    public void setRateField(String rateField) {
        this.rateField = rateField;
    }

    public String getYearMonthField() {
        return yearMonthField;
    }

    public void setYearMonthField(String yearMonthField) {
        this.yearMonthField = yearMonthField;
    }

    public String getAmountErroMsg() {
        return amountErroMsg;
    }

    public void setAmountErroMsg(String amountErroMsg) {
        this.amountErroMsg = amountErroMsg;
    }

    public String getGsErroMsg() {
        return gsErroMsg;
    }

    public void setGsErroMsg(String gsErroMsg) {
        this.gsErroMsg = gsErroMsg;
    }

    public String getNyErroMsg() {
        return nyErroMsg;
    }

    public void setNyErroMsg(String nyErroMsg) {
        this.nyErroMsg = nyErroMsg;
    }
}
