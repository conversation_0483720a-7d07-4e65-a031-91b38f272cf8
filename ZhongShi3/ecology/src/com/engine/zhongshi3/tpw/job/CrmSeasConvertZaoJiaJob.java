package com.engine.zhongshi3.tpw.job;

import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongshi3.tpw.common.cmd.WlxCustomerJoinseasInfoByLog;
import com.engine.zhongshi3.tpw.job.util.CrmSeasUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.toolbox.db.recordset.QueryUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 功能说明 客户公海转换
 * 出现状况客户进入部门公海
 * 部门公海转换到公司公海
 *
 * <AUTHOR>
 * @crete Nov 30, 2022 14:27
 */
@Getter
@Setter
public class CrmSeasConvertZaoJiaJob extends BaseCronJob {

    BaseBean bb = new BaseBean();
    private Map<String, String> lapseMap;

    @Override
    public void execute() {
        bb.writeLog("CrmSeasConvertJob start");
        resetParam();
        init();
        ArrayList<String> customeridList = new ArrayList<>();
        // 获取公海客户集合，如果已经存在于公海的客户，不再进行转换
        Map<String, String> seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        bb.writeLog("seasCustomerMap:" + seasCustomerMap);
        // region 基础客户 start
        // 基础客户X天未联系或Y天未转成意向客户，则进入公海
        // 基础客户未联系的天数
        String jckhwlx = lapseMap.get("jckhwlx");
        // 基础客户未转化为意向客户的天数
        String wzyxkh = lapseMap.get("wzyxkh");
        String qry5 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id" +
                " where a.bcjckhsj < getdate() - " + wzyxkh + " and a.status = 5 and b.departmentid > 0 and a.bmlx = 2";
        bb.writeLog("qry5 = " + qry5);
        RecordSet rs5 = new RecordSet();
        rs5.execute(qry5);
        while (rs5.next()) {
            String customerId = Util.null2String(rs5.getString("id"));
            String topDeptId = Util.null2String(rs5.getString("topdeptid"));
            if (!seasCustomerMap.containsKey(customerId)) {
                bb.writeLog("qry5 客户id：" + customerId + "不在map中");
                // 获取部门公海的id
                String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
//                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                if (!customerId.isEmpty()) {
                    bb.writeLog("qry5 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                    // 添加到部门公海
//                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                    CrmSeasUtil.addCustomer2Seas(customerId, "4");
                }
            }
        }
//
//        String qry51 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id" +
//                " where  a.lastcontacteddate < getdate() - "+jckhwlx+" and a.status = 5 and b.departmentid > 0 and a.bmlx = 2";
        String qry51 = "select a.id,a.createdate,a.bcjckhsj,a.lastcontacteddate,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id" +
                " where  a.status = 5 and b.departmentid > 0 and a.bmlx = 2";
        bb.writeLog("qry51 = " + qry51);
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        RecordSet rs51 = new RecordSet();
        rs51.execute(qry51);
        while (rs51.next()) {
            bb.writeLog("客户id:" + rs51.getString("id"));
            bb.writeLog("客户lastcontacteddate:" + rs51.getString("lastcontacteddate"));
            bb.writeLog("客户bcjckhsj:" + rs51.getString("bcjckhsj"));
            bb.writeLog("客户createdate:" + rs51.getString("createdate"));
            String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(jckhwlx)-2*Integer.parseInt(jckhwlx));
            bb.writeLog("referenceDate:" + referenceDate);
            boolean flag = false;
            if (StringUtils.isBlank(rs51.getString("lastcontacteddate"))) {
                //按照转基础客户时间判断
                String bcjckhsj = rs51.getString("bcjckhsj");
                if(StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) < 0){
                    flag = true;
                }else if(StringUtils.isBlank(bcjckhsj)){
                    String createdate = rs51.getString("createdate");
                    if(StringUtils.isNotBlank(createdate)&& createdate.compareTo(referenceDate) < 0){
                        flag = true;
                    }
                }
            } else {
                //按照未联系时间的条件
                String lastcontacteddate = rs51.getString("lastcontacteddate");
                if(lastcontacteddate.compareTo(referenceDate)<0){
                    flag = true;
                }
            }
            if(flag){
                String customerId = Util.null2String(rs51.getString("id"));
                String topDeptId = Util.null2String(rs51.getString("topdeptid"));
                String manager = Util.null2String(rs51.getString("manager"));
                if (!seasCustomerMap.containsKey(customerId)) {
                    bb.writeLog("qry51 客户id：" + customerId + "不在map中");
                    // 获取部门公海的id
                    String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
//                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                    if (!customerId.isEmpty()) {
                        bb.writeLog("qry51 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                        // 添加到部门公海
//                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                        //插入建模数据
                        HashMap<String, Object> params = new HashMap<>();
                        params.put("customerIds", customerId);
                        customeridList.add(customerId);
                        WlxCustomerJoinseasInfoByLog wlxCustomerJoinseasInfoByLog = new WlxCustomerJoinseasInfoByLog(params, manager);
                        wlxCustomerJoinseasInfoByLog.executeData();
                        CrmSeasUtil.addCustomer2Seas(customerId, "4");
                    }
                }
            }
        }

        // endregion 基础客户 end

        // region 意向客户 start
        // 意向客户X天未联系或Y天未转成成交客户，则进入公海
        // 意向客户未联系天数
        String yxkhwlx = lapseMap.get("yxkhwlx");
        // 意向客户未成交天数
        String wzcjkh = lapseMap.get("wzcjkh");
        // 意向不联系且未成交的客户所属顶级部门
        String qry2 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                " where a.jckhzwyjkhsj < getdate() - " + wzcjkh + " and a.status = 2 and b.departmentid > 0 and a.bmlx = 2";
        bb.writeLog("qry2 = " + qry2);
        RecordSet rs2 = new RecordSet();
        rs2.execute(qry2);
        while (rs2.next()) {
            String customerId = Util.null2String(rs2.getString("id"));
            String topDeptId = Util.null2String(rs2.getString("topdeptid"));
            if (!seasCustomerMap.containsKey(customerId)) {
                bb.writeLog("qry2 客户id：" + customerId + "不在map中");
                // 获取部门公海的id
                String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                //判断有公海id和客户id
//                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                if (!customerId.isEmpty()) {
                    bb.writeLog("qry2 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                    // 添加到部门公海
//                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                    CrmSeasUtil.addCustomer2Seas(customerId, "4");
                }
            }
        }

//        String qry21 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
//                " where  a.lastcontacteddate < getdate() - " + yxkhwlx + " and a.status = 2 and b.departmentid > 0 and a.bmlx = 2";
        String qry21 = "select a.id,a.createdate,a.bcjckhsj,a.lastcontacteddate,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                " where  a.status = 2 and b.departmentid > 0 and a.bmlx = 2";
        bb.writeLog("qry21 = " + qry21);
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        RecordSet rs21 = new RecordSet();
        rs21.execute(qry21);
        while (rs21.next()) {
            bb.writeLog("客户id:" + rs21.getString("id"));
            bb.writeLog("客户lastcontacteddate:" + rs21.getString("lastcontacteddate"));
            bb.writeLog("客户bcjckhsj:" + rs21.getString("bcjckhsj"));
            bb.writeLog("客户createdate:" + rs21.getString("createdate"));
            String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(yxkhwlx)-2*Integer.parseInt(yxkhwlx));
            bb.writeLog("referenceDate:" + referenceDate);
            boolean flag = false;
            if (StringUtils.isBlank(rs21.getString("lastcontacteddate"))) {
                //按照转基础客户时间判断
                String bcjckhsj = rs21.getString("bcjckhsj");
                if(StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) < 0){
                    flag = true;
                }else if(StringUtils.isBlank(bcjckhsj)){
                    String createdate = rs21.getString("createdate");
                    if(StringUtils.isNotBlank(createdate)&& createdate.compareTo(referenceDate) < 0){
                        flag = true;
                    }
                }
            } else {
                //按照未联系时间的条件
                String lastcontacteddate = rs21.getString("lastcontacteddate");
                if(lastcontacteddate.compareTo(referenceDate)<0){
                    flag = true;
                }
            }
            if(flag){
                String customerId = Util.null2String(rs21.getString("id"));
                String topDeptId = Util.null2String(rs21.getString("topdeptid"));
                String manager = Util.null2String(rs21.getString("manager"));
                if (!seasCustomerMap.containsKey(customerId)) {
                    bb.writeLog("qry21 客户id：" + customerId + "不在map中");
                    // 获取部门公海的id
                    String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                    //判断有公海id和客户id
//                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                    if (!customerId.isEmpty()) {
                        bb.writeLog("qry21 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                        // 添加到部门公海
//                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                        HashMap<String, Object> params = new HashMap<>();
                        params.put("customerIds", customerId);
                        customeridList.add(customerId);
                        WlxCustomerJoinseasInfoByLog wlxCustomerJoinseasInfoByLog = new WlxCustomerJoinseasInfoByLog(params, manager);
                        wlxCustomerJoinseasInfoByLog.executeData();
                        CrmSeasUtil.addCustomer2Seas(customerId, "4");
                    }
                }
            }
        }

        // endregion 意向客户 end

        // region 成交客户 start
        // 若N个月内没有新项目
        // 成交客户3个月未联系或1年无新项目成交，则进入公海

        //成交客户未联系天数（天）
        String cjkhwlx = lapseMap.get("cjkhwlx");
        //成交客户未有新项目天数（天）
        String wyxxm = lapseMap.get("wyxxm");
        //成交无新项目的客户所属顶级部门
        String qry3 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                " where a.zjcjsj < getdate() - " + wyxxm + " and a.status = 3 and b.departmentid > 0 and a.bmlx = 2";
        bb.writeLog("qry3 = " + qry3);
        RecordSet rs3 = new RecordSet();
        rs3.execute(qry3);
        while (rs3.next()) {
            String customerId = Util.null2String(rs3.getString("id"));
            String topDeptId = Util.null2String(rs3.getString("topdeptid"));
            if (!seasCustomerMap.containsKey(customerId)) {
                bb.writeLog("qry3 客户id：" + customerId + "不在map中");
                // 获取部门公海的id
                String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                //判断有公海id和客户id
//                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                if (!customerId.isEmpty()) {
                    bb.writeLog("qry3 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                    // 添加到部门公海
//                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                    CrmSeasUtil.addCustomer2Seas(customerId, "4");
                }
            }
        }

        String qry31 = "select a.id,a.createdate,a.bcjckhsj,a.lastcontacteddate,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                " where  a.status = 3 and b.departmentid > 0 and a.bmlx = 2";
        bb.writeLog("qry31 = " + qry31);
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        RecordSet rs31 = new RecordSet();
        rs31.execute(qry31);
        while (rs31.next()) {
            bb.writeLog("客户id:" + rs31.getString("id"));
            bb.writeLog("客户lastcontacteddate:" + rs31.getString("lastcontacteddate"));
            bb.writeLog("客户bcjckhsj:" + rs31.getString("bcjckhsj"));
            bb.writeLog("客户createdate:" + rs31.getString("createdate"));
            String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(cjkhwlx)-2*Integer.parseInt(cjkhwlx));
            bb.writeLog("referenceDate:" + referenceDate);
            boolean flag = false;
            if (StringUtils.isBlank(rs31.getString("lastcontacteddate"))) {
                //按照转基础客户时间判断
                String bcjckhsj = rs31.getString("bcjckhsj");
                if(StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) < 0){
                    flag = true;
                }else if(StringUtils.isBlank(bcjckhsj)){
                    String createdate = rs31.getString("createdate");
                    if(StringUtils.isNotBlank(createdate)&& createdate.compareTo(referenceDate) < 0){
                        flag = true;
                    }
                }
            } else {
                //按照未联系时间的条件
                String lastcontacteddate = rs31.getString("lastcontacteddate");
                if(lastcontacteddate.compareTo(referenceDate)<0){
                    flag = true;
                }
            }
            if(flag){
                String customerId = Util.null2String(rs31.getString("id"));
                String topDeptId = Util.null2String(rs31.getString("topdeptid"));
                String manager = Util.null2String(rs31.getString("manager"));
                if (!seasCustomerMap.containsKey(customerId)) {
                    bb.writeLog("qry31 客户id：" + customerId + "不在map中");
                    // 获取部门公海的id
                    String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                    //判断有公海id和客户id
//                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                    if (!customerId.isEmpty()) {
                        bb.writeLog("qry31 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                        // 添加到部门公海
//                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                        HashMap<String, Object> params = new HashMap<>();
                        params.put("customerIds", customerId);
                        customeridList.add(customerId);
                        WlxCustomerJoinseasInfoByLog wlxCustomerJoinseasInfoByLog = new WlxCustomerJoinseasInfoByLog(params, manager);
                        wlxCustomerJoinseasInfoByLog.executeData();
                        CrmSeasUtil.addCustomer2Seas(customerId, "4");
                    }
                }
            }
        }
        // endregion 成交客户 end

        //最后联系日期没有置空
        if(!customeridList.isEmpty()){
            RecordSet recordSet = new RecordSet();
            recordSet.executeUpdate("update CRM_CustomerInfo set lastcontacteddate = '' where id in ("+String.join(",", customeridList)+")");
        }

        bb.writeLog("CrmSeasConvertJob end");
    }

    private void resetParam() {
        lapseMap = new HashMap<>();
    }

    private void init() {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        HashMap<String, String> config = new HashMap<>();
        recordSet.executeQuery("SELECT * FROM uf_gncspzb");
        while (recordSet.next()) {
            String id = recordSet.getString("id");
            if ("KHJRGH".equals(recordSet.getString("gnbm"))) {
                config.put("KHJRGH", id);
            }
        }
        if (!config.isEmpty()) {
            loadConfigValues(config.get("KHJRGH"), lapseMap);
        }
    }

    private void loadConfigValues(String mainId, Map<String, String> targetMap) {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        if (mainId != null) {
            recordSet.executeQuery("SELECT csmc, csz FROM uf_gncspzb_dt1 WHERE mainid = " + mainId);
            while (recordSet.next()) {
                targetMap.put(recordSet.getString("csmc"), recordSet.getString("csz"));
            }
        }
    }

}

