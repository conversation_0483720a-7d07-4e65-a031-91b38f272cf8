package com.engine.zhongshi3.tpw.job;

import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongshi3.tpw.common.cmd.WlxCustomerJoinseasInfoByLog;
import com.engine.zhongshi3.tpw.job.util.CrmSeasUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;

import weaver.interfaces.schedule.BaseCronJob;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;

import java.util.*;

/**
 * 功能说明 客户公海转换
 * 出现状况客户进入部门公海
 * 部门公海转换到公司公海
 *
 * <AUTHOR>
 * @crete Nov 30, 2022 14:27
 */
@Getter
@Setter
public class CrmSeasConvertFeiZaoJiaJob extends BaseCronJob {

    BaseBean bb = new BaseBean();
    private Map<String, String> lapseMap;
    private Map<String, String> customerMap;
    @Override
    public void execute() {
        try {
            bb.writeLog("CrmSeasConvertFeiZaoJiaJob start");
            resetParam();
            init();
            ArrayList<String> customeridList = new ArrayList<>();
            // 获取公海客户集合，如果已经存在于公海的客户，不再进行转换
            Map<String, String> seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
            bb.writeLog("seasCustomerMap:" + seasCustomerMap);
            // region 基础客户 start
            // 基础客户X天未联系或Y天未转成意向客户，则进入公海
            // 基础客户未联系的天数
            String jckhwlx = lapseMap.get("jckhwlx");
            // 基础客户未转化为意向客户的天数
            String wzyxkh = lapseMap.get("wzyxkh");
            String qry5 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                    " where a.bcjckhsj < getdate() - " + wzyxkh + " and a.status = 5 and b.departmentid > 0 and a.bmlx <> 2";
            bb.writeLog("非造价qry5 = " + qry5);
            RecordSet rs5 = new RecordSet();
            rs5.executeQuery(qry5);
            while (rs5.next()) {
                String customerId = Util.null2String(rs5.getString("id"));
                String topDeptId = Util.null2String(rs5.getString("topdeptid"));
                if (!seasCustomerMap.containsKey(customerId)) {
                    bb.writeLog("qry5 客户id：" + customerId + "不在map中");
                    // 获取部门公海的id
                    String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                    if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                        bb.writeLog("qry5 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                        // 添加到部门公海
                        CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                    }
                }
            }

//            String qry51 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id" +
//                    " where  a.lastcontacteddate < getdate() - "+jckhwlx+" and a.status = 5 and b.departmentid > 0 and a.bmlx <> 2";
            String qry51 = "select a.id,a.createdate,a.bcjckhsj,a.lastcontacteddate,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id" +
                    " where a.status = 5 and b.departmentid > 0 and a.bmlx <> 2";
            bb.writeLog("非造价qry51 = " + qry51);
            bb.writeLog("qry51 = " + qry51);
            RecordSet rs51 = new RecordSet();
            rs51.executeQuery(qry51);
            while (rs51.next()) {
                bb.writeLog("客户id:" + rs51.getString("id"));
                bb.writeLog("客户lastcontacteddate:" + rs51.getString("lastcontacteddate"));
                bb.writeLog("客户bcjckhsj:" + rs51.getString("bcjckhsj"));
                bb.writeLog("客户createdate:" + rs51.getString("createdate"));
                String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(jckhwlx)-2*Integer.parseInt(jckhwlx));
                bb.writeLog("referenceDate:" + referenceDate);
                boolean flag = false;
                if (StringUtils.isBlank(rs51.getString("lastcontacteddate"))) {

                    //按照转基础客户时间判断
                    String bcjckhsj = rs51.getString("bcjckhsj");
                    if(StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) < 0){
                        flag = true;
                    }else if(StringUtils.isBlank(bcjckhsj)){
                        String createdate = rs51.getString("createdate");
                        if(StringUtils.isNotBlank(createdate)&& createdate.compareTo(referenceDate) < 0){
                            flag = true;
                        }
                    }
                } else {
                    //按照未联系时间的条件
                    String lastcontacteddate = rs51.getString("lastcontacteddate");
                    if(lastcontacteddate.compareTo(referenceDate)<0){
                        flag = true;
                    }
                }
                if(flag){
                    String customerId = Util.null2String(rs51.getString("id"));
                    String topDeptId = Util.null2String(rs51.getString("topdeptid"));
                    String manager = Util.null2String(rs51.getString("manager"));
                    if (!seasCustomerMap.containsKey(customerId)) {
                        bb.writeLog("qry51 客户id：" + customerId + "不在map中");
                        // 获取部门公海的id
                            String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                            if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                                //插入建模数据
                                HashMap<String, Object> params = new HashMap<>();
                                params.put("customerIds", customerId);
                                customeridList.add(customerId);
                                WlxCustomerJoinseasInfoByLog wlxCustomerJoinseasInfoByLog = new WlxCustomerJoinseasInfoByLog(params, manager);
                                wlxCustomerJoinseasInfoByLog.executeData();
                                // 获取部门公海的id
                                bb.writeLog("qry51 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                                // 添加到部门公海
                                CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                            }
                        }
                    }
            }
            // endregion 基础客户 end

            // region 意向客户 start
            // 意向客户X天未联系或Y天未转成成交客户，则进入公海
            // 意向客户未联系天数
            String yxkhwlx = lapseMap.get("yxkhwlx");
            // 意向客户未成交天数
            String wzcjkh = lapseMap.get("wzcjkh");
            // 意向不联系且未成交的客户所属顶级部门
            String qry2 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                    " where a.jckhzwyjkhsj < getdate() - " + wzcjkh + " and a.status = 2 and b.departmentid > 0 and a.bmlx <> 2";
            bb.writeLog("非造价qry2 = " + qry2);
            RecordSet rs2 = new RecordSet();
            rs2.executeQuery(qry2);
            while (rs2.next()) {
                String customerId = Util.null2String(rs2.getString("id"));
                String topDeptId = Util.null2String(rs2.getString("topdeptid"));
                if (!seasCustomerMap.containsKey(customerId)) {
                    bb.writeLog("qry2 客户id：" + customerId + "不在map中");
                    // 获取部门公海的id
                    String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                    //判断有公海id和客户id
                    if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                        bb.writeLog("qry2 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                        // 添加到部门公海
                
                        CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                    }
                }
            }

//            String qry21 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
//                    " where a.lastcontacteddate < getdate() - " + yxkhwlx + " and a.status = 2 and b.departmentid > 0 and a.bmlx <> 2";

            String qry21 = "select a.id,a.createdate,a.bcjckhsj,a.lastcontacteddate,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                    " where  a.status = 2 and b.departmentid > 0 and a.bmlx <> 2";
            bb.writeLog("非造价qry21 = " + qry21);
            seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
            RecordSet rs21 = new RecordSet();
            rs21.executeQuery(qry21);
            while (rs21.next()) {
                bb.writeLog("客户id:" + rs21.getString("id"));
                bb.writeLog("客户lastcontacteddate:" + rs21.getString("lastcontacteddate"));
                bb.writeLog("客户bcjckhsj:" + rs21.getString("bcjckhsj"));
                bb.writeLog("客户createdate:" + rs21.getString("createdate"));
                String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(yxkhwlx)-2*Integer.parseInt(yxkhwlx));
                bb.writeLog("客户referenceDate:" + referenceDate);
                boolean flag = false;
                if (StringUtils.isBlank(rs21.getString("lastcontacteddate"))) {
                    //按照转基础客户时间判断
                    String bcjckhsj = rs21.getString("bcjckhsj");
                    if(StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) < 0){
                        flag = true;
                    }else if(StringUtils.isBlank(bcjckhsj)){
                        String createdate = rs21.getString("createdate");
                        if(StringUtils.isNotBlank(createdate)&& createdate.compareTo(referenceDate) < 0){
                            flag = true;
                        }
                    }
                } else {
                    //按照未联系时间的条件
                    String lastcontacteddate = rs21.getString("lastcontacteddate");
                    if(lastcontacteddate.compareTo(referenceDate)<0){
                        flag = true;
                    }
                }
                if(flag){
                    String customerId = Util.null2String(rs21.getString("id"));
                    String topDeptId = Util.null2String(rs21.getString("topdeptid"));
                    String manager = Util.null2String(rs21.getString("manager"));
                    if (!seasCustomerMap.containsKey(customerId)) {
                        bb.writeLog("qry21 客户id：" + customerId + "不在map中");
                        // 获取部门公海的id
                        String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                            if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                                //插入建模数据
                                HashMap<String, Object> params = new HashMap<>();
                                params.put("customerIds", customerId);
                                customeridList.add(customerId);
                                WlxCustomerJoinseasInfoByLog wlxCustomerJoinseasInfoByLog = new WlxCustomerJoinseasInfoByLog(params, manager);
                                wlxCustomerJoinseasInfoByLog.executeData();
                                // 获取部门公海的id
                                bb.writeLog("qry21 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                                // 添加到部门公海
                                CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                            }

                        }
                    }
            }
            // endregion 意向客户 end

            // region 成交客户 start
            // 若N个月内没有新项目
            // 成交客户3个月未联系或1年无新项目成交，则进入公海

            //成交客户未联系天数（天）
            String cjkhwlx = lapseMap.get("cjkhwlx");
            //成交客户未有新项目天数（天）
            String wyxxm = lapseMap.get("wyxxm");
            // 成交无新项目的客户所属顶级部门
            String qry3 = "select a.id,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                    " where a.zjcjsj < getdate() - " + wyxxm + " and a.status = 3 and b.departmentid > 0 and a.bmlx <> 2";
            bb.writeLog("非造价qry3 = " + qry3);
            RecordSet rs3 = new RecordSet();
            rs3.executeQuery(qry3);
            while (rs3.next()) {
                String customerId = Util.null2String(rs3.getString("id"));
                String topDeptId = Util.null2String(rs3.getString("topdeptid"));
                if (!seasCustomerMap.containsKey(customerId)) {
                    bb.writeLog("qry3 客户id：" + customerId + "不在map中");
                    // 获取部门公海的id
                    String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                    //判断有公海id和客户id
                    if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                        bb.writeLog("qry3 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                        // 添加到部门公海
                        CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                    }
                }
            }

            String qry31 = "select a.id,a.createdate,a.bcjckhsj,a.lastcontacteddate,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                    " where  a.status = 3 and b.departmentid > 0 and a.bmlx <> 2";

            bb.writeLog("非造价qry31 = " + qry31);
            seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
            RecordSet rs31 = new RecordSet();
            rs31.executeQuery(qry31);
            while (rs31.next()) {
                 bb.writeLog("客户id:" + rs31.getString("id"));
                bb.writeLog("客户lastcontacteddate:" + rs31.getString("lastcontacteddate"));
                bb.writeLog("客户bcjckhsj:" + rs31.getString("bcjckhsj"));
                bb.writeLog("客户createdate:" + rs31.getString("createdate"));
                String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(cjkhwlx)-2*Integer.parseInt(cjkhwlx));
                bb.writeLog("referenceDate:" + referenceDate);
                boolean flag = false;
                if (StringUtils.isBlank(rs31.getString("lastcontacteddate"))) {
                    //按照转基础客户时间判断*
                    String bcjckhsj = rs31.getString("bcjckhsj");
                    if(StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) < 0){
                        flag = true;
                    }else if(StringUtils.isBlank(bcjckhsj)){
                        String createdate = rs31.getString("createdate");
                        if(StringUtils.isNotBlank(createdate)&& createdate.compareTo(referenceDate) < 0){
                            flag = true;
                        }
                    }
                } else {
                    //按照未联系时间的条件
                    String lastcontacteddate = rs31.getString("lastcontacteddate");
                    if(lastcontacteddate.compareTo(referenceDate)<0){
                        flag = true;
                    }
                }
                if(flag){
                    String customerId = Util.null2String(rs31.getString("id"));
                    String topDeptId = Util.null2String(rs31.getString("topdeptid"));
                    String manager = Util.null2String(rs31.getString("manager"));
                    if (!seasCustomerMap.containsKey(customerId)) {
                        bb.writeLog("qry31 客户id：" + customerId + "不在map中");
                        // 获取部门公海的id
                        String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                        if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                            //插入建模数据
                            HashMap<String, Object> params = new HashMap<>();
                            params.put("customerIds", customerId);
                            customeridList.add(customerId);
                            WlxCustomerJoinseasInfoByLog wlxCustomerJoinseasInfoByLog = new WlxCustomerJoinseasInfoByLog(params, manager);
                            wlxCustomerJoinseasInfoByLog.executeData();
                            // 获取部门公海的id
                            bb.writeLog("qry31 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                            // 添加到部门公海
                            CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                        }
                    }
                }
            }
            // endregion 成交客户 end


            if(!customeridList.isEmpty()){
                RecordSet recordSet = new RecordSet();
                recordSet.executeUpdate("update CRM_CustomerInfo set lastcontacteddate = '' where id in ("+String.join(",", customeridList)+")");
            }

            // region 部门公海的客户只能存在N天（可配置），超过N天，则回归公司公海 start
            // 获取部门转公司的天数
            String deptSeasDays = lapseMap.get("xghzdgh");

            bb.writeLog("deptSeasDays:" + deptSeasDays);
            // 获取公司公海的id
//            String companySeasId = Util.null2String(CrmSeasUtil.getCompanySeasId());
            String companySeasId = lapseMap.get("zbzghid");
            bb.writeLog("公司companySeasId:" + companySeasId);
            // 获取部门公海的客户
            String qryDeptSeasCustomersSql = "select customerids = stuff((select ','+cast(customerid as varchar) from crm_seasCustomer " +
                    " where seasid not in (" + companySeasId + ") and enterdate < getdate() - " + deptSeasDays + " for xml path('')),1,1,'')";
            bb.writeLog("qryDeptSeasCustomersSql:" + qryDeptSeasCustomersSql);
            String customerids = QueryUtil.doQueryFieldValue(qryDeptSeasCustomersSql, "customerids");
            bb.writeLog("公司customerids:" + customerids);
            if(StringUtils.isNotBlank(customerids)){
                // 使用逗号分隔字符串
                String[] ids = customerids.split(",");
                // 将数组转换为 List，方便操作
                List<String> idList = new ArrayList<>(Arrays.asList(ids));
                // 遍历 idList，删除 customerMap 中存在的 ID
                Iterator<String> iterator = idList.iterator();
                while (iterator.hasNext()) {
                    String id = iterator.next();
                    if (customerMap.containsKey(id)) {
                        iterator.remove();  // 从 idList 中移除该 id
                    }
                }
                // 将剩余的 ids 拼接成逗号分隔的字符串
                customerids = String.join(",", idList);
            }
            // 所有客户转到公司公海
            if (!companySeasId.isEmpty() && !customerids.isEmpty()) {
                String sqlDept2Company = "update crm_seasCustomer set seasid = " + companySeasId + " where customerid in (" + customerids + ")";
                bb.writeLog("sqlDept2Company sql:" + sqlDept2Company);
                ExecuteUtil.executeSql(sqlDept2Company);
                // endregion 部门公海的客户只能存在N天（可配置），超过N天，则回归公司公海 end
            }

            bb.writeLog("CrmSeasConvertFeiZaoJiaJob end");
        }catch (Exception e){
            bb.writeLog("CrmSeasConvertFeiZaoJiaJob 异常", SDUtil.getExceptionDetail(e));
        }
    }

    private void resetParam() {
        lapseMap = new HashMap<>();
        customerMap = new HashMap<>();
    }
    private void init() {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        HashMap<String, String> config = new HashMap<>();
        recordSet.executeQuery("SELECT * FROM uf_gncspzb");
        while (recordSet.next()) {
            String id = recordSet.getString("id");
            if ("KHJRGH".equals(recordSet.getString("gnbm"))) {
                config.put("KHJRGH", id);
            }
        }
        if (!config.isEmpty()) {
            loadConfigValues(config.get("KHJRGH"), lapseMap);
        }

        recordSet.executeQuery("SELECT id,bmlx FROM crm_CustomerInfo");
        while (recordSet.next()) {
            String id = recordSet.getString("id");
            String bmlx = recordSet.getString("bmlx");
            if("2".equals(bmlx)){
                customerMap.put(id,bmlx);
            }

        }

    }
    private void loadConfigValues( String mainId, Map<String, String> targetMap) {
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        if (mainId != null) {
            recordSet.executeQuery("SELECT csmc, csz FROM uf_gncspzb_dt1 WHERE mainid = " + mainId);
            while (recordSet.next()) {
                targetMap.put(recordSet.getString("csmc"), recordSet.getString("csz"));
            }
        }
    }
}

