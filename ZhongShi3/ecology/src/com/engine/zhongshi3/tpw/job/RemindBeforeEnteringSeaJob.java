package com.engine.zhongshi3.tpw.job;

import com.alibaba.fastjson.JSONObject;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.email.util.EmailUtil;
import com.engine.sd2.message.util.MessageUtil;
import com.engine.zhongshi3.tpw.job.util.CrmSeasUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.toolbox.db.recordset.QueryUtil;

import java.util.*;

/**
 * @FileName RemindBeforeEnteringSeaJob
 * @Description 客户进入公海之前提醒
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/11/7
 */
@Getter
@Setter
public class RemindBeforeEnteringSeaJob extends BaseCronJob {

    private BaseBean bb = new BaseBean();
    //消息中心绑定标识 209
    private String messageSourceId;
    //回归天数
    private Map<String, String> lapseMap;
    //提醒天数
    private Map<String, String> reminderTimeMap;
    //人力资源map
    private Map<String, String> hrmMap;
    //客户Map
    private Map<String, String> customerMap;

    @Override
    public void execute() {
        bb.writeLog("RemindBeforeEnteringSeaJob start");

        Map<String, JSONObject> custMap = new HashMap<>();

        try {
            //重置参数
            resetParam();
            //初始化数据
            init();
            //获取提前提醒的时间（天）
            String advanceDays = Util.null2String(reminderTimeMap.get("tqts"));
            //已经再公海中存在的客户
            Map<String, String> seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
            retrieveCustomers(custMap, seasCustomerMap, advanceDays);
            sendReminders(custMap, advanceDays);
        } catch (Exception e) {
            bb.writeLog("Exception in RemindBeforeEnteringSeaJob: " + e.getMessage());
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("RemindBeforeEnteringSeaJob end");
    }

    private void retrieveCustomers(Map<String, JSONObject> custMap, Map<String, String> seasCustomerMap, String advanceDays) {

        //基础客户未转意向客户提醒
        retrieveCustomerByStatus(custMap, seasCustomerMap, advanceDays, "wzyxkh", "基础客户未转意向客户", "5", "bcjckhsj");
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        //todo 基础客户未联系提醒
        retrieveCustomerNotContactedBy5(custMap, seasCustomerMap, advanceDays, "jckhwlx", "基础客户未联系", "5", "lastcontacteddate");

        //意向客户未成交提醒
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        retrieveCustomerByStatus(custMap, seasCustomerMap, advanceDays, "wzcjkh", "意向客户未成交", "2", "jckhzwyjkhsj");

        //todo 意向客户未联系提醒
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        retrieveCustomerNotContactedBy5(custMap, seasCustomerMap, advanceDays, "yxkhwlx", "意向客户未联系", "2", "lastcontacteddate");

        //成交客户无新项目提醒
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        retrieveCustomerByStatus(custMap, seasCustomerMap, advanceDays, "wyxxm", "成交客户无新项目", "3", "zjcjsj");

        //todo 成交客户未联系提醒
        seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        retrieveCustomerNotContactedBy5(custMap, seasCustomerMap, advanceDays, "cjkhwlx", "成交客户未联系", "3", "lastcontacteddate");

    }

    private void retrieveCustomerNotContactedBy5(Map<String, JSONObject> custMap, Map<String, String> seasCustomerMap,
                                                 String advanceDays, String lapseKey, String conditionDesc, String status, String columnName) {
        // 主逻辑
        String lapseDays = lapseMap.getOrDefault(lapseKey, "0");
        String qry51 = "select a.id,a.name, a.createdate, a.bcjckhsj, a.lastcontacteddate, a.manager, dbo.getTopDepId(b.departmentid) as topdeptid " +
                "from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                "where a.status = "+status+" and b.departmentid > 0 ";
        bb.writeLog(conditionDesc+" 请求sql: " + qry51);
        RecordSet rs = new RecordSet();
        rs.executeQuery(qry51);
        while (rs.next()) {
            String customerId = Util.null2String(rs.getString("id"));
            String lastContactedDate = Util.null2String(rs.getString("lastcontacteddate"));
            String bcjckhsj = Util.null2String(rs.getString("bcjckhsj"));
            String createDate = Util.null2String(rs.getString("createdate"));
            String referenceDate = TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), Integer.parseInt(lapseDays) - 2 * Integer.parseInt(lapseDays) + Integer.parseInt(advanceDays));
            if (shouldMoveToSeas(lastContactedDate, bcjckhsj, createDate, referenceDate)) {
                String topDeptId = Util.null2String(rs.getString("topdeptid"));
                String manager = Util.null2String(rs.getString("manager"));
                String name = Util.null2String(rs.getString("name"));
                bb.writeLog("成交客户未联系判断");
                moveCustomerToSeas(custMap,seasCustomerMap,customerId, topDeptId, manager,name);
            }
        }
    }

    private void retrieveCustomerByStatus(Map<String, JSONObject> custMap, Map<String, String> seasCustomerMap,
                                          String advanceDays, String lapseKey, String conditionDesc, String status, String columnName) {
        try {
            RecordSet recordSet = new RecordSet();
            String lapseDays = lapseMap.getOrDefault(lapseKey, "0");


            String sql = String.format("SELECT a.id,a.name,a.manager,dbo.getTopDepId(b.departmentid) as topdeptid FROM crm_CustomerInfo a LEFT JOIN HrmResource b ON a.manager = b.id " +
                    "WHERE CONVERT(DATE, a." + columnName + ") = CONVERT(DATE, GETDATE() - %s) " +
                    "AND a.status = %s AND b.departmentid > 0", Integer.parseInt(lapseDays) - Integer.parseInt(advanceDays), status);
            // wzyxkh 20 advanceDays
            bb.writeLog(conditionDesc + " SQL: " + sql);
            recordSet.executeQuery(sql);
            HashMap<String, Object> custSingleMap = new HashMap<>();
            while (recordSet.next()) {
                String customerId = Util.null2String(recordSet.getString("id"));
                if (!seasCustomerMap.containsKey(customerId) && !custMap.containsKey(customerId)) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", Util.null2String(recordSet.getString("name")));
                    jsonObject.put("manager", Util.null2String(recordSet.getString("manager")));
                    custMap.put(customerId, jsonObject);
                    custSingleMap.put(customerId, jsonObject);
                }
            }
            bb.writeLog(conditionDesc+"将要进入公海的客户提醒信息" + JSONObject.toJSONString(custSingleMap));
        } catch (Exception e) {
            bb.writeLog("Exception in retrieveCustomerByStatus: " + e.getMessage());
        }
    }

    //发送消息中心和邮件提醒
    private void sendReminders(Map<String, JSONObject> custMap, String advanceDays) {
        try {
            if (!custMap.isEmpty()) {
                bb.writeLog("将要进入公海的客户提醒信息" + JSONObject.toJSONString(custMap));
                for (Map.Entry<String, JSONObject> entry : custMap.entrySet()) {
                    String customerId = entry.getKey();
                    JSONObject jo = entry.getValue();
                    String name = jo.getString("name");
                    String manager = jo.getString("manager");
                    String title = "客户即将加入公海提醒";
                    String content = String.format("%s客户%s天后将回归公海", name, advanceDays);
                    Set<String> userIdList = new HashSet<>();
                    userIdList.add(manager);
                    MessageUtil.sendMsg(Integer.parseInt(messageSourceId), 1, userIdList, title, content, "", "");
                    String sendTo = hrmMap.get(manager);
                    if (sendTo != null) {
                        EmailUtil.sendMail(sendTo, title, content);
                    }
                }
            }
        } catch (Exception e) {
            bb.writeLog("Exception in sendReminders: " + e.getMessage());
        }
    }

    private void init() {

        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        HashMap<String, String> config = new HashMap<>();
        recordSet.executeQuery("SELECT * FROM uf_gncspzb");
        while (recordSet.next()) {
            String id = recordSet.getString("id");
            if ("KHJRGH".equals(recordSet.getString("gnbm"))) {
                config.put("KHJRGH", id);
            } else if ("HGGHTX".equals(recordSet.getString("gnbm"))) {
                config.put("HGGHTX", id);
            }
        }

        if (!config.isEmpty()) {
            loadConfigValues(recordSet, config.get("KHJRGH"), lapseMap);
            loadConfigValues(recordSet, config.get("HGGHTX"), reminderTimeMap);
        }

        recordSet = DBUtil.getThreadLocalRecordSet();
        recordSet.executeQuery("SELECT id, email FROM hrmresource");
        while (recordSet.next()) {
            hrmMap.put(recordSet.getString("id"), recordSet.getString("email"));
        }

        recordSet = DBUtil.getThreadLocalRecordSet();
        recordSet.executeQuery("SELECT id,jckhzwyjkhsj  FROM crm_CustomerInfo");
        while (recordSet.next()) {
            customerMap.put(recordSet.getString("id"), recordSet.getString("jckhzwyjkhsj"));
        }

        bb.writeLog("lapseMap: " + lapseMap);
        bb.writeLog("reminderTimeMap: " + reminderTimeMap);
    }

    private void loadConfigValues(RecordSet recordSet, String mainId, Map<String, String> targetMap) {
        if (mainId != null) {
            recordSet.executeQuery("SELECT csmc, csz FROM uf_gncspzb_dt1 WHERE mainid = " + mainId);
            while (recordSet.next()) {
                targetMap.put(recordSet.getString("csmc"), recordSet.getString("csz"));
            }
        }
    }

    private void resetParam() {
        lapseMap = new HashMap<>();
        reminderTimeMap = new HashMap<>();
        hrmMap = new HashMap<>();
        customerMap = new HashMap<>();
    }

    // 判断是否移入公海的逻辑
    private boolean shouldMoveToSeas(String lastContactedDate, String bcjckhsj, String createDate, String referenceDate) {
        if (StringUtils.isBlank(lastContactedDate)) {
            if (StringUtils.isNotBlank(bcjckhsj) && bcjckhsj.compareTo(referenceDate) == 0) {
                return true;
            } else if (StringUtils.isBlank(bcjckhsj) && StringUtils.isNotBlank(createDate) && createDate.compareTo(referenceDate) == 0) {
                return true;
            }
        } else if (lastContactedDate.compareTo(referenceDate) == 0) {
            return true;
        }
        return false;
    }

    // 移入公海的操作逻辑
    private void moveCustomerToSeas(Map<String, JSONObject> custMap, Map<String, String> seasCustomerMap, String customerId, String topDeptId, String manager, String name) {
        if (!seasCustomerMap.containsKey(customerId)) {
//            String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
//            if (!customerId.isEmpty() && !deptSeasId.isEmpty() && !custMap.containsKey(customerId)) {
            if (!customerId.isEmpty() && !custMap.containsKey(customerId)) {
                HashMap<String, Object> custSingleMap = new HashMap<>();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", name);
                jsonObject.put("manager", manager);
                custMap.put(customerId, jsonObject);
                custSingleMap.put(customerId, jsonObject);
                bb.writeLog("将要进入公海的客户提醒信息custSingleMap" + JSONObject.toJSONString(custSingleMap));
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), -5));
    }

}
