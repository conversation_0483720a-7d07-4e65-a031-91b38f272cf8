package com.engine.zhongshi3.tpw.common.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongshi3.tpw.common.cmd.CustomerJoinseasInfoByLogCmd;
import com.engine.zhongshi3.tpw.common.service.CustomerJoinseasInfoByLogService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class CustomerJoinseasInfoByLogServiceImpl extends Service implements CustomerJoinseasInfoByLogService {
    @Override
    public Map<String, Object> customerJoinseasInfoByLog(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CustomerJoinseasInfoByLogCmd(params, user));
    }

}
