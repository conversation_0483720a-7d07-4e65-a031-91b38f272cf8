package com.engine.zhongshi3.tpw.common.cmd;


import com.engine.core.interceptor.CommandContext;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongshi3.tpw.common.bean.CustomerEnterSeasLog;
import com.engine.zhongshi3.tpw.common.bean.WLXCustomerEnterSeasLog;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WlxCustomerJoinseasInfoByLog{
    private BaseBean bb ;
    private Map<String, Object> params;
    private String uid;
    public WlxCustomerJoinseasInfoByLog(Map<String, Object> params, String uid) {
        this.uid = uid;
        this.params = params;
        this.bb = new BaseBean();
    }
    public Map<String, Object> executeData() {
        bb.writeLog("WlxCustomerJoinseasInfoByLogCmd---START");
        bb.writeLog("params: " + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            HashMap<String, String> customerInfoMap = new HashMap<>();
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            rs.executeQuery("select id,name from crm_CustomerInfo");
            while (rs.next()) {
                customerInfoMap.put(rs.getString("id"), rs.getString("name"));
            }
            if (!params.isEmpty()) {
                // 当前日期字符串
                String currentDateString = TimeUtil.getCurrentDateString();
                // 客户IDs
                String customerIds = Util.null2String(params.get("customerIds"));
                if (StringUtils.isNotBlank(customerIds)) {
                    // 分割并遍历 customerIds
                    String[] customerIdArray = customerIds.split(",");
                    for (String customerId : customerIdArray) {
                        if (StringUtils.isNotBlank(customerId)) {
                            // 执行每个客户ID的加入公海逻辑
                            bb.writeLog("Processing customerId: " + customerId);
                            String customerName = customerInfoMap.get(customerId);
                            // 插入操作
                            int moduleId = ModuleDataUtil.getModuleIdByName(WLXCustomerEnterSeasLog.TABLE_NAME);
                            List<WLXCustomerEnterSeasLog> newData = new ArrayList<>();
                            WLXCustomerEnterSeasLog wlxCustomerEnterSeasLog = new WLXCustomerEnterSeasLog();
                            wlxCustomerEnterSeasLog.setCustomerId(customerId);
                            wlxCustomerEnterSeasLog.setCustomerName(customerName);
                            wlxCustomerEnterSeasLog.setJoinDate(currentDateString);
                            wlxCustomerEnterSeasLog.setOperatorId(uid);
                            newData.add(wlxCustomerEnterSeasLog);
                            ModuleResult mr = ModuleDataUtil.insertObjList(newData, WLXCustomerEnterSeasLog.TABLE_NAME, moduleId, Integer.parseInt(uid));
                            if (mr.isSuccess()) {
                                bb.writeLog("Customer " + customerId + " successfully added to public sea.");
                            } else {
                                bb.writeLog("Failed to add customer " + customerId + " to public sea.");
                            }
                        }
                    }
                } else {
                    errorMsg = "Customer IDs are empty.";
                }
            } else {
                errorMsg = "Parameters are empty.";
            }
        } catch (Exception e) {
            errorMsg = "An error occurred while executing the command.";
            bb.writeLog("Exception in WlxCustomerJoinseasInfoByLogCmd: " + e.getMessage());
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result: " + result);
        bb.writeLog("WlxCustomerJoinseasInfoByLogCmd---END");
        return result;
    }
}
