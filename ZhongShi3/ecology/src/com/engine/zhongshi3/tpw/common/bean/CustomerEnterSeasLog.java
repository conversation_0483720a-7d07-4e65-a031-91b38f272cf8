package com.engine.zhongshi3.tpw.common.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @FileName ActualWorking
 * @Description 工时明细台账-按日
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/27
 */
@Data
@Accessors(chain = true)//可以使用链式set
public class CustomerEnterSeasLog {
    /**
     * 数据id
     */
    private Integer id;
    /**
     *	操作人id
     */
    private String operatorId;
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 回归时间
     */
    private String joinDate;
    /**
     * 客户名称
     */
    private String customerName;

    public static String TABLE_NAME = "uf_sdhgghrz";

}
