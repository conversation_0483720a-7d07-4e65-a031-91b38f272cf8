package com.engine.zhongshi3.tpw.common.cmd;


import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongshi3.tpw.common.bean.CustomerEnterSeasLog;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomerJoinseasInfoByLogCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public CustomerJoinseasInfoByLogCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        this.bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog("CustomerJoinseasInfoByLogCmd---START");
        bb.writeLog("params: " + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            HashMap<String, String> customerInfoMap = new HashMap<>();
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            rs.executeQuery("select id,name from crm_CustomerInfo");
            while (rs.next()) {
                customerInfoMap.put(rs.getString("id"), rs.getString("name"));
            }
            if (!params.isEmpty()) {
                // 操作者ID
                int uid = user.getUID();
                // 当前日期字符串
                String currentDateString = TimeUtil.getCurrentDateString();
                // 客户IDs
                String customerIds = Util.null2String(params.get("customerIds"));
                if (StringUtils.isNotBlank(customerIds)) {
                    // 分割并遍历 customerIds
                    String[] customerIdArray = customerIds.split(",");
                    for (String customerId : customerIdArray) {
                        if (StringUtils.isNotBlank(customerId)) {
                            // 执行每个客户ID的加入公海逻辑
                            bb.writeLog("Processing customerId: " + customerId);
                            String customerName = customerInfoMap.get(customerId);
                            // 插入操作
                            int moduleId = ModuleDataUtil.getModuleIdByName(CustomerEnterSeasLog.TABLE_NAME);
                            List<CustomerEnterSeasLog> newData = new ArrayList<>();
                            CustomerEnterSeasLog customerEnterSeasLog = new CustomerEnterSeasLog();
                            customerEnterSeasLog.setCustomerId(customerId);
                            customerEnterSeasLog.setCustomerName(customerName);
                            customerEnterSeasLog.setJoinDate(currentDateString);
                            customerEnterSeasLog.setOperatorId(String.valueOf(uid));
                            newData.add(customerEnterSeasLog);
                            ModuleResult mr = ModuleDataUtil.insertObjList(newData, CustomerEnterSeasLog.TABLE_NAME, moduleId, uid);
                            if (mr.isSuccess()) {
                                bb.writeLog("Customer " + customerId + " successfully added to public sea.");
                            } else {
                                bb.writeLog("Failed to add customer " + customerId + " to public sea.");
                            }
                        }
                    }
                } else {
                    errorMsg = "Customer IDs are empty.";
                }

                if (StringUtils.isNotBlank(customerIds)) {
                    String sql = "update CRM_CustomerInfo set lastcontacteddate='' where id in ("+customerIds+")";
                    bb.writeLog("执行更新清空最后联系人时间的sql语句"+sql);
                    boolean b = rs.executeUpdate(sql);
                    bb.writeLog("执行结果"+b);
                }
            } else {
                errorMsg = "Parameters are empty.";
            }
        } catch (Exception e) {
            errorMsg = "An error occurred while executing the command.";
            bb.writeLog("Exception in CustomerJoinseasInfoByLogCmd: " + e.getMessage());
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result: " + result);
        bb.writeLog("CustomerJoinseasInfoByLogCmd---END");
        return result;
    }
}
