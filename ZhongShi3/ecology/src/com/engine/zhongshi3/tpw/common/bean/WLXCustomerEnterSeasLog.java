package com.engine.zhongshi3.tpw.common.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
    * @FileName WLXCustomerEnterSeasLog
    * @Description 
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/12/2
    */
@Data
@Accessors(chain = true)
public class WLXCustomerEnterSeasLog {
    /**
     * 数据id
     */
    private Integer id;
    /**
     *	操作人id
     */
    private String operatorId;
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 回归时间
     */
    private String joinDate;
    /**
     * 客户名称
     */
    private String customerName;

    public static String TABLE_NAME = "uf_wlxjljrghrz";

}
