package com.engine.zhongshi3.tpw.workflow.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.crm.service.impl.ContactLogServiceImpl;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.weaver.general.Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
    * @FileName AddContactRecordAction
    * @Description 调用系统标准的联系记录的新增接口
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/11/27
    */
@Getter
@Setter
public class AddContactRecordAction implements Action {
    private BaseBean bb = new BaseBean();
    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = "";//出错信息
        try {
            bb.writeLog("AddContactRecordAction----START");
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            Map<String, String> mainData = actionInfo.getMainData();
            //获取流程数据
            String customerId = Util.null2String(mainData.get("kh"));
            String contacterId = Util.null2String(mainData.get("lxr"));
            String ContactInfo = Util.null2String(mainData.get("lxwb"));
            String contactWayId = Util.null2String(mainData.get("lxfs"));
            String relatedimgs = Util.null2String(mainData.get("sctp"));
            String sqr = Util.null2String(mainData.get("sqr"));
            Map<String, Object> params = new HashMap<>();
            params.put("customerId", customerId);
            params.put("sellChanceId", "");
            params.put("contacterId", contacterId);
            params.put("ContactInfo", ContactInfo);
            params.put("contactWayId", contactWayId);
            params.put("relateddoc", "");
            params.put("relatedwf", "");
            params.put("relatedprj", "");
            params.put("relatedfile", "");
            ArrayList<String> fileids = new ArrayList<>();
            if(StringUtils.isNotBlank(relatedimgs)){
                List<DocFileInfo> docFileInfoByDocId = DocUtil.getDocFileInfoByDocId(relatedimgs);
                for (int i = 0; i < docFileInfoByDocId.size(); i++) {
                    DocFileInfo docFileInfo = docFileInfoByDocId.get(i);
                    String fileid = docFileInfo.getFileid();
                    fileids.add(fileid);
                }
            }
            if(!fileids.isEmpty()){
                relatedimgs = String.join(",", fileids);
            }
            params.put("relatedimgs", relatedimgs);
            User user = new User(Integer.parseInt(sqr));
            bb.writeLog("AddContactRecordAction params======>" + JSONObject.toJSONString(params));
            ContactLogServiceImpl service = ServiceUtil.getService(ContactLogServiceImpl.class, user);
            Map<String, Object> stringObjectMap = service.contactLogCreate(params);
            bb.writeLog("AddContactRecordAction 接口响应结果result======>" + JSONObject.toJSONString(stringObjectMap));
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("AddContactRecordAction Exception======>" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("AddContactRecordAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

}
