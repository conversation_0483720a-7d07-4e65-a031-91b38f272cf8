package com.engine.zhongshi3.tpw.workflow.action;


import com.engine.parent.common.util.SDUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import com.weaver.general.Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
    * @FileName FieldValidateAction
    * @Description 客户需要在流程提交后，对单行文本字段做校验优化
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/11/11
    */
@Getter
@Setter
public class FieldValidateAction implements Action {

    private BaseBean bb = new BaseBean();

    /*
     * 获取当前流程表中，处于审批中流程的客户名称字段
     * */
    private List<String> wfList;
    /*
     * 获取客户台账数据（CRM_CustomerInfo）中的客户名称（name）字段
     * */
    private List<String> crmCustomerInfoNameList;

    /*
     * 获取当前流程表中，处于审批中流程的客户名称字段
     * */
    private RecordSet recordSet = DBUtil.getThreadLocalRecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {
        String errorMsg = "";//出错信息
        try {
            bb.writeLog("FieldValidateAction----START");
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            resetParam();
            init(actionInfo);
            // 1.主表 请求的“客户名称”（khmc）字段值，并去除文本中的所有特殊符号
            String wfkhmc = "";
            Map<String, String> mainData = actionInfo.getMainData();
            if (mainData != null) {
                wfkhmc = Util.null2String(mainData.get("khmc"));
            }
            // 将获取到的客户名称与两个数据源中的数据进行文本比对
            //（1） 获取当前流程表中，处于审批中流程的客户名称字段，
            //（2） 获取客户台账数据（CRM_CustomerInfo）中的客户名称（name）字段
            if (StringUtils.isNotBlank(wfkhmc)) {
                wfkhmc = removeSymbols(wfkhmc);
                if (wfList.contains(wfkhmc) ) {
                    errorMsg = "该客户名称已存在其他流程中，无法新建客户";
                }
                if (crmCustomerInfoNameList.contains(wfkhmc)) {
                    errorMsg = "该客户名称已存在客户台账中，无法新建客户";
                }
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("FieldValidateAction Exception======>" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("FieldValidateAction----END");
        return ActionUtil.handleResult(errorMsg, requestInfo);
    }

    private void resetParam() {
        wfList = new ArrayList();
        crmCustomerInfoNameList = new ArrayList();
    }

    private void init(ActionInfo actionInfo) {
        String formtableName = actionInfo.getFormtableName();
        String workflowId = actionInfo.getWorkflowId();
        String requestId = actionInfo.getRequestId();
        //获取当前流程表中，处于审批中流程的客户名称字段，并去除所有特殊符号
        String sql1 = "SELECT a.khmc FROM " + formtableName + " a left join  workflow_requestbase b on a.requestId = b.requestId   WHERE b.currentnodetype != 3 and b.workflowid = '" + workflowId + "' and b.requestId <> '"+requestId+"'";
        bb.writeLog("获取当前流程表中，处于审批中流程的客户名称字段sql:"+sql1);
        recordSet.executeQuery(sql1);
        while (recordSet.next()) {
            wfList.add(removeSymbols(recordSet.getString("khmc")));
        }
        String sql2 = "SELECT name FROM CRM_CustomerInfo";
        recordSet.executeQuery(sql2);
        while (recordSet.next()) {
            crmCustomerInfoNameList.add(removeSymbols(recordSet.getString("name")));
        }
    }

    public String removeSymbols(String text) {
        // 使用正则表达式替换所有中英文符号和空格
        return text.replaceAll("[\\p{P}\\p{S}\\s]", "");
    }
}
