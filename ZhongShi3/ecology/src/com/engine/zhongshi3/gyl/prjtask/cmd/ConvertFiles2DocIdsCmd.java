package com.engine.zhongshi3.gyl.prjtask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ConvertFiles2DocIdsCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private String error;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public ConvertFiles2DocIdsCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.error = "";
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        JSONObject jsonObject;
        String docids = "";
        List<String> docidsList = new ArrayList<>();
        try {
            String datas = Util.null2String(params.get("datas"));//文件数据
            int catid = Util.getIntValue(Util.null2String(params.get("catid")));//文档目录id
            if (!datas.isEmpty()) {
                JSONArray jsonArray = JSONArray.parseArray(datas);
                for (int i = 0; i < jsonArray.size(); i++) {
                    jsonObject = jsonArray.getJSONObject(i);
                    int fileid = Util.getIntValue(Util.null2String(jsonObject.get("fileid")));
                    String fileName = Util.null2String(jsonObject.get("filename"));
                    int docid = DocUtil.createDocWithFileid(fileid, user, fileName, catid);
                    if (docid > 0) {
                        docidsList.add(String.valueOf(docid));
                    }
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        if (!docidsList.isEmpty()) {
            docids = String.join(",", docidsList);
        }
        result.put("docids", docids);
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info(this.getClass().getName() + "---END");
        return result;
    }


}
