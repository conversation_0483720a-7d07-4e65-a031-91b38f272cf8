package com.engine.zhongshi3.gyl.prjtask.service.impl;

import com.engine.core.impl.Service;
import com.engine.zhongshi3.gyl.prjtask.cmd.ConvertFiles2DocIdsCmd;
import com.engine.zhongshi3.gyl.prjtask.cmd.SaveCustomFieldShowCmd;
import com.engine.zhongshi3.gyl.prjtask.service.PrjTaskService;
import weaver.hrm.User;

import java.util.Map;


public class PrjTaskServiceImpl extends Service implements PrjTaskService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> saveCustomShowField(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SaveCustomFieldShowCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> convertFiles2Doc(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ConvertFiles2DocIdsCmd(params, user));
    }


}
