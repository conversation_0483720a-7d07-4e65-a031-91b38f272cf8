package com.engine.zhongshi3.gyl.prjtask.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongshi3.gyl.prjtask.bean.CustomShowField;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class SaveCustomFieldShowCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private String error;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public SaveCustomFieldShowCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.error = "";
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info("---START---");
        log.info("params:" + params);
        try {
            //获取已存在的数据
            int existId = getExistID();
            if (existId > 0) {
                result.put("billid", existId);
                //1.更新主表最后更新人、更新日期
                updateMain(existId);
                //2.获取已有的明细数据
                List<CustomShowField.Detail1> existList = getExistDetail(existId);
                //3.先删除明细行数据
                delDetail(existId);
                //4.重新插入明细1
                coverDetail1(existId, existList);
            } else {
                //1.新增主表
                int newBillid = insertMain();
                if (newBillid > 0) {
                    result.put("billid", newBillid);
                    //2.插入明细
                    insertDetail1(newBillid);
                }
            }

        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("---END---");
        return result;
    }

    /**
     * 获取已有的个性配置数据
     *
     * @return
     */
    private int getExistID() {
        int billid = -1;
        String prjid = Util.null2String(params.get("prjid"));
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String sql = "select id from " + CustomShowField.TABLE_NAME + " where stage_type= 0 and xm= ? ";
        if (rs.executeQuery(sql, prjid)) {
            if (rs.next()) {
                billid = rs.getInt("id");
            }
        }
        log.info("getExistID billid:" + billid);
        return billid;
    }

    private void delDetail(int billid) {
        String sql = "delete from " + CustomShowField.TABLE_NAME_DT1 + " where mainid = " + billid;
        log.info("delDetail sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (!rs.executeUpdate(sql)) {
            error = "删除明细出错:" + rs.getExceptionMsg();
        }
    }

    private int insertMain() {
        String prjid = Util.null2String(params.get("prjid"));
        CustomShowField customShowField = new CustomShowField();
        customShowField.setStage_type(0);
        customShowField.setXm(Util.getIntValue(prjid));
        int modid = ModuleDataUtil.getModuleIdByName(CustomShowField.TABLE_NAME);
        ModuleResult mr = ModuleDataUtil.insertObj(customShowField, CustomShowField.TABLE_NAME, modid, user.getUID());
        log.info("insertMain result:" + mr);
        if (mr.isSuccess()) {
            return mr.getBillid();
        } else {
            error = "插入主表出错：" + mr.getErroMsg();
        }
        return -1;
    }

    /**
     * 覆盖明细1
     *
     * @param mainid
     */
    private void coverDetail1(int mainid, List<CustomShowField.Detail1> existList) {
        JSONObject jo;
        List<CustomShowField.Detail1> list = new ArrayList<>();
        CustomShowField.Detail1 detail1;
        String datas = Util.null2String(params.get("datas"));
        JSONArray datasJson = JSONArray.parseArray(datas);
        if (datasJson != null && !datasJson.isEmpty()) {
            for (int i = 0; i < datasJson.size(); i++) {
                detail1 = new CustomShowField.Detail1();
                jo = datasJson.getJSONObject(i);
                detail1.setField_name(Util.null2String(jo.get("fieldnames")));//数据库字段名
                detail1.setField_orginshow(Util.null2String(jo.get("labelnames")));//原始显示名
                detail1.setField_order(BigDecimal.valueOf(i));//排序
                //根据字段数据库名，匹配原先的配置数据
                CustomShowField.Detail1 oldDetail1 = getOriginDetail(detail1, existList);
                //匹配到字段名，则设置字段显示名、宽度为原始的值
                if (oldDetail1 != null) {
                    detail1.setField_show(oldDetail1.getField_show());//设置原先的显示名
                    detail1.setWidth(oldDetail1.getWidth());//宽度
                } else {
                    //匹配不到，则该条数据为新的
                    detail1.setField_show(Util.null2String(jo.get("labelnames")));//默认为原始显示名
                    detail1.setWidth(BigDecimal.valueOf(10)); //默认宽度10
                }
                list.add(detail1);
            }
            ModuleResult mr = ModuleDataUtil.insertObjDetail(list, CustomShowField.TABLE_NAME_DT1, mainid);
            log.info("insertDetail1 result:" + mr);
        }
    }

    private void updateMain(int existId) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String now = TimeUtil.getCurrentTimeString();
        String sql = "update " + CustomShowField.TABLE_NAME + " set modedatamodifier= ?,modedatamodifydatetime = ? where id = " + existId;
        if (!rs.executeUpdate(sql, user.getUID(), now)) {
            error = "更新主表人和时间出错：" + rs.getExceptionMsg();
        }

    }

    private CustomShowField.Detail1 getOriginDetail(CustomShowField.Detail1 coverDetail, List<CustomShowField.Detail1> existList) {
        CustomShowField.Detail1 result = null;
        String fieldName = Util.null2String(coverDetail.getField_name());
        for (CustomShowField.Detail1 detail1 : existList) {
            if (fieldName.equals(detail1.getField_name())) {
                result = detail1;
                break;
            }
        }
        return result;
    }

    /**
     * 插入明细
     *
     * @param mainid
     */
    private void insertDetail1(int mainid) {
        JSONObject jo;
        List<CustomShowField.Detail1> list = new ArrayList<>();
        CustomShowField.Detail1 detail1;
        String datas = Util.null2String(params.get("datas"));
        JSONArray datasJson = JSONArray.parseArray(datas);
        if (datasJson != null && !datasJson.isEmpty()) {
            for (int i = 0; i < datasJson.size(); i++) {
                detail1 = new CustomShowField.Detail1();
                jo = datasJson.getJSONObject(i);
                detail1.setField_name(Util.null2String(jo.get("fieldnames"))); //数据库字段名
                detail1.setField_orginshow(Util.null2String(jo.get("labelnames")));//原始显示名
                detail1.setField_show(Util.null2String(jo.get("labelnames")));//原始显示名作为新显示名
                detail1.setField_order(BigDecimal.valueOf(i));
                detail1.setWidth(BigDecimal.valueOf(10)); //默认宽度10
                list.add(detail1);
            }
            ModuleResult mr = ModuleDataUtil.insertObjDetail(list, CustomShowField.TABLE_NAME_DT1, mainid);
            log.info("insertDetail1 result:" + mr);
        }
    }

    private List<CustomShowField.Detail1> getExistDetail(int existId) {
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String sql = "select * from " + CustomShowField.TABLE_NAME_DT1 + " where mainid = ?";
        if (rs.executeQuery(sql, existId)) {
            return QueryUtil.getObjList(rs, CustomShowField.Detail1.class);
        }
        return null;
    }

}
