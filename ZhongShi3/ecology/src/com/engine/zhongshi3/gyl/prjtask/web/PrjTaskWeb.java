package com.engine.zhongshi3.gyl.prjtask.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.zhongshi3.gyl.prjtask.service.PrjTaskService;
import com.engine.zhongshi3.gyl.prjtask.service.impl.PrjTaskServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;


public class PrjTaskWeb {

    private PrjTaskService getService(User user) {
        return ServiceUtil.getService(PrjTaskServiceImpl.class, user);
    }

    /**
     * 保存明细7的显示列定制
     * 【开评标阶段】- 【供应商信息】
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/saveCustomShowField")
    @Produces(MediaType.TEXT_PLAIN)
    public String saveCustomShowField(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).saveCustomShowField(params, user)
        );
    }

    /**
     * 保存明细7的显示列定制
     * 【开评标阶段】- 【供应商信息】
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/convertFiles2Doc")
    @Produces(MediaType.TEXT_PLAIN)
    public String convertFiles2Doc(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).convertFiles2Doc(params, user)
        );
    }

}
