package com.engine.zhongshi3.gyl.prjtask.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @FileName CustomShowField.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/12/3
 */
@Data
public class CustomShowField {
    private Integer id;
    /**
     * 定制阶段类型
     * 0 开评标供应商
     */
    private Integer stage_type;
    /**
     * 项目
     */
    private Integer xm;

    @Data
    public static class Detail1 {
        private Integer id;
        private Integer mainid;
        /**
         * 字段数据库名
         */
        private String field_name;
        /**
         * 字段原显示名
         */
        private String field_orginshow;
        /**
         * 字段显示名
         */
        private String field_show;
        /**
         * 字段顺序
         */
        private BigDecimal field_order;
        /**
         * 占宽百分比
         */
        private BigDecimal width;

    }

    public static final String TABLE_NAME = "uf_customshow_stage";

    public static final String TABLE_NAME_DT1 = "uf_customshow_stage_dt1";

}
