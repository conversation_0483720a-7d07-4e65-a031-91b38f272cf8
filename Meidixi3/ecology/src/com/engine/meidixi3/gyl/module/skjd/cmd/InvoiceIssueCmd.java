package com.engine.meidixi3.gyl.module.skjd.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.meidixi3.gyl.module.skjd.bean.InvoiceIssueLog;
import com.engine.meidixi3.gyl.module.skjd.executor.InvoiceIssueExecutor;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import weaver.conn.RecordSet;
import weaver.general.ThreadPoolUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;

/**
 * @FileName InvoiceIssueCmd.java
 * @Description 开票接口(供第三方调用)
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/3/20
 */
public class InvoiceIssueCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 错误信息
     */
    private String error;
    /**
     * body参数
     */
    private JSONObject bodyParam;
    /**
     * 更新日志bean
     */
    private final InvoiceIssueLog mainLog;
    /**
     * 更新明细日志bean list
     */
    private final List<InvoiceIssueLog.Detail1> detailLogList;
    /**
     * 接口失败的数据
     */
    private final List<JSONObject> faildata;
    /**
     * 接口成功的数据
     */
    private final List<String> successdata;
    /**
     * 执行类
     */
    private InvoiceIssueExecutor invoiceIssueExecutor;


    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public InvoiceIssueCmd(Map<String, Object> params, User user) {
        this.params = params; //接口参数
        this.user = user; //操作人
        error = ""; //初始化错误信息
        bodyParam = null;
        mainLog = new InvoiceIssueLog();
        detailLogList = new CopyOnWriteArrayList<>();
        faildata = new CopyOnWriteArrayList<>(); //线程安全
        successdata = new CopyOnWriteArrayList<>(); //线程安全

    }

    /**
     * 初始化
     */
    private void init() {
        mainLog.setOperate_begin_time(SDTimeUtil.getCurrentTimeMillliString());
        mainLog.setOperate_from("收款阶段开票接口触发");
        //初始化执行类
        invoiceIssueExecutor = new InvoiceIssueExecutor(true, user);
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        int totalCount = 0;  //总数量
        int successCount = 0; //成功数量
        int stageId, summaryStageId;
        List<InvoiceIssueLog.Detail1> successList = new ArrayList<>();
        String eachError;
        JSONObject failedObject;
        try {
            //初始化
            init();
            appendRemark("输入参数params:" + params);
            //生成一个批次号,用UUID
            String serialNum = UUID.randomUUID().toString().replace("-", "");
            result.put("serialnum", serialNum);
            mainLog.setSerial_num(serialNum);

            //step 1 : 校验参数
            checkParams();
            if (error.isEmpty()) {
                //step 2: 将data数据解析插入到台账日志明细中
                fillDetailLog();
                //step 3: 先插入到日志台账中
                error = invoiceIssueExecutor.insertLogData(mainLog, detailLogList);
                if (error.isEmpty()) {
                    //step 4: 设置汇总的明细数据
                    invoiceIssueExecutor.setSummaryDetailData();
                    //step 4: 执行更新收款阶段数据
                    invoiceIssueExecutor.updateStage();
                    //获取当前更新后最新的汇总明细
                    //获取当前最新的明细
                    List<InvoiceIssueLog.Detail1> updatedLogList = invoiceIssueExecutor.getSummaryLogList();
                    //step 5: 将成功的数据，插入发票流水
                    if (!detailLogList.isEmpty()) {
                        totalCount = detailLogList.size();
                        for (InvoiceIssueLog.Detail1 detail : detailLogList) {
                            stageId = detail.getStage_id();
                            for (InvoiceIssueLog.Detail1 updatedDetail : updatedLogList) {
                                summaryStageId = updatedDetail.getStage_id();
                                //根据阶段id判断，更新日志
                                if (stageId == summaryStageId) {
                                    eachError = updatedDetail.getErrorMsg();
                                    if (!eachError.isEmpty()) {
                                        //错误的数据，添加到failData
                                        failedObject = new JSONObject();
                                        failedObject.put("id", stageId);
                                        failedObject.put("msg", eachError);
                                        faildata.add(failedObject);
                                    } else {
                                        //成功的添加到successdata
                                        successdata.add(String.valueOf(stageId));
                                        successCount++;
                                        //成功的需要插入发票流水
                                        successList.add(detail);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    error = "插入日志台账出错:" + error;
                }
            } else {
                //如果参数有误，也插入建模
                String insertError = invoiceIssueExecutor.insertLogData(mainLog, null);
                if (!insertError.isEmpty()) {
                    error += ";插入日志台账出错:" + insertError;
                }
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        //失败的数量
        int failCount = totalCount - successCount;
        //设置响应报文
        if (error.isEmpty() && failCount == 0) {
            result.put("code", "SUCCESS");
            result.put("msg", "处理成功！");
        } else {
            result.put("code", "FAIL");
            result.put("msg", "处理有误！" + error);
        }
        result.put("faildata", faildata);
        result.put("successdata", successdata);

        appendRemark("接口响应:" + result);
        //方式1：使用泛微的线程池工具类，这个线程池会先从缓存读取，没有则会创建。这个线程池不可销毁
        ExecutorService executorService = ThreadPoolUtil.getThreadPool("InvoiceIssueCmd", "5");
        //异步操作
        executorService.execute(() -> doOtherThings(
                invoiceIssueExecutor,
                JSONObject.toJSONString(params),
                JSONObject.toJSONString(result),
                successList));
        log.info("接口result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 保存日志
     *
     * @param requestParam
     * @param responseResult
     */
    private void doOtherThings(InvoiceIssueExecutor invoiceIssueExecutor,
                               String requestParam,
                               String responseResult,
                               List<InvoiceIssueLog.Detail1> successList) {
        Map<Integer, Map<String, Object>> stageMap = new HashMap<>();
        Map<String, Object> defaultMap = new HashMap<>();
        int stageId;
        try {
            //成功的插入开票流水

            if (successList != null && !successList.isEmpty()) {
                List<String> idsList = new ArrayList<>();
                for (InvoiceIssueLog.Detail1 detail : successList) {
                    appendRemark("成功开票的阶段id:" + detail.getStage_id());
                    idsList.add(String.valueOf(detail.getStage_id()));
                }
                String ids = String.join(",", idsList);
                //获取成功的阶段列表数据
                List<Map<String, Object>> stageDataList = getStageData(ids);
                if (stageDataList != null && !stageDataList.isEmpty()) {
                    //做map缓存
                    for (Map<String, Object> eachMap : stageDataList) {
                        stageId = Util.getIntValue(Util.null2String(eachMap.get("id")));
                        stageMap.put(stageId, eachMap);
                    }
                }
                //成功的开票数据，插入流水
                for (InvoiceIssueLog.Detail1 issueData : successList) {
                    //根绝阶段id，获取当前阶段信息
                    Map<String, Object> currentStageData = stageMap.getOrDefault(issueData.getStage_id(), defaultMap);
                    //判断人民币还是美元
                    BigDecimal issue_jshj = SDUtil.getBigDecimalValue(issueData.getJshj());
                    //人民币-更新收款阶段数据
                    if (issue_jshj != null && issue_jshj.compareTo(BigDecimal.ZERO) != 0) {
                        invoiceIssueExecutor.appendRemark("插入RMB开票流水，阶段id:" + issueData.getStage_id());
                        invoiceIssueExecutor.insertFlowDataRMB(issueData, currentStageData, 1);
                    } else {
                        invoiceIssueExecutor.appendRemark("插入美元开票流水，阶段id:" + issueData.getStage_id());
                        invoiceIssueExecutor.insertFlowDataDollar(issueData, currentStageData, 1);
                    }
                }
            }
            //更新开票日志
            invoiceIssueExecutor.updateLog(requestParam, responseResult);
        } catch (Exception e) {
            log.error("保存日志操作出错", e);
        }
    }

    /**
     * 将接口数据填充到日志对象中
     */
    private void fillDetailLog() {
        JSONObject eachData;
        String eachError;
        String stageName;
        InvoiceIssueLog.Detail1 detailLog;
        JSONArray dataArray = bodyParam.getJSONArray("data");
        //获取阶段id和name的map
        Map<String, String> stageMap = getStageNameMap();
        appendRemark("stageMap:" + stageMap);
        for (int i = 0; i < dataArray.size(); i++) {
            eachData = dataArray.getJSONObject(i);
            stageName = stageMap.getOrDefault(Util.null2String(eachData.get("id")), "");
            detailLog = new InvoiceIssueLog.Detail1();

            detailLog.setStage_id(Util.getIntValue(Util.null2String(eachData.get("id")))); //收款阶段数据id
            detailLog.setStage_name(stageName); //说款阶段名称
            detailLog.setKqrq(Util.null2String(eachData.get("kqrq"))); //国内开票日期格式：yyyy-MM-dd
            detailLog.setGnfpzl(Util.getIntValue(Util.null2String(eachData.get("gnfpzl")))); //国内发票种类0: 电子发票（增值税专用发票）1: 电子发票（普通发票）2: 增值税专用发票3: 增值税普通发票
            detailLog.setJe(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("je")))); //人民币开票金额(2位小数)
            detailLog.setSe(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("se")))); //人民币税额(2位小数)
            detailLog.setJshj(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("jshj")))); //人民币价税合计(2位小数)
            detailLog.setGnfph(Util.null2String(eachData.get("gnfph"))); //国内发票号
            detailLog.setMyykp(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("myykp")))); //美元开票金额(2位小数)
            detailLog.setWbkprq(Util.null2String(eachData.get("wbkprq"))); //外币开票日期
            detailLog.setWbfpzl(Util.getIntValue(Util.null2String(eachData.get("wbfpzl")))); //外币发票种类0 : 电子发票（增值税专用发票）1 : 电子发票（普通发票）2 : 纸质发票（增值税专用发票）3 : 纸质发票（普通发票）
            detailLog.setZhkpjermb(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("zhkpjermb")))); //折合开票金额RMB(2位小数)
            detailLog.setSermb(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("sermb"))));//税额RMB(2位小数)
            detailLog.setZhjsrmb(SDUtil.getBigDecimalValue(Util.null2String(eachData.get("zhjsrmb"))));//折合价税RMB(2位小数)
            detailLog.setWbfph(Util.null2String(eachData.get("wbfph")));//外币发票号
            detailLog.setSfjxkp(Util.getIntValue(Util.null2String(eachData.get("sfjxkp")), -1)); //是否继续开票（用于分批次开票）
            //校验参数字段，如果有错误，则当前数据失败
            eachError = checkField(eachData);
            detailLog.setSuccess(eachError.isEmpty() ? 0 : 1); //校验的成功和失败
            detailLog.setErrorMsg(eachError); //校验失败信息
            detailLogList.add(detailLog);
        }
    }

    /**
     * 获取阶段名称的map
     *
     * @return
     */
    private Map<String, String> getStageNameMap() {
        Map<String, String> stageMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        JSONArray dataArray = bodyParam.getJSONArray("data");
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject eachData = dataArray.getJSONObject(i);
            list.add(Util.null2String(eachData.get("id")));
        }
        if (!list.isEmpty()) {
            String ids = String.join(",", list);
            String sql = "select id,skjd from uf_zhuxmskjd where id in (" + ids + ") ";
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            log.info("getStageNameMap sql :" + sql);
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    stageMap.put(Util.null2String(rs.getString("id")), Util.null2String(rs.getString("skjd")));
                }
            }
        }
        return stageMap;

    }


    /**
     * 获取收款阶段数据
     *
     * @param stageIds
     * @return
     */
    private List<Map<String, Object>> getStageData(String stageIds) {
        String sql = "select " +
                " a.*," +
                "  b.sl as sl_select," + //税率(下拉框)- 从形式发票获取
                "  (case when b.sl = 0 then 0 " +
                "  when b.sl =1 then 0.06 " +
                "  when b.sl =2 then 0.13 " +
                "  when b.sl =3 then 0.17 " +
                "  else 0.16 end ) as sl_value " + //税率(数值)
                " from uf_zhuxmskjd a " + //建模表-收款阶段
                " inner join uf_xsfpglnew b on (a.skjind = b.xsfpbh) " + //关联建模表-形式发票
                " where a.id in (" + stageIds + ") ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getMapList(rs);
        }
        return null;
    }

    /**
     * 校验参数
     */
    @SuppressWarnings("unchecked")
    private void checkParams() {
        JSONObject eachData;
        String bodyStr = Util.null2String(params.get("body"));
        if (bodyStr.isEmpty()) {
            error = "缺失body参数";
            return;
        }
        //获取body的map
        Map<String, Object> bodyMap = (Map<String, Object>) params.get("body");
        try {
            bodyParam = new JSONObject(bodyMap);
        } catch (Exception e) {
            error = "body参数格式有误，请传入JSON对象";
            return;
        }

        if (!bodyParam.containsKey("data")) {
            error = "data节点数据缺失";
            return;
        }
        JSONArray dataArray = bodyParam.getJSONArray("data");
        if (dataArray == null || dataArray.isEmpty()) {
            error = "data节点数据缺失，请传入JSON数组";
            return;
        }

        //校验dataArray每个数据必须要id字段，且id都不重复
        //  Set<String> idSet = new HashSet<>(); // 用于存储所有id，确保唯一性
        for (int i = 0; i < dataArray.size(); i++) {
            eachData = dataArray.getJSONObject(i);
            // 检查是否包含id字段
            if (!eachData.containsKey("id")) {
                error = "第" + (i + 1) + "行数据缺失id字段";
                break;
            }
            // 获取id值并校验是否为空
            String idValue = Util.null2String(eachData.get("id")).trim();
            if (idValue.isEmpty()) {
                error = "第" + (i + 1) + "行id字段值不能为空";
                break;
            }
//                // 校验id是否重复
//                if (idSet.contains(idValue)) {
//                    error = "第" + (i + 1) + "行id字段值重复：" + idValue;
//                    break;
//                }
//                idSet.add(idValue); // 将id添加到集合中
        }

    }

    /**
     * 校验字段
     *
     * @param eachData
     * @return
     */
    private String checkField(JSONObject eachData) {
        String error = "";
        String eachFieldValue;
        //判断必填字段
        //收款阶段id，是否继续开票 0还需要继续开(用于分批次开票，开一次不会更新开票标记，可以继续调用接口进行开票)，1开票完成
        String[] requiredFields = new String[]{"id", "sfjxkp"};
        for (String eachField : requiredFields) {
            if (!eachData.containsKey(eachField)) {
                error = "缺失参数:" + eachField;
            } else {
                eachFieldValue = Util.null2String(eachData.get(eachField)).trim();
                if (eachFieldValue.isEmpty()) {
                    error = "参数:" + eachField + "必填!";
                }
            }
            if (!error.isEmpty()) {
                break;
            }
        }
        //美元字段和人民币字段两种必填一种
        String[] rmbFields = new String[]{"kqrq", "gnfpzl", "je", "se", "jshj", "gnfph"};
        String[] dollarFields = new String[]{"myykp", "wbkprq", "wbfpzl", "zhkpjermb", "sermb", "zhjsrmb", "wbfph"};

        // 检查人民币字段是否全部填写
        boolean rmbFieldsFilled = true;
        for (String rmbField : rmbFields) {
            if (!eachData.containsKey(rmbField) || Util.null2String(eachData.get(rmbField)).trim().isEmpty()) {
                rmbFieldsFilled = false;
                break;
            }
        }

        // 检查美元字段是否全部填写
        boolean dollarFieldsFilled = true;
        for (String dollarField : dollarFields) {
            if (!eachData.containsKey(dollarField) || Util.null2String(eachData.get(dollarField)).trim().isEmpty()) {
                dollarFieldsFilled = false;
                break;
            }
        }
        // 如果两组字段都未填写或都填写了，返回错误信息
        if (!rmbFieldsFilled && !dollarFieldsFilled) {
            error = "必须填写人民币字段或美元字段中的一种!";
        } else if (rmbFieldsFilled && dollarFieldsFilled) {
            error = "不能同时填写人民币字段和美元!";
        }
        //校验数字的字段
        String[] numberFields = new String[]{"id", "gnfpzl", "je", "se", "jshj", "myykp", "wbfpzl", "zhkpjermb", "sermb", "zhjsrmb"};
        //如果这些数字字段有值，则必须是数字
        for (String numberField : numberFields) {
            if (eachData.containsKey(numberField)) {
                eachFieldValue = Util.null2String(eachData.get(numberField)).trim();
                if (!eachFieldValue.isEmpty() && !isNumeric(eachFieldValue)) {
                    error = "参数:" + numberField + "必须是数字!";
                    break;
                }
            }
        }
        //校验
        return error;
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str 需要判断的字符串
     * @return true 是数字，false 不是数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private void appendRemark(String msg) {
        invoiceIssueExecutor.appendRemark(msg);
    }

}
