package com.engine.meidixi3.gyl.module.skjd.bean;

import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @FileName InvoiceIssueLog.java
 * @Description 收款阶段开票日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/3/20
 */
@Data
public class InvoiceIssueLog {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 操作开始时间
     */
    private String operate_begin_time;
    /**
     * 操作结束时间
     */
    private String operate_end_time;
    /**
     * 操作耗时
     */
    private String during;
    /**
     * 操作是否成功
     * 0成功
     * 1失败
     */
    private Integer success;
    /**
     * 失败信息
     */
    private String errorMsg;
    /**
     * 日志文件
     */
    private String log_file;
    /**
     * 日志文本
     * 不超过3500字符
     */
    private String log_str;
    /**
     * 请求报文
     */
    private String request_file;
    /**
     * 响应报文
     */
    private String response_file;
    /**
     * 总共开票数量
     */
    private Integer total_count;
    /**
     * 成功数量
     */
    private Integer success_count;
    /**
     * 失败数量
     */
    private Integer fail_count;
    /**
     * 操作来源
     * 接口开票
     * 流程action开票
     */
    private String operate_from;
    /**
     * 开票流程requestid
     */
    private String operate_requestid;
    /**
     * 批次号
     */
    private String serial_num;

    /**
     * 明细表1
     * 存每个阶段的开票数据
     */
    @Data
    public static class Detail1 {
        /**
         * 明细表id
         */
        private Integer id;
        /**
         * 主表id
         */
        private Integer mainid;
        /**
         * 接口字段
         * 收款阶段id
         */
        private Integer stage_id;
        /**
         * 接口字段
         * 收款阶段名称
         */
        private String stage_name;
        /**
         * 接口字段
         * 国内开票日期
         */
        private String kqrq;
        /**
         * 接口字段
         * 国内发票种类
         * 0: 电子发票（增值税专用发票）
         * 1: 电子发票（普通发票）
         * 2: 增值税专用发票
         * 3: 增值税普通发票
         */
        private Integer gnfpzl;
        /**
         * 接口字段
         * 人民币开票金额(2位小数)
         */
        private BigDecimal je;
        /**
         * 接口字段
         * 人民币税额(2位小数)
         */
        private BigDecimal se;
        /**
         * 接口字段
         * 人民币价税合计(2位小数)
         */
        private BigDecimal jshj;
        /**
         * 接口字段
         * 国内发票号
         */
        private String gnfph;
        /**
         * 接口字段
         * 美元开票金额(2位小数)
         */
        private BigDecimal myykp;
        /**
         * 接口字段
         * 外币开票日期
         */
        private String wbkprq;
        /**
         * 接口字段
         * 外币发票种类
         * 0 : 电子发票（增值税专用发票）
         * 1 : 电子发票（普通发票）
         * 2 : 纸质发票（增值税专用发票）
         * 3 : 纸质发票（普通发票）
         */
        private Integer wbfpzl;
        /**
         * 接口字段
         * 折合开票金额RMB(2位小数)
         */
        private BigDecimal zhkpjermb;
        /**
         * 接口字段
         * 税额RMB(2位小数)
         */
        private BigDecimal sermb;
        /**
         * 接口字段
         * 折合价税RMB(2位小数)
         */
        private BigDecimal zhjsrmb;
        /**
         * 接口字段
         * 外币发票号
         */
        private String wbfph;
        /**
         * 操作是否成功
         * 0成功
         * 1失败
         */
        private Integer success;
        /**
         * 更新前数据
         */
        private String before_data;
        /**
         * 更新后数据
         */
        private String after_data;
        /**
         * 失败信息
         */
        private String errorMsg;
        /**
         * 是否继续开票
         * 0 还需要继续开。 用于分批次开票，开一次票不会更新开票标记，可以继续调用接口进行开票
         * 1 开票完成。接口会自动更新开票标记，下次开票需要OA这边人员操作打上开票标记后，才可以继续开票
         */
        private Integer sfjxkp;

    }

    public static final String TABLE_NAME = "uf_skjd_kprz";

    /**
     * 保存日志bean
     *
     * @param log
     * @param detailLogList
     * @param operator
     * @return
     */
    public static Integer saveLog(InvoiceIssueLog log, List<Detail1> detailLogList, int operator) {
        int billid = -1;
        //同步保存主表日志
        ModuleResult mr = ModuleDataUtil.insertObj(log, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), operator);
        if (mr.isSuccess()) {
            billid = mr.getBillid();
            if (detailLogList != null && !detailLogList.isEmpty()) {
                ModuleDataUtil.insertObjDetail(detailLogList, TABLE_NAME + "_dt1", billid);
            }
        }
        return billid;
    }
}
