package com.engine.meidixi3.gyl.workflow.invoiceissue;


import com.engine.meidixi3.gyl.module.skjd.bean.InvoiceIssueLog;
import com.engine.meidixi3.gyl.module.skjd.executor.InvoiceIssueExecutor;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @FileName InvoiceCollectAction.java
 * @Description 开票action, 只针对人民币,action成功后，后面流程转数据会转入开票流水
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/3/21
 */
@Getter
@Setter
public class InvoiceIssueAction extends BaseSDAction implements Action {
    //Action参数---START---
    /**
     * 开票明细的index
     * 必填
     * 目前是1
     */
    private String detailIndex;
    //Action参数---END---


    //锁
    private static final Object lock = new Object();

    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 自定义action类私有变量
     * 使用父类的ThreadLocal管理
     */
    private static class Params {
        /**
         * 操作日志
         */
        private final InvoiceIssueLog mainLog = new InvoiceIssueLog();
    }

    private Params getParam() {
        return getThreadLocalCustomParam(this.getClass(), Params.class, Params::new);
    }

    //初始化
    private void init() {
        getParam().mainLog.setOperate_begin_time(SDTimeUtil.getCurrentTimeMillliString());
        getParam().mainLog.setOperate_from("InvoiceIssueAction开票action触发");
        getParam().mainLog.setOperate_requestid(getThreadLocalBaseParam().actionInfo.getRequestId());
    }

    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), Params.class, "InvoiceIssueAction开票action触发");
        try {
            //初始化
            init();
            //执行业务逻辑
            execuetMy();
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "执行异常：" + e.getMessage();
            log.error("执行异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        log.info(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
        return actionReturn(false);
    }

    /**
     * 执行逻辑
     */
    private void execuetMy() {
        InvoiceIssueLog.Detail1 detailLog;
        try {
            //明细1数据
            List<Map<String, Object>> detailData1 = getDetailData();
            if (detailData1 != null && !detailData1.isEmpty()) {
                //明细数据先插入建模
                List<InvoiceIssueLog.Detail1> detailLogList = new ArrayList<>();
                //遍历明细1，构建日志明细
                for (Map<String, Object> eachDetail : detailData1) {
                    detailLog = new InvoiceIssueLog.Detail1();
                    detailLog.setStage_id(Util.getIntValue(Util.null2String(eachDetail.get("skjd")))); //收款阶段id
                    detailLog.setStage_name(Util.null2String(eachDetail.get("skjd_name")));
                    detailLog.setKqrq(Util.null2String(eachDetail.get("gnkprq"))); //国内开票日期
                    detailLog.setGnfpzl(Util.getIntValue(Util.null2String(eachDetail.get("gnfpzl1")))); //国内发票种类
                    detailLog.setGnfph(Util.null2String(eachDetail.get("gnfph"))); //国内发票号
                    detailLog.setJe(SDUtil.getBigDecimalValue(eachDetail.get("je"))); //金额（RMB）
                    detailLog.setSe(SDUtil.getBigDecimalValue(eachDetail.get("se"))); //税额（RMB）
                    detailLog.setJshj(SDUtil.getBigDecimalValue(eachDetail.get("jshj"))); //价税合计（RMB）
                    detailLogList.add(detailLog);
                }
                InvoiceIssueExecutor invoiceIssueExecutor = new InvoiceIssueExecutor(false, getThreadLocalBaseParam().actionInfo.getUser());
                //生成一个批次号,用UUID
                String serialNum = UUID.randomUUID().toString().replace("-", "");
                getParam().mainLog.setSerial_num(serialNum);
                //插入建模原始数据
                getThreadLocalBaseParam().actionError = invoiceIssueExecutor.insertLogData(getParam().mainLog, detailLogList);
                if (getActionError().isEmpty()) {
                    //锁住校验和更新
                    synchronized (lock) {
                        //校验是否可以开票
                        getThreadLocalBaseParam().actionError = invoiceIssueExecutor.checkCanIssue();
                        if (getActionError().isEmpty()) {
                            //更新收款阶段开票
                            getThreadLocalBaseParam().actionError = invoiceIssueExecutor.updateStageWithoutCheck();
                        }
                        //更新日志
                        invoiceIssueExecutor.updateLog("", "");
                    }
                } else {
                    getThreadLocalBaseParam().actionError = "插入日志台账有误：" + getThreadLocalBaseParam().actionError;
                    log.error(getActionError());
                }
            } else {
                log.warn("无明细数据，不处理逻辑");
            }

        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = this.getClass().getName() + "异常：" + SDUtil.getExceptionDetail(e);
        }
    }


    /**
     * 获取流程明细数据
     * 按照收款阶段汇总
     *
     * @return
     */
    private List<Map<String, Object>> getDetailData() {
        String detailTable = getThreadLocalBaseParam().actionInfo.getFormtableName() + "_dt" + detailIndex;
        String sql = "select " +
                " a.*," + //收款阶段
                " b.skjd as skjd_name " + //收款阶段名称
                " from " + detailTable + " a " +
                " left join uf_zhuxmskjd b on (a.skjd = b.id)" +
                " where a.mainid = " + getThreadLocalBaseParam().actionInfo.getId();
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getMapList(rs);
        }
        return null;
    }
}
