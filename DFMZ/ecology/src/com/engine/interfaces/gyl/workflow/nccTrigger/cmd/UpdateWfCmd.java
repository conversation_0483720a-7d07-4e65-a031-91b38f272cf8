package com.engine.interfaces.gyl.workflow.nccTrigger.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.common.util.ServiceUtil;
import com.engine.core.interceptor.CommandContext;
import com.engine.interfaces.gyl.workflow.center.util.EsbEventUtil;
import com.engine.interfaces.gyl.workflow.nccTrigger.service.UpdateWfService;
import com.engine.interfaces.gyl.workflow.nccTrigger.service.impl.UpdateWfServiceImpl;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.query.util.QueryResultUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.util.WorkFlowUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import weaver.general.ThreadPoolUtil;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;
import java.util.concurrent.ExecutorService;


/**
 * NCC调用更新，交换中心处理数据后同步给前端OA
 * 调前端OA时，使用了线程池，共有变量增加了volatile属性
 * type = 0：更新交换中心，前端oa数据
 * tupe = 1;更新交换中心，前端oa数据，还要退单操作
 * tupe = 2；更新交换中数据（可能会传归档字段），需要执行当前单据归档，还要执行把原OA单据的实际支付状态
 * type = 3；把当前单据实际支付状态改为失败
 *
 * <AUTHOR>
 */
public class UpdateWfCmd extends AbstractCommonCommand<Map<String, Object>> {

    private UpdateWfService getService(User user) {
        return ServiceUtil.getService(UpdateWfServiceImpl.class, user);
    }

    public UpdateWfCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        rs = null;
        rsFlag = false;
        paramErroList = new ArrayList<>();
        exptList = new ArrayList<>();
        sql = "";
        mainTableName = "";
        mainTableWfId = "";
        joMainData = new JSONObject();
        mainParamMap = new HashMap<>();
        need2RejectFrontOA = false;
        need2ForceOverFrontOA = false;
        need2UpdateRemark = false;
        composeRemark = "";
        appid = "";
        exptReSubmit = "";
        currentNodeId = "";
        paramJson = new JSONObject();
        centerConfig = new JSONObject();
    }

    //基类
    private final BaseBean bb;
    //数据库操作类（不带事务）
    private volatile RecordSet rs;
    //RecordSet返回成功失败值
    private volatile boolean rsFlag;
    //参数错误信息列表
    private final List<String> paramErroList;
    //异常错误信息列表
    private final List<String> exptList;
    //sql
    private volatile String sql;
    //接口参数requestid
    private volatile String requestid;
    //requestid对应的流程主表
    private volatile String mainTableName;
    // requestid对应的流程id
    private volatile String mainTableWfId;
    // requestid对应的流程主表数据
    private volatile JSONObject joMainData;
    //mainData参数转换为map
    private final Map<String, Object> mainParamMap;
    //是否需要更新签字意见
    private volatile boolean need2UpdateRemark;
    // 合并之后的签字意见字符串
    private volatile String composeRemark;
    //是否需要退回前端OA
    private volatile boolean need2RejectFrontOA;
    //是否需要强制归档前端OA
    private volatile boolean need2ForceOverFrontOA;
    //接口调用传来的appid
    private volatile String appid;
    //是否是异常节点重新提交，1为是，重新提交时候不执行更新签字意见
    private volatile String exptReSubmit;
    //流程当前的节点
    private volatile String currentNodeId;
    //param参数转换JSONObject
    private volatile JSONObject paramJson;
    //交换中心配置信息
    private volatile JSONObject centerConfig;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("start time:" + TimeUtil.getCurrentTimeString());
        Map<String, Object> result = new HashMap<>(2);
        bb.writeLog("接口传入参数：" + params.toString());
        paramJson = new JSONObject(params);
        //默认成功
        result.put("code", "SUCCESS");
        result.put("errMsg", "");
        try {
            //step 1 : 读取配置，初始化
            bb.writeLog("step 0: 读取配置，初始化");
            appid = Util.null2String(params.get("appid"));
            exptReSubmit = Util.null2String(params.get("exptReSubmit"));

            //step 2: 校验NCC接口传来的参数
            bb.writeLog("step 2: 校验NCC接口传来的参数");
            checkParams();
            if (paramErroList.isEmpty()) {
                //step 3：解析数据并更新交换中心数据
                bb.writeLog("step 3: 解析数据并更新交换中心数据");
                updateCenterData();
                if (paramErroList.isEmpty() && exptList.isEmpty()) {
                    //step 4: 读配置，更新前端OA表单
                    bb.writeLog("step 4: 读配置，更新前端OA表单");
                    //首先重新获取一遍交换中心的主表值，因为前面已经更新的交换中心的数据
                    getMainColumns();
                    //调用前端OA使用新线程，当从异常节点重新提交过来时，同步请求，NCC调用时，异步使用新线程
                    if ("1".equals(exptReSubmit)) {
                        //异常节点过来的请求，同步执行前端oa
                        String erroMsgFrontOA = updateFrontOA(joMainData,
                                mainTableWfId,
                                need2ForceOverFrontOA,
                                need2UpdateRemark,
                                need2RejectFrontOA,
                                composeRemark,
                                params,
                                requestid,
                                centerConfig,
                                appid,
                                mainTableName,
                                paramJson,
                                user);
                        result.put("erroMsgFrontOA", erroMsgFrontOA);
                    } else {
                        //正常的NCC请求，启用新线程，调用前端oa
                        bb.writeLog("创建了新的线程前的线程：" + Thread.currentThread().getName());
                        ExecutorService executorService = ThreadPoolUtil.getThreadPool("SD_DFMZ_THREAD_POOL", "50");
                        executorService.execute(() -> {
                            bb.writeLog("创建了新的线程，执行前端oa操作：" + Thread.currentThread().getName());
                            updateFrontOA(joMainData,
                                    mainTableWfId,
                                    need2ForceOverFrontOA,
                                    need2UpdateRemark,
                                    need2RejectFrontOA,
                                    composeRemark,
                                    params,
                                    requestid,
                                    centerConfig,
                                    appid,
                                    mainTableName,
                                    paramJson,
                                    user);
                        });
                        bb.writeLog("异步执行了更新前端OA：" + Thread.currentThread().getName());
                        //销毁线程池（ 不能销毁，销毁了不能再持续调用了）
                        //executorService.shutdown();
                    }
                }
            }


        } catch (Exception e) {
            exptList.add("执行" + this.getClass().getName() + "程序异常，请联系OA管理员，异常信息：" + e.getMessage());
        }

        //step5 : 结果返回
        if (!paramErroList.isEmpty()) {
            result.put("code", "PARAM_ERROR");
            result.put("errMsg", paramErroList.toString());
        }
        if (!exptList.isEmpty()) {
            result.put("code", "SYSTEM_INNER_ERROR");
            result.put("errMsg", exptList.toString());
        }
        bb.writeLog(this.getClass().getName() + "---END");
        bb.writeLog("end time:" + TimeUtil.getCurrentTimeString());
        return result;
    }

    /**
     * 校验参数
     */
    private void checkParams() {
        JSONObject jo;
        JSONArray jaMain = new JSONArray();
        JSONArray jaDetail = new JSONArray();
        JSONArray jaDetailRecords, jaDetailTableFields;
        //操作类型
        String type = Util.null2String(params.get("type"));
        //必填字段组
        String[] requiredParams = {"requestid", "type", "mainData"};
        //step1：校验参数是否为空
        for (String str : requiredParams) {
            if (!params.containsKey(str) || Util.null2String(params.get(str)).isEmpty()) {
                paramErroList.add("根节点缺失" + str + "参数!");
            }
        }
        if (paramErroList.isEmpty()) {
            requestid = Util.null2String(params.get("requestid"));
            //step2：校验参数正确性
            //1.requestid能否匹配
            rs = new RecordSet();
            sql = "select " +
                    " a.requestid, " +
                    " a.currentnodeid, " +
                    " a.lastoperator, " +
                    " a.workflowid,  " +
                    " c.tablename,  " +
                    " b.workflowname  " +
                    " from " +
                    " workflow_requestbase a " +
                    " left join workflow_base b on a.workflowid = b.id " +
                    " left join workflow_bill c on c.id = b.formid  " +
                    " where " +
                    " a.requestid = ? ";
            bb.writeLog("查询requestidid的sql：" + sql);
            rsFlag = rs.executeQuery(sql, requestid);
            if (rsFlag) {
                if (!rs.next()) {
                    paramErroList.add("未在OA系统中找到相应的流程，请检查requestid参数！");
                } else {
                    mainTableName = Util.null2String(rs.getString("tablename"));
                    mainTableWfId = Util.null2String(rs.getString("workflowid"));
                    currentNodeId = Util.null2String(rs.getString("currentnodeid"));

                    //step1 : 根据mainTableWfId，获取对应启用的交换中心配置项
                    rs = new RecordSet();
                    sql = "select * from uf_centerconfig where wfid = ? and sfqy = 1";
                    if (rs.executeQuery(sql, mainTableWfId)) {
                        JSONArray ja = QueryUtil.getJSONList(rs);
                        if (!ja.isEmpty()) {
                            centerConfig = ja.getJSONObject(0);
                        }
                    }
                    if (centerConfig.isEmpty()) {
                        paramErroList.add("requestid：" + requestid + "对应的交换中心workflowid：" + mainTableWfId + ",未在OA系统中未找到交换中心启用的配置信息，请检查requestid参数");
                    } else {
                        //step2 : mainData，detailData解析，数据结构检查
                        try {
                            jaMain = JSONArray.parseArray(Util.null2String(params.get("mainData")));
                        } catch (Exception e) {
                            paramErroList.add("格式化mainData数据出错，请检查JSON格式！");
                        }
                        //明细数据
                        String detailData = Util.null2String(params.get("detailData"));
                        if (!detailData.isEmpty()) {
                            try {
                                jaDetail = JSONArray.parseArray(detailData);
                            } catch (Exception e) {
                                paramErroList.add("格式化detailData数据出错，请检查JSON格式！");
                            }
                        }

                        if (paramErroList.isEmpty()) {
                            //step3: 主表数据字段校验
                            if (!jaMain.isEmpty()) {
                                List<String> allMainColumns = getMainColumns();
                                for (int i = 0; i < jaMain.size(); i++) {
                                    int iIndex = i + 1;
                                    jo = jaMain.getJSONObject(i);
                                    if (jo.containsKey("fieldName") && jo.containsKey("fieldValue")) {
                                        if (!allMainColumns.contains(Util.null2String(jo.get("fieldName")))) {
                                            paramErroList.add("mainData第" + iIndex + "行数据错误，" + Util.null2String(jo.get("fieldName")) + "字段不存在");
                                        } else {
                                            mainParamMap.put(Util.null2String(jo.get("fieldName")), jo.get("fieldValue"));
                                        }
                                    } else {
                                        paramErroList.add("mainData第" + iIndex + "行数据格式不正确，缺失fieldName或fieldValue参数");
                                    }
                                }
                            }
                            //step4: 明细表数据字段校验
                            if (!jaDetail.isEmpty()) {
                                for (int i = 0; i < jaDetail.size(); i++) {
                                    int iIndex = i + 1;
                                    jo = jaDetail.getJSONObject(i);
                                    String[] checkParams = new String[]{"detailIndex", "updateMode", "workflowRequestTableRecords"};
                                    if (checkRequiredParams(jo, checkParams, "detailData节点的第" + iIndex + "行数据")) {

                                        jaDetailRecords = jo.getJSONArray("workflowRequestTableRecords");
                                        if (jaDetailRecords != null && !jaDetailRecords.isEmpty()) {

                                            for (int j = 0; j < jaDetailRecords.size(); j++) {
                                                int jIndex = j + 1;
                                                jo = jaDetailRecords.getJSONObject(j);
                                                checkParams = new String[]{"workflowRequestTableFields"};
                                                if (checkRequiredParams(jo, checkParams, "detailData节点的第" + iIndex + "行数据的" +
                                                        "workflowRequestTableRecords节点的第" + jIndex + "行数据")) {

                                                    jaDetailTableFields = jo.getJSONArray("workflowRequestTableFields");
                                                    for (int k = 0; k < jaDetailTableFields.size(); k++) {
                                                        int kIndex = k + 1;
                                                        jo = jaDetailTableFields.getJSONObject(k);
                                                        checkParams = new String[]{"fieldName", "fieldValue"};
                                                        checkRequiredParams(jo, checkParams, "detailData节点的第" + iIndex + "行数据的" +
                                                                "workflowRequestTableRecords节点的第" + jIndex + "行数据的workflowRequestTableFields节点第" + kIndex + "行数据");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            //step 2-1 : 交换当前流程是否在可执行接口的节点范围
                            String availableNodes = Util.null2String(centerConfig.get("kzxgxjkdjd"));
                            if (availableNodes.isEmpty()) {
                                paramErroList.add("该流程在交换中心未配置可执行更新操作的节点id,请联系OA管理员");
                            } else {
                                boolean hasNode = false;
                                for (String str : availableNodes.split(CommonCst.COMMA_EN)) {
                                    if (str.equals(currentNodeId)) {
                                        hasNode = true;
                                        break;
                                    }
                                }
                                if (!hasNode) {
                                    paramErroList.add("该流程在交换中心当前节点不可执行该接口的更新动作,请检查流程状态");
                                }
                            }

                            //step 2-2：如果有归档参数，校验交换中心节点必须在共享审批节点
                            String archiveNodes, archiveFieldName, archiveFieldValue;
                            if (params.containsKey("archiveFieldName") && !"".equals(Util.null2String(params.get("archiveFieldName")))
                                    && params.containsKey("archiveFieldValue") && !"".equals(Util.null2String(params.get("archiveFieldValue")))) {
                                //可执行归档的节点s
                                archiveNodes = Util.null2String(centerConfig.get("kzxgddjd"));
                                archiveFieldName = Util.null2String(params.get("archiveFieldName"));
                                archiveFieldValue = Util.null2String(params.get("archiveFieldValue"));

                                if (archiveNodes.isEmpty()) {
                                    paramErroList.add("该流程在交换中心未配置可执行归档操作的节点id,请联系OA管理员");
                                } else {
                                    boolean hasNode = false;
                                    for (String str : archiveNodes.split(CommonCst.COMMA_EN)) {
                                        if (str.equals(currentNodeId)) {
                                            hasNode = true;
                                            break;
                                        }
                                    }
                                    if (!hasNode) {
                                        paramErroList.add("该流程在交换中心当前节点不可执行该接口的归档动作,请检查流程状态");
                                    } else {
                                        //从主表信息里获取归档的字段
                                        if (!mainParamMap.containsKey(archiveFieldName)) {
                                            paramErroList.add("在传入archiveFieldName字段后，mainData参数中必须包含此参数");
                                        } else {
                                            //当参数archiveFieldValue等于mainData参数中的archiveFieldName的字段值时，执行归档操作
                                            String mainArchiveValue = Util.null2String(mainParamMap.get(archiveFieldName));
                                            if (mainArchiveValue.equals(archiveFieldValue)) {
                                                need2ForceOverFrontOA = true;
                                            } else {
                                                //归档主表字段值，当mainData中归档字段值不等于该值时，不触发流程归档
                                                bb.writeLog("mainData中归档字段值不等于该值时，不触发流程归档");
                                            }
                                        }
                                    }
                                }
                            }

                            //判断type类型，还要执行其他操作
                            //typ1 为1时，还要进行退回单据操作
                            if ("1".equals(type)) {
                                if (need2ForceOverFrontOA) {
                                    paramErroList.add("不可同时操作归档和退单！请检查参数");
                                } else {
                                    need2RejectFrontOA = true;
                                }
                            }

                        }
                    }
                }
            } else {
                bb.writeLog("查询requestid对应流程出错，检查sql");
                exptList.add("内部错误，查询requestid对应流程出错，请联系OA管理员");
            }
        }
    }


    /**
     * 获取主表所有字段列表
     *
     * @return
     */
    private List<String> getMainColumns() {
        List<String> result = new ArrayList<>();
        sql = " select a.*,b.requestname from " + mainTableName + " a  " +
                " left join workflow_requestbase b on(a.requestid = b.requestid) " +
                " where a.requestid = ?";
        rs = new RecordSet();
        rsFlag = rs.executeQuery(sql, requestid);
        if (rsFlag) {
            joMainData = QueryResultUtil.getJSONArrayList(rs).getJSONObject(0);
            RecordSetData rd = rs.getData();
            String[] columnNames = rd.getColumnName();
            result.addAll(Arrays.asList(columnNames));
        }
        return result;
    }


    /**
     * 校验必填参数名
     *
     * @param jo
     * @param checkParams
     * @return
     */
    private boolean checkRequiredParams(JSONObject jo, String[] checkParams, String prefix) {
        boolean flag = true;
        for (String str : checkParams) {
            if (!jo.containsKey(str)) {
                paramErroList.add(prefix + "数据有误，缺失" + str + "参数");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 执行更新交换中心数据
     */
    private void updateCenterData() {
        StringBuilder sbSet = new StringBuilder();
        StringBuilder sbInsertColumns = new StringBuilder();
        StringBuilder sbInsertValues = new StringBuilder();
        JSONObject jo;
        JSONObject joFields;
        String detailIndex, updateMode, detailTableName, fieldName, fieldValue;
        JSONArray workflowRequestTableRecords, workflowRequestTableFields, currentDetailData;
        //签字意见明细序列 1代表明细1，2代表明细2
        String remark_detail_index = Util.null2String(centerConfig.get("qzyjmxxl"));

        JSONArray jaMain = JSONArray.parseArray(Util.null2String(params.get("mainData")));
        JSONArray jaDetail = new JSONArray();
        if (params.containsKey("detailData")) {
            jaDetail = JSONArray.parseArray(Util.null2String(params.get("detailData")));
        }

        //拼接主表set sql
        for (int i = 0; i < jaMain.size(); i++) {
            jo = jaMain.getJSONObject(i);
            sbSet.append(Util.null2String(jo.get("fieldName"))).append(" = '").append(jo.get("fieldValue")).append("'");
            if (i < jaMain.size() - 1) {
                sbSet.append(", ");
            }
        }
        sql = " update " + mainTableName + " set " + sbSet + " where requestid = ? ";
        bb.writeLog("更新交换中心数据sql:" + sql);
        rsFlag = rs.executeUpdate(sql, requestid);
        if (!rsFlag) {
            exptList.add("更新交换中心数据出错，请联系OA管理员，" + rs.getMsg());
        } else {
            //如果明细参数有值，则需要插入明细，并拼接字段成主表的签字意见
            if (!jaDetail.isEmpty()) {
                for (int i = 0; i < jaDetail.size(); i++) {
                    jo = jaDetail.getJSONObject(i);
                    detailIndex = Util.null2String(jo.get("detailIndex"));
                    updateMode = Util.null2String(jo.get("updateMode"));
                    workflowRequestTableRecords = jo.getJSONArray("workflowRequestTableRecords");
                    detailTableName = mainTableName + "_dt" + detailIndex;
                    currentDetailData = getDetailData(detailTableName);

                    //非异常处理节点的请求，需要插入明细表数据，异常节点请求的，保持原明细表数据不变
                    if (!"1".equals(exptReSubmit)) {
                        //0类型的要先清除原有明细数据
                        if ("0".equals(updateMode)) {
                            currentDetailData = new JSONArray();
                            //先删除原明细数据，再插入新数据
                            sql = "delete from " + detailTableName + " where mainid = ? ";
                            rsFlag = rs.executeUpdate(sql, joMainData.get("id"));
                            if (!rsFlag) {
                                exptList.add("删除OA明细数据出错，请联系OA管理员，" + rs.getMsg());
                                break;
                            }
                        }
                        //插入明细数据
                        for (int j = 0; j < workflowRequestTableRecords.size(); j++) {
                            workflowRequestTableFields = workflowRequestTableRecords.getJSONObject(j).getJSONArray("workflowRequestTableFields");
                            for (int k = 0; k < workflowRequestTableFields.size(); k++) {
                                joFields = workflowRequestTableFields.getJSONObject(k);
                                fieldName = Util.null2String(joFields.get("fieldName"));
                                fieldValue = Util.null2String(joFields.get("fieldValue"));
                                sbInsertColumns.append(fieldName);
                                sbInsertValues.append("'").append(fieldValue).append("'");
                                if (k < workflowRequestTableFields.size() - 1) {
                                    sbInsertColumns.append(",");
                                    sbInsertValues.append(",");
                                }

                            }
                            //插入明细表
                            sql = "INSERT INTO " + detailTableName + "(" + sbInsertColumns + ",mainid) VALUES (" + sbInsertValues + ",?) ";
                            bb.writeLog("插入交换中心明细sql：" + sql);
                            rsFlag = rs.executeUpdate(sql, joMainData.get("id"));
                            if (!rsFlag) {
                                exptList.add("插入OA明细数据出错，请联系OA管理员，" + rs.getMsg() + ";" + rs.getExceptionMsg());
                                break;
                            }
                        }
                    }
                    //拼接明细1至主表签字意见，并更新前端OA的签字意见
                    if (remark_detail_index.equals(detailIndex)) {
                        need2UpdateRemark = true;
                        resolveDetailData2Suggestion(currentDetailData, workflowRequestTableRecords);
                    }
                }
            }
            if (need2ForceOverFrontOA) {
                //执行交换中心的-提交动作（因为这里在共享审批节点，提交及代表归档）
                bb.writeLog("交换中心执行节点提交操作--开始");
                //干预共享审批节点接收人
                int userId = Integer.parseInt(Util.null2String(centerConfig.get("gygxspjdjsr")));
                bb.writeLog("交换中心执行节点提交操作人：" + userId);
                String gdResult = WorkFlowUtil.submitWorkflowRequest(Integer.parseInt(requestid), userId, "submit");
                bb.writeLog("交换中心执行节点提交操作--结果：" + gdResult);
                if (!"success".equals(gdResult)) {
                    exptList.add("流程归档失败，请联系OA管理员：" + gdResult);
                }
            }
            //归档和退单不会同时操作，前面有做参数校验
            if (need2RejectFrontOA) {
                //退单，1.交换中心直接退回
                bb.writeLog("交换中心执行节点退回操作--开始");
                int userId = Integer.parseInt(Util.null2String(centerConfig.get("gygxspjdjsr")));
                bb.writeLog("交换中心执行节点退回操作人：" + userId);
                String rejectRst = WorkFlowUtil.submitWorkflowRequest(Integer.parseInt(requestid), userId, "reject");
                bb.writeLog("交换中心执行节点退回操作--结果:" + rejectRst);
                if (!"success".equals(rejectRst)) {
                    exptList.add("流程退回失败，请联系OA管理员：" + rejectRst);
                }
            }

        }
    }

    /**
     * 更新前端OA
     */
    private String updateFrontOA(JSONObject joMainData,
                                 String mainTableWfId,
                                 boolean need2ForceOverFrontOA,
                                 boolean need2UpdateRemark,
                                 boolean need2RejectFrontOA,
                                 String composeRemark,
                                 Map<String, Object> params,
                                 String requestid,
                                 JSONObject centerConfig,
                                 String appid,
                                 String mainTableName,
                                 JSONObject paramJson,
                                 User user) {

        //组装调用前端OA需要的参数
        Map<String, Object> mapFrontOa = new HashMap<>(2);
        mapFrontOa.put("joMainData", joMainData);
        mapFrontOa.put("mainTableWfId", mainTableWfId);
        mapFrontOa.put("need2ForceOverFrontOA", need2ForceOverFrontOA);
        mapFrontOa.put("need2UpdateRemark", need2UpdateRemark);
        mapFrontOa.put("need2RejectFrontOA", need2RejectFrontOA);
        mapFrontOa.put("composeRemark", composeRemark);
        mapFrontOa.put("type", params.get("type"));
        mapFrontOa.put("requestid", requestid);
        mapFrontOa.put("centerConfig", centerConfig);
        mapFrontOa.put("updateApiParam", paramJson);
        bb.writeLog("mapFrontOa:" + mapFrontOa);

        //实际执行更新前端OA
        Map<String, Object> mapFrontOAResult = getService(user).updateFrontOA(mapFrontOa, user);
        String erroMsgFrontOA = Util.null2String(mapFrontOAResult.get("erroMsgFrontOA"));
        bb.writeLog("erroMsgFrontOA : " + erroMsgFrontOA);
        if (!erroMsgFrontOA.isEmpty()) {
            //失败时，将流程干预到异常节点，并且更新失败信息
            EsbEventUtil.interveneWf2ExceptionNode(requestid,
                    appid,
                    mainTableName,
                    "0",
                    erroMsgFrontOA,
                    paramJson.toJSONString(),
                    centerConfig);
        }

        return erroMsgFrontOA;

    }


    /**
     * 获取明细表数据
     *
     * @param detailTableName 明细表名
     * @return
     */
    private JSONArray getDetailData(String detailTableName) {
        sql = "select * from " + detailTableName + " where mainid = ?";
        rs = new RecordSet();
        rs.executeQuery(sql, joMainData.get("id"));
        return QueryResultUtil.getJSONArrayList(rs);
    }


    /**
     * 解析明细参数数据，拼接签字意见，传输签字意见到前端OA
     *
     * @param currentDetailData
     * @param workflowRequestTableRecords
     */
    private void resolveDetailData2Suggestion(JSONArray currentDetailData, JSONArray workflowRequestTableRecords) {
        StringBuilder sbResult = new StringBuilder();
        JSONObject jo, joFields;
        JSONArray workflowRequestTableFields;
        String fieldName, fieldValue;
        Map<String, String> mapRecord;
        //当前节点
        String dqjd_field = Util.null2String(centerConfig.get("dqjdzd"));
        //审批意见
        String spyj_field = Util.null2String(centerConfig.get("spyjzd"));
        //审批时间
        String spsj_field = Util.null2String(centerConfig.get("spsjzd"));
//        //交换中心的-签字意见esb事件
//        String jhzx_qzyj_esb = Util.null2String(bb.getPropValue("DFMZ_centerconfig", "jhzx_qzyj_esb"));
        //交易类型
        String jylx_field = Util.null2String(centerConfig.get("jylxzd"));
        //2025-08-12 新增审批人字段
        String spr_field = Util.null2String(centerConfig.get("sprzd"));

        //根据当前节点获取下一节点
        Map<String, String> jdMap = getAllNextNodeConfig(Util.null2String(joMainData.get(jylx_field)));

        //不是从异常节点触发的， 拼接此次接口的明细参数数据
        if (!"1".equals(exptReSubmit)) {
            for (int j = 0; j < workflowRequestTableRecords.size(); j++) {
                workflowRequestTableFields = workflowRequestTableRecords.getJSONObject(j).getJSONArray("workflowRequestTableFields");
                mapRecord = new HashMap<>();
                for (int k = 0; k < workflowRequestTableFields.size(); k++) {
                    joFields = workflowRequestTableFields.getJSONObject(k);
                    fieldName = Util.null2String(joFields.get("fieldName"));
                    fieldValue = Util.null2String(joFields.get("fieldValue"));
                    mapRecord.put(fieldName, fieldValue);
                }
                String xyjdValue = Util.null2String(jdMap.get(mapRecord.getOrDefault(dqjd_field, "")));
                String sprValue = Util.null2String(mapRecord.getOrDefault(spr_field, ""));
                sbResult.append(Util.null2String(mapRecord.getOrDefault(dqjd_field, ""))).append("&nbsp;&nbsp;").append("</br>") //当前节点
                        .append(Util.null2String(mapRecord.getOrDefault(spyj_field, ""))).append("&nbsp;&nbsp;")//审批意见
                        .append(Util.null2String(mapRecord.getOrDefault(spsj_field, ""))).append("</br>");//审批时间
                if (!sprValue.isEmpty()) {
                    sbResult.append("审批人：").append(sprValue).append("</br>"); //审批人
                }
                sbResult.append("下一节点：").append(xyjdValue).append("</br>")//下一节点
                        .append("------------------------------")
                        .append("</br>");
            }
        }

        composeRemark = sbResult.toString();


    }

    /**
     * 根据交易类型获取节点配置
     *
     * @param jylx
     * @return
     */
    private Map<String, String> getAllNextNodeConfig(String jylx) {

        Map<String, String> result = new HashMap<>();
        rs = new RecordSet();
        sql = "select * from uf_nccspjddz where jylx = ? ";
        rsFlag = rs.executeQuery(sql, jylx);
        if (rsFlag) {
            while (rs.next()) {
                //当前节点，对应的下一节点
                result.put(Util.null2String(rs.getString("dqjd")), Util.null2String(rs.getString("xyjd")));
            }
        }
        return result;
    }

}
