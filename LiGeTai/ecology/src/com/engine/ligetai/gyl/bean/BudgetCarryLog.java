package com.engine.ligetai.gyl.bean;

import lombok.Data;

/**
 * @FileName BudgetCarryLog.java
 * @Description 预算结转日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/7
 */
@Data
public class BudgetCarryLog {
    /**
     * 结转日期
     */
    private String jzrq;
    /**
     * 结转时间
     */
    private String jzsj;
    /**
     * 结转来源
     */
    private String jzly;
    /**
     * 剩余预算处理信息
     */
    private String syysclxx;
    /**
     * 审批中预算处理信息
     */
    private String spzysclxx;
    /**
     * 转入台账处理信息
     */
    private String zrtzclxx;
    /**
     * 转出台账处理信息
     */
    private String zctzclxx;
    /**
     * 流程周期处理信息
     */
    private String lczqclxx;
    /**
     * 备注
     */
    private String bz;
}
