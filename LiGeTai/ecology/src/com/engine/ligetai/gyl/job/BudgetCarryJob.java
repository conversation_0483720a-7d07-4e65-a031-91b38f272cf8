package com.engine.ligetai.gyl.job;


import com.engine.ligetai.gyl.bean.BudgetCarryLog;
import com.engine.parent.common.util.SDTimeUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.workflow.dto.WfInfo;
import com.engine.parent.workflow.util.WfUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * @FileName BudgetCarryJob.java
 * @Description 预算结转job
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/6
 */
@Getter
@Setter
public class BudgetCarryJob extends BaseCronJob {
    //---Job参数---
    /**
     * 周期类型 0月度 1季度 2半年度
     */
    private String periodType;
    /**
     * 是否结转剩余预算 1是，其他否
     */
    private String runLeftBudget;
    /**
     * 是否结转审批中预算 1是，其他否
     */
    private String runApproveBudget;
    /**
     * 查询总预算的视图名称
     * 默认为：BudgetALL
     * 也就是建模里 预算管理台账-全量写的虚拟视图
     */
    private String viewBudget;
    /**
     * 维度周期表 默认为：FnaPeriodSetting_0
     */
    private String tableNamePeriod;
    /**
     * 预算金额总表 默认为：FnaBudgetDFgro_0_2
     */
    private String tableNameBudgetTotal;

    /**
     * 预算明细表 默认为：FnaExpenseInfo_0_2
     */
    private String tableNameBudgetExpense;
    /**
     * 预算结转入台账表名-uf_ysjzrtz
     */
    private String tableNameBudgetIn;
    /**
     * 预算结转出台账表名-uf_ysjzctz
     */
    private String tableNameBudgetOut;
    /**
     * 建模表名-结转日志表
     */
    private String tableNameLog;
    /**
     * 流程的预算周期字段名
     */
    private String fieldYszq;
    /**
     * 账套id
     */
    private String accountId;
    /**
     * 指定结算日期，格式为：2024-04-01 用于手动执行结转。例如：比如我在4.2号，配置为"2024-04-01"，那么就会执行4.1号的结转逻辑
     */
    private String manualDate;

    //---Job参数---

    private BaseBean bb;

    private String errorMsg;
    //预算转入台账字段列表
    //预算周期,费控科目,成本中心,项目,金额,转入时间,转入来源
    private static final String[] FIELDS_BUDGET_IN = new String[]{"yszq", "fkkm", "cbzx", "xm", "je", "zrsj", "zrly"};
    //预算转出台账字段列表
    //预算周期,费控科目,成本中心,项目,金额,转出时间,转出来源
    private static final String[] FIELDS_BUDGET_OUT = new String[]{"yszq", "fkkm", "cbzx", "xm", "je", "zcsj", "zcly"};


    private void init() {
        bb = new BaseBean();
        errorMsg = "";
    }

    /**
     * 执行
     */
    @Override
    public void execute() {
        //初始化
        init();
        //校验参数
        checkParams();
        if (errorMsg.isEmpty()) {
            //当天是否是预算结转日
            boolean isPeriodDay = false;
            //判断参数
            String today = TimeUtil.getCurrentDateString();
            //如果有指定日期，那么用指定日期
            if (StringUtils.isNotBlank(manualDate)) {
                today = manualDate;
            }

            //判断当天是否是周期日
            //月度预算
            if ("0".equals(periodType)) {
                if (today.endsWith("-01")) {
                    isPeriodDay = true;
                }
            } else if ("1".equals(periodType)) {
                //季度预算
                //4.1号，会把1季度结转到2季度
                //7.1号，会把2季度结转到3季度
                //10.1号，会把3季度结转到4季度
                if (today.endsWith("04-01") || today.endsWith("07-01") || today.endsWith("10-01")) {
                    isPeriodDay = true;
                }
            } else if ("2".equals(periodType)) {
                //半年度预算
                //7.1号，把上半年结转到下半年
                if (today.endsWith("07-01")) {
                    isPeriodDay = true;
                }
            }
            if (isPeriodDay) {
                bb.writeLog("今天为：" + today + ",是预算结转日");
                if ("1".equals(runLeftBudget) || "1".equals(runApproveBudget)) {
                    //step 1: 匹配当天对应的周期
                    //step 2：根据昨天=周期的结束日期 匹配到上个周期
                    //step 3: 查询当前周期、上个周期的预算数据
                    //step 4: 将上个周期结转到当前周期
                    //step 5: 记录结转日志
                    PeriodInfo periodInfo = getPeriodInfo(today);
                    handleBudget(periodInfo.getCurrentPeriod(), periodInfo.getLastPeriod());

                } else {
                    bb.writeLog("runLeftBudget和runApproveBudget参数都不是1，不执行预算结转动作！");
                }
            } else {
                bb.writeLog("今天为：" + today + ",不是预算结转日，不执行逻辑");
            }
        }


    }

    /**
     * 校验参数
     */
    private void checkParams() {
        if (StringUtils.isBlank(periodType) ||
                StringUtils.isBlank(runLeftBudget) ||
                StringUtils.isBlank(runApproveBudget) ||
                StringUtils.isBlank(viewBudget) ||
                StringUtils.isBlank(tableNamePeriod)) {
            errorMsg = "缺失job参数";
            bb.writeLog(errorMsg);
        }
    }

    /**
     * 获取预算周期信息
     *
     * @param today
     * @return
     */
    private PeriodInfo getPeriodInfo(String today) {
        PeriodInfo periodInfo = new PeriodInfo();
        String beginDate, endDate, id;
        String yesterday = TimeUtil.dateAdd(today, -1);
        RecordSet rs = new RecordSet();
        String sql = " select * from " + tableNamePeriod;
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                beginDate = Util.null2String(rs.getString("begindate"));
                endDate = Util.null2String(rs.getString("enddate"));
                id = Util.null2String(rs.getString("id"));
                if (SDTimeUtil.isDateInRange(beginDate, endDate, today)) {
                    periodInfo.setCurrentPeriod(id);
                }
                if (yesterday.equals(endDate)) {
                    periodInfo.setLastPeriod(id);
                }
            }
        }
        return periodInfo;
    }

    /**
     * 处理预算
     *
     * @param currentPeriodId
     * @param lastPeriodId
     */
    private void handleBudget(String currentPeriodId, String lastPeriodId) {
        //成本中心,费控科目,项目
        String department, yskm, project, id, requestid;
        //剩余预算,在途预算
        BigDecimal syamount, ztamount;

        String handleSql;
        List<String> currentLeftSqlList = new ArrayList<>();
        List<String> lastLeftSqlList = new ArrayList<>();
        List<String> currentApproveSqlList = new ArrayList<>();
        List<String> lastApproveSqlList = new ArrayList<>();
        List<String> currentApproveSqlList2 = new ArrayList<>();
        List<String> lastApproveSqlList2 = new ArrayList<>();

        List<String> reqApproveSqlList = new ArrayList<>();


        List<List<Object>> budgetInValues = new ArrayList<>();
        List<List<Object>> budgetOutValues = new ArrayList<>();

        List<Object> value;
        int moduleId;
        StringBuilder sb;
        BudgetCarryLog log = new BudgetCarryLog();
        log.setJzrq(TimeUtil.getCurrentDateString());
        log.setJzsj(TimeUtil.getCurrentTimeString());

        try {
            String currentTime = TimeUtil.getCurrentTimeString();
            RecordSet rs = new RecordSet();
            RecordSet rs2 = new RecordSet();
            //step 1: 查询上个周期的预算
            String sql = "select * from " + viewBudget + " where yszq = ?";
            bb.writeLog("query sql:" + sql);
            bb.writeLog("query sql lastPeriodId:" + lastPeriodId);
            Set<String> allRequestId = new HashSet<>();
            if (rs.executeQuery(sql, lastPeriodId)) {
                while (rs.next()) {
                    id = Util.null2String(rs.getString("id"));
                    department = Util.null2String(rs.getString("department"));
                    yskm = Util.null2String(rs.getString("yskm"));
                    project = Util.null2String(rs.getString("project"));
                    syamount = SDUtil.getBigDecimalValue(Util.null2String(rs.getString("syamount")));
                    ztamount = SDUtil.getBigDecimalValue(Util.null2String(rs.getString("ztamount")));


                    //剩余预算不为0，并且开启了结转剩余预算
                    if (syamount.compareTo(BigDecimal.ZERO) != 0 && "1".equals(runLeftBudget)) {
                        bb.writeLog("---执行结转剩余预算---");
                        bb.writeLog("id:" + id);
                        bb.writeLog("department:" + department);
                        bb.writeLog("yskm:" + yskm);
                        bb.writeLog("project:" + project);
                        bb.writeLog("syamount:" + syamount);
                        bb.writeLog("ztamount:" + ztamount);
                        //step 2-1 更新当前周期的总金额=总金额+上个周期剩余金额
                        handleSql = "update " + tableNameBudgetTotal + " set budgetdata = budgetdata+(" + syamount + ") where 1=1 " +
                                " and budgetMember_1 = '" + department + "' " +
                                " and budgetMember_2 = '" + yskm + "' " +
                                " and budgetMember_3 = '" + currentPeriodId + "' " +
                                " and budgetMember_4 = '" + project + "' ";
                        currentLeftSqlList.add(handleSql);

                        ///step 2-2 更新上个周期的总金额=总金额-上个周期剩余金额
                        handleSql = "update " + tableNameBudgetTotal + " set budgetdata = budgetdata-(" + syamount + ") where 1=1 " +
                                " and budgetMember_1 = '" + department + "' " +
                                " and budgetMember_2 = '" + yskm + "' " +
                                " and budgetMember_3 = '" + lastPeriodId + "' " +
                                " and budgetMember_4 = '" + project + "' ";
                        lastLeftSqlList.add(handleSql);

                        //step 2-3 插入当前周期转入台账数据
                        value = new ArrayList<>();
                        value.add(currentPeriodId);
                        value.add(yskm);
                        value.add(department);
                        value.add(project);
                        value.add(syamount);
                        value.add(currentTime);
                        value.add("定时任务BudgetCarryJob触发,预算id:" + id + ",当前周期id:" + currentPeriodId + ",当前周期转入剩余金额:" + syamount);
                        budgetInValues.add(value);

                        //step 2-4 插入上个周期转出台账数据
                        value = new ArrayList<>();
                        value.add(lastPeriodId);
                        value.add(yskm);
                        value.add(department);
                        value.add(project);
                        value.add(syamount);
                        value.add(currentTime);
                        value.add("定时任务BudgetCarryJob触发，预算id:" + id + ",上个周期id:" + lastPeriodId + ",上个前周期转出剩余金额:" + syamount);
                        budgetOutValues.add(value);

                    }

                    //在途预算不为0，并且开启了结转在途预算
                    if (ztamount.compareTo(BigDecimal.ZERO) != 0 && "1".equals(runApproveBudget)) {
                        bb.writeLog("---执行结转在途预算---");
                        bb.writeLog("id:" + id);
                        bb.writeLog("department:" + department);
                        bb.writeLog("yskm:" + yskm);
                        bb.writeLog("project:" + project);
                        bb.writeLog("syamount:" + syamount);
                        bb.writeLog("ztamount:" + ztamount);
                        //step 3-1 插入当前周期的在途金额
//                        String uuid = String.valueOf(UUID.randomUUID()).replace("-", "");
//                        handleSql = "insert into " + tableNameBudgetExpense + " " +
//                                " (id,accountId,budgetMember_1,budgetMember_2,budgetMember_3,budgetMember_4,expenseStaus,amount)" +
//                                " values (" +
//                                " '" + uuid + "','" + accountId + "','" + department + "','" + yskm + "','" + currentPeriodId + "','" + project + "','0'," + ztamount +
//                                " )";
//                        currentApproveSqlList.add(handleSql);
                        ///step 3-2 更新上个周期的在途预算，将周期改为当前周期
                        handleSql = "update " + tableNameBudgetExpense + " set " +
                                " budgetMember_3 = '" + currentPeriodId + "' " +
                                " where 1=1  " +
                                " and expenseStaus = 0 " +
                                " and budgetMember_1 = '" + department + "' " +
                                " and budgetMember_2 = '" + yskm + "' " +
                                " and budgetMember_3 = '" + lastPeriodId + "' " +
                                " and budgetMember_4 = '" + project + "' ";
                        currentApproveSqlList.add(handleSql);


//                        ///step 3-2 更新上个周期的在途金额都为0
//                        handleSql = "update " + tableNameBudgetExpense + " set amount = 0 where 1=1 and expenseStaus = 0 " +
//                                " and budgetMember_1 = '" + department + "' " +
//                                " and budgetMember_2 = '" + yskm + "' " +
//                                " and budgetMember_3 = '" + lastPeriodId + "' " +
//                                " and budgetMember_4 = '" + project + "' ";
//                        lastApproveSqlList.add(handleSql);


                        //step 3-3 更新当前周期的总金额=总金额+上个周期剩余金额
                        handleSql = "update " + tableNameBudgetTotal + " set budgetdata = budgetdata+(" + ztamount + ") where 1=1 " +
                                " and budgetMember_1 = '" + department + "' " +
                                " and budgetMember_2 = '" + yskm + "' " +
                                " and budgetMember_3 = '" + currentPeriodId + "' " +
                                " and budgetMember_4 = '" + project + "' ";
                        currentApproveSqlList2.add(handleSql);

                        ///step 3-4 更新上个周期的总金额=总金额-上个周期剩余金额
                        handleSql = "update " + tableNameBudgetTotal + " set budgetdata = budgetdata-(" + ztamount + ") where 1=1 " +
                                " and budgetMember_1 = '" + department + "' " +
                                " and budgetMember_2 = '" + yskm + "' " +
                                " and budgetMember_3 = '" + lastPeriodId + "' " +
                                " and budgetMember_4 = '" + project + "' ";
                        lastApproveSqlList2.add(handleSql);


                        //step 3-3 插入当前周期转入台账数据-在途金额
                        value = new ArrayList<>();
                        value.add(currentPeriodId);
                        value.add(yskm);
                        value.add(department);
                        value.add(project);
                        value.add(ztamount);
                        value.add(currentTime);
                        value.add("定时任务BudgetCarryJob触发，预算id:" + id + ", 当前周期id:" + currentPeriodId + ",当前周期转入在途金额:" + ztamount);
                        budgetInValues.add(value);

                        //step 3-4 插入上个周期转出台账数据-在途金额
                        value = new ArrayList<>();
                        value.add(lastPeriodId);
                        value.add(yskm);
                        value.add(department);
                        value.add(project);
                        value.add(ztamount);
                        value.add(currentTime);
                        value.add("定时任务BudgetCarryJob触发，预算id:" + id + ",上个周期id:" + lastPeriodId + ",上个周期转出在途金额:" + ztamount);
                        budgetOutValues.add(value);

                        //step 3-5 加入requestid列表
                        // allRequestId.add(requestid);
                        //获取所有审批中的requestid
                        Set<String> reqIdSet = getReqId(rs2, department, yskm, lastPeriodId, project);
                        bb.writeLog("reqIdSet:" + reqIdSet);
                        allRequestId.addAll(reqIdSet);
                    }

                }
            }
            //处理requestid对应的流程周期字段值
            Map<String, String> reqTableMap = new HashMap<>();
            String tableName;
            //根据预算id，查询所有的相关流程

            //处理在途的预算对应的流程，将周期改为当前新的周期
            for (String reqid : allRequestId) {
                if (reqTableMap.containsKey(reqid)) {
                    tableName = reqTableMap.get(reqid);
                } else {
                    WfInfo wfInfo = WfUtil.getWfInfoByReqId(reqid);
                    tableName = wfInfo.getFormtableName();
                    reqTableMap.put(reqid, tableName);
                }
                handleSql = "update " + tableName + " set " + fieldYszq + "='" + currentPeriodId + "' where requestId = '" + reqid + "' ";
                reqApproveSqlList.add(handleSql);
            }


            //剩余预算处理信息
            sb = new StringBuilder();
            sb.append("更新当前周期的总金额=总金额+上个周期剩余金额,sql列表数：").append("\n");
            sb.append(currentLeftSqlList.size()).append("\n");
            sb.append("更新上个周期的总金额=总金额-上个周期剩余金额,sql列表：").append("\n");
            sb.append(lastLeftSqlList.size()).append("\n");
            log.setSyysclxx(sb.toString());


            //审批中预算处理信息
            sb = new StringBuilder();
            sb.append("更新当前周期的在途预算,sql列表数:").append("\n");
            sb.append(currentApproveSqlList.size()).append("\n");

            log.setSpzysclxx(sb.toString());


            List<String> sqlHandleList = new ArrayList<>(currentLeftSqlList);
            bb.writeLog("currentLeftSqlList:" + currentLeftSqlList);

            sqlHandleList.addAll(lastLeftSqlList);
            bb.writeLog("lastLeftSqlList:" + lastLeftSqlList);

            sqlHandleList.addAll(currentApproveSqlList);
            bb.writeLog("currentApproveSqlList:" + currentApproveSqlList);

            sqlHandleList.addAll(lastApproveSqlList);
            bb.writeLog("lastApproveSqlList:" + lastApproveSqlList);

            sqlHandleList.addAll(currentApproveSqlList2);
            bb.writeLog("currentApproveSqlList2:" + currentApproveSqlList2);

            sqlHandleList.addAll(lastApproveSqlList2);
            bb.writeLog("lastApproveSqlList2:" + lastApproveSqlList2);

            sqlHandleList.addAll(reqApproveSqlList);
            bb.writeLog("reqApproveSqlList:" + reqApproveSqlList);

            //处理sql列表
            if (!sqlHandleList.isEmpty()) {
                bb.writeLog("sqlHandleList size:" + sqlHandleList.size());
                for (String eachSql : sqlHandleList) {
                    if (!rs.executeUpdate(eachSql)) {
                        bb.writeLog("sql:" + eachSql + ";执行出错：" + rs.getExceptionMsg());
                    }
                }
                //预算转入建模插入
                if (!budgetInValues.isEmpty()) {
                    //构造插入bean
                    ModuleInsertBean mb = new ModuleInsertBean();
                    moduleId = ModuleDataUtil.getModuleIdByName(tableNameBudgetIn);
                    mb.setTableName(tableNameBudgetIn)
                            .setFields(Arrays.asList(FIELDS_BUDGET_IN))
                            .setValues(budgetInValues)
                            .setCreatorId(1)
                            .setModuleId(moduleId);
                    //转入台账处理信息
                    log.setZrtzclxx(mb.toString());
                    ModuleResult mr = ModuleDataUtil.insert(mb);
                    if (!mr.isSuccess()) {
                        log.setZrtzclxx(log.getZrtzclxx() + ";出错：" + mr.getErroMsg());
                        bb.writeLog("预算转入建模插入，失败：" + mr.getErroMsg());
                    }
                }
                //预算转出建模插入
                if (!budgetOutValues.isEmpty()) {
                    //构造插入bean
                    ModuleInsertBean mb = new ModuleInsertBean();
                    moduleId = ModuleDataUtil.getModuleIdByName(tableNameBudgetOut);
                    mb.setTableName(tableNameBudgetOut)
                            .setFields(Arrays.asList(FIELDS_BUDGET_OUT))
                            .setValues(budgetOutValues)
                            .setCreatorId(1)
                            .setModuleId(moduleId);
                    log.setZctzclxx(mb.toString());
                    ModuleResult mr = ModuleDataUtil.insert(mb);
                    if (!mr.isSuccess()) {
                        log.setZctzclxx(log.getZctzclxx() + ";出错：" + mr.getErroMsg());
                        bb.writeLog("预算转出建模插入，失败：" + mr.getErroMsg());
                    }
                }
            }

        } catch (Exception e) {
            log.setBz("处理异常：" + SDUtil.getExceptionDetail(e));
            bb.writeLog("处理异常：" + SDUtil.getExceptionDetail(e));
        }
        //插入日志
        insertLog(log);
    }

    /**
     * 插入日志
     *
     * @param log
     */
    private void insertLog(BudgetCarryLog log) {
        String fieldName;
        Object fieldValue;
        try {
            String tableName = tableNameLog;
            Class<?> clazz = BudgetCarryLog.class;
            Field[] fields = clazz.getDeclaredFields();
            List<String> insertFields = new ArrayList<>();
            List<Object> value = new ArrayList<>();
            for (Field field : fields) {
                field.setAccessible(true); // 设置可访问私有字段
                //字段名
                fieldName = field.getName();
                //获取字段值
                fieldValue = field.get(log);
                insertFields.add(fieldName);
                value.add(fieldValue);
            }
            int moduleId = ModuleDataUtil.getModuleIdByName(tableName);
            ModuleInsertBean mb = new ModuleInsertBean();
            mb.setTableName(tableName)
                    .setFields(insertFields)
                    .setValue(value)
                    .setCreatorId(1)
                    .setModuleId(moduleId);
            ModuleDataUtil.insertOneAc(mb);
        } catch (Exception ignored) {
        }
    }

    @Data
    public static class PeriodInfo {
        /**
         * 当前周期id
         */
        private String currentPeriod;
        /**
         * 上个周期id
         */
        private String lastPeriod;
    }

    /**
     * 获取流程requestid
     *
     * @param budgetMember_1
     * @param budgetMember_2
     * @param budgetMember_3
     * @param budgetMember_4
     * @return
     */
    private Set<String> getReqId(RecordSet rs, String budgetMember_1, String budgetMember_2, String budgetMember_3, String budgetMember_4) {
        Set<String> allRequestId = new HashSet<>();
        String sql = "select requestid from " + tableNameBudgetExpense + " where 1=1 " +
                " and budgetMember_1 = ? " +
                " and budgetMember_2 = ? " +
                " and budgetMember_3 = ? " +
                " and budgetMember_4 = ? " +
                " and expenseStaus = 0 ";
        if (rs.executeQuery(sql, budgetMember_1, budgetMember_2, budgetMember_3, budgetMember_4)) {
            while (rs.next()) {
                allRequestId.add(rs.getString("requestid"));
            }
        }
        return allRequestId;

    }

}
