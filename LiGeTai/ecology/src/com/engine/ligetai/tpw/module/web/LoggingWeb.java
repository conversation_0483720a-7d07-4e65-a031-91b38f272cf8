package com.engine.ligetai.tpw.module.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.workflow.biz.requestForm.RequestFormBiz;
import com.engine.workflow.service.impl.RequestFormServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName LoggingWeb
 * @Description
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/11/9
 */
public class LoggingWeb {

    private RequestFormServiceImpl getFormService(HttpServletRequest request, HttpServletResponse response) {
        User user = RequestFormBiz.getFormUser(request, response, false);
        return (RequestFormServiceImpl) ServiceUtil.getService(RequestFormServiceImpl.class, user);
    }

    @POST
    @Path("/generateLogs")
    @Produces({MediaType.TEXT_PLAIN})
    public String getDetailTableInfos(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", true);
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map map = getFormService(request, response).generatePrintLog(request);

            } else {
                result.put("code", false);
                result.put("errorMsg", "user info error");
            }
        } catch (Exception e) {
            result.put("code", false);
            result.put("errorMsg", "catch exception : " + e.getMessage());
        }
        return JSONObject.toJSONString(result);
    }
}